syntax = "proto2";

package pywidevine_license_protocol;

// need this if we are using libprotobuf-cpp-2.3.0-lite
option optimize_for = LITE_RUNTIME;

option java_package = "com.rlaphoenix.pywidevine.protos";

enum LicenseType {
  STREAMING = 1;
  OFFLINE = 2;
  // License type decision is left to provider.
  AUTOMATIC = 3;
}

enum PlatformVerificationStatus {
  // The platform is not verified.
  PLATFORM_UNVERIFIED = 0;
  // Tampering detected on the platform.
  PLATFORM_TAMPERED = 1;
  // The platform has been verified by means of software.
  PLATFORM_SOFTWARE_VERIFIED = 2;
  // The platform has been verified by means of hardware (e.g. secure boot).
  PLATFORM_HARDWARE_VERIFIED = 3;
  // Platform verification was not performed.
  PLATFORM_NO_VERIFICATION = 4;
  // Platform and secure storage capability have been verified by means of
  // software.
  PLATFORM_SECURE_STORAGE_SOFTWARE_VERIFIED = 5;
}

// LicenseIdentification is propagated from LicenseRequest to License,
// incrementing version with each iteration.
message LicenseIdentification {
  optional bytes request_id = 1;
  optional bytes session_id = 2;
  optional bytes purchase_id = 3;
  optional LicenseType type = 4;
  optional int32 version = 5;
  optional bytes provider_session_token = 6;
}

message License {
  message Policy {
    // Indicates that playback of the content is allowed.
    optional bool can_play = 1 [default = false];

    // Indicates that the license may be persisted to non-volatile
    // storage for offline use.
    optional bool can_persist = 2 [default = false];

    // Indicates that renewal of this license is allowed.
    optional bool can_renew = 3 [default = false];

    // For the |*duration*| fields, playback must halt when
    // license_start_time (seconds since the epoch (UTC)) +
    // license_duration_seconds is exceeded. A value of 0
    // indicates that there is no limit to the duration.

    // Indicates the rental window.
    optional int64 rental_duration_seconds = 4 [default = 0];

    // Indicates the viewing window, once playback has begun.
    optional int64 playback_duration_seconds = 5 [default = 0];

    // Indicates the time window for this specific license.
    optional int64 license_duration_seconds = 6 [default = 0];

    // The |renewal*| fields only apply if |can_renew| is true.

    // The window of time, in which playback is allowed to continue while
    // renewal is attempted, yet unsuccessful due to backend problems with
    // the license server.
    optional int64 renewal_recovery_duration_seconds = 7 [default = 0];

    // All renewal requests for this license shall be directed to the
    // specified URL.
    optional string renewal_server_url = 8;

    // How many seconds after license_start_time, before renewal is first
    // attempted.
    optional int64 renewal_delay_seconds = 9 [default = 0];

    // Specifies the delay in seconds between subsequent license
    // renewal requests, in case of failure.
    optional int64 renewal_retry_interval_seconds = 10 [default = 0];

    // Indicates that the license shall be sent for renewal when usage is
    // started.
    optional bool renew_with_usage = 11 [default = false];

    // Indicates to client that license renewal and release requests ought to
    // include ClientIdentification (client_id).
    optional bool always_include_client_id = 12 [default = false];

    // Duration of grace period before playback_duration_seconds (short window)
    // goes into effect. Optional.
    optional int64 play_start_grace_period_seconds = 13 [default = 0];

    // Enables "soft enforcement" of playback_duration_seconds, letting the user
    // finish playback even if short window expires. Optional.
    optional bool soft_enforce_playback_duration = 14 [default = false];

    // Enables "soft enforcement" of rental_duration_seconds. Initial playback
    // must always start before rental duration expires.  In order to allow
    // subsequent playbacks to start after the rental duration expires,
    // soft_enforce_playback_duration must be true. Otherwise, subsequent
    // playbacks will not be allowed once rental duration expires. Optional.
    optional bool soft_enforce_rental_duration = 15 [default = true];
  }

  message KeyContainer {
    enum KeyType {
      SIGNING = 1;           // Exactly one key of this type must appear.
      CONTENT = 2;           // Content key.
      KEY_CONTROL = 3;       // Key control block for license renewals. No key.
      OPERATOR_SESSION = 4;  // wrapped keys for auxiliary crypto operations.
      ENTITLEMENT = 5;       // Entitlement keys.
      OEM_CONTENT = 6;       // Partner-specific content key.
    }

    // The SecurityLevel enumeration allows the server to communicate the level
    // of robustness required by the client, in order to use the key.
    enum SecurityLevel {
      // Software-based whitebox crypto is required.
      SW_SECURE_CRYPTO = 1;

      // Software crypto and an obfuscated decoder is required.
      SW_SECURE_DECODE = 2;

      // The key material and crypto operations must be performed within a
      // hardware backed trusted execution environment.
      HW_SECURE_CRYPTO = 3;

      // The crypto and decoding of content must be performed within a hardware
      // backed trusted execution environment.
      HW_SECURE_DECODE = 4;

      // The crypto, decoding and all handling of the media (compressed and
      // uncompressed) must be handled within a hardware backed trusted
      // execution environment.
      HW_SECURE_ALL = 5;
    }

    message KeyControl {
      // |key_control| is documented in:
      // Widevine Modular DRM Security Integration Guide for CENC
      // If present, the key control must be communicated to the secure
      // environment prior to any usage. This message is automatically generated
      // by the Widevine License Server SDK.
      optional bytes key_control_block = 1;
      optional bytes iv = 2;
    }

    message OutputProtection {
      // Indicates whether HDCP is required on digital outputs, and which
      // version should be used.
      enum HDCP {
        HDCP_NONE = 0;
        HDCP_V1 = 1;
        HDCP_V2 = 2;
        HDCP_V2_1 = 3;
        HDCP_V2_2 = 4;
        HDCP_V2_3 = 5;
        HDCP_NO_DIGITAL_OUTPUT = 0xff;
      }
      optional HDCP hdcp = 1 [default = HDCP_NONE];

      // Indicate the CGMS setting to be inserted on analog output.
      enum CGMS {
        CGMS_NONE = 42;
        COPY_FREE = 0;
        COPY_ONCE = 2;
        COPY_NEVER = 3;
      }
      optional CGMS cgms_flags = 2 [default = CGMS_NONE];

      enum HdcpSrmRule {
        HDCP_SRM_RULE_NONE = 0;
        // In 'required_protection', this means most current SRM is required.
        // Update the SRM on the device. If update cannot happen,
        // do not allow the key.
        // In 'requested_protection', this means most current SRM is requested.
        // Update the SRM on the device. If update cannot happen,
        // allow use of the key anyway.
        CURRENT_SRM = 1;
      }
      optional HdcpSrmRule hdcp_srm_rule = 3 [default = HDCP_SRM_RULE_NONE];
      // Optional requirement to indicate analog output is not allowed.
      optional bool disable_analog_output = 4 [default = false];
      // Optional requirement to indicate digital output is not allowed.
      optional bool disable_digital_output = 5 [default = false];
    }

    message VideoResolutionConstraint {
      // Minimum and maximum video resolutions in the range (height x width).
      optional uint32 min_resolution_pixels = 1;
      optional uint32 max_resolution_pixels = 2;
      // Optional output protection requirements for this range. If not
      // specified, the OutputProtection in the KeyContainer applies.
      optional OutputProtection required_protection = 3;
    }

    message OperatorSessionKeyPermissions {
      // Permissions/key usage flags for operator service keys
      // (type = OPERATOR_SESSION).
      optional bool allow_encrypt = 1 [default = false];
      optional bool allow_decrypt = 2 [default = false];
      optional bool allow_sign = 3 [default = false];
      optional bool allow_signature_verify = 4 [default = false];
    }

    optional bytes id = 1;
    optional bytes iv = 2;
    optional bytes key = 3;
    optional KeyType type = 4;
    optional SecurityLevel level = 5 [default = SW_SECURE_CRYPTO];
    optional OutputProtection required_protection = 6;
    // NOTE: Use of requested_protection is not recommended as it is only
    // supported on a small number of platforms.
    optional OutputProtection requested_protection = 7;
    optional KeyControl key_control = 8;
    optional OperatorSessionKeyPermissions operator_session_key_permissions = 9;
    // Optional video resolution constraints. If the video resolution of the
    // content being decrypted/decoded falls within one of the specified ranges,
    // the optional required_protections may be applied. Otherwise an error will
    // be reported.
    // NOTE: Use of this feature is not recommended, as it is only supported on
    // a small number of platforms.
    repeated VideoResolutionConstraint video_resolution_constraints = 10;
    // Optional flag to indicate the key must only be used if the client
    // supports anti rollback of the user table.  Content provider can query the
    // client capabilities to determine if the client support this feature.
    optional bool anti_rollback_usage_table = 11 [default = false];
    // Optional not limited to commonly known track types such as SD, HD.
    // It can be some provider defined label to identify the track.
    optional string track_label = 12;
  }

  optional LicenseIdentification id = 1;
  optional Policy policy = 2;
  repeated KeyContainer key = 3;
  // Time of the request in seconds (UTC) as set in
  // LicenseRequest.request_time.  If this time is not set in the request,
  // the local time at the license service is used in this field.
  optional int64 license_start_time = 4;
  optional bool remote_attestation_verified = 5 [default = false];
  // Client token generated by the content provider. Optional.
  optional bytes provider_client_token = 6;
  // 4cc code specifying the CENC protection scheme as defined in the CENC 3.0
  // specification. Propagated from Widevine PSSH box. Optional.
  optional uint32 protection_scheme = 7;
  // 8 byte verification field "HDCPDATA" followed by unsigned 32 bit minimum
  // HDCP SRM version (whether the version is for HDCP1 SRM or HDCP2 SRM
  // depends on client max_hdcp_version).
  // Additional details can be found in Widevine Modular DRM Security
  // Integration Guide for CENC.
  optional bytes srm_requirement = 8;
  // If present this contains a signed SRM file (either HDCP1 SRM or HDCP2 SRM
  // depending on client max_hdcp_version) that should be installed on the
  // client device.
  optional bytes srm_update = 9;
  // Indicates the status of any type of platform verification performed by the
  // server.
  optional PlatformVerificationStatus platform_verification_status = 10
      [default = PLATFORM_NO_VERIFICATION];
  // IDs of the groups for which keys are delivered in this license, if any.
  repeated bytes group_ids = 11;
}

enum ProtocolVersion {
  VERSION_2_0 = 20;
  VERSION_2_1 = 21;
  VERSION_2_2 = 22;
}

message LicenseRequest {
  message ContentIdentification {
    message WidevinePsshData {
      repeated bytes pssh_data = 1;
      optional LicenseType license_type = 2;
      optional bytes request_id = 3;  // Opaque, client-specified.
    }

    message WebmKeyId {
      optional bytes header = 1;
      optional LicenseType license_type = 2;
      optional bytes request_id = 3;  // Opaque, client-specified.
    }

    message ExistingLicense {
      optional LicenseIdentification license_id = 1;
      optional int64 seconds_since_started = 2;
      optional int64 seconds_since_last_played = 3;
      optional bytes session_usage_table_entry = 4;
    }

    message InitData {
      enum InitDataType {
        CENC = 1;
        WEBM = 2;
      }

      optional InitDataType init_data_type = 1 [default = CENC];
      optional bytes init_data = 2;
      optional LicenseType license_type = 3;
      optional bytes request_id = 4;
    }

    oneof content_id_variant {
      // Exactly one of these must be present.
      WidevinePsshData widevine_pssh_data = 1;
      WebmKeyId webm_key_id = 2;
      ExistingLicense existing_license = 3;
      InitData init_data = 4;
    }
  }

  enum RequestType {
    NEW = 1;
    RENEWAL = 2;
    RELEASE = 3;
  }

  // The client_id provides information authenticating the calling device.  It
  // contains the Widevine keybox token that was installed on the device at the
  // factory.  This field or encrypted_client_id below is required for a valid
  // license request, but both should never be present in the same request.
  optional ClientIdentification client_id = 1;
  optional ContentIdentification content_id = 2;
  optional RequestType type = 3;
  // Time of the request in seconds (UTC) as set by the client.
  optional int64 request_time = 4;
  // Old-style decimal-encoded string key control nonce.
  optional bytes key_control_nonce_deprecated = 5;
  optional ProtocolVersion protocol_version = 6 [default = VERSION_2_0];
  // New-style uint32 key control nonce, please use instead of
  // key_control_nonce_deprecated.
  optional uint32 key_control_nonce = 7;
  // Encrypted ClientIdentification message, used for privacy purposes.
  optional EncryptedClientIdentification encrypted_client_id = 8;
}

message MetricData {
  enum MetricType {
    // The time spent in the 'stage', specified in microseconds.
    LATENCY = 1;
    // The UNIX epoch timestamp at which the 'stage' was first accessed in
    // microseconds.
    TIMESTAMP = 2;
  }

  message TypeValue {
    optional MetricType type = 1;
    // The value associated with 'type'.  For example if type == LATENCY, the
    // value would be the time in microseconds spent in this 'stage'.
    optional int64 value = 2 [default = 0];
  }

  // 'stage' that is currently processing the SignedMessage.  Required.
  optional string stage_name = 1;
  // metric and associated value.
  repeated TypeValue metric_data = 2;
}

message VersionInfo {
  // License SDK version reported by the Widevine License SDK. This field
  // is populated automatically by the SDK.
  optional string license_sdk_version = 1;
  // Version of the service hosting the license SDK. This field is optional.
  // It may be provided by the hosting service.
  optional string license_service_version = 2;
}

message SignedMessage {
  enum MessageType {
    LICENSE_REQUEST = 1;
    LICENSE = 2;
    ERROR_RESPONSE = 3;
    SERVICE_CERTIFICATE_REQUEST = 4;
    SERVICE_CERTIFICATE = 5;
    SUB_LICENSE = 6;
    CAS_LICENSE_REQUEST = 7;
    CAS_LICENSE = 8;
    EXTERNAL_LICENSE_REQUEST = 9;
    EXTERNAL_LICENSE = 10;
  }

  enum SessionKeyType {
    UNDEFINED = 0;
    WRAPPED_AES_KEY = 1;
    EPHERMERAL_ECC_PUBLIC_KEY = 2;
  }
  optional MessageType type = 1;
  optional bytes msg = 2;
  // Required field that contains the signature of the bytes of msg.
  // For license requests, the signing algorithm is determined by the
  // certificate contained in the request.
  // For license responses, the signing algorithm is HMAC with signing key based
  // on |session_key|.
  optional bytes signature = 3;
  // If populated, the contents of this field will be signaled by the
  // |session_key_type| type. If the |session_key_type| is WRAPPED_AES_KEY the
  // key is the bytes of an encrypted AES key. If the |session_key_type| is
  // EPHERMERAL_ECC_PUBLIC_KEY the field contains the bytes of an RFC5208 ASN1
  // serialized ECC public key.
  optional bytes session_key = 4;
  // Remote attestation data which will be present in the initial license
  // request for ChromeOS client devices operating in verified mode. Remote
  // attestation challenge data is |msg| field above. Optional.
  optional bytes remote_attestation = 5;

  repeated MetricData metric_data = 6;
  // Version information from the SDK and license service. This information is
  // provided in the license response.
  optional VersionInfo service_version_info = 7;
  // Optional field that contains the algorithm type used to generate the
  // session_key and signature in a LICENSE message.
  optional SessionKeyType session_key_type = 8 [default = WRAPPED_AES_KEY];
  // The core message is the simple serialization of fields used by OEMCrypto.
  // This field was introduced in OEMCrypto API v16.
  optional bytes oemcrypto_core_message = 9;
}

enum HashAlgorithmProto {
  // Unspecified hash algorithm: SHA_256 shall be used for ECC based algorithms
  // and SHA_1 shall be used otherwise.
  HASH_ALGORITHM_UNSPECIFIED = 0;
  HASH_ALGORITHM_SHA_1 = 1;
  HASH_ALGORITHM_SHA_256 = 2;
  HASH_ALGORITHM_SHA_384 = 3;
}

// ClientIdentification message used to authenticate the client device.
message ClientIdentification {
  enum TokenType {
    KEYBOX = 0;
    DRM_DEVICE_CERTIFICATE = 1;
    REMOTE_ATTESTATION_CERTIFICATE = 2;
    OEM_DEVICE_CERTIFICATE = 3;
  }

  message NameValue {
    optional string name = 1;
    optional string value = 2;
  }

  // Capabilities which not all clients may support. Used for the license
  // exchange protocol only.
  message ClientCapabilities {
    enum HdcpVersion {
      HDCP_NONE = 0;
      HDCP_V1 = 1;
      HDCP_V2 = 2;
      HDCP_V2_1 = 3;
      HDCP_V2_2 = 4;
      HDCP_V2_3 = 5;
      HDCP_NO_DIGITAL_OUTPUT = 0xff;
    }

    enum CertificateKeyType {
      RSA_2048 = 0;
      RSA_3072 = 1;
      ECC_SECP256R1 = 2;
      ECC_SECP384R1 = 3;
      ECC_SECP521R1 = 4;
    }

    enum AnalogOutputCapabilities {
      ANALOG_OUTPUT_UNKNOWN = 0;
      ANALOG_OUTPUT_NONE = 1;
      ANALOG_OUTPUT_SUPPORTED = 2;
      ANALOG_OUTPUT_SUPPORTS_CGMS_A = 3;
    }

    optional bool client_token = 1 [default = false];
    optional bool session_token = 2 [default = false];
    optional bool video_resolution_constraints = 3 [default = false];
    optional HdcpVersion max_hdcp_version = 4 [default = HDCP_NONE];
    optional uint32 oem_crypto_api_version = 5;
    // Client has hardware support for protecting the usage table, such as
    // storing the generation number in secure memory.  For Details, see:
    // Widevine Modular DRM Security Integration Guide for CENC
    optional bool anti_rollback_usage_table = 6 [default = false];
    // The client shall report |srm_version| if available.
    optional uint32 srm_version = 7;
    // A device may have SRM data, and report a version, but may not be capable
    // of updating SRM data.
    optional bool can_update_srm = 8 [default = false];
    repeated CertificateKeyType supported_certificate_key_type = 9;
    optional AnalogOutputCapabilities analog_output_capabilities = 10
        [default = ANALOG_OUTPUT_UNKNOWN];
    optional bool can_disable_analog_output = 11 [default = false];
    // Clients can indicate a performance level supported by OEMCrypto.
    // This will allow applications and providers to choose an appropriate
    // quality of content to serve. Currently defined tiers are
    // 1 (low), 2 (medium) and 3 (high). Any other value indicates that
    // the resource rating is unavailable or reporting erroneous values
    // for that device. For details see,
    // Widevine Modular DRM Security Integration Guide for CENC
    optional uint32 resource_rating_tier = 12 [default = 0];
  }

  message ClientCredentials {
    optional TokenType type = 1 [default = KEYBOX];
    optional bytes token = 2;
  }

  // Type of factory-provisioned device root of trust. Optional.
  optional TokenType type = 1 [default = KEYBOX];
  // Factory-provisioned device root of trust. Required.
  optional bytes token = 2;
  // Optional client information name/value pairs.
  repeated NameValue client_info = 3;
  // Client token generated by the content provider. Optional.
  optional bytes provider_client_token = 4;
  // Number of licenses received by the client to which the token above belongs.
  // Only present if client_token is specified.
  optional uint32 license_counter = 5;
  // List of non-baseline client capabilities.
  optional ClientCapabilities client_capabilities = 6;
  // Serialized VmpData message. Optional.
  optional bytes vmp_data = 7;
  // Optional field that may contain additional provisioning credentials.
  repeated ClientCredentials device_credentials = 8;
}

// EncryptedClientIdentification message used to hold ClientIdentification
// messages encrypted for privacy purposes.
message EncryptedClientIdentification {
  // Provider ID for which the ClientIdentifcation is encrypted (owner of
  // service certificate).
  optional string provider_id = 1;
  // Serial number for the service certificate for which ClientIdentification is
  // encrypted.
  optional bytes service_certificate_serial_number = 2;
  // Serialized ClientIdentification message, encrypted with the privacy key
  // using AES-128-CBC with PKCS#5 padding.
  optional bytes encrypted_client_id = 3;
  // Initialization vector needed to decrypt encrypted_client_id.
  optional bytes encrypted_client_id_iv = 4;
  // AES-128 privacy key, encrypted with the service public key using RSA-OAEP.
  optional bytes encrypted_privacy_key = 5;
}

// DRM certificate definition for user devices, intermediate, service, and root
// certificates.
message DrmCertificate {
  enum Type {
    ROOT = 0;  // ProtoBestPractices: ignore.
    DEVICE_MODEL = 1;
    DEVICE = 2;
    SERVICE = 3;
    PROVISIONER = 4;
  }
  enum ServiceType {
    UNKNOWN_SERVICE_TYPE = 0;
    LICENSE_SERVER_SDK = 1;
    LICENSE_SERVER_PROXY_SDK = 2;
    PROVISIONING_SDK = 3;
    CAS_PROXY_SDK = 4;
  }
  enum Algorithm {
    UNKNOWN_ALGORITHM = 0;
    RSA = 1;
    ECC_SECP256R1 = 2;
    ECC_SECP384R1 = 3;
    ECC_SECP521R1 = 4;
  }

  message EncryptionKey {
    // Device public key. PKCS#1 ASN.1 DER-encoded. Required.
    optional bytes public_key = 1;
    // Required. The algorithm field contains the curve used to create the
    // |public_key| if algorithm is one of the ECC types.
    // The |algorithm| is used for both to determine the if the certificate is
    // ECC or RSA. The |algorithm| also specifies the parameters that were used
    // to create |public_key| and are used to create an ephemeral session key.
    optional Algorithm algorithm = 2 [default = RSA];
  }

  // Type of certificate. Required.
  optional Type type = 1;
  // 128-bit globally unique serial number of certificate.
  // Value is 0 for root certificate. Required.
  optional bytes serial_number = 2;
  // POSIX time, in seconds, when the certificate was created. Required.
  optional uint32 creation_time_seconds = 3;
  // POSIX time, in seconds, when the certificate should expire. Value of zero
  // denotes indefinite expiry time. For more information on limited lifespan
  // DRM certificates see (go/limited-lifespan-drm-certificates).
  optional uint32 expiration_time_seconds = 12;
  // Device public key. PKCS#1 ASN.1 DER-encoded. Required.
  optional bytes public_key = 4;
  // Widevine system ID for the device. Required for intermediate and
  // user device certificates.
  optional uint32 system_id = 5;
  // Deprecated field, which used to indicate whether the device was a test
  // (non-production) device. The test_device field in ProvisionedDeviceInfo
  // below should be observed instead.
  optional bool test_device_deprecated = 6 [deprecated = true];
  // Service identifier (web origin) for the provider which owns the
  // certificate. Required for service and provisioner certificates.
  optional string provider_id = 7;
  // This field is used only when type = SERVICE to specify which SDK uses
  // service certificate. This repeated field is treated as a set. A certificate
  // may be used for the specified service SDK if the appropriate ServiceType
  // is specified in this field.
  repeated ServiceType service_types = 8;
  // Required. The algorithm field contains the curve used to create the
  // |public_key| if algorithm is one of the ECC types.
  // The |algorithm| is used for both to determine the if the certificate is ECC
  // or RSA. The |algorithm| also specifies the parameters that were used to
  // create |public_key| and are used to create an ephemeral session key.
  optional Algorithm algorithm = 9 [default = RSA];
  // Optional. May be present in DEVICE certificate types. This is the root
  // of trust identifier that holds an encrypted value that identifies the
  // keybox or other root of trust that was used to provision a DEVICE drm
  // certificate.
  optional bytes rot_id = 10;
  // Optional. May be present in devices that explicitly support dual keys. When
  // present the |public_key| is used for verification of received license
  // request messages.
  optional EncryptionKey encryption_key = 11;
}

// DrmCertificate signed by a higher (CA) DRM certificate.
message SignedDrmCertificate {
  // Serialized certificate. Required.
  optional bytes drm_certificate = 1;
  // Signature of certificate. Signed with root or intermediate
  // certificate specified below. Required.
  optional bytes signature = 2;
  // SignedDrmCertificate used to sign this certificate.
  optional SignedDrmCertificate signer = 3;
  // Optional field that indicates the hash algorithm used in signature scheme.
  optional HashAlgorithmProto hash_algorithm = 4;
}

message WidevinePsshData {
  enum Type {
    SINGLE = 0;        // Single PSSH to be used to retrieve content keys.
    ENTITLEMENT = 1;   // Primary PSSH used to retrieve entitlement keys.
    ENTITLED_KEY = 2;  // Secondary PSSH containing entitled key(s).
  }

  message EntitledKey {
    // ID of entitlement key used for wrapping |key|.
    optional bytes entitlement_key_id = 1;
    // ID of the entitled key.
    optional bytes key_id = 2;
    // Wrapped key. Required.
    optional bytes key = 3;
    // IV used for wrapping |key|. Required.
    optional bytes iv = 4;
    // Size of entitlement key used for wrapping |key|.
    optional uint32 entitlement_key_size_bytes = 5 [default = 32];
  }

  // Entitlement or content key IDs. Can onnly present in SINGLE or ENTITLEMENT
  // PSSHs. May be repeated to facilitate delivery of multiple keys in a
  // single license. Cannot be used in conjunction with content_id or
  // group_ids, which are the preferred mechanism.
  repeated bytes key_ids = 2;

  // Content identifier which may map to multiple entitlement or content key
  // IDs to facilitate the delivery of multiple keys in a single license.
  // Cannot be present in conjunction with key_ids, but if used must be in all
  // PSSHs.
  optional bytes content_id = 4;

  // Crypto period index, for media using key rotation. Always corresponds to
  // The content key period. This means that if using entitlement licensing
  // the ENTITLED_KEY PSSHs will have sequential crypto_period_index's, whereas
  // the ENTITELEMENT PSSHs will have gaps in the sequence. Required if doing
  // key rotation.
  optional uint32 crypto_period_index = 7;

  // Protection scheme identifying the encryption algorithm. The protection
  // scheme is represented as a uint32 value. The uint32 contains 4 bytes each
  // representing a single ascii character in one of the 4CC protection scheme
  // values. To be deprecated in favor of signaling from content.
  // 'cenc' (AES-CTR) protection_scheme = 0x63656E63,
  // 'cbc1' (AES-CBC) protection_scheme = 0x63626331,
  // 'cens' (AES-CTR pattern encryption) protection_scheme = 0x63656E73,
  // 'cbcs' (AES-CBC pattern encryption) protection_scheme = 0x63626373.
  optional uint32 protection_scheme = 9;

  // Optional. For media using key rotation, this represents the duration
  // of each crypto period in seconds.
  optional uint32 crypto_period_seconds = 10;

  // Type of PSSH. Required if not SINGLE.
  optional Type type = 11 [default = SINGLE];

  // Key sequence for Widevine-managed keys. Optional.
  optional uint32 key_sequence = 12;

  // Group identifiers for all groups to which the content belongs. This can
  // be used to deliver licenses to unlock multiple titles / channels.
  // Optional, and may only be present in ENTITLEMENT and ENTITLED_KEY PSSHs, and
  // not in conjunction with key_ids.
  repeated bytes group_ids = 13;

  // Copy/copies of the content key used to decrypt the media stream in which
  // the PSSH box is embedded, each wrapped with a different entitlement key.
  // May also contain sub-licenses to support devices with OEMCrypto 13 or
  // older. May be repeated if using group entitlement keys. Present only in
  // PSSHs of type ENTITLED_KEY.
  repeated EntitledKey entitled_keys = 14;

  // Video feature identifier, which is used in conjunction with |content_id|
  // to determine the set of keys to be returned in the license. Cannot be
  // present in conjunction with |key_ids|.
  // Current values are "HDR".
  optional string video_feature = 15;

  ////////////////////////////  Deprecated Fields  ////////////////////////////
  enum Algorithm {
    UNENCRYPTED = 0;
    AESCTR = 1;
  };
  optional Algorithm algorithm = 1 [deprecated = true];

  // Content provider name.
  optional string provider = 3 [deprecated = true];

  // Track type. Acceptable values are SD, HD and AUDIO. Used to
  // differentiate content keys used by an asset.
  optional string track_type = 5 [deprecated = true];

  // The name of a registered policy to be used for this asset.
  optional string policy = 6 [deprecated = true];

  // Optional protected context for group content. The grouped_license is a
  // serialized SignedMessage.
  optional bytes grouped_license = 8 [deprecated = true];
}

// File Hashes for Verified Media Path (VMP) support.
message FileHashes {
  message Signature {
    optional string filename = 1;
    optional bool test_signing = 2; //0 - release, 1 - testing
    optional bytes SHA512Hash = 3;
    optional bool main_exe = 4; //0 for dlls, 1 for exe, this is field 3 in file
    optional bytes signature = 5;
  }
  optional bytes signer = 1;
  repeated Signature signatures = 2;
}
