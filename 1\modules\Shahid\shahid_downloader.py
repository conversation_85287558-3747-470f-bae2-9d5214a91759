"""
Shahid Downloader Module
This module handles downloading content from <PERSON><PERSON>.
"""

import os
import shutil
import subprocess
import re
import requests
import time
from bs4 import BeautifulSoup
import xml.etree.ElementTree as ET
from langcodes import Language
import threading
from PySide6.QtCore import QObject, Signal, QTimer
import glob

# Import resource utils for exe compatibility
try:
    from modules.resource_utils import get_resource_path, get_binary_path
except ImportError:
    # Fallback for development mode
    def get_resource_path(path):
        return path
    def get_binary_path(path):
        return f"binaries/{path}"

class DownloadProgressSignals(QObject):
    """Signals for download progress updates."""
    progress_updated = Signal(int, int)  # row, progress
    status_updated = Signal(int, str)    # row, status
    overall_progress_updated = Signal(int)  # overall progress
    download_completed = Signal(int, bool)  # row, success

class ShahidDownloader:
    def __init__(self, settings_manager=None):
        # Get the YANGO root directory (parent of the modules directory)
        self.dir_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        print(f"Downloader initialized with base directory: {self.dir_path}")

        self.signals = DownloadProgressSignals()
        self.active_downloads = {}  # Dictionary to track active downloads
        self.download_threads = {}  # Dictionary to track download threads
        self.download_logs = {}     # Dictionary to store download logs for error analysis
        self.settings_manager = settings_manager

        # Define paths to binaries using resource utils for exe compatibility
        self.libs_path = get_resource_path('binaries')
        self.mkvmerge_path = get_binary_path('mkvmerge.exe')
        self.n_m3u8dl_path = get_binary_path('N_m3u8DL-RE.exe')
        self.mediainfo_path = get_binary_path('mediainfo.exe')
        self.subtitleedit_path = get_binary_path('SubtitleEdit.exe')
        self.ffmpeg_path = get_binary_path('ffmpeg.exe')
        self.ytdlp_path = get_binary_path('yt-dlp.exe')
        self.mp4decrypt_path = get_binary_path('mp4decrypt.exe')
        self.keys_path = os.path.join(self.dir_path, 'KEYS', 'KEYS.txt')

        # Create necessary directories
        os.makedirs(os.path.join(self.dir_path, 'cache'), exist_ok=True)
        os.makedirs(os.path.join(self.dir_path, 'downloads'), exist_ok=True)

        # Create KEYS directory and file if they don't exist
        os.makedirs(os.path.dirname(self.keys_path), exist_ok=True)
        if not os.path.exists(self.keys_path):
            with open(self.keys_path, 'w') as f:
                f.write("# Shahid DRM Keys\n# Format: KID:KEY\n")

        # IDM path detection
        self.idm_path = self.detect_idm_path()

        print(f"Cache directory: {os.path.join(self.dir_path, 'cache')}")
        print(f"Downloads directory: {os.path.join(self.dir_path, 'downloads')}")
        print(f"Keys file: {self.keys_path}")
        print(f"IDM path: {self.idm_path}")

    def detect_idm_path(self):
        """Detect IDM installation path."""
        # First check if custom path is set in settings
        if self.settings_manager:
            custom_path = self.settings_manager.get_setting("idm", "custom_path")
            if custom_path and os.path.exists(custom_path):
                print(f"[IDM] Using custom IDM path: {custom_path}")
                return custom_path

        # Check common installation paths
        possible_paths = [
            r"C:\Program Files (x86)\Internet Download Manager\IDMan.exe",
            r"C:\Program Files\Internet Download Manager\IDMan.exe",
            r"C:\IDM\IDMan.exe",
            r"D:\Program Files (x86)\Internet Download Manager\IDMan.exe",
            r"D:\Program Files\Internet Download Manager\IDMan.exe"
        ]

        for path in possible_paths:
            if os.path.exists(path):
                print(f"[IDM] Found IDM at: {path}")
                return path

        print("[IDM] IDM not found in common locations")
        return None

    def is_idm_available(self):
        """Check if IDM is available for use."""
        return self.idm_path is not None and os.path.exists(self.idm_path)

    def download_with_idm(self, url, download_path, filename, headers=None):
        """Download a file using IDM."""
        if not self.is_idm_available():
            print("[IDM] IDM is not available")
            return False

        try:
            # Create download directory if it doesn't exist
            os.makedirs(download_path, exist_ok=True)

            # Construct IDM command
            # /d URL - downloads a file
            # /p local_path - defines the local path where to save the file
            # /f local_file_name - defines the local file name to save the file
            # /n - turns on the silent mode when IDM doesn't ask any questions
            # /a - add a file to download queue, but don't start downloading

            idm_command = [
                self.idm_path,
                "/d", url,
                "/p", download_path,
                "/f", filename,
                "/n"  # Silent mode
            ]

            print(f"[IDM] Starting download with command: {' '.join(idm_command)}")

            # Start IDM download
            process = subprocess.Popen(
                idm_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=False
            )

            # Wait for process to complete
            stdout, stderr = process.communicate()

            if process.returncode == 0:
                print(f"[IDM] Download started successfully for: {filename}")
                return True
            else:
                print(f"[IDM] Download failed. Return code: {process.returncode}")
                print(f"[IDM] Error: {stderr.decode('utf-8', errors='ignore')}")
                return False

        except Exception as e:
            print(f"[IDM] Error starting download: {e}")
            return False

    def download_with_idm_queue(self, url, download_path, filename, headers=None):
        """Add a file to IDM download queue without starting immediately."""
        if not self.is_idm_available():
            print("[IDM] IDM is not available")
            return False

        try:
            # Create download directory if it doesn't exist
            os.makedirs(download_path, exist_ok=True)

            # Construct IDM command with /a flag to add to queue
            idm_command = [
                self.idm_path,
                "/d", url,
                "/p", download_path,
                "/f", filename,
                "/n",  # Silent mode
                "/a"   # Add to queue without starting
            ]

            print(f"[IDM] Adding to queue with command: {' '.join(idm_command)}")

            # Start IDM download
            process = subprocess.Popen(
                idm_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=False
            )

            # Wait for process to complete
            stdout, stderr = process.communicate()

            if process.returncode == 0:
                print(f"[IDM] Added to queue successfully: {filename}")
                return True
            else:
                print(f"[IDM] Failed to add to queue. Return code: {process.returncode}")
                print(f"[IDM] Error: {stderr.decode('utf-8', errors='ignore')}")
                return False

        except Exception as e:
            print(f"[IDM] Error adding to queue: {e}")
            return False

    def extract_stream_info_from_url(self, mpd_url):
        """Extract stream information from MPD URL."""
        try:
            print(f"[DOWNLOADER] Requesting MPD from URL: {mpd_url}")
            response = requests.get(mpd_url)
            print(f"[DOWNLOADER] MPD response status code: {response.status_code}")

            if response.status_code != 200:
                print(f"[ERROR] Failed to retrieve MPD file. Status code: {response.status_code}")
                print(f"[ERROR] Response content: {response.text[:500]}...")
                return [], [], []

            mpd_content = response.content
            print(f"[DOWNLOADER] MPD content length: {len(mpd_content)} bytes")
            print(f"[DOWNLOADER] MPD content preview: {mpd_content[:500]}...")

            return self.extract_stream_info(mpd_content)
        except Exception as e:
            print(f"[ERROR] Error extracting stream info from URL: {e}")
            import traceback
            traceback.print_exc()
            return [], [], []

    def extract_stream_info(self, mpd_content):
        """Extract stream information from MPD content."""
        try:
            # Verificar que mpd_content sea bytes o string
            if isinstance(mpd_content, bytes):
                mpd_content_str = mpd_content.decode('utf-8')
            else:
                mpd_content_str = str(mpd_content)

            print(f"[DOWNLOADER] MPD content type: {type(mpd_content)}")

            # Usar BeautifulSoup para parsear el XML
            soup = BeautifulSoup(mpd_content_str, 'xml')
            qualities = []
            audios = []
            subtitles = []

            # Lista de códigos de idioma a excluir
            exclude_tracks = ['qor', 'qae', 'qaf', 'qag', 'qad', 'qaa', 'qab', 'qac']

            # Mapeo de códigos de idioma especiales - solo mapear si realmente existe el idioma
            lang_mapping = {
                # No mapear automáticamente, solo usar idiomas realmente disponibles
            }

            # Buscar todos los AdaptationSet
            adaptation_sets = soup.find_all('AdaptationSet')
            print(f"[DOWNLOADER] Found {len(adaptation_sets)} adaptation sets")

            # Determinar si este MPD es para H264 o H265
            codec_type = "unknown"

            # Verificar el codec en la URL
            if "video_codec=H264" in mpd_content_str or "video_codec:H264" in mpd_content_str:
                codec_type = "H264"
            elif "video_codec=H265" in mpd_content_str or "video_codec:H265" in mpd_content_str:
                codec_type = "H265"
            # Si no se encuentra en la URL, buscar en el contenido del MPD
            elif "avc1" in mpd_content_str:
                codec_type = "H264"
            elif "hvc1" in mpd_content_str or "hev1" in mpd_content_str:
                codec_type = "H265"

            print(f"[DOWNLOADER] Detected codec type: {codec_type}")

            for adaptation_set in adaptation_sets:
                # Obtener el tipo de contenido y el idioma
                content_type = adaptation_set.get('mimeType', 'Unknown')
                lang = adaptation_set.get('lang', 'Unknown')

                print(f"[DOWNLOADER] Processing adaptation set: type={content_type}, lang={lang}")

                # Handle video
                if 'video' in content_type:
                    for representation in adaptation_set.find_all('Representation'):
                        bandwidth = representation.get('bandwidth', 'Unknown')
                        width = representation.get('width', 'Unknown')
                        height = representation.get('height', 'Unknown')
                        resolution = f"{width}x{height}" if width != 'Unknown' and height != 'Unknown' else 'Unknown'

                        # Añadir información del codec a la calidad
                        codec_info = representation.get('codecs', '')
                        if codec_info:
                            qualities.append((resolution, f"{bandwidth} bps ({codec_info})"))
                        else:
                            qualities.append((resolution, f"{bandwidth} bps ({codec_type})"))

                # Handle audio
                elif 'audio' in content_type:
                    # Verificar si es un idioma real y no un código especial
                    if isinstance(lang, str) and lang not in exclude_tracks:
                        # Verificar si hay representaciones para este idioma
                        representations = adaptation_set.find_all('Representation')
                        if representations:
                            try:
                                # Solo procesar idiomas reales (no códigos especiales)
                                if len(lang) == 2 or len(lang) == 3:  # Códigos de idioma ISO estándar
                                    display_name = Language.get(lang).display_name()
                                    audio_info = {
                                        'code': lang,
                                        'name': display_name,
                                        'display': f"{display_name} ({lang})",
                                        'codec': codec_type
                                    }
                                    if not any(a.get('code', '') == lang for a in audios):
                                        print(f"[DOWNLOADER] Found valid audio track: {lang} ({display_name})")
                                        audios.append(audio_info)
                            except Exception as e:
                                print(f"[ERROR] Error processing audio track {lang}: {e}")

                # Handle subtitles
                elif 'text' in content_type or 'application/mp4' in content_type or 'application' in content_type:
                    # Map special language codes
                    mapped_lang = lang_mapping.get(lang, lang) if isinstance(lang, str) else 'Unknown'

                    if mapped_lang not in exclude_tracks:
                        try:
                            display_name = Language.get(mapped_lang).display_name()
                            subtitle_info = {
                                'code': mapped_lang,
                                'name': display_name,
                                'display': f"{display_name} ({mapped_lang})",
                                'codec': codec_type
                            }
                            if not any(s.get('code', '') == mapped_lang for s in subtitles):
                                subtitles.append(subtitle_info)
                        except Exception as e:
                            print(f"[ERROR] Error processing subtitle track {mapped_lang}: {e}")

            # Sort qualities by resolution (height)
            sorted_qualities = []
            for quality in qualities:
                try:
                    if 'x' in quality[0] and quality[0].split('x')[1].isdigit():
                        sorted_qualities.append(quality)
                except Exception as e:
                    print(f"[ERROR] Error processing quality {quality}: {e}")

            sorted_qualities.sort(key=lambda x: int(x[0].split('x')[1]) if 'x' in x[0] and x[0].split('x')[1].isdigit() else 0)

            print(f"[DOWNLOADER] Extracted qualities for {codec_type}: {sorted_qualities}")
            print(f"[DOWNLOADER] Extracted audio tracks for {codec_type}: {audios}")
            print(f"[DOWNLOADER] Extracted subtitle tracks for {codec_type}: {subtitles}")

            return sorted_qualities, audios, subtitles

        except Exception as e:
            print(f"[ERROR] Error in extract_stream_info: {e}")
            import traceback
            traceback.print_exc()
            return [], [], []

    def format_resolution(self, resolution):
        """Format resolution string."""
        width, height = resolution.split('x')
        return f"{height}p"

    def get_language_name(self, lang_code):
        """Convert language code to full language name."""
        language_map = {
            "ar": "Arabic",
            "en": "English",
            "fr": "French",
            "tr": "Turkish",
            "es": "Spanish",
            "de": "German",
            "it": "Italian",
            "ru": "Russian",
            "ja": "Japanese",
            "ko": "Korean",
            "zh": "Chinese",
            "pt": "Portuguese",
            "nl": "Dutch",
            "pl": "Polish",
            "hi": "Hindi",
            "bn": "Bengali",
            "id": "Indonesian",
            "ms": "Malay",
            "th": "Thai",
            "vi": "Vietnamese",
            "fa": "Persian",
            "ur": "Urdu",
            "el": "Greek",
            "no": "Norwegian",
            "sv": "Swedish",
            "da": "Danish",
            "fi": "Finnish",
            "cs": "Czech",
            "hu": "Hungarian",
            "ro": "Romanian",
            "bg": "Bulgarian",
            "hr": "Croatian",
            "sr": "Serbian",
            "sk": "Slovak",
            "sl": "Slovenian",
            "uk": "Ukrainian",
            "he": "Hebrew",
            "tl": "Tagalog",
            "ml": "Malayalam",
            "ct": "Catalan"
        }

        # Return the full language name if available, otherwise return the code
        return language_map.get(lang_code, lang_code)

    def download_movie(self, row_index, mpd_url, resolution, audio_tracks, subtitle_tracks, movie_title, drm_info=None):
        """Download movie with selected quality."""
        # Define paths
        cache_dir = os.path.join(self.dir_path, "cache", f"movie_{row_index}")
        download_dir = os.path.join(self.dir_path, "downloads", movie_title)

        # Create directories if they don't exist
        os.makedirs(cache_dir, exist_ok=True)
        os.makedirs(download_dir, exist_ok=True)

        # Map resolution to a more readable format
        resolution_map = {
            "256x144": "144p",
            "426x240": "240p",
            "426x252": "252p",
            "512x288": "288p",
            "640x360": "360p",
            "832x468": "468p",
            "854x480": "480p",
            "1024x576": "576p",
            "1280x720": "720p",
            "1920x1080": "1080p",
            "2560x1440": "1440p",
            "3840x2160": "2160p",  # 4K
        }

        # Reverse map for converting from "360p" to "640x360"
        reverse_resolution_map = {
            "144p": "256x144",
            "240p": "426x240",
            "252p": "426x252",
            "288p": "512x288",
            "360p": "640x360",
            "468p": "832x468",
            "480p": "854x480",
            "576p": "1024x576",
            "720p": "1280x720",
            "1080p": "1920x1080",
            "1440p": "2560x1440",
            "2160p": "3840x2160",
        }

        # Check if resolution is in format "360p" and convert to "640x360"
        if resolution.endswith("p") and resolution in reverse_resolution_map:
            resolution = reverse_resolution_map[resolution]
            print(f"[DOWNLOADER] Converted resolution to: {resolution}")

        # Get the display resolution for filename
        actual_resolution = resolution_map.get(resolution, resolution)

        # Sanitize the movie title by removing invalid characters
        sanitized_movie_title = re.sub(r'[<>:"/\\|?*]', '', movie_title)

        # Build the movie name with the correct resolution
        movie_filename = f"{sanitized_movie_title}.{actual_resolution}.SHAHID.VIP.WEB-DL"

        # Add codec to the filename
        if "H265" in mpd_url or "HEVC" in mpd_url:
            movie_filename += ".H265"
        else:
            movie_filename += ".H264"

        movie_filename += ".AAC"

        # Construct the final output filename with correct resolution
        final_output_file = os.path.join(download_dir, f"{movie_filename}.mkv")

        # Check if the movie already exists
        if os.path.exists(final_output_file):
            print(f"The movie '{movie_title}' already exists at: {final_output_file}. Skipping download.")
            self.signals.status_updated.emit(row_index, "Already exists")
            self.signals.progress_updated.emit(row_index, 100)
            return

        # Update status to "Downloading"
        self.signals.status_updated.emit(row_index, "Downloading")

        # Save DRM keys to the keys file if provided
        if drm_info and 'kid' in drm_info and 'key' in drm_info:
            kid = drm_info['kid']
            key = drm_info['key']

            # Check if the key already exists in the file
            key_exists = False
            if os.path.exists(self.keys_path):
                with open(self.keys_path, 'r') as f:
                    for line in f:
                        if kid in line:
                            key_exists = True
                            break

            # Add the key if it doesn't exist
            if not key_exists:
                with open(self.keys_path, 'a') as f:
                    f.write(f"{kid}:{key}\n")
                print(f"Added DRM key for KID: {kid}")

        # Construct audio and subtitle options based on selected tracks
        # Filter out invalid audio tracks (like 'qae', 'qad', etc.)
        filtered_audio_tracks = []
        for track in audio_tracks:
            if track not in ['qae', 'qad', 'qor', 'qaf', 'qag', 'qaa', 'qab', 'qac']:
                filtered_audio_tracks.append(track)

        # If no valid audio tracks after filtering, use default Arabic
        if not filtered_audio_tracks:
            filtered_audio_tracks = ["ar"]

        print(f"[DOWNLOADER] Original audio tracks: {audio_tracks}")
        print(f"[DOWNLOADER] Filtered audio tracks: {filtered_audio_tracks}")
        print(f"[DOWNLOADER] Selected subtitle tracks: {subtitle_tracks}")

        # Construct audio option with all languages in one parameter
        if filtered_audio_tracks:
            # Usar el formato del código original de Shahid.py (línea 567)
            # --select-audio "lang=ar|en|tr:for=best3"
            audio_langs = "|".join(filtered_audio_tracks)
            # Usar "all" para asegurarse de que se descarguen todos los idiomas disponibles
            # que coincidan con los seleccionados
            audio_option = f'--select-audio "lang={audio_langs}:for=all"'
            print(f"[DOWNLOADER] Using audio option: {audio_option}")
        else:
            # Default to Arabic if no audio tracks selected
            audio_option = '--select-audio "lang=ar:for=best1"'
            print(f"[DOWNLOADER] Using default audio option: {audio_option}")

        # Construct subtitle option
        if subtitle_tracks:
            # Filtrar códigos de idioma inválidos (asegurarse de que sean 2-3 caracteres)
            valid_subtitle_tracks = [track for track in subtitle_tracks if isinstance(track, str) and len(track) in [2, 3]]

            if valid_subtitle_tracks:
                # Construir un solo parámetro de subtítulos con todos los idiomas
                subtitle_langs = "|".join(valid_subtitle_tracks)
                subtitle_option = f'--select-subtitle "lang={subtitle_langs}:for=all"'
                print(f"[DOWNLOADER] Downloading subtitles for languages: {valid_subtitle_tracks}")
                print(f"[DOWNLOADER] Using subtitle option: {subtitle_option}")

                # Advertir al usuario que algunos idiomas podrían no estar disponibles
                print(f"[DOWNLOADER] NOTA: Solo se descargarán los idiomas que estén disponibles en el contenido.")
            else:
                # Si no hay subtítulos válidos después del filtrado
                subtitle_option = '--sub-only=false'
                print(f"[DOWNLOADER] No valid subtitle tracks after filtering. Original tracks: {subtitle_tracks}")
        else:
            # If no subtitles were selected, use the correct parameter to disable subtitles
            subtitle_option = '--sub-only=false'
            print("[DOWNLOADER] No subtitles selected, disabling subtitle download")

        # Add base URL parameter to help with relative URLs
        base_url = "/".join(mpd_url.split("/")[:-1]) + "/"

        # Select video by resolution
        video_option = f'-sv res="{resolution}":for=best'
        print(f"[DOWNLOADER] Using resolution selection: {video_option}")

        # Construct the download command with compatible options
        download_command = (
            f'"{self.n_m3u8dl_path}" "{mpd_url}" '
            f'{video_option} '
            f'{audio_option} '
            f'{subtitle_option} '
            f'--tmp-dir "{cache_dir}" '
            f'--save-dir "{cache_dir}" '
            f'--save-name "{movie_filename}" '
            f'--base-url "{base_url}" '
            f'--decryption-binary-path="{self.mp4decrypt_path}" '
            f'--key-text-file="{self.keys_path}" '
            f'--log-level "INFO" '
            f'--binary-merge '
            f'--thread-count 16 '
            f'--download-retry-count 3 '
            f'--sub-format "SRT" '  # Forzar formato SRT para subtítulos
            f'-mt'  # Use concurrent download mode
        )

        print(f"Running download command: {download_command}")

        # Track this download
        self.active_downloads[row_index] = {
            'process': None,
            'progress': 0,
            'status': 'Downloading',
            'output_file': final_output_file,
            'cache_dir': cache_dir,
            'start_time': time.time(),  # Track start time for progress estimation
            'last_update': time.time(),  # Track last update time
            'audio_tracks': filtered_audio_tracks,  # Store selected audio tracks
            'subtitle_tracks': subtitle_tracks  # Store selected subtitle tracks
        }

        # Send initial progress update
        self.signals.progress_updated.emit(row_index, 0)

        # Start the download in a separate thread
        download_thread = threading.Thread(
            target=self._run_download_process,
            args=(row_index, download_command, cache_dir, movie_filename, final_output_file)
        )
        download_thread.daemon = True
        download_thread.start()

        # Store the thread reference
        self.download_threads[row_index] = download_thread

        return final_output_file

    def merge_with_mkvmerge(self, files_to_merge, output_file):
        """Merge files using mkvmerge."""
        # Separar archivos de video, audio y subtítulos
        video_files = []
        audio_files = []
        subtitle_files = []

        subtitle_extensions = (".srt", ".vtt", ".ass", ".ttml", ".xml", ".dfxp", ".sub")
        audio_extensions = (".m4a", ".aac", ".mp3", ".ac3", ".eac3", ".dts")
        video_extensions = (".mp4", ".mkv", ".webm", ".avi", ".mov", ".ts")

        # Primero, filtrar archivos duplicados
        unique_files = []
        for file in files_to_merge:
            if file not in unique_files:
                unique_files.append(file)

        # Luego, clasificar los archivos por tipo
        for file in unique_files:
            file_lower = file.lower()

            # Clasificar por extensión
            if any(file_lower.endswith(ext) for ext in video_extensions):
                video_files.append(file)
            elif any(file_lower.endswith(ext) for ext in audio_extensions):
                # Verificar que no sea un archivo de subtítulos con extensión de audio
                is_subtitle = False
                for lang in ['en', 'ar', 'fr', 'tr']:
                    if f".{lang}." in file_lower and any(file_lower.endswith(ext) for ext in subtitle_extensions):
                        is_subtitle = True
                        break

                if not is_subtitle:
                    audio_files.append(file)
            elif any(file_lower.endswith(ext) for ext in subtitle_extensions):
                subtitle_files.append(file)

        # Construct the mkvmerge command
        mkvmerge_command = f'"{self.mkvmerge_path}" -o "{output_file}"'

        # Primero agregar archivos de video
        for file in video_files:
            mkvmerge_command += f' "{file}"'

        # Crear un mapa de códigos de idioma a nombres completos
        language_map = {
            'ar': 'Arabic',
            'en': 'English',
            'fr': 'French',
            'tr': 'Turkish',
            'es': 'Spanish',
            'de': 'German',
            'it': 'Italian',
            'ru': 'Russian',
            'ja': 'Japanese',
            'ko': 'Korean',
            'pt': 'Portuguese',
            'nl': 'Dutch',
            'pl': 'Polish',
            'sv': 'Swedish',
            'no': 'Norwegian',
            'da': 'Danish',
            'fi': 'Finnish',
            'el': 'Greek',
            'hi': 'Hindi',
            'bn': 'Bengali',
            'id': 'Indonesian',
            'ms': 'Malay',
            'th': 'Thai',
            'vi': 'Vietnamese',
            'tl': 'Tagalog',
            'ml': 'Malayalam'
        }

        # Agrupar archivos de audio por idioma para evitar duplicados
        audio_by_lang = {}

        # Primero, agrupar los archivos de audio por idioma
        for file in audio_files:
            file_name = os.path.basename(file)
            file_lower = file_name.lower()
            detected_lang = None

            # Buscar códigos de idioma en el nombre del archivo
            for lang in language_map.keys():
                if f".{lang}." in file_lower:
                    detected_lang = lang
                    break

            # Si se detectó un idioma, agregar el archivo a ese grupo
            if detected_lang:
                if detected_lang not in audio_by_lang:
                    audio_by_lang[detected_lang] = []
                audio_by_lang[detected_lang].append(file)
            else:
                # Para archivos sin idioma detectado, usar árabe como predeterminado
                if 'ar' not in audio_by_lang:
                    audio_by_lang['ar'] = []
                audio_by_lang['ar'].append(file)

        # Ahora, agregar solo un archivo de audio por idioma
        for lang, files in audio_by_lang.items():
            # Tomar solo el primer archivo para cada idioma
            file = files[0]
            language_name = language_map.get(lang, self.get_language_name(lang))
            mkvmerge_command += f' --language 0:{lang} --track-name 0:"{language_name}" "{file}"'
            print(f"[DOWNLOADER] Adding audio track: {lang} ({language_name}) from {os.path.basename(file)}")

        # Agrupar subtítulos por idioma para evitar duplicados
        subtitle_by_lang = {}

        # Primero, agrupar los archivos de subtítulos por idioma
        for file in subtitle_files:
            file_name = os.path.basename(file)
            file_lower = file_name.lower()
            detected_lang = None

            # Buscar códigos de idioma en el nombre del archivo
            for lang in language_map.keys():
                if f".{lang}." in file_lower:
                    detected_lang = lang
                    break

            # Si se detectó un idioma, agregar el archivo a ese grupo
            if detected_lang:
                if detected_lang not in subtitle_by_lang:
                    subtitle_by_lang[detected_lang] = []
                subtitle_by_lang[detected_lang].append(file)
            else:
                # Para archivos sin idioma detectado, usar un grupo especial
                if 'unknown' not in subtitle_by_lang:
                    subtitle_by_lang['unknown'] = []
                subtitle_by_lang['unknown'].append(file)

        # Ahora, agregar solo un archivo de subtítulos por idioma
        for lang, files in subtitle_by_lang.items():
            if lang != 'unknown':
                # Tomar solo el primer archivo para cada idioma
                file = files[0]
                language_name = language_map.get(lang, self.get_language_name(lang))
                mkvmerge_command += f' --language 0:{lang} --track-name 0:"{language_name}" "{file}"'
                print(f"[DOWNLOADER] Adding subtitle track: {lang} ({language_name}) from {os.path.basename(file)}")
            else:
                # Para archivos sin idioma detectado, intentar detectar el idioma por el contenido
                for i, file in enumerate(files):
                    # Asignar un idioma genérico basado en el índice
                    mkvmerge_command += f' --track-name 0:"Subtitle {i+1}" "{file}"'
                    print(f"[DOWNLOADER] Adding subtitle track with generic name: Subtitle {i+1} from {os.path.basename(file)}")

        # Run the mkvmerge command
        print(f"Running mkvmerge command: {mkvmerge_command}")
        subprocess.call(mkvmerge_command, shell=True)

    def download_series_with_selected_quality(self, row_index, mpd_url, resolution, audio_tracks, subtitle_tracks, series_title, season_number, episode_number, drm_info=None):
        """Download series episode with selected quality."""
        # Sanitize the directory name by removing invalid characters
        sanitized_series_title = re.sub(r'[<>:"/\\|?*]', '', series_title)

        # Define paths
        cache_dir = os.path.join(self.dir_path, "cache", f"episode_{row_index}")
        season_dir = os.path.join(self.dir_path, "downloads", sanitized_series_title, f"Season {season_number}")

        # Create directories if they don't exist
        os.makedirs(cache_dir, exist_ok=True)
        os.makedirs(season_dir, exist_ok=True)

        # Map resolution to a more readable format and vice versa
        resolution_map = {
            "256x144": "144p",
            "426x240": "240p",
            "426x252": "252p",
            "512x288": "288p",
            "640x360": "360p",
            "832x468": "468p",
            "854x480": "480p",
            "1024x576": "576p",
            "1280x720": "720p",
            "1920x1080": "1080p",
            "2560x1440": "1440p",
            "3840x2160": "2160p",  # 4K
        }

        # Reverse map for converting from "360p" to "640x360"
        reverse_resolution_map = {
            "144p": "256x144",
            "240p": "426x240",
            "252p": "426x252",
            "288p": "512x288",
            "360p": "640x360",
            "468p": "832x468",
            "480p": "854x480",
            "576p": "1024x576",
            "720p": "1280x720",
            "1080p": "1920x1080",
            "1440p": "2560x1440",
            "2160p": "3840x2160",
        }

        # Check if resolution is in format "360p" and convert to "640x360"
        if resolution.endswith("p") and resolution in reverse_resolution_map:
            resolution = reverse_resolution_map[resolution]
            print(f"[DOWNLOADER] Converted resolution to: {resolution}")

        # Get the display resolution for filename
        actual_resolution = resolution_map.get(resolution, resolution)

        # Build the episode name with the correct resolution
        episode_name = f"{sanitized_series_title}.S{int(season_number):02d}.E{int(episode_number):02d}.{actual_resolution}.SHAHID.VIP.WEB-DL"

        # Add codec to the filename
        if "H265" in mpd_url or "HEVC" in mpd_url:
            episode_name += ".H265"
        else:
            episode_name += ".H264"

        episode_name += ".AAC"

        # Final output file path
        final_output_file = os.path.join(season_dir, f"{episode_name}.mkv")

        # Check if the file already exists
        if os.path.exists(final_output_file):
            print(f"The episode {sanitized_series_title} S{int(season_number):02d}E{int(episode_number):02d} already exists. Skipping download.")
            self.signals.status_updated.emit(row_index, "Already exists")
            self.signals.progress_updated.emit(row_index, 100)
            return

        # Update status to "Downloading"
        self.signals.status_updated.emit(row_index, "Downloading")

        # Save DRM keys to the keys file if provided
        if drm_info and 'kid' in drm_info and 'key' in drm_info:
            kid = drm_info['kid']
            key = drm_info['key']

            # Check if the key already exists in the file
            key_exists = False
            if os.path.exists(self.keys_path):
                with open(self.keys_path, 'r') as f:
                    for line in f:
                        if kid in line:
                            key_exists = True
                            break

            # Add the key if it doesn't exist
            if not key_exists:
                with open(self.keys_path, 'a') as f:
                    f.write(f"{kid}:{key}\n")
                print(f"Added DRM key for KID: {kid}")

        # Construct audio and subtitle options based on selected tracks
        # Filter out invalid audio tracks (like 'qae', 'qad', etc.)
        filtered_audio_tracks = []
        for track in audio_tracks:
            if track not in ['qae', 'qad', 'qor', 'qaf', 'qag', 'qaa', 'qab', 'qac']:
                filtered_audio_tracks.append(track)

        # If no valid audio tracks after filtering, use default Arabic
        if not filtered_audio_tracks:
            filtered_audio_tracks = ["ar"]

        print(f"[DOWNLOADER] Original audio tracks: {audio_tracks}")
        print(f"[DOWNLOADER] Filtered audio tracks: {filtered_audio_tracks}")
        print(f"[DOWNLOADER] Selected subtitle tracks: {subtitle_tracks}")

        # Construct audio option with all languages in one parameter
        if filtered_audio_tracks:
            # Usar el formato del código original de Shahid.py (línea 567)
            # --select-audio "lang=ar|en|tr:for=best3"
            audio_langs = "|".join(filtered_audio_tracks)
            # Usar "all" para asegurarse de que se descarguen todos los idiomas disponibles
            # que coincidan con los seleccionados
            audio_option = f'--select-audio "lang={audio_langs}:for=all"'
            print(f"[DOWNLOADER] Using audio option: {audio_option}")
        else:
            # Default to Arabic if no audio tracks selected
            audio_option = '--select-audio "lang=ar:for=best1"'
            print(f"[DOWNLOADER] Using default audio option: {audio_option}")

        # Construct subtitle option
        if subtitle_tracks:
            # Filtrar códigos de idioma inválidos (asegurarse de que sean 2-3 caracteres)
            valid_subtitle_tracks = [track for track in subtitle_tracks if isinstance(track, str) and len(track) in [2, 3]]

            if valid_subtitle_tracks:
                # Construir un solo parámetro de subtítulos con todos los idiomas
                subtitle_langs = "|".join(valid_subtitle_tracks)
                subtitle_option = f'--select-subtitle "lang={subtitle_langs}:for=all"'
                print(f"[DOWNLOADER] Downloading subtitles for languages: {valid_subtitle_tracks}")
                print(f"[DOWNLOADER] Using subtitle option: {subtitle_option}")

                # Advertir al usuario que algunos idiomas podrían no estar disponibles
                print(f"[DOWNLOADER] NOTA: Solo se descargarán los idiomas que estén disponibles en el contenido.")
            else:
                # Si no hay subtítulos válidos después del filtrado
                subtitle_option = '--sub-only=false'
                print(f"[DOWNLOADER] No valid subtitle tracks after filtering. Original tracks: {subtitle_tracks}")
        else:
            # If no subtitles were selected, use the correct parameter to disable subtitles
            subtitle_option = '--sub-only=false'
            print("[DOWNLOADER] No subtitles selected, disabling subtitle download")

        # Construct the download command with compatible options
        # Add base URL parameter to help with relative URLs
        base_url = "/".join(mpd_url.split("/")[:-1]) + "/"

        # Map common resolutions to their typical stream IDs in Shahid
        # This mapping is based on observed patterns in Shahid streams
        resolution_to_id = {
            "1920x1080": "2",  # 1080p is usually stream ID 2
            "1280x720": "3",   # 720p is usually stream ID 3
            "1024x576": "1",   # 576p is usually stream ID 1
            "832x468": "4",    # 468p is usually stream ID 4
            "640x360": "5",    # 360p is usually stream ID 5
            "512x288": "6",    # 288p is usually stream ID 6
            "448x252": "7",    # 252p is usually stream ID 7
            "256x144": "8"     # 144p is usually stream ID 8
        }

        # Select video by ID if possible, otherwise by exact resolution
        if 'x' in resolution and resolution in resolution_to_id:
            # Use ID-based selection for known resolutions (more reliable)
            stream_id = resolution_to_id[resolution]
            video_option = f'-sv id="{stream_id}":for=best'
            print(f"[DOWNLOADER] Using stream ID selection for {resolution}: {video_option}")

            # Also extract height for logging
            try:
                _, height = resolution.split('x')
                print(f"[DOWNLOADER] Target height: {height}p (Stream ID: {stream_id})")
            except:
                pass
        elif 'x' in resolution:
            # For other resolutions, use the "res=" parameter
            video_option = f'-sv res="{resolution}":for=best'
            print(f"[DOWNLOADER] Using exact resolution selection: {video_option}")

            # Also extract height for logging
            try:
                _, height = resolution.split('x')
                print(f"[DOWNLOADER] Target height: {height}p")
            except:
                pass
        else:
            # Fallback to exact resolution if format is unexpected
            video_option = f'-sv res="{resolution}":for=best'
            print(f"[DOWNLOADER] Using fallback resolution selection: {video_option}")

        # Construct the download command with compatible options
        download_command = (
            f'"{self.n_m3u8dl_path}" "{mpd_url}" '
            f'{video_option} '
            f'{audio_option} '
            f'{subtitle_option} '
            f'--tmp-dir "{cache_dir}" '
            f'--save-dir "{cache_dir}" '
            f'--save-name "{episode_name}" '
            f'--base-url "{base_url}" '
            f'--decryption-binary-path="{self.mp4decrypt_path}" '
            f'--key-text-file="{self.keys_path}" '
            f'--log-level "INFO" '
            f'--binary-merge '
            f'--thread-count 16 '
            f'--download-retry-count 3 '
            f'--sub-format "SRT" '  # Forzar formato SRT para subtítulos
            f'-mt'  # Use concurrent download mode
        )

        print(f"Running download command: {download_command}")

        # Track this download
        self.active_downloads[row_index] = {
            'process': None,
            'progress': 0,
            'status': 'Downloading',
            'output_file': final_output_file,
            'cache_dir': cache_dir,
            'start_time': time.time(),  # Track start time for progress estimation
            'last_update': time.time(),  # Track last update time
            'audio_tracks': filtered_audio_tracks,  # Store selected audio tracks
            'subtitle_tracks': subtitle_tracks  # Store selected subtitle tracks
        }

        # Send initial progress update
        self.signals.progress_updated.emit(row_index, 0)

        # Start the download in a separate thread
        download_thread = threading.Thread(
            target=self._run_download_process,
            args=(row_index, download_command, cache_dir, episode_name, final_output_file)
        )
        download_thread.daemon = True
        download_thread.start()

        # Store the thread reference
        self.download_threads[row_index] = download_thread

        return final_output_file

    def _run_download_process(self, row_index, download_command, cache_dir, episode_name, final_output_file):
        """Run the download process and monitor progress."""
        try:
            # Store subtitle files in the active_downloads dictionary for this row
            self.active_downloads[row_index]['step1_subtitle_files'] = []

            # STEP 1: Just use the original command with all subtitles
            # This is more reliable than trying to download subtitles separately
            print(f"[DOWNLOADER] Starting download with all selected subtitles")
            self.signals.status_updated.emit(row_index, "Downloading")

            # Start the download process
            process = subprocess.Popen(
                download_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                encoding='utf-8',
                errors='replace'
            )

            # Store the process
            self.active_downloads[row_index]['process'] = process

            # Set up a timer to periodically update progress even if no output is received
            start_time = time.time()

            # Function to estimate progress based on time elapsed and file system monitoring
            def estimate_progress():
                if row_index not in self.active_downloads:
                    return

                current_time = time.time()
                elapsed_time = current_time - start_time

                # Only update if no progress update in the last 2 seconds
                if current_time - self.active_downloads[row_index].get('last_update', 0) > 2:
                    # Check if we can estimate progress based on file sizes
                    try:
                        # Get the cache directory
                        if os.path.exists(cache_dir):
                            # Get all files in the cache directory
                            files = os.listdir(cache_dir)

                            # Calculate total size of downloaded files
                            total_size = sum(os.path.getsize(os.path.join(cache_dir, f)) for f in files if os.path.isfile(os.path.join(cache_dir, f)))

                            # Estimate progress based on file size (1MB = 1%)
                            # Adjust the divisor based on expected file size
                            size_based_progress = min(95, int(total_size / (1024 * 1024)))

                            # Use time-based estimation as a fallback
                            time_based_progress = min(95, int(elapsed_time / 2))

                            # Use the higher of the two estimates
                            estimated_progress = max(size_based_progress, time_based_progress)

                            # Only update if it's higher than the last known progress
                            current_progress = self.active_downloads[row_index].get('progress', 0)
                            if estimated_progress > current_progress:
                                self._update_progress(row_index, estimated_progress, "file size estimation")
                    except Exception as e:
                        # Fallback to simple time-based estimation
                        time_based_progress = min(95, int(elapsed_time / 2))
                        current_progress = self.active_downloads[row_index].get('progress', 0)
                        if time_based_progress > current_progress:
                            self._update_progress(row_index, time_based_progress, "time estimation")

            # Start a thread to periodically update progress
            def progress_updater():
                update_count = 0
                while row_index in self.active_downloads and self.active_downloads[row_index]['process'] == process:
                    estimate_progress()

                    # Print debug info every 10 updates
                    update_count += 1
                    if update_count % 10 == 0:
                        print(f"[DOWNLOADER] Progress updater still running for row {row_index}")
                        if os.path.exists(cache_dir):
                            try:
                                files = os.listdir(cache_dir)
                                print(f"[DOWNLOADER] Files in cache directory: {len(files)}")
                                if files:
                                    print(f"[DOWNLOADER] Sample files: {files[:3]}")
                            except Exception as e:
                                print(f"[DOWNLOADER] Error listing cache directory: {e}")

                    time.sleep(1)  # Update every 1 second for more responsive UI

            progress_thread = threading.Thread(target=progress_updater)
            progress_thread.daemon = True
            progress_thread.start()

            # Initialize logs for this download
            self.download_logs[row_index] = []

            # Monitor the process output for progress updates
            for line in process.stdout:
                # Print the line for debugging
                line_text = line.strip()
                print(line_text)

                # Store the line in logs for error analysis
                self.download_logs[row_index].append(line_text)

                # Update the last update time to prevent estimated progress from overriding real progress
                if row_index in self.active_downloads:
                    self.active_downloads[row_index]['last_update'] = time.time()

                # Check for any kind of progress information in the line
                try:
                    # N_m3u8DL-RE specific progress patterns

                    # Method 1: Look for "Downloading" with percentage
                    if "Downloading" in line and "%" in line:
                        percent_index = line.find("%")
                        if percent_index > 0:
                            # Look for numbers before the % sign
                            number_str = ""
                            i = percent_index - 1
                            while i >= 0 and (line[i].isdigit() or line[i] == '.'):
                                number_str = line[i] + number_str
                                i -= 1

                            if number_str:
                                progress = int(float(number_str))
                                self._update_progress(row_index, progress, "downloading percentage")

                    # Method 2: Look for "Merging" with percentage
                    elif "Merging" in line and "%" in line:
                        percent_index = line.find("%")
                        if percent_index > 0:
                            # Look for numbers before the % sign
                            number_str = ""
                            i = percent_index - 1
                            while i >= 0 and (line[i].isdigit() or line[i] == '.'):
                                number_str = line[i] + number_str
                                i -= 1

                            if number_str:
                                # For merging, we're already at least 80% done
                                progress = 80 + int(float(number_str)) // 5  # Scale 0-100 to 80-100
                                self._update_progress(row_index, progress, "merging percentage")

                    # Method 3: Look for "Progress:" format (standard)
                    elif "Progress:" in line:
                        progress_str = line.split("Progress:")[1].strip().split("%")[0].strip()
                        progress = int(float(progress_str))
                        self._update_progress(row_index, progress, "standard format")

                    # Method 4: Look for segment download indicators
                    elif "Segment" in line and ("downloaded" in line or "downloading" in line):
                        # Try to extract segment numbers
                        if "of" in line:
                            # Find the numbers around "of"
                            of_index = line.find("of")

                            # Look for numbers before "of"
                            before_text = line[:of_index].strip()
                            after_text = line[of_index+2:].strip()

                            # Extract numbers using regex
                            import re
                            before_numbers = re.findall(r'\d+', before_text)
                            after_numbers = re.findall(r'\d+', after_text)

                            if before_numbers and after_numbers:
                                current = int(before_numbers[-1])  # Last number before "of"
                                total = int(after_numbers[0])      # First number after "of"

                                if total > 0:
                                    progress = min(100, int((current / total) * 100))
                                    self._update_progress(row_index, progress, "segment format")

                    # Method 5: Look for "Downloading" with file size
                    elif "Downloading" in line and ("MB" in line or "KB" in line) and "/" in line:
                        # Format like "Downloading: 10.5MB/20MB"
                        size_parts = line.split("/")
                        if len(size_parts) >= 2:
                            # Extract the part before the slash that contains the current size
                            current_part = size_parts[0]
                            # Extract the part after the slash that contains the total size
                            total_part = size_parts[1]

                            # Extract numbers using regex
                            import re
                            current_numbers = re.findall(r'\d+\.?\d*', current_part)
                            total_numbers = re.findall(r'\d+\.?\d*', total_part)

                            if current_numbers and total_numbers:
                                current_size = float(current_numbers[-1])  # Last number in current part
                                total_size = float(total_numbers[0])       # First number in total part

                                # Determine units (MB or KB)
                                current_unit = "MB" if "MB" in current_part else "KB"
                                total_unit = "MB" if "MB" in total_part else "KB"

                                # Convert to same unit if needed
                                if current_unit != total_unit:
                                    if current_unit == "KB" and total_unit == "MB":
                                        current_size /= 1024
                                    elif current_unit == "MB" and total_unit == "KB":
                                        current_size *= 1024

                                if total_size > 0:
                                    progress = min(100, int((current_size / total_size) * 100))
                                    self._update_progress(row_index, progress, "file size format")

                    # Method 6: Look for any percentage in the line
                    elif "%" in line:
                        percent_index = line.find("%")
                        if percent_index > 0:
                            # Look for numbers before the % sign
                            number_str = ""
                            i = percent_index - 1
                            while i >= 0 and (line[i].isdigit() or line[i] == '.'):
                                number_str = line[i] + number_str
                                i -= 1

                            if number_str:
                                progress = int(float(number_str))
                                self._update_progress(row_index, progress, "generic percentage")

                except Exception as e:
                    # Don't print the error for every line to avoid log spam
                    pass

            # Wait for the process to complete
            process.wait()

            # Check if the process completed successfully
            if process.returncode == 0:
                print(f"Download completed successfully for row {row_index}")

                # Define paths to downloaded files
                video_file = os.path.join(cache_dir, f"{episode_name}.mp4")

                # Check if there's a zero-byte file (common when download fails but process returns 0)
                if os.path.exists(video_file) and os.path.getsize(video_file) == 0:
                    print(f"[WARNING] Found zero-byte video file: {video_file}")
                    # Remove the zero-byte file to avoid confusion
                    try:
                        os.remove(video_file)
                        print(f"[DOWNLOADER] Removed zero-byte file: {video_file}")
                    except Exception as e:
                        print(f"[ERROR] Failed to remove zero-byte file: {e}")

                    # Set video_file to non-existent path to trigger alternative search
                    video_file = os.path.join(cache_dir, "non_existent_file.mp4")

                # Check if video file exists
                if not os.path.exists(video_file):
                    print(f"[ERROR] Video file not found: {video_file}")

                    # Try to find any video file in the cache directory
                    video_found = False
                    for file in os.listdir(cache_dir):
                        if file.endswith(".mp4") or file.endswith(".m4v") or file.endswith(".ts"):
                            video_file = os.path.join(cache_dir, file)
                            print(f"[DOWNLOADER] Found alternative video file: {video_file}")
                            video_found = True
                            break

                    # If still no video file found, check for any file that might be a video
                    if not video_found:
                        print("[DOWNLOADER] Searching for any potential video file...")
                        for file in os.listdir(cache_dir):
                            # Check file size - videos are usually larger than 1MB
                            file_path = os.path.join(cache_dir, file)
                            try:
                                file_size = os.path.getsize(file_path)
                                if file_size > 1000000:  # Larger than 1MB
                                    print(f"[DOWNLOADER] Found potential video file by size: {file} ({file_size} bytes)")
                                    video_file = file_path
                                    video_found = True
                                    break
                            except Exception as e:
                                print(f"[ERROR] Error checking file size: {e}")

                # Find audio files
                audio_files = []
                audio_extensions = (".m4a", ".aac", ".mp3", ".ac3", ".eac3", ".dts")

                # Obtener los idiomas de audio seleccionados para verificación
                selected_audio_langs = []
                if 'audio_tracks' in self.active_downloads[row_index]:
                    selected_audio_langs = self.active_downloads[row_index]['audio_tracks']

                print(f"[DOWNLOADER] Looking for audio files for selected languages: {selected_audio_langs}")

                for file in os.listdir(cache_dir):
                    file_lower = file.lower()
                    file_path = os.path.join(cache_dir, file)

                    if any(file.endswith(ext) for ext in audio_extensions):
                        # Verificar si el archivo tiene un código de idioma en su nombre
                        detected_lang = None
                        for lang in ["en", "fr", "ar", "tr", "el", "no", "pt", "ru", "it", "tl"]:
                            if f".{lang}." in file_lower:
                                detected_lang = lang
                                audio_files.append(file_path)
                                print(f"[DOWNLOADER] Found audio file for language {lang}: {file}")

                                # Verificar si este idioma estaba entre los seleccionados
                                if lang in selected_audio_langs:
                                    print(f"[DOWNLOADER] ✓ Audio for selected language {lang} was successfully downloaded")
                                else:
                                    print(f"[DOWNLOADER] ℹ️ Found audio for non-selected language {lang}")
                                break

                        if not detected_lang:
                            # Si no tiene código de idioma pero tiene extensión de audio
                            audio_files.append(file_path)
                            print(f"[DOWNLOADER] Found audio file without language code: {file}")

                # Verificar si todos los idiomas seleccionados fueron descargados
                if selected_audio_langs:
                    found_langs = []
                    for file_path in audio_files:
                        file_lower = os.path.basename(file_path).lower()
                        for lang in selected_audio_langs:
                            if f".{lang}." in file_lower:
                                found_langs.append(lang)

                    missing_langs = [lang for lang in selected_audio_langs if lang not in found_langs]
                    if missing_langs:
                        print(f"[DOWNLOADER] ⚠️ Warning: Some selected audio languages were not found: {missing_langs}")
                        print(f"[DOWNLOADER] ℹ️ This es normal si esos idiomas no están disponibles en el contenido original.")
                    else:
                        print(f"[DOWNLOADER] ✓ All selected audio languages were successfully downloaded")

                # Find subtitle files
                subtitle_files = []
                subtitle_extensions = (".srt", ".vtt", ".ass", ".ttml", ".xml", ".dfxp")

                # Obtener los idiomas de subtítulos seleccionados para verificación
                selected_subtitle_langs = []
                if 'subtitle_tracks' in self.active_downloads[row_index]:
                    selected_subtitle_langs = self.active_downloads[row_index]['subtitle_tracks']

                print(f"[DOWNLOADER] Looking for subtitle files for selected languages: {selected_subtitle_langs}")

                # Buscar archivos de subtítulos con patrones más específicos
                for file in os.listdir(cache_dir):
                    file_lower = file.lower()
                    file_path = os.path.join(cache_dir, file)

                    # Verificar si es un archivo de subtítulos por extensión
                    if any(file.endswith(ext) for ext in subtitle_extensions):
                        # Verificar si el archivo tiene un código de idioma en su nombre
                        detected_lang = None
                        for lang in ["en", "fr", "ar", "tr", "el", "no", "pt", "ru", "it", "tl"]:
                            if f".{lang}." in file_lower:
                                detected_lang = lang
                                subtitle_files.append(file_path)
                                print(f"[DOWNLOADER] Found subtitle file for language {lang}: {file}")

                                # Verificar si este idioma estaba entre los seleccionados
                                if lang in selected_subtitle_langs:
                                    print(f"[DOWNLOADER] ✓ Subtitle for selected language {lang} was successfully downloaded")
                                else:
                                    print(f"[DOWNLOADER] ℹ️ Found subtitle for non-selected language {lang}")
                                break

                        if not detected_lang:
                            # Si no tiene código de idioma pero tiene extensión de subtítulos
                            subtitle_files.append(file_path)
                            print(f"[DOWNLOADER] Found subtitle file without language code: {file}")

                    # Buscar archivos que contengan "subtitle" o "sub" en su nombre y no sean archivos de audio
                    elif ("subtitle" in file_lower or "sub" in file_lower) and not file.endswith((".m4a", ".aac", ".mp3")):
                        subtitle_files.append(file_path)
                        print(f"[DOWNLOADER] Found subtitle file by name pattern: {file}")

                # Verificar si todos los idiomas seleccionados fueron descargados
                if selected_subtitle_langs:
                    found_langs = []
                    for file_path in subtitle_files:
                        file_lower = os.path.basename(file_path).lower()
                        for lang in selected_subtitle_langs:
                            if f".{lang}." in file_lower:
                                found_langs.append(lang)

                    missing_langs = [lang for lang in selected_subtitle_langs if lang not in found_langs]
                    if missing_langs:
                        print(f"[DOWNLOADER] ⚠️ Warning: Some selected subtitle languages were not found: {missing_langs}")
                        print(f"[DOWNLOADER] ℹ️ This es normal si esos idiomas no están disponibles en el contenido original.")
                    else:
                        print(f"[DOWNLOADER] ✓ All selected subtitle languages were successfully downloaded")

                # Add the subtitle files that were downloaded in STEP 1 (if they're not already in the list)
                if row_index in self.active_downloads and 'step1_subtitle_files' in self.active_downloads[row_index]:
                    step1_files = self.active_downloads[row_index]['step1_subtitle_files']
                    if isinstance(step1_files, list):
                        for sub_file in step1_files:
                            if sub_file not in subtitle_files:
                                subtitle_files.append(sub_file)
                                print(f"[DOWNLOADER] Added previously downloaded subtitle file: {os.path.basename(sub_file)}")

                # List all files in cache directory for debugging
                print(f"[DOWNLOADER] Files in cache directory:")
                for file in os.listdir(cache_dir):
                    file_path = os.path.join(cache_dir, file)
                    file_size = os.path.getsize(file_path)
                    print(f"  - {file} ({file_size} bytes)")

                # Try to detect if we have a video file by checking file extensions and sizes
                video_files = []
                for file in os.listdir(cache_dir):
                    file_path = os.path.join(cache_dir, file)
                    file_size = os.path.getsize(file_path)

                    # Check for common video extensions
                    if file.endswith(('.mp4', '.m4v', '.ts', '.mkv', '.webm')):
                        video_files.append(file_path)
                        print(f"[DOWNLOADER] Found video file by extension: {file} ({file_size} bytes)")
                    # Check for large files that might be videos
                    elif file_size > 5000000:  # Larger than 5MB
                        video_files.append(file_path)
                        print(f"[DOWNLOADER] Found potential video file by size: {file} ({file_size} bytes)")

                # List of files to merge starting with the video file
                files_to_merge = []
                if os.path.exists(video_file):
                    files_to_merge.append(video_file)
                elif video_files:
                    # Use the largest video file
                    largest_video = max(video_files, key=os.path.getsize)
                    files_to_merge.append(largest_video)
                    print(f"[DOWNLOADER] Using largest video file: {largest_video} ({os.path.getsize(largest_video)} bytes)")

                if not files_to_merge:
                    print(f"[ERROR] No video file found to merge")

                # Add audio files
                files_to_merge.extend(audio_files)

                # Add subtitle files
                files_to_merge.extend(subtitle_files)
                print(f"[DOWNLOADER] Adding {len(subtitle_files)} subtitle files to merge")

                print(f"[DOWNLOADER] Files to merge: {files_to_merge}")

                # Check if we have a video file
                has_video = False
                for file_path in files_to_merge:
                    if file_path.endswith('.mp4'):
                        has_video = True
                        break

                # Proceed with the files we have found

                # Proceed to merge if at least the video file exists
                if len(files_to_merge) > 0 and has_video:
                    print(f"Downloaded files found with video, proceeding to merge...")
                    self.signals.status_updated.emit(row_index, "Merging")

                    # Merge video and audio files
                    self.merge_with_mkvmerge(files_to_merge, final_output_file)
                    print(f"Final merged file saved to: {final_output_file}")

                    # No longer copying individual subtitle files to the output directory
                    # All subtitles are already merged into the MKV file
                    print(f"[DOWNLOADER] Subtitles merged into MKV file, not copying individual subtitle files")

                    # Update status to "Completed"
                    self.signals.status_updated.emit(row_index, "Completed")
                    self.signals.progress_updated.emit(row_index, 100)

                    # Emit download completed signal with success=True
                    self.signals.download_completed.emit(row_index, True)

                    # Copy poster image if it exists in cache
                    try:
                        # Get the content title from the output file path
                        content_dir = os.path.dirname(final_output_file)

                        # Check if there's a poster in the cache/posters directory
                        poster_cache_dir = os.path.join(self.dir_path, "cache", "posters")
                        if os.path.exists(poster_cache_dir):
                            # Find the most recent poster file
                            poster_files = glob.glob(os.path.join(poster_cache_dir, "*.jpg"))
                            if poster_files:
                                # Sort by modification time (newest first)
                                newest_poster = max(poster_files, key=os.path.getmtime)

                                # Copy to the content directory
                                poster_dest = os.path.join(content_dir, "poster.jpg")
                                if not os.path.exists(poster_dest):
                                    shutil.copy(newest_poster, poster_dest)
                                    print(f"Copied poster to: {poster_dest}")
                    except Exception as poster_error:
                        print(f"Error copying poster: {poster_error}")

                    # Clean up the cache directory
                    try:
                        shutil.rmtree(cache_dir)
                        print(f"Cache directory {cache_dir} has been deleted.")
                    except Exception as e:
                        print(f"Error deleting cache directory: {e}")
                elif len(files_to_merge) > 0 and not has_video:
                    print("[ERROR] Only audio files were downloaded, no video file found.")
                    self.signals.status_updated.emit(row_index, "No Video")
                else:
                    print("[ERROR] Download failed or required files not found. Nothing to merge.")

                    # Check if we should retry with auto-select
                    if "No stream found to download" in ''.join(self.download_logs.get(row_index, [])):
                        print("[DOWNLOADER] Detected 'No stream found to download' error, retrying with auto-select...")

                        # Get the original download information from the active_downloads dictionary
                        download_info = self.active_downloads.get(row_index, {})
                        if not download_info:
                            print("[ERROR] Cannot retry: no download information available")
                            self.signals.status_updated.emit(row_index, "Failed")
                            return

                        # Extract information from the original download command
                        original_command = download_command

                        # Extract the MPD URL
                        mpd_url_start = original_command.find('"https://')
                        if mpd_url_start == -1:
                            print("[ERROR] Cannot retry: MPD URL not found in original command")
                            self.signals.status_updated.emit(row_index, "Failed")
                            return

                        mpd_url_end = original_command.find('"', mpd_url_start + 1)
                        if mpd_url_end == -1:
                            print("[ERROR] Cannot retry: MPD URL end not found in original command")
                            self.signals.status_updated.emit(row_index, "Failed")
                            return

                        mpd_url = original_command[mpd_url_start+1:mpd_url_end]

                        # Extract base URL from MPD URL
                        base_url = "/".join(mpd_url.split("/")[:-1]) + "/"

                        # Extract resolution from the original command
                        resolution = None
                        if '--select-video "height=' in original_command:
                            height_start = original_command.find('--select-video "height=') + len('--select-video "height=')
                            height_end = original_command.find('"', height_start)
                            if height_end > height_start:
                                height_str = original_command[height_start:height_end]
                                resolution = f"0x{height_str}"  # Create a dummy resolution with the height
                        elif '--select-video "res=' in original_command:
                            res_start = original_command.find('--select-video "res=') + len('--select-video "res=')
                            res_end = original_command.find('"', res_start)
                            if res_end > res_start:
                                resolution = original_command[res_start:res_end]

                        # Extract audio and subtitle options
                        audio_option = ""
                        if '--select-audio "' in original_command:
                            audio_start = original_command.find('--select-audio "')
                            audio_end = original_command.find('"', audio_start + len('--select-audio "') + 1)
                            if audio_end > audio_start:
                                audio_option = original_command[audio_start:audio_end+1]

                        subtitle_option = ""
                        if '--select-subtitle "' in original_command:
                            sub_start = original_command.find('--select-subtitle "')
                            sub_end = original_command.find('"', sub_start + len('--select-subtitle "') + 1)
                            if sub_end > sub_start:
                                subtitle_option = original_command[sub_start:sub_end+1]
                        elif '--sub-only=' in original_command:
                            subtitle_option = '--sub-only=false'

                        # Extract height from the original resolution or command
                        height = None

                        # Try to get height from resolution string
                        if resolution and 'x' in resolution:
                            try:
                                # We don't need width, just extract height
                                _, height_str = resolution.split('x')
                                height = int(height_str)
                                print(f"[DOWNLOADER] Extracted height {height} from resolution {resolution}")
                            except Exception as e:
                                print(f"[WARNING] Could not parse resolution: {resolution}, error: {e}")

                        # If we couldn't get height from resolution, try to extract it from logs
                        if not height:
                            # Look for resolution in the download logs
                            for log_line in self.download_logs.get(row_index, []):
                                # Look for lines like "Vid *CENC 448x252 | 363 Kbps"
                                if "Vid" in log_line and "x" in log_line:
                                    try:
                                        # Extract resolution from log line
                                        res_start = log_line.find("Vid") + 3
                                        res_end = log_line.find("|", res_start)
                                        if res_end > res_start:
                                            res_part = log_line[res_start:res_end].strip()
                                            # Find the part that looks like a resolution (e.g., "448x252")
                                            for part in res_part.split():
                                                if "x" in part and part.replace("x", "").isdigit():
                                                    _, height_str = part.split("x")
                                                    height = int(height_str)
                                                    print(f"[DOWNLOADER] Found height {height} in log line: {log_line}")
                                                    break
                                    except Exception as e:
                                        print(f"[WARNING] Error parsing log line for resolution: {e}")

                        # Create a retry command based on the information we have
                        if height:
                            # Look for exact resolution in the logs
                            exact_resolution = None
                            for log_line in self.download_logs.get(row_index, []):
                                # Look for lines like "Vid *CENC 640x360 | 429 Kbps"
                                if "Vid" in log_line and "x" in log_line:
                                    try:
                                        # Extract resolution from log line
                                        res_start = log_line.find("Vid") + 3
                                        res_end = log_line.find("|", res_start)
                                        if res_end > res_start:
                                            res_part = log_line[res_start:res_end].strip()
                                            # Find the part that looks like a resolution (e.g., "640x360")
                                            for part in res_part.split():
                                                if "x" in part and part.replace("x", "").isdigit():
                                                    # Split but only use the height part
                                                    parts = part.split("x")
                                                    if len(parts) == 2 and parts[1].isdigit():
                                                        h = int(parts[1])
                                                        if h == height:
                                                            exact_resolution = part
                                                            print(f"[DOWNLOADER] Found exact resolution {exact_resolution} in log line: {log_line}")
                                                            break
                                    except Exception as e:
                                        print(f"[WARNING] Error parsing log line for resolution: {e}")

                            # Try to find the stream ID for the desired height
                            stream_id = None
                            for log_line in self.download_logs.get(row_index, []):
                                # Look for lines like "Vid *CENC 640x360 | 429 Kbps | 5 | 25 | avc1.4D401E"
                                if "Vid" in log_line and f"x{height}" in log_line:
                                    try:
                                        # Extract the stream ID (usually the third number in the line)
                                        parts = log_line.split("|")
                                        if len(parts) >= 3:
                                            id_part = parts[2].strip()
                                            if id_part.isdigit():
                                                stream_id = id_part
                                                print(f"[DOWNLOADER] Found stream ID {stream_id} for height {height}")
                                                break
                                    except Exception as e:
                                        print(f"[WARNING] Error extracting stream ID: {e}")

                            if stream_id:
                                # Use the stream ID for precise selection
                                print(f"[DOWNLOADER] Retrying with stream ID {stream_id}")

                                retry_command = (
                                    f'"{self.n_m3u8dl_path}" "{mpd_url}" '
                                    f'--select-video "id={stream_id}" '  # Use ID for exact stream selection
                                )
                            elif exact_resolution:
                                # Fallback to exact resolution if ID not found
                                print(f"[DOWNLOADER] Retrying with exact resolution {exact_resolution}")

                                retry_command = (
                                    f'"{self.n_m3u8dl_path}" "{mpd_url}" '
                                    f'--select-video "res={exact_resolution}" '
                                )
                            else:
                                # Last resort: try with a fixed ID that often corresponds to 360p
                                print(f"[DOWNLOADER] Retrying with fallback ID for height {height}")

                                # ID 5 often corresponds to 360p in Shahid streams
                                retry_command = (
                                    f'"{self.n_m3u8dl_path}" "{mpd_url}" '
                                    f'--select-video "id=5" '  # Try using ID 5 which is often 360p
                                )

                            # Add audio option if available
                            if audio_option:
                                retry_command += f'{audio_option} '
                            else:
                                retry_command += f'--select-audio "lang=ar" '  # Default to Arabic

                            # Add subtitle option if available
                            if subtitle_option:
                                retry_command += f'{subtitle_option} '

                            # Add the rest of the parameters
                            retry_command += (
                                f'--tmp-dir "{cache_dir}" '
                                f'--save-dir "{cache_dir}" '
                                f'--save-name "{episode_name}" '
                                f'--base-url "{base_url}" '
                                f'--decryption-binary-path="{self.mp4decrypt_path}" '
                                f'--key-text-file="{self.keys_path}" '
                                f'--log-level "INFO" '
                                f'--binary-merge '
                                f'--thread-count 16 '
                                f'--download-retry-count 3 '
                                f'-mt'
                            )
                        else:
                            # Fallback to a more specific selection if we couldn't determine the height
                            print("[DOWNLOADER] Could not determine height, falling back to SD quality")

                            retry_command = (
                                f'"{self.n_m3u8dl_path}" "{mpd_url}" '
                                f'--select-video "height<=480" '  # Select a reasonable default (SD quality)
                            )

                            # Add audio option if available
                            if audio_option:
                                retry_command += f'{audio_option} '
                            else:
                                retry_command += f'--select-audio "lang=ar" '  # Default to Arabic

                            # Add subtitle option if available
                            if subtitle_option:
                                retry_command += f'{subtitle_option} '

                            # Add the rest of the parameters
                            retry_command += (
                                f'--tmp-dir "{cache_dir}" '
                                f'--save-dir "{cache_dir}" '
                                f'--save-name "{episode_name}" '
                                f'--base-url "{base_url}" '
                                f'--decryption-binary-path="{self.mp4decrypt_path}" '
                                f'--key-text-file="{self.keys_path}" '
                                f'--log-level "INFO" '
                                f'--binary-merge '
                                f'--thread-count 16 '
                                f'--download-retry-count 3 '
                                f'-mt'
                            )

                        print(f"[DOWNLOADER] Retrying with command: {retry_command}")
                        self.signals.status_updated.emit(row_index, "Retrying")

                        # Run the retry command
                        retry_process = subprocess.Popen(
                            retry_command,
                            shell=True,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.STDOUT,
                            universal_newlines=True,
                            encoding='utf-8',
                            errors='replace'
                        )

                        # Monitor the retry process
                        for line in retry_process.stdout:
                            print(line.strip())

                        # Wait for the retry process to complete
                        retry_process.wait()

                        # Check if retry was successful
                        if retry_process.returncode == 0:
                            print("[DOWNLOADER] Retry successful, checking for files...")

                            # Look for video files again
                            video_files = []
                            for file in os.listdir(cache_dir):
                                file_path = os.path.join(cache_dir, file)
                                if file.endswith(('.mp4', '.m4v', '.ts', '.mkv', '.webm')) and os.path.getsize(file_path) > 0:
                                    video_files.append(file_path)
                                    print(f"[DOWNLOADER] Found video file after retry: {file}")

                            if video_files:
                                # Found video files, proceed with merging
                                largest_video = max(video_files, key=os.path.getsize)
                                files_to_merge = [largest_video]

                                # Find audio files again
                                audio_files = []
                                for file in os.listdir(cache_dir):
                                    if file.endswith(".m4a"):
                                        audio_files.append(os.path.join(cache_dir, file))
                                        print(f"[DOWNLOADER] Found audio file after retry: {file}")

                                files_to_merge.extend(audio_files)

                                print(f"[DOWNLOADER] Files to merge after retry: {files_to_merge}")

                                # Proceed with merging
                                self.signals.status_updated.emit(row_index, "Merging")
                                self.merge_with_mkvmerge(files_to_merge, final_output_file)
                                print(f"Final merged file saved to: {final_output_file}")

                                # Update status to "Completed"
                                self.signals.status_updated.emit(row_index, "Completed")
                                self.signals.progress_updated.emit(row_index, 100)

                                # Emit download completed signal with success=True
                                self.signals.download_completed.emit(row_index, True)

                                # Copy poster image if it exists in cache
                                try:
                                    # Get the content title from the output file path
                                    content_dir = os.path.dirname(final_output_file)

                                    # Check if there's a poster in the cache/posters directory
                                    poster_cache_dir = os.path.join(self.dir_path, "cache", "posters")
                                    if os.path.exists(poster_cache_dir):
                                        # Find the most recent poster file
                                        poster_files = glob.glob(os.path.join(poster_cache_dir, "*.jpg"))
                                        if poster_files:
                                            # Sort by modification time (newest first)
                                            newest_poster = max(poster_files, key=os.path.getmtime)

                                            # Copy to the content directory
                                            poster_dest = os.path.join(content_dir, "poster.jpg")
                                            if not os.path.exists(poster_dest):
                                                shutil.copy(newest_poster, poster_dest)
                                                print(f"Copied poster to: {poster_dest}")
                                except Exception as poster_error:
                                    print(f"Error copying poster: {poster_error}")

                                return

                    # If we get here, all attempts failed
                    self.signals.status_updated.emit(row_index, "Failed")

                    # Emit download completed signal with success=False
                    self.signals.download_completed.emit(row_index, False)
            else:
                print(f"Download process failed with return code {process.returncode}")
                self.signals.status_updated.emit(row_index, "Failed")

                # Emit download completed signal with success=False
                self.signals.download_completed.emit(row_index, False)

        except Exception as e:
            print(f"Error in download process: {e}")
            import traceback
            traceback.print_exc()
            self.signals.status_updated.emit(row_index, "Error")

            # Emit download completed signal with success=False
            self.signals.download_completed.emit(row_index, False)

        finally:
            # Remove from active downloads
            if row_index in self.active_downloads:
                del self.active_downloads[row_index]

            # Remove from download threads
            if row_index in self.download_threads:
                del self.download_threads[row_index]

            # Update overall progress
            self._update_overall_progress()

    def _update_progress(self, row_index, progress, method):
        """Update the progress for a specific download."""
        if row_index not in self.active_downloads:
            return

        # Ensure progress is within valid range
        progress = max(0, min(100, progress))

        # Only update if the new progress is higher than the current progress
        current_progress = self.active_downloads[row_index].get('progress', 0)
        if progress > current_progress:
            self.active_downloads[row_index]['progress'] = progress
            self.signals.progress_updated.emit(row_index, progress)
            print(f"[DOWNLOADER] Progress updated for row {row_index}: {progress}% (method: {method})")

            # Update the last update time
            self.active_downloads[row_index]['last_update'] = time.time()

            # Update overall progress
            self._update_overall_progress()

    def _extract_size(self, text):
        """Extract file size from text (in KB)."""
        try:
            # Find all numbers in the text
            import re
            numbers = re.findall(r'\d+\.?\d*', text)
            if not numbers:
                return 0

            # Get the last number (most likely the size)
            size_str = numbers[-1]
            size = float(size_str)

            # Convert to KB based on unit
            if "MB" in text:
                size *= 1024  # Convert MB to KB
            elif "GB" in text:
                size *= 1024 * 1024  # Convert GB to KB

            return size
        except Exception as e:
            print(f"Error extracting size: {e}")
            return 0

    def _update_overall_progress(self):
        """Update the overall progress based on all active downloads."""
        if not self.active_downloads:
            # No active downloads, set progress to 0
            self.signals.overall_progress_updated.emit(0)
            return

        # Calculate average progress
        total_progress = sum(download['progress'] for download in self.active_downloads.values())
        average_progress = total_progress // len(self.active_downloads)

        # Emit the signal
        self.signals.overall_progress_updated.emit(average_progress)

    def cancel_download(self, row_index):
        """Cancel a download in progress."""
        if row_index in self.active_downloads:
            # Get the process
            process = self.active_downloads[row_index]['process']

            # Kill the process if it's running
            if process and process.poll() is None:
                try:
                    process.terminate()
                    print(f"Download process terminated for row {row_index}")
                except Exception as e:
                    print(f"Error terminating process: {e}")

            # Update status
            self.signals.status_updated.emit(row_index, "Cancelled")

            # Clean up cache directory
            cache_dir = self.active_downloads[row_index]['cache_dir']
            try:
                if os.path.exists(cache_dir):
                    shutil.rmtree(cache_dir)
                    print(f"Cache directory {cache_dir} has been deleted.")
            except Exception as e:
                print(f"Error deleting cache directory: {e}")

            # Remove from active downloads
            del self.active_downloads[row_index]

            # Remove from download threads
            if row_index in self.download_threads:
                del self.download_threads[row_index]

            # Update overall progress
            self._update_overall_progress()

    def download_content_with_idm(self, row_index, mpd_url, resolution, audio_tracks, subtitle_tracks, content_title, season_number=None, episode_number=None, content_type="SERIES", drm_info=None):
        """Download content using IDM by extracting direct video URLs from MPD."""
        try:
            print(f"[IDM] Starting IDM download for: {content_title}")

            # Check if IDM is available
            if not self.is_idm_available():
                print("[IDM] IDM is not available. Please install IDM or check the installation path.")
                self.signals.status_updated.emit(row_index, "IDM Not Available")
                return False

            # Update status
            self.signals.status_updated.emit(row_index, "Extracting URLs")

            # Extract stream information from MPD
            qualities, audios, subtitles = self.extract_stream_info_from_url(mpd_url)

            if not qualities:
                print("[IDM] No video qualities found in MPD")
                self.signals.status_updated.emit(row_index, "No Video Found")
                return False

            # Find the best matching quality
            target_resolution = resolution
            if resolution.endswith("p"):
                # Convert from "360p" to "640x360" format
                resolution_map = {
                    "144p": "256x144", "240p": "426x240", "252p": "426x252", "288p": "512x288",
                    "360p": "640x360", "468p": "832x468", "480p": "854x480", "576p": "1024x576",
                    "720p": "1280x720", "1080p": "1920x1080", "1440p": "2560x1440", "2160p": "3840x2160"
                }
                target_resolution = resolution_map.get(resolution, resolution)

            # Find matching quality
            selected_quality = None
            for quality in qualities:
                if quality[0] == target_resolution:
                    selected_quality = quality
                    break

            if not selected_quality:
                # Fallback to the highest available quality
                selected_quality = qualities[-1] if qualities else None
                print(f"[IDM] Target resolution {target_resolution} not found, using: {selected_quality[0] if selected_quality else 'None'}")

            if not selected_quality:
                print("[IDM] No suitable video quality found")
                self.signals.status_updated.emit(row_index, "No Quality Found")
                return False

            # Create download directory
            sanitized_title = re.sub(r'[<>:"/\\|?*]', '', content_title)
            if content_type == "MOVIE":
                download_dir = os.path.join(self.dir_path, "downloads", sanitized_title)
                filename = f"{sanitized_title}.{resolution}.SHAHID.VIP.WEB-DL.H264.AAC.mkv"
            else:
                # Convert season_number and episode_number to integers if they're strings
                season_num = int(season_number) if isinstance(season_number, str) else season_number
                episode_num = int(episode_number) if isinstance(episode_number, str) else episode_number
                download_dir = os.path.join(self.dir_path, "downloads", sanitized_title, f"Season {season_num}")
                filename = f"{sanitized_title}.S{season_num:02d}E{episode_num:02d}.{resolution}.SHAHID.VIP.WEB-DL.H264.AAC.mkv"

            os.makedirs(download_dir, exist_ok=True)

            # Update status
            self.signals.status_updated.emit(row_index, "Starting IDM")

            # Check if the URL is an MPD file
            if mpd_url.endswith('.mpd') or 'manifest' in mpd_url.lower():
                print(f"[IDM] Warning: Detected MPD/DASH URL. IDM may not handle this directly.")
                print(f"[IDM] URL: {mpd_url}")

                # Try to extract a direct video URL from MPD if possible
                # For now, we'll still try with the MPD URL but warn the user
                self.signals.status_updated.emit(row_index, "MPD URL - IDM may not support")

            # Use N_m3u8DL-RE to download the content (IDM-style)
            success = self.download_with_n_m3u8dl_for_idm(mpd_url, download_dir, filename, resolution, audio_tracks, subtitle_tracks, row_index, drm_info)

            if success:
                print(f"[IDM] Successfully completed download: {filename}")
                self.signals.status_updated.emit(row_index, "Completed")
                self.signals.progress_updated.emit(row_index, 100)
                return True
            else:
                print(f"[IDM] Failed to download: {filename}")
                self.signals.status_updated.emit(row_index, "Failed")
                return False

        except Exception as e:
            print(f"[IDM] Error in IDM download: {e}")
            import traceback
            traceback.print_exc()
            self.signals.status_updated.emit(row_index, "Error")
            return False

    def download_with_n_m3u8dl_for_idm(self, mpd_url, download_dir, filename, resolution, audio_tracks, subtitle_tracks, row_index, drm_info=None):
        """Download content using N_m3u8DL-RE for IDM-style downloading."""
        try:
            print(f"[IDM] Using N_m3u8DL-RE to download content")

            # Check if N_m3u8DL-RE exists
            n_m3u8dl_path = os.path.join(self.dir_path, "binaries", "N_m3u8DL-RE.exe")
            if not os.path.exists(n_m3u8dl_path):
                # Try alternative path (root directory)
                n_m3u8dl_path = os.path.join(self.dir_path, "N_m3u8DL-RE.exe")
                if not os.path.exists(n_m3u8dl_path):
                    print(f"[IDM] N_m3u8DL-RE.exe not found at: {n_m3u8dl_path}")
                    print(f"[IDM] Also checked: {os.path.join(self.dir_path, 'binaries', 'N_m3u8DL-RE.exe')}")
                    self.signals.status_updated.emit(row_index, "N_m3u8DL-RE Not Found")
                    return False

            # Update status
            self.signals.status_updated.emit(row_index, "Starting Download")

            # Prepare command arguments - simplified for better compatibility
            cmd = [n_m3u8dl_path]
            cmd.append(mpd_url)  # Add URL as separate argument
            cmd.extend(["--save-dir", download_dir])
            cmd.extend(["--save-name", filename.replace('.mkv', '')])

            # Add resolution selection
            if resolution and resolution != "Best":
                if resolution.endswith('p'):
                    height = resolution.replace('p', '')
                    cmd.extend(["--select-video", f"height:{height}"])

            # Add audio track selection
            if audio_tracks:
                for track in audio_tracks:
                    cmd.extend(["--select-audio", f"lang:{track}"])
            else:
                # Default to Arabic if no audio tracks specified
                cmd.extend(["--select-audio", "lang:ar"])

            # Add subtitle selection
            if subtitle_tracks:
                for track in subtitle_tracks:
                    cmd.extend(["--select-subtitle", f"lang:{track}"])

            # Add essential options
            cmd.extend(["--binary-merge"])
            cmd.extend(["--del-after-done"])
            cmd.extend(["-mt"])  # Concurrent download mode
            cmd.extend(["--download-retry-count", "3"])

            # Add ffmpeg path if available in binaries folder
            ffmpeg_path = os.path.join(self.dir_path, "binaries", "ffmpeg.exe")
            if os.path.exists(ffmpeg_path):
                cmd.extend(["--ffmpeg-binary-path", ffmpeg_path])
                print(f"[IDM] Using ffmpeg from: {ffmpeg_path}")

            # Add decryption key if available
            if drm_info and 'formatted_key' in drm_info:
                cmd.extend(["--key", drm_info['formatted_key']])
                print(f"[IDM] Using decryption key: {drm_info['formatted_key']}")

                # Also save the key to the keys file for N_m3u8DL-RE
                if drm_info.get('kid') and drm_info.get('key'):
                    try:
                        # Check if the key already exists in the file
                        key_exists = False
                        if os.path.exists(self.keys_path):
                            with open(self.keys_path, 'r') as f:
                                for line in f:
                                    if drm_info['kid'] in line:
                                        key_exists = True
                                        break

                        # Add the key if it doesn't exist
                        if not key_exists:
                            with open(self.keys_path, 'a') as f:
                                f.write(f"{drm_info['kid']}:{drm_info['key']}\n")
                            print(f"[IDM] Added DRM key to keys file: {drm_info['kid']}")

                        # Use the keys file with N_m3u8DL-RE
                        cmd.extend(["--key-text-file", self.keys_path])

                        # Add mp4decrypt path if available
                        mp4decrypt_path = os.path.join(self.dir_path, "binaries", "mp4decrypt.exe")
                        if os.path.exists(mp4decrypt_path):
                            cmd.extend(["--decryption-binary-path", mp4decrypt_path])
                            print(f"[IDM] Using mp4decrypt from: {mp4decrypt_path}")
                    except Exception as e:
                        print(f"[IDM] Error handling DRM key: {e}")
            else:
                print(f"[IDM] No DRM info provided")

            # Skip headers for now to avoid parsing issues
            # cmd.extend(["-H", "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"])
            # cmd.extend(["-H", "Referer: https://shahid.mbc.net/"])

            print(f"[IDM] Executing command: {' '.join(cmd)}")

            # Execute N_m3u8DL-RE
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=self.dir_path,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            # Monitor the process and update progress
            progress = 0
            while process.poll() is None:
                output = process.stdout.readline()
                if output:
                    output_line = output.strip()
                    print(f"[N_m3u8DL-RE] {output_line}")

                    # Parse progress from output
                    if "%" in output_line:
                        try:
                            # Look for percentage patterns
                            import re
                            percent_match = re.search(r'(\d+(?:\.\d+)?)%', output_line)
                            if percent_match:
                                progress = float(percent_match.group(1))
                                self.signals.progress_updated.emit(row_index, int(progress))
                                self.signals.status_updated.emit(row_index, f"Downloading: {progress:.1f}%")
                        except:
                            pass

                    # Update status based on output keywords
                    if "Downloading" in output_line:
                        self.signals.status_updated.emit(row_index, "Downloading")
                    elif "Merging" in output_line:
                        self.signals.status_updated.emit(row_index, "Merging")
                    elif "Done" in output_line:
                        self.signals.status_updated.emit(row_index, "Finalizing")

            # Wait for completion
            stdout, stderr = process.communicate()

            if process.returncode == 0:
                print(f"[IDM] Download completed successfully")

                # Find the downloaded file
                downloaded_file = None
                for ext in ['.mkv', '.mp4', '.ts']:
                    potential_file = os.path.join(download_dir, filename.replace('.mkv', ext))
                    if os.path.exists(potential_file):
                        downloaded_file = potential_file
                        break

                # Also check for files without extension change
                if not downloaded_file:
                    potential_file = os.path.join(download_dir, filename)
                    if os.path.exists(potential_file):
                        downloaded_file = potential_file

                if downloaded_file and os.path.exists(downloaded_file):
                    file_size = os.path.getsize(downloaded_file)
                    print(f"[IDM] File downloaded successfully: {downloaded_file} ({file_size} bytes)")
                    return True
                else:
                    print(f"[IDM] Downloaded file not found in: {download_dir}")
                    # List files in directory for debugging
                    try:
                        files = os.listdir(download_dir)
                        print(f"[IDM] Files in directory: {files}")
                    except:
                        pass
                    return False
            else:
                print(f"[IDM] Download failed with return code: {process.returncode}")
                if stderr:
                    print(f"[IDM] Error: {stderr}")
                return False

        except Exception as e:
            print(f"[IDM] Error in N_m3u8DL-RE download: {e}")
            import traceback
            traceback.print_exc()
            return False

    def add_to_idm_queue(self, row_index, mpd_url, resolution, audio_tracks, subtitle_tracks, content_title, season_number=None, episode_number=None, content_type="SERIES", drm_info=None):
        """Add content to IDM download queue."""
        try:
            print(f"[IDM] Adding to IDM queue: {content_title}")

            # Check if IDM is available
            if not self.is_idm_available():
                print("[IDM] IDM is not available. Please install IDM or check the installation path.")
                self.signals.status_updated.emit(row_index, "IDM Not Available")
                return False

            # Create download directory and filename
            sanitized_title = re.sub(r'[<>:"/\\|?*]', '', content_title)
            if content_type == "MOVIE":
                download_dir = os.path.join(self.dir_path, "downloads", sanitized_title)
                filename = f"{sanitized_title}.{resolution}.SHAHID.VIP.WEB-DL.H264.AAC.mkv"
            else:
                # Convert season_number and episode_number to integers if they're strings
                season_num = int(season_number) if isinstance(season_number, str) else season_number
                episode_num = int(episode_number) if isinstance(episode_number, str) else episode_number
                download_dir = os.path.join(self.dir_path, "downloads", sanitized_title, f"Season {season_num}")
                filename = f"{sanitized_title}.S{season_num:02d}E{episode_num:02d}.{resolution}.SHAHID.VIP.WEB-DL.H264.AAC.mkv"

            os.makedirs(download_dir, exist_ok=True)

            # Update status
            self.signals.status_updated.emit(row_index, "Queued for Download")

            # Use the same download method but mark as queued
            success = self.download_with_n_m3u8dl_for_idm(mpd_url, download_dir, filename, resolution, audio_tracks, subtitle_tracks, row_index, drm_info)

            if success:
                print(f"[IDM] Successfully completed queued download: {filename}")
                self.signals.status_updated.emit(row_index, "Completed")
                self.signals.progress_updated.emit(row_index, 100)
                return True
            else:
                print(f"[IDM] Failed queued download: {filename}")
                self.signals.status_updated.emit(row_index, "Failed")
                return False

        except Exception as e:
            print(f"[IDM] Error adding to IDM queue: {e}")
            import traceback
            traceback.print_exc()
            self.signals.status_updated.emit(row_index, "Error")
            return False