{"advertising": {"admessage": "Este anúncio terminará em xx", "cuetext": "Publicidade", "displayHeading": "Publicidade", "loadingAd": "<PERSON><PERSON><PERSON>", "podmessage": "<PERSON><PERSON><PERSON> __AD_POD_CURRENT__ de __AD_POD_LENGTH__.", "skipmessage": "Pular anúncio em xx", "skiptext": "<PERSON><PERSON>"}, "airplay": "AirPlay", "audioTracks": "Faixas de Áudio", "auto": "Automático", "buffer": "Carregando", "cast": "Chromecast", "cc": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "errors": {"badConnection": "Este vídeo não pode ser reproduzido devido a um problema com sua conexão com a Internet.", "cantLoadPlayer": "<PERSON><PERSON><PERSON><PERSON>, o reprodutor de vídeo não foi carregado.", "cantPlayInBrowser": "O vídeo não pode ser reproduzido neste navegador.", "cantPlayVideo": "Este arquivo de vídeo não pode ser reproduzido.", "errorCode": "<PERSON>ó<PERSON>", "liveStreamDown": "A transmissão ao vivo está inativa ou foi encerrada.", "protectedContent": "Houve um problema ao fornecer acesso ao conteúdo protegido.", "technicalError": "Este vídeo não pode ser reproduzido devido a um problema técnico."}, "exitFullscreen": "<PERSON><PERSON> <PERSON>", "fullscreen": "Tela Cheia", "hd": "Qualidade", "liveBroadcast": "Ao Vivo", "logo": "Logo", "mute": "<PERSON>ati<PERSON>", "next": "Próximo", "nextUp": "A Seguir", "notLive": "Gravado", "off": "<PERSON><PERSON><PERSON>", "pause": "Pausar", "pipIcon": "Picture in Picture (PiP)", "play": "Reproduzir", "playback": "Reproduzir", "playbackRates": "Taxas de Reprodução", "player": "Reprodutor de Vídeo", "poweredBy": "Produzido por", "prev": "Anterior", "related": {"autoplaymessage": "A Seguir em xx", "heading": "<PERSON><PERSON>"}, "replay": "<PERSON><PERSON>r", "rewind": "Voltar 10 Segundos", "settings": "Configurações", "sharing": {"copied": "Copiado", "email": "E-Mail", "embed": "Embutir", "heading": "Compartilhar", "link": "Link"}, "slider": "Controle de Posicionamento", "stop": "<PERSON><PERSON>", "unmute": "Ativar Som", "videoInfo": "Sobre Este Vídeo", "volume": "Volume", "volumeSlider": "Controle de Volume", "shortcuts": {"playPause": "Reproduzir/Pausar", "volumeToggle": "Desativar Som/At<PERSON>r <PERSON>", "fullscreenToggle": "Tela Cheia/<PERSON><PERSON> da Tela Cheia", "seekPercent": "Adiantar %", "keyboardShortcuts": "Atalhos de Teclado", "increaseVolume": "Aumentar o Volume", "decreaseVolume": "<PERSON><PERSON><PERSON><PERSON> o <PERSON>", "seekForward": "Adiantar", "seekBackward": "Retroceder", "spacebar": "Espaço", "captionsToggle": "Legend<PERSON>/Desativar"}, "captionsStyles": {"subtitleSettings": "Configurações da Legenda", "color": "<PERSON><PERSON>", "fontOpacity": "Opacidade da Fonte", "userFontScale": "<PERSON><PERSON><PERSON>", "fontFamily": "Família <PERSON>", "edgeStyle": "Borda do Caractere", "backgroundColor": "<PERSON><PERSON> <PERSON>", "backgroundOpacity": "Opacidade de Fundo", "windowColor": "<PERSON><PERSON> <PERSON>", "windowOpacity": "Opacidade da Janela", "white": "Branco", "black": "Preto", "red": "Vermelho", "green": "Verde", "blue": "Azul", "yellow": "<PERSON><PERSON>", "magenta": "Ma<PERSON><PERSON>", "cyan": "<PERSON><PERSON>", "none": "<PERSON><PERSON><PERSON>", "raised": "<PERSON><PERSON><PERSON>", "depressed": "<PERSON><PERSON><PERSON><PERSON>", "uniform": "Uniforme", "dropShadow": "Sombra Projetada"}, "disabled": "Desativado", "enabled": "<PERSON><PERSON>do", "reset": "Reiniciar"}