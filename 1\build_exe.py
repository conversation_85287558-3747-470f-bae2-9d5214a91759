#!/usr/bin/env python3
"""
YANGO PLAY - Build Script for EXE Creation
==========================================

This script builds YANGO PLAY application into a standalone executable using Nuitka.
YANGO PLAY is a multi-platform streaming application supporting YANGO, OSN+, and Shahid VIP.

Requirements:
- Python 3.8+
- Nuitka
- All dependencies from requirements.txt

Usage:
    python build_exe.py

The script will:
1. Install required dependencies
2. Create necessary folder structure
3. Build the executable with all required resources
4. Create a distributable package

Author: AI Assistant
Date: 2025-01-06
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print("\n" + "="*60)
    print(f"  {title}")
    print("="*60)

def print_step(step_num, description):
    """Print a formatted step"""
    print(f"\n🔧 Step {step_num}: {description}")
    print("-" * 50)

def run_command(command, description=""):
    """Run a command and handle errors"""
    try:
        if description:
            print(f"📋 {description}")
        print(f"💻 Running: {command}")
        
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        
        if result.stdout:
            print(f"✅ Output: {result.stdout.strip()}")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error: {e}")
        if e.stdout:
            print(f"📤 Stdout: {e.stdout}")
        if e.stderr:
            print(f"📥 Stderr: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    print_step(1, "Checking Python Version")
    
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required!")
        return False
    
    print("✅ Python version is compatible")
    return True

def install_dependencies():
    """Install required dependencies"""
    print_step(2, "Installing Dependencies")
    
    # Install from requirements.txt
    if not run_command("pip install -r requirements.txt", 
                      "Installing dependencies from requirements.txt"):
        return False
    
    # Ensure Nuitka is installed
    if not run_command("pip install nuitka>=1.8.0", 
                      "Ensuring Nuitka is installed"):
        return False
    
    print("✅ All dependencies installed successfully")
    return True

def prepare_build_environment():
    """Prepare the build environment"""
    print_step(3, "Preparing Build Environment")
    
    # Get current directory
    current_dir = Path.cwd()
    print(f"📁 Current directory: {current_dir}")
    
    # Required folders and files for YANGO PLAY
    required_items = [
        "binaries",
        "modules", 
        "widgets",
        "themes",
        "images",
        "images-osn",
        "images-shahid", 
        "player",
        "player-osn",
        "player-shahid",
        "icons",
        "main.py",
        "qt_core.py",
        "cookies.txt",
        "device.wvd"
    ]
    
    missing_items = []
    for item in required_items:
        item_path = current_dir / item
        if not item_path.exists():
            missing_items.append(item)
            print(f"⚠️  Missing: {item}")
        else:
            print(f"✅ Found: {item}")
    
    if missing_items:
        print(f"\n❌ Missing required items: {missing_items}")
        print("Please ensure all required folders and files are present.")
        return False
    
    print("✅ Build environment is ready")
    return True

def build_executable():
    """Build the executable using Nuitka"""
    print_step(4, "Building Executable with Nuitka")
    
    # Get current directory for paths
    current_dir = Path.cwd()
    
    # Build the Nuitka command
    nuitka_cmd = [
        "python", "-m", "nuitka",
        "--standalone",
        "--enable-plugin=pyside6",
        "--windows-console-mode=disable",
        "--windows-icon-from-ico=icons/yango.ico",
        "--include-data-dir=binaries=binaries",
        "--include-data-dir=modules=modules", 
        "--include-data-dir=widgets=widgets",
        "--include-data-dir=themes=themes",
        "--include-data-dir=images=images",
        "--include-data-dir=images-osn=images-osn",
        "--include-data-dir=images-shahid=images-shahid",
        "--include-data-dir=player=player",
        "--include-data-dir=player-osn=player-osn", 
        "--include-data-dir=player-shahid=player-shahid",
        "--include-data-dir=icons=icons",
        "--include-data-file=cookies.txt=cookies.txt",
        "--include-data-file=device.wvd=device.wvd",
        "--include-data-file=qt_core.py=qt_core.py",
        "--output-dir=dist",
        "--output-filename=YANGO_PLAY.exe",
        "main.py"
    ]
    
    # Add optional files if they exist
    optional_files = [
        "cookies-osn.txt",
        "osn_refresh_token.txt"
    ]
    
    for optional_file in optional_files:
        if (current_dir / optional_file).exists():
            nuitka_cmd.insert(-1, f"--include-data-file={optional_file}={optional_file}")
            print(f"✅ Including optional file: {optional_file}")
    
    # Convert to string command
    command = " ".join(nuitka_cmd)
    
    print("🚀 Starting Nuitka build process...")
    print("⏳ This may take several minutes...")
    
    if not run_command(command, "Building executable with Nuitka"):
        return False
    
    print("✅ Executable built successfully")
    return True

def verify_build():
    """Verify the build was successful"""
    print_step(5, "Verifying Build")
    
    dist_dir = Path("dist")
    exe_path = dist_dir / "YANGO_PLAY.exe"
    
    if not dist_dir.exists():
        print("❌ Distribution directory not found")
        return False
    
    if not exe_path.exists():
        print("❌ Executable file not found")
        return False
    
    # Check file size
    file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
    print(f"📊 Executable size: {file_size:.2f} MB")
    
    # List contents of dist directory
    print("\n📁 Distribution directory contents:")
    for item in dist_dir.iterdir():
        if item.is_file():
            size = item.stat().st_size / (1024 * 1024)
            print(f"   📄 {item.name} ({size:.2f} MB)")
        else:
            print(f"   📁 {item.name}/")
    
    print("✅ Build verification completed")
    return True

def create_package():
    """Create a distributable package"""
    print_step(6, "Creating Distribution Package")
    
    try:
        # Create package directory
        package_dir = Path("YANGO_PLAY_Package")
        if package_dir.exists():
            shutil.rmtree(package_dir)
        package_dir.mkdir()
        
        # Copy executable and dependencies
        dist_dir = Path("dist")
        if dist_dir.exists():
            # Copy all contents from dist to package
            for item in dist_dir.iterdir():
                if item.is_file():
                    shutil.copy2(item, package_dir)
                else:
                    shutil.copytree(item, package_dir / item.name)
        
        # Create README for the package
        readme_content = """YANGO PLAY - Streaming Application
=====================================

This package contains YANGO PLAY, a multi-platform streaming application 
supporting YANGO, OSN+, and Shahid VIP platforms.

Files included:
- YANGO_PLAY.exe - Main application executable
- All required dependencies and resources

To run:
1. Double-click YANGO_PLAY.exe
2. The application will start with the welcome screen
3. Choose your preferred streaming platform from the sidebar

Features:
- YANGO streaming support
- OSN+ integration  
- Shahid VIP support
- Modern Qt-based interface
- Download capabilities
- DRM support

Requirements:
- Windows 10/11
- Internet connection for streaming

For support or issues, please contact the developer.

Built with Nuitka - Python to executable compiler
"""
        
        readme_path = package_dir / "README.txt"
        readme_path.write_text(readme_content, encoding='utf-8')
        
        print(f"✅ Package created: {package_dir}")
        print(f"📦 Package size: {sum(f.stat().st_size for f in package_dir.rglob('*') if f.is_file()) / (1024*1024):.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating package: {e}")
        return False

def main():
    """Main build process"""
    print_header("YANGO PLAY - EXE Build Script")
    print("🎵 Building YANGO PLAY multi-platform streaming application")
    
    # Check if we're in the right directory
    if not Path("main.py").exists():
        print("❌ main.py not found. Please run this script from the YANGO PLAY directory.")
        return False
    
    # Execute build steps
    steps = [
        check_python_version,
        install_dependencies, 
        prepare_build_environment,
        build_executable,
        verify_build,
        create_package
    ]
    
    for step_func in steps:
        if not step_func():
            print(f"\n❌ Build failed at step: {step_func.__name__}")
            return False
    
    print_header("BUILD COMPLETED SUCCESSFULLY!")
    print("🎉 YANGO PLAY executable has been created successfully!")
    print("\n📁 Output locations:")
    print("   • Executable: dist/YANGO_PLAY.exe")
    print("   • Package: YANGO_PLAY_Package/")
    print("\n🚀 You can now distribute the YANGO_PLAY_Package folder")
    print("   or run the executable directly from the dist folder.")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
