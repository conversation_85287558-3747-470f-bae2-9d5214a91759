#!/usr/bin/env python3
"""
YANGO PLAY - Portable Package Creator
====================================

This script creates a portable version of YANGO PLAY that can be moved anywhere and run independently.
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_portable_package():
    """Create a portable package of YANGO PLAY"""
    
    print("🎵 Creating YANGO PLAY Portable Package...")
    print("=" * 60)
    
    # Define paths
    source_dir = Path("dist/main.dist")
    package_name = "YANGO_PLAY_Portable"
    package_dir = Path(package_name)
    
    # Check if source exists
    if not source_dir.exists():
        print("❌ Source directory not found. Please build the application first.")
        return False
    
    # Remove existing package if it exists
    if package_dir.exists():
        print("🗑️ Removing existing package...")
        shutil.rmtree(package_dir)
    
    # Create package directory
    print("📁 Creating package directory...")
    package_dir.mkdir()
    
    # Copy all files from dist
    print("📋 Copying application files...")
    shutil.copytree(source_dir, package_dir / "YANGO_PLAY", dirs_exist_ok=True)
    
    # Create launcher script
    print("🚀 Creating launcher script...")
    launcher_content = '''@echo off
title YANGO PLAY - Multi-Platform Streaming Application
echo.
echo ================================================================
echo   YANGO PLAY - Starting Application
echo   Multi-Platform Streaming: YANGO, OSN+, Shahid VIP
echo ================================================================
echo.

cd /d "%~dp0YANGO_PLAY"
start "" "YANGO_PLAY.exe"

echo ✅ YANGO PLAY started successfully!
echo.
echo You can now close this window.
timeout /t 3 >nul
'''
    
    launcher_path = package_dir / "Start_YANGO_PLAY.bat"
    launcher_path.write_text(launcher_content, encoding='utf-8')
    
    # Create README file
    print("📝 Creating README file...")
    readme_content = '''YANGO PLAY - Portable Edition
=============================

🎵 Multi-Platform Streaming Application
Supporting YANGO, OSN+, and Shahid VIP platforms

📁 PACKAGE CONTENTS:
===================
- YANGO_PLAY/          : Application files and dependencies
- Start_YANGO_PLAY.bat : Quick launcher (double-click to run)
- README.txt           : This file

🚀 HOW TO USE:
=============
1. Extract this package to any location on your computer
2. Double-click "Start_YANGO_PLAY.bat" to launch the application
3. Or navigate to YANGO_PLAY folder and run YANGO_PLAY.exe directly

✨ FEATURES:
===========
- YANGO streaming platform support
- OSN+ integration with full download capabilities  
- Shahid VIP streaming support
- Modern Qt-based user interface
- Built-in video player
- Download manager with progress tracking
- DRM content support
- Multiple quality options
- Subtitle support

🔧 TECHNICAL DETAILS:
====================
- Standalone executable (no Python installation required)
- All dependencies included
- Portable - can be moved to any Windows computer
- No installation required
- Works on Windows 10/11

📋 SYSTEM REQUIREMENTS:
======================
- Windows 10 or Windows 11
- 4GB RAM minimum (8GB recommended)
- 1GB free disk space
- Internet connection for streaming

🛠️ INCLUDED TOOLS:
==================
- N_m3u8DL-RE: Advanced HLS/DASH downloader
- aria2c: High-speed download manager
- ffmpeg: Video/audio processing
- mkvmerge: Video container management
- mp4decrypt: DRM decryption support

⚙️ CONFIGURATION:
=================
- Settings are saved automatically
- Recent URLs are remembered
- Download preferences are preserved
- Proxy settings supported

🔐 DRM SUPPORT:
==============
- Widevine DRM decryption
- Content protection handling
- License key extraction
- Secure playback

📞 SUPPORT:
==========
For issues or questions, please contact the developer.

🎯 VERSION INFO:
===============
Built with: Nuitka (Python to EXE compiler)
GUI Framework: PySide6 (Qt for Python)
Build Date: 2025-01-06

⚠️ IMPORTANT NOTES:
==================
- This is a portable application - no installation needed
- Can be run from USB drives or external storage
- All settings and downloads are saved in the application folder
- Antivirus software may flag the executable (false positive)
- For best performance, run from local drive rather than network location

🎉 ENJOY STREAMING WITH YANGO PLAY!
===================================
'''
    
    readme_path = package_dir / "README.txt"
    readme_path.write_text(readme_content, encoding='utf-8')
    
    # Create uninstaller
    print("🗑️ Creating uninstaller...")
    uninstall_content = '''@echo off
title YANGO PLAY - Uninstaller
echo.
echo ================================================================
echo   YANGO PLAY - Uninstaller
echo ================================================================
echo.
echo This will completely remove YANGO PLAY from your computer.
echo.
set /p confirm="Are you sure you want to uninstall? (y/N): "

if /i "%confirm%"=="y" (
    echo.
    echo 🗑️ Removing YANGO PLAY...
    
    REM Close any running instances
    taskkill /f /im "YANGO_PLAY.exe" 2>nul
    
    REM Wait a moment
    timeout /t 2 >nul
    
    REM Remove the application folder
    cd ..
    rmdir /s /q "%~dp0"
    
    echo ✅ YANGO PLAY has been completely removed.
    echo.
    pause
) else (
    echo.
    echo ❌ Uninstallation cancelled.
    echo.
    pause
)
'''
    
    uninstall_path = package_dir / "YANGO_PLAY" / "Uninstall.bat"
    uninstall_path.write_text(uninstall_content, encoding='utf-8')
    
    # Create version info file
    print("ℹ️ Creating version info...")
    version_content = '''YANGO PLAY - Version Information
===============================

Application: YANGO PLAY Portable
Version: 1.0.0
Build Date: 2025-01-06
Build Type: Standalone Portable

Supported Platforms:
- YANGO Streaming
- OSN+ (with download support)
- Shahid VIP

Technical Stack:
- Python 3.9
- PySide6 (Qt 6)
- Nuitka Compiler
- Custom UI Framework

Package Contents:
- Main executable: YANGO_PLAY.exe
- All required DLLs and dependencies
- Streaming tools and utilities
- DRM support files
- UI themes and resources

Total Package Size: ~200MB
Compressed Size: ~80MB (estimated)

This is a completely portable version that requires no installation.
'''
    
    version_path = package_dir / "VERSION.txt"
    version_path.write_text(version_content, encoding='utf-8')
    
    # Calculate package size
    total_size = sum(f.stat().st_size for f in package_dir.rglob('*') if f.is_file())
    size_mb = total_size / (1024 * 1024)
    
    print(f"📊 Package created successfully!")
    print(f"📁 Location: {package_dir.absolute()}")
    print(f"📏 Size: {size_mb:.1f} MB")
    print(f"📦 Files: {len(list(package_dir.rglob('*')))}")
    
    # Ask if user wants to create ZIP
    print("\n" + "=" * 60)
    print("🗜️ Creating ZIP archive for easy distribution...")
    
    zip_path = Path(f"{package_name}.zip")
    if zip_path.exists():
        zip_path.unlink()
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
        for file_path in package_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(package_dir.parent)
                zipf.write(file_path, arcname)
                
    zip_size = zip_path.stat().st_size / (1024 * 1024)
    
    print(f"✅ ZIP archive created: {zip_path}")
    print(f"📏 Compressed size: {zip_size:.1f} MB")
    
    print("\n" + "=" * 60)
    print("🎉 PORTABLE PACKAGE READY!")
    print("=" * 60)
    print(f"📁 Folder: {package_dir}")
    print(f"🗜️ ZIP: {zip_path}")
    print("\n🚀 To use:")
    print("   1. Extract the ZIP to any location")
    print("   2. Double-click 'Start_YANGO_PLAY.bat'")
    print("   3. Enjoy streaming!")
    
    return True

if __name__ == "__main__":
    success = create_portable_package()
    if success:
        print("\n✅ Portable package creation completed successfully!")
    else:
        print("\n❌ Failed to create portable package!")
    
    input("\nPress Enter to exit...")
