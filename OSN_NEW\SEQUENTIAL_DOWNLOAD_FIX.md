# 🔧 Sequential Download System Fix

## 🎯 **المشكلة المحددة:**
- التحميل المتعدد يبدأ جميع الحلقات في نفس الوقت
- الحلقات المختارة لا تظهر في تاب التحميل
- النظام لا يستخدم المتغيرات العالمية مثل OSN.py

## ✅ **الإصلاحات المطبقة:**

### **1. إضافة المتغيرات العالمية (مثل OSN.py):**
```python
# في __init__ method:
# Global variables for sequential download (like OSN.py)
self.saved_stream_id = None
self.saved_resolution = None
self.saved_uuid = None
self.selected_mpd_quality = None
self.has_shown_stream_table = False
self.has_shown_quality_table = False

# Sequential download queue variables
self.sequential_episodes_queue = []
self.sequential_download_settings = {}
self.is_multi_episode_mode = False
```

### **2. تحديث النظام المتتالي (مثل OSN.py):**
```python
def start_multi_episode_download_with_selections(self, selected_quality, selected_audio, selected_subtitles):
    """Start multi-episode download with user selections - SEQUENTIAL DOWNLOAD (like OSN.py)"""
    
    # Store global settings (like OSN.py)
    self.selected_mpd_quality = selected_quality['resolution'] if selected_quality else None
    self.saved_stream_id = self.current_detailed_info.get('stream_data', {}).get('streamId')
    
    # Process episodes sequentially (like OSN.py loop)
    self.process_episodes_sequentially(episodes, selected_quality, selected_audio, selected_subtitles)
```

### **3. معالجة متتالية (مثل OSN.py):**
```python
def process_episodes_sequentially(self, episodes, selected_quality, selected_audio, selected_subtitles):
    """Process episodes sequentially like OSN.py - download one by one"""
    
    # Process each episode one by one (like OSN.py loop)
    for i, episode in enumerate(episodes):
        episode_number = episode.get('episodeNumber', 'N/A')
        episode_title = episode.get('title', {}).get('en', 'No Title')
        
        print(f"📺 Processing Episode {i+1}/{len(episodes)}: {episode_number} - {episode_title}")
        
        # Add this episode to downloads table
        self.add_episode_to_downloads_table_with_selections(
            episode, selected_quality, selected_audio, selected_subtitles
        )
        
        # Start download for this episode immediately
        current_row = len(self.download_items) - 1  # Last added item
        self.start_individual_download(current_row)
        
        print(f"✅ Started download for Episode {episode_number}")
```

## 🔄 **كيف يعمل النظام الجديد:**

### **مقارنة مع OSN.py:**

#### **OSN.py الأصلي:**
```python
# في OSN.py - المتغيرات العالمية
saved_stream_id = None
saved_resolution = None
selected_mpd_quality = None

# في OSN.py - المعالجة المتتالية
for selected_episode in selected_episodes:
    episode_title = selected_episode["title"].get("en", "No Title")
    episode_number = selected_episode["episodeNumber"]
    
    # معالجة كل حلقة واحدة تلو الأخرى
    get_streams_table(...)
```

#### **OSN_NEW المحدث:**
```python
# في OSN_NEW - المتغيرات العالمية (نفس OSN.py)
self.saved_stream_id = None
self.saved_resolution = None
self.selected_mpd_quality = None

# في OSN_NEW - المعالجة المتتالية (نفس OSN.py)
for i, episode in enumerate(episodes):
    episode_number = episode.get('episodeNumber', 'N/A')
    
    # معالجة كل حلقة واحدة تلو الأخرى
    self.add_episode_to_downloads_table_with_selections(...)
    self.start_individual_download(current_row)
```

## 🎮 **كيفية الاختبار:**

### **خطوات الاختبار:**
1. **حدد حلقتين أو أكثر** من قائمة الحلقات
2. **اضغط "View Streams"** 
3. **اختر استريم** في تاب Available Streams
4. **كوّن الإعدادات** في تاب Download Options  
5. **اضغط "Download"**

### **النتيجة المتوقعة:**
```
🚀 Starting SEQUENTIAL multi-episode download (OSN.py style)...
📺 Episodes: 2
🔄 Processing 2 episodes sequentially (OSN.py style)...
📺 Processing Episode 1/2: 1 - Episode Title 1
✅ Started download for Episode 1
📺 Processing Episode 2/2: 2 - Episode Title 2
✅ Started download for Episode 2
```

### **في تاب Downloads:**
- ستظهر الحلقة الأولى وتبدأ التحميل
- ستظهر الحلقة الثانية وتبدأ التحميل
- كل حلقة لها صف منفصل في الجدول

## 🔍 **الفروق الرئيسية:**

### **قبل الإصلاح:**
❌ لا توجد متغيرات عالمية
❌ النظام يحاول بدء جميع التحميلات معاً
❌ الحلقات لا تظهر في جدول التحميل
❌ لا يتبع نمط OSN.py

### **بعد الإصلاح:**
✅ متغيرات عالمية مثل OSN.py
✅ معالجة متتالية مثل OSN.py
✅ كل حلقة تظهر في جدول التحميل
✅ يتبع نفس نمط OSN.py

## 🎉 **النتيجة النهائية:**

النظام الآن يعمل مثل OSN.py تماماً:
- **متغيرات عالمية** لحفظ الإعدادات
- **معالجة متتالية** للحلقات واحدة تلو الأخرى
- **إضافة فورية** للحلقات في جدول التحميل
- **بدء فوري** للتحميل لكل حلقة

🚀 **النظام جاهز للاختبار!**
