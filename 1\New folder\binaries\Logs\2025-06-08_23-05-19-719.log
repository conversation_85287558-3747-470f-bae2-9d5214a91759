﻿LOG 2025/06/08
Save Path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\Logs
Task Start: 2025/06/08 23:05:19
Task CommandLine: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\N_m3u8DL-RE.exe https://osn-video.anghcdn.co/public/154179-57503-PR681509-BX-AS045367-283435-a2bcb382d51e5bbfa901c5b763fa53c3-1749045009/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD01NzUwMyZleHBpcnk9MTc0OTQ1NjMxNSZzaWduYXR1cmU9MjliZDQ0ZjM0YTIxNGY1YjcyOGVlZGNkYzdjNzNkMDY1NjgzZmIxOSZzdHJlYW0taWQ9MTI4MDA5JnVzZXItaWQ9MTAzMDI2MDY5 -mt --select-video id=103ad727-5b86-4dd2-a06f-9a201b34bf4f --select-audio lang=ar --drop-subtitle .* --tmp-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache --save-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache --save-name "Al Mushardoon S01E28.360p.OSN+.WEB-DL.H264.AAC" --decryption-binary-path=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\mp4decrypt.exe --key-text-file=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\KEYS\OSNPLUS_KEYS.txt --log-level OFF

23:05:19.722 EXTRA: ffmpeg => C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\ffmpeg.exe
23:05:20.073 EXTRA: DropSubtitleFilter => For: best
23:05:20.073 EXTRA: VideoFilter => GroupIdReg: 103ad727-5b86-4dd2-a06f-9a201b34bf4f For: best
23:05:20.074 EXTRA: AudioFilter => LanguageReg: ar For: best
