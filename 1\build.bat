@echo off
echo ================================================================
echo   YANGO PLAY - EXE Builder
echo   Multi-Platform Streaming Application Builder
echo ================================================================
echo.

echo 🎵 Starting YANGO PLAY build process...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo ✅ Python is available
echo.

REM Check if we're in the right directory
if not exist "main.py" (
    echo ❌ main.py not found!
    echo Please run this script from the YANGO PLAY directory
    pause
    exit /b 1
)

echo ✅ Found main.py - we're in the right directory
echo.

REM Run the Python build script
echo 🚀 Running Python build script...
python build_exe.py

REM Check if build was successful
if errorlevel 1 (
    echo.
    echo ❌ Build failed! Check the error messages above.
    pause
    exit /b 1
)

echo.
echo ================================================================
echo   BUILD COMPLETED SUCCESSFULLY!
echo ================================================================
echo.
echo 🎉 YANGO PLAY executable has been created!
echo.
echo 📁 Output locations:
echo    • Executable: dist\YANGO_PLAY.exe
echo    • Package: YANGO_PLAY_Package\
echo.
echo 🚀 You can now run the executable or distribute the package folder.
echo.

REM Ask if user wants to run the executable
set /p choice="Do you want to run the executable now? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 🎵 Starting YANGO PLAY...
    start "" "dist\YANGO_PLAY.exe"
)

echo.
echo Press any key to exit...
pause >nul
