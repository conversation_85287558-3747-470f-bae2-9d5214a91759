from PySide6.QtCore import QO<PERSON>, Signal, Qt, QUrl
from PySide6.QtWidgets import (QMessageBox, QProgressBar, QLabel, QVBoxLayout, QHBoxLayout,
                               QPushButton, QComboBox, QListWidget, QTextEdit, QWidget,
                               QListWidgetItem, QTabWidget, QCheckBox, QGroupBox,
                               QTableWidget, QTableWidgetItem, QFrame, QGridLayout)
from PySide6.QtGui import QPixmap, QFont
from PySide6.QtMultimedia import QSoundEffect
import requests
import json
import os

class OSNUi(QObject):
    # Signals for communication with main window
    status_updated = Signal(str)
    progress_updated = Signal(int, str)
    content_displayed = Signal(dict)

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.widgets = main_window.ui
        # Initialize ALL variables to prevent any leftover data
        self.current_content = None
        self.current_qualities = []
        self.current_seasons = []
        self.current_series_info = {}
        self.current_episode_streams = []
        self.current_selected_episode = None
        self.selected_stream_data = None
        self.current_episodes_list = []
        self.current_season_ids = []
        self.setup_ui_connections()
        self.setup_content_tabs()

        # Connect API signals
        if hasattr(main_window, 'osn_api'):
            main_window.osn_api.error_occurred.connect(self.handle_api_error)
            main_window.osn_api.content_found.connect(self.handle_content_found)
            main_window.osn_api.login_status_changed.connect(self.handle_login_status)

    def setup_ui_connections(self):
        """Setup UI connections and initialize components"""
        # Connect search functionality from the existing UI
        if hasattr(self.widgets, 'search_button'):
            self.widgets.search_button.clicked.connect(self.handle_search)
            print("✅ Connected search_button")

        if hasattr(self.widgets, 'url_input'):
            # Connect return pressed for search
            self.widgets.url_input.returnPressed.connect(self.handle_search)
            # Add text changed signal for live filtering of recent combo
            self.widgets.url_input.textChanged.connect(self.handle_search_filter)
            # Add autocomplete functionality
            self.setup_main_search_autocomplete()
            print("✅ Found url_input")

        if hasattr(self.widgets, 'recent_combo'):
            # Make combo box editable first
            self.widgets.recent_combo.setEditable(True)
            self.widgets.recent_combo.setInsertPolicy(QComboBox.NoInsert)

            # Use activated signal only - this is triggered only when user clicks on an item
            self.widgets.recent_combo.activated.connect(self.handle_recent_selection_by_index)
            # Also connect to handle when user presses Enter in the editable combo
            self.widgets.recent_combo.lineEdit().returnPressed.connect(self.handle_combo_enter_pressed)
            # Connect text changed for live filtering
            self.widgets.recent_combo.lineEdit().textChanged.connect(self.handle_combo_search)
            # Track when user starts typing
            self.widgets.recent_combo.lineEdit().textEdited.connect(self.mark_user_typing)
            print("✅ Connected recent_combo")

        # Connect clear functionality
        if hasattr(self.widgets, 'clear_button'):
            self.widgets.clear_button.clicked.connect(self.handle_clear)
            print("✅ Connected clear_button")

        # Setup status updates
        self.status_updated.connect(self.update_status_bar)
        self.progress_updated.connect(self.update_progress_bar)

        print("✅ UI connections setup completed")

        # Setup enhanced search system first
        self.setup_enhanced_search()

        # Initialize flag to prevent auto-popup during startup
        self._app_startup = True

        # Load recent URLs from file
        self.load_recent_urls_from_file()

        # Clear startup flag after loading
        self._app_startup = False

        # Setup completion sound
        self.setup_completion_sound()

    def setup_completion_sound(self):
        """Setup sound effect for download completion notification"""
        try:
            # Initialize sound effect
            self.completion_sound = QSoundEffect()

            # Set the path to alert.wav in binaries folder
            sound_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "binaries", "alert.wav")
            sound_url = QUrl.fromLocalFile(sound_path)

            if os.path.exists(sound_path):
                self.completion_sound.setSource(sound_url)
                self.completion_sound.setVolume(0.7)  # Set volume to 70%
                print(f"✅ Download completion sound loaded: {sound_path}")
            else:
                print(f"⚠️ Sound file not found: {sound_path}")
                self.completion_sound = None

        except Exception as e:
            print(f"❌ Error setting up completion sound: {str(e)}")
            self.completion_sound = None

    def play_completion_sound(self):
        """Play download completion sound"""
        try:
            if self.completion_sound and self.completion_sound.isLoaded():
                self.completion_sound.play()
                print("🔊 Playing download completion sound")
            else:
                print("⚠️ Completion sound not available")
        except Exception as e:
            print(f"❌ Error playing completion sound: {str(e)}")

    def setup_enhanced_search(self):
        """Setup enhanced search system for filtering recent combo"""
        try:
            # Store original recent URLs for filtering
            self.original_recent_urls = []
            self.load_original_recent_urls()

            print("✅ Enhanced search system initialized")

        except Exception as e:
            print(f"❌ Error setting up enhanced search: {str(e)}")

    def load_original_recent_urls(self):
        """Load original recent URLs for filtering"""
        try:
            recent_file = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config", "osn_recent_urls.json")

            if os.path.exists(recent_file):
                with open(recent_file, 'r', encoding='utf-8') as f:
                    self.original_recent_urls = json.load(f)
                print(f"📂 Loaded {len(self.original_recent_urls)} recent URLs for filtering")
            else:
                self.original_recent_urls = []

        except Exception as e:
            print(f"❌ Error loading original recent URLs: {str(e)}")
            self.original_recent_urls = []

    def mark_user_typing(self, text):
        """Mark that user is actively typing"""
        self._user_is_typing = True

    def find_exact_match_in_saved(self, search_text):
        """Find exact match in saved items by ID or name"""
        try:
            if not hasattr(self, 'original_recent_urls') or not search_text:
                return None

            search_lower = search_text.lower().strip()

            for item in self.original_recent_urls:
                if not item or not item.strip():
                    continue

                # Check if it's an exact ID match
                if " - " in item:
                    item_id = item.split(" - ")[0].strip()
                    if item_id == search_text:
                        return item

                # Check if it's a name match (partial or full)
                item_lower = item.lower()
                if search_lower in item_lower:
                    # If the search text is substantial part of the name
                    if len(search_text) >= 3:
                        return item

            return None

        except Exception as e:
            print(f"❌ Error finding exact match: {str(e)}")
            return None

    def setup_main_search_autocomplete(self):
        """Setup autocomplete for main search bar"""
        try:
            from PySide6.QtWidgets import QCompleter
            from PySide6.QtCore import QStringListModel, Qt

            # Create completer
            self.search_completer = QCompleter()
            self.search_completer.setCaseSensitivity(Qt.CaseInsensitive)
            self.search_completer.setFilterMode(Qt.MatchContains)

            # Set the completer to the search input
            self.widgets.url_input.setCompleter(self.search_completer)

            # Update completer data
            self.update_search_autocomplete()

            print("✅ Setup main search autocomplete")

        except Exception as e:
            print(f"❌ Error setting up autocomplete: {str(e)}")

    def update_search_autocomplete(self):
        """Update autocomplete data with saved items"""
        try:
            if not hasattr(self, 'search_completer') or not hasattr(self, 'original_recent_urls'):
                return

            # Create list of suggestions (both IDs and names)
            suggestions = []

            for item in self.original_recent_urls:
                if not item or not item.strip():
                    continue

                # Add the full item
                suggestions.append(item)

                # Add just the ID if it's in the format "ID - Name (Year)"
                if " - " in item:
                    item_id = item.split(" - ")[0].strip()
                    if item_id not in suggestions:
                        suggestions.append(item_id)

                # Add just the name part
                if " - " in item and " (" in item:
                    name_part = item.split(" - ")[1].split(" (")[0].strip()
                    if name_part and name_part not in suggestions:
                        suggestions.append(name_part)

            # Update the completer model
            from PySide6.QtCore import QStringListModel
            model = QStringListModel(suggestions)
            self.search_completer.setModel(model)

            print(f"🔄 Updated autocomplete with {len(suggestions)} suggestions")

        except Exception as e:
            print(f"❌ Error updating autocomplete: {str(e)}")

    def handle_search_filter(self, text):
        """Filter recent combo box based on search text from main search bar"""
        try:
            if not hasattr(self.widgets, 'recent_combo'):
                return

            # If text is empty, show all items
            if not text.strip():
                self.populate_recent_combo(self.original_recent_urls)
                return

            # Filter items based on search text
            query_lower = text.lower()
            filtered_items = []

            for item in self.original_recent_urls:
                # Check if query matches ID or name (both English and Arabic)
                if query_lower in item.lower():
                    filtered_items.append(item)

            # Update combo box with filtered items
            self.populate_recent_combo(filtered_items)

            # If we found exact matches and user is typing, show suggestions
            if (filtered_items and len(text) >= 2 and
                not hasattr(self, '_loading_recent_data') and
                not hasattr(self, '_app_startup')):

                # Check if there's an exact match or close match
                exact_match = None
                for item in filtered_items:
                    # Extract ID from item (format: "ID - Name (Year)")
                    if " - " in item:
                        item_id = item.split(" - ")[0].strip()
                        if item_id == text or item.lower().startswith(text.lower()):
                            exact_match = item
                            break

                # If we have a close match, suggest it
                if exact_match and len(text) >= 3:
                    print(f"💡 Found suggestion for '{text}': {exact_match}")
                    # Update the recent combo to show the suggestion
                    self.widgets.recent_combo.setCurrentText(exact_match)

            print(f"🔍 Main search filtered to {len(filtered_items)} items for query: '{text}'")

        except Exception as e:
            print(f"❌ Error filtering search: {str(e)}")

    def populate_recent_combo(self, items):
        """Populate recent combo box with given items"""
        try:
            if not hasattr(self.widgets, 'recent_combo'):
                return

            # Clear current items
            self.widgets.recent_combo.clear()

            # Add placeholder
            self.widgets.recent_combo.addItem("Select from recent searches...")

            # Add filtered items (avoid duplicates and placeholder text)
            for item in items:
                if (item.strip() and
                    not item.startswith("Select from recent") and
                    self.widgets.recent_combo.findText(item) == -1):
                    self.widgets.recent_combo.addItem(item)

            # Make sure dropdown is closed during initialization or startup
            if (hasattr(self, '_loading_recent_data') or
                hasattr(self, '_app_startup') or
                not hasattr(self, '_user_is_typing')):
                self.widgets.recent_combo.hidePopup()

        except Exception as e:
            print(f"❌ Error populating combo: {str(e)}")

    def handle_combo_search(self, text):
        """Handle search within the combo box itself"""
        try:
            if not hasattr(self.widgets, 'recent_combo'):
                return

            # If text is empty, show all items
            if not text.strip():
                self.populate_combo_with_all_items()
                return

            # Filter items based on search text
            query_lower = text.lower()
            filtered_items = []

            for item in self.original_recent_urls:
                # Check if query matches ID or name (both English and Arabic)
                if query_lower in item.lower():
                    filtered_items.append(item)

            # Update combo box with filtered items
            self.populate_combo_filtered(filtered_items, text)

            print(f"🔍 Combo filtered to {len(filtered_items)} items for query: '{text}'")

        except Exception as e:
            print(f"❌ Error in combo search: {str(e)}")

    def populate_combo_filtered(self, items, current_text):
        """Populate combo box with filtered items while preserving current text"""
        try:
            if not hasattr(self.widgets, 'recent_combo'):
                return

            # Temporarily disconnect signals to avoid recursion
            self.widgets.recent_combo.lineEdit().textChanged.disconnect()

            # Clear and add filtered items
            self.widgets.recent_combo.clear()
            for item in items:
                self.widgets.recent_combo.addItem(item)

            # Restore the current text
            self.widgets.recent_combo.lineEdit().setText(current_text)

            # Reconnect the signal
            self.widgets.recent_combo.lineEdit().textChanged.connect(self.handle_combo_search)

            # Only show dropdown if user is actively typing
            if (items and
                not hasattr(self, '_loading_recent_data') and
                not hasattr(self, '_app_startup') and
                hasattr(self, '_user_is_typing')):
                self.widgets.recent_combo.showPopup()

        except Exception as e:
            print(f"❌ Error populating filtered combo: {str(e)}")

    def populate_combo_with_all_items(self):
        """Populate combo box with all original items"""
        try:
            if not hasattr(self.widgets, 'recent_combo'):
                return

            # Temporarily disconnect signals
            self.widgets.recent_combo.lineEdit().textChanged.disconnect()

            # Clear and add all items
            self.widgets.recent_combo.clear()
            for item in self.original_recent_urls:
                self.widgets.recent_combo.addItem(item)

            # Reconnect the signal
            self.widgets.recent_combo.lineEdit().textChanged.connect(self.handle_combo_search)

        except Exception as e:
            print(f"❌ Error populating combo with all items: {str(e)}")

    def handle_combo_enter_pressed(self):
        """Handle when user presses Enter in the combo box"""
        try:
            if not hasattr(self.widgets, 'recent_combo'):
                return

            # Get the current text from the combo box
            current_text = self.widgets.recent_combo.lineEdit().text().strip()

            if current_text:
                # Set this text in the main search input
                if hasattr(self.widgets, 'url_input'):
                    self.widgets.url_input.setText(current_text)

                # Trigger the search
                self.handle_search()

                print(f"🔍 Combo search executed: {current_text}")

        except Exception as e:
            print(f"❌ Error handling combo enter: {str(e)}")

    def setup_content_tabs(self):
        """Setup content tabs like Shahid design"""
        # Use existing content_tabs from UI if available
        if hasattr(self.widgets, 'content_tabs'):
            self.content_tabs = self.widgets.content_tabs
            print("✅ Using existing content_tabs from UI")
        else:
            # Create new content tabs widget
            self.content_tabs = QTabWidget()
            print("✅ Created new content_tabs widget")

        # Apply Shahid-like styling
        self.content_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #44475a;
                background-color: #282a36;
            }
            QTabBar::tab {
                background-color: #282a36;
                color: #f8f8f2;
                padding: 10px 20px;
                margin-right: 2px;
                border: 1px solid #44475a;
                border-bottom: none;
            }
            QTabBar::tab:selected {
                background-color: #44475a;
                border-bottom: 3px solid #00bcd4;
            }
            QTabBar::tab:!selected {
                margin-top: 2px;
            }
        """)

        # Clear existing tabs and add new ones
        self.content_tabs.clear()
        print("🔄 Cleared existing tabs")

        self.setup_content_info_tab()
        self.setup_seasons_tab()
        # Episodes will be shown in seasons tab - no separate episodes tab needed
        self.setup_available_streams_tab()
        self.setup_download_options_tab()
        self.setup_downloads_tab()

        # Keep tabs hidden initially - will show after search
        self.content_tabs.setVisible(False)
        self.content_tabs.setCurrentIndex(0)  # Show first tab when visible

        print("✅ All tabs setup completed (hidden until search)")

    def setup_content_info_tab(self):
        """Setup Content Info tab like Shahid"""
        self.content_info_tab = QWidget()
        self.content_info_layout = QVBoxLayout(self.content_info_tab)
        self.content_info_layout.setContentsMargins(10, 10, 10, 10)

        # Content info frame with horizontal layout (poster + details)
        self.content_info_frame = QFrame()
        self.content_info_frame.setFrameShape(QFrame.StyledPanel)
        self.content_info_frame.setFrameShadow(QFrame.Raised)
        self.content_info_frame.setStyleSheet("""
            QFrame {
                background-color: #282a36;
                border-radius: 8px;
                border: 1px solid #44475a;
            }
        """)
        self.content_info_frame_layout = QHBoxLayout(self.content_info_frame)
        self.content_info_frame_layout.setSpacing(20)
        self.content_info_frame_layout.setContentsMargins(20, 20, 20, 20)

        # Left side - Poster
        self.poster_label = QLabel()
        self.poster_label.setMinimumSize(240, 360)
        self.poster_label.setMaximumSize(240, 360)
        self.poster_label.setStyleSheet("""
            QLabel {
                background-color: #44475a;
                border-radius: 4px;
                border: 1px solid #6272a4;
            }
        """)
        self.poster_label.setAlignment(Qt.AlignCenter)
        self.poster_label.setScaledContents(True)
        self.content_info_frame_layout.addWidget(self.poster_label)

        # Right side - Content details
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout(self.details_widget)
        self.details_layout.setAlignment(Qt.AlignTop)
        self.details_layout.setSpacing(8)

        # Title
        self.title_label = QLabel()
        self.title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #ff79c6; margin-bottom: 10px;")
        self.details_layout.addWidget(self.title_label)

        # Type
        self.type_label = QLabel()
        self.type_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.details_layout.addWidget(self.type_label)

        # Year
        self.year_label = QLabel()
        self.year_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.details_layout.addWidget(self.year_label)

        # Episodes count
        self.episodes_count_label = QLabel()
        self.episodes_count_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.details_layout.addWidget(self.episodes_count_label)

        # Genres
        self.genres_label = QLabel()
        self.genres_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.genres_label.setWordWrap(True)
        self.details_layout.addWidget(self.genres_label)

        # Cast
        self.cast_label = QLabel()
        self.cast_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.cast_label.setWordWrap(True)
        self.details_layout.addWidget(self.cast_label)

        # Description
        self.description_text = QTextEdit()
        self.description_text.setReadOnly(True)
        self.description_text.setStyleSheet("""
            QTextEdit {
                background-color: transparent;
                border: none;
                color: #f8f8f2;
                font-size: 14px;
            }
        """)
        self.description_text.setMinimumHeight(100)
        self.details_layout.addWidget(self.description_text)

        self.content_info_frame_layout.addWidget(self.details_widget)
        self.content_info_layout.addWidget(self.content_info_frame)

        # Play and Continue buttons
        self.play_button = QPushButton("Play")
        self.play_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px;
                font-size: 14px;
                min-width: 120px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #a5d6a7;
                color: #e8f5e9;
            }
        """)
        self.play_button.setEnabled(False)
        self.play_button.clicked.connect(self.handle_play)

        self.continue_button = QPushButton("Continue")
        self.continue_button.setStyleSheet("""
            QPushButton {
                background-color: #00bcd4;
                color: white;
                padding: 10px;
                font-size: 14px;
                min-width: 120px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #00acc1;
            }
            QPushButton:pressed {
                background-color: #0097a7;
            }
            QPushButton:disabled {
                background-color: #80deea;
                color: #e0f7fa;
            }
        """)
        self.continue_button.setEnabled(False)
        self.continue_button.clicked.connect(self.handle_continue)

        self.content_info_button_layout = QHBoxLayout()
        self.content_info_button_layout.addStretch()
        self.content_info_button_layout.addWidget(self.play_button)
        self.content_info_button_layout.addWidget(self.continue_button)
        self.content_info_layout.addLayout(self.content_info_button_layout)

        self.content_tabs.addTab(self.content_info_tab, "Content Info")

    def setup_seasons_tab(self):
        """Setup Seasons tab"""
        self.seasons_tab = QWidget()
        self.seasons_layout = QVBoxLayout(self.seasons_tab)
        self.seasons_layout.setContentsMargins(10, 10, 10, 10)

        # Seasons label
        self.seasons_label = QLabel("Seasons:")
        self.seasons_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        self.seasons_layout.addWidget(self.seasons_label)

        # Seasons list
        self.seasons_list = QListWidget()
        self.seasons_list.setStyleSheet("""
            QListWidget {
                background-color: #282a36;
                border: 1px solid #44475a;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #44475a;
            }
            QListWidget::item:selected {
                background-color: #00bcd4;
                color: white;
            }
        """)
        self.seasons_layout.addWidget(self.seasons_list)

        self.content_tabs.addTab(self.seasons_tab, "Seasons")

    # Episodes will be shown in seasons tab - no separate episodes tab needed

    def setup_available_streams_tab(self):
        """Setup Available Streams tab"""
        self.available_streams_tab = QWidget()
        self.available_streams_layout = QVBoxLayout(self.available_streams_tab)
        self.available_streams_layout.setContentsMargins(10, 10, 10, 10)

        # Streams container with scroll area
        from PySide6.QtWidgets import QScrollArea, QFrame
        streams_scroll = QScrollArea()
        streams_scroll.setWidgetResizable(True)
        streams_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        streams_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Streams container widget
        self.streams_container = QWidget()
        self.streams_container_layout = QVBoxLayout(self.streams_container)
        self.streams_container_layout.setContentsMargins(10, 10, 10, 10)
        self.streams_container_layout.setSpacing(10)

        # Style the scroll area
        streams_scroll.setStyleSheet("""
            QScrollArea {
                background-color: #282a36;
                border: 1px solid #44475a;
                border-radius: 5px;
            }
            QScrollBar:vertical {
                background-color: #44475a;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #6272a4;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #00bcd4;
            }
        """)

        streams_scroll.setWidget(self.streams_container)
        self.available_streams_layout.addWidget(streams_scroll)

        # Initialize selected stream variable
        self.selected_stream_data = None

        self.content_tabs.addTab(self.available_streams_tab, "Available Streams")

    def setup_download_options_tab(self):
        """Setup Download Options tab"""
        self.download_options_tab = QWidget()
        self.download_options_layout = QVBoxLayout(self.download_options_tab)
        self.download_options_layout.setContentsMargins(10, 10, 10, 10)

        # Download Options Title
        self.download_options_label = QLabel("Download Options")
        self.download_options_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #ff79c6; margin-bottom: 10px;")
        self.download_options_layout.addWidget(self.download_options_label)

        # Batch Download Section (for series only)
        self.batch_download_group = QGroupBox("Batch Download Options")
        self.batch_download_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #f8f8f2;
                border: 2px solid #44475a;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        self.batch_download_layout = QVBoxLayout(self.batch_download_group)

        # Episode Range Selection
        self.episode_range_layout = QHBoxLayout()

        # Download All Episodes Checkbox
        self.download_all_episodes_cb = QCheckBox("Download All Episodes")
        self.download_all_episodes_cb.setStyleSheet("color: #f8f8f2; font-size: 14px;")
        self.download_all_episodes_cb.toggled.connect(self.toggle_episode_range)
        self.episode_range_layout.addWidget(self.download_all_episodes_cb)

        # Episode Range Inputs
        self.episode_range_label = QLabel("From Episode:")
        self.episode_range_label.setStyleSheet("color: #f8f8f2; font-size: 14px;")
        self.episode_range_layout.addWidget(self.episode_range_label)

        self.episode_from_input = QComboBox()
        self.episode_from_input.setStyleSheet("""
            QComboBox {
                background-color: #44475a;
                color: #f8f8f2;
                border: 1px solid #6272a4;
                border-radius: 4px;
                padding: 5px;
                min-width: 60px;
            }
        """)
        self.episode_range_layout.addWidget(self.episode_from_input)

        self.episode_to_label = QLabel("To Episode:")
        self.episode_to_label.setStyleSheet("color: #f8f8f2; font-size: 14px;")
        self.episode_range_layout.addWidget(self.episode_to_label)

        self.episode_to_input = QComboBox()
        self.episode_to_input.setStyleSheet("""
            QComboBox {
                background-color: #44475a;
                color: #f8f8f2;
                border: 1px solid #6272a4;
                border-radius: 4px;
                padding: 5px;
                min-width: 60px;
            }
        """)
        self.episode_range_layout.addWidget(self.episode_to_input)

        self.episode_range_layout.addStretch()
        self.batch_download_layout.addLayout(self.episode_range_layout)

        # Quality and Audio/Subtitle Selection for Batch
        self.batch_settings_layout = QHBoxLayout()

        # Quality Selection
        self.batch_quality_label = QLabel("Quality:")
        self.batch_quality_label.setStyleSheet("color: #f8f8f2; font-size: 14px;")
        self.batch_settings_layout.addWidget(self.batch_quality_label)

        self.batch_quality_combo = QComboBox()
        self.batch_quality_combo.setStyleSheet("""
            QComboBox {
                background-color: #44475a;
                color: #f8f8f2;
                border: 1px solid #6272a4;
                border-radius: 4px;
                padding: 5px;
                min-width: 100px;
            }
        """)
        self.batch_settings_layout.addWidget(self.batch_quality_combo)

        # Audio Selection
        self.batch_audio_label = QLabel("Audio:")
        self.batch_audio_label.setStyleSheet("color: #f8f8f2; font-size: 14px;")
        self.batch_settings_layout.addWidget(self.batch_audio_label)

        self.batch_audio_combo = QComboBox()
        self.batch_audio_combo.setStyleSheet("""
            QComboBox {
                background-color: #44475a;
                color: #f8f8f2;
                border: 1px solid #6272a4;
                border-radius: 4px;
                padding: 5px;
                min-width: 100px;
            }
        """)
        self.batch_settings_layout.addWidget(self.batch_audio_combo)

        # Subtitle Selection
        self.batch_subtitle_label = QLabel("Subtitles:")
        self.batch_subtitle_label.setStyleSheet("color: #f8f8f2; font-size: 14px;")
        self.batch_settings_layout.addWidget(self.batch_subtitle_label)

        self.batch_subtitle_combo = QComboBox()
        self.batch_subtitle_combo.setStyleSheet("""
            QComboBox {
                background-color: #44475a;
                color: #f8f8f2;
                border: 1px solid #6272a4;
                border-radius: 4px;
                padding: 5px;
                min-width: 100px;
            }
        """)
        self.batch_settings_layout.addWidget(self.batch_subtitle_combo)

        self.batch_settings_layout.addStretch()
        self.batch_download_layout.addLayout(self.batch_settings_layout)

        # Start Batch Download Button - HIDDEN to avoid confusion with sequential download
        self.start_batch_download_btn = QPushButton("Start Batch Download")
        self.start_batch_download_btn.setStyleSheet("""
            QPushButton {
                background-color: #50fa7b;
                color: #282a36;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #5af78e;
            }
            QPushButton:disabled {
                background-color: #6272a4;
                color: #44475a;
            }
        """)
        self.start_batch_download_btn.clicked.connect(self.start_batch_download)
        self.start_batch_download_btn.setEnabled(False)
        self.start_batch_download_btn.setVisible(False)  # HIDE to prevent confusion
        self.batch_download_layout.addWidget(self.start_batch_download_btn)

        # Hide batch download group initially (show only for series) - KEEP HIDDEN to prevent confusion with sequential download
        self.batch_download_group.setVisible(False)
        self.download_options_layout.addWidget(self.batch_download_group)

        # Add stretch to push content to top
        self.download_options_layout.addStretch()

        self.content_tabs.addTab(self.download_options_tab, "Download Options")

    def setup_downloads_tab(self):
        """Setup Downloads tab with compact modern design"""
        self.downloads_tab = QWidget()
        self.downloads_layout = QVBoxLayout(self.downloads_tab)
        self.downloads_layout.setContentsMargins(8, 8, 8, 8)
        self.downloads_layout.setSpacing(8)

        # Header section with controls
        header_widget = QWidget()
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(8)

        # Overall progress label
        self.overall_progress_label = QLabel("📥 Downloads: 0 active")
        self.overall_progress_label.setStyleSheet("""
            QLabel {
                color: #f8f8f2;
                font-size: 13px;
                font-weight: 600;
                padding: 6px 12px;
                background-color: #44475a;
                border-radius: 6px;
                border: 1px solid #6272a4;
            }
        """)

        # Control buttons - smaller and more compact
        self.start_download_btn = QPushButton("▶️ Start")
        self.start_download_btn.setStyleSheet("""
            QPushButton {
                background-color: #50fa7b;
                color: #282a36;
                border: none;
                padding: 6px 12px;
                border-radius: 5px;
                font-weight: 600;
                font-size: 11px;
                min-width: 70px;
                max-height: 28px;
            }
            QPushButton:hover {
                background-color: #5af78e;
                transform: translateY(-1px);
            }
            QPushButton:disabled {
                background-color: #6272a4;
                color: #44475a;
            }
        """)

        self.clear_completed_btn = QPushButton("🧹 Clear")
        self.clear_completed_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffb86c;
                color: #282a36;
                border: none;
                padding: 6px 12px;
                border-radius: 5px;
                font-weight: 600;
                font-size: 11px;
                min-width: 70px;
                max-height: 28px;
            }
            QPushButton:hover {
                background-color: #ffc78a;
                transform: translateY(-1px);
            }
        """)

        self.clear_all_btn = QPushButton("🗑️ All")
        self.clear_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff5555;
                color: #f8f8f2;
                border: none;
                padding: 6px 12px;
                border-radius: 5px;
                font-weight: 600;
                font-size: 11px;
                min-width: 60px;
                max-height: 28px;
            }
            QPushButton:hover {
                background-color: #ff6b6b;
                transform: translateY(-1px);
            }
        """)

        header_layout.addWidget(self.overall_progress_label)
        header_layout.addStretch()
        header_layout.addWidget(self.start_download_btn)
        header_layout.addWidget(self.clear_completed_btn)
        header_layout.addWidget(self.clear_all_btn)

        self.downloads_layout.addWidget(header_widget)

        # Downloads table with exact sizing like the image
        self.downloads_table = QTableWidget()
        self.downloads_table.setColumnCount(5)  # 5 columns as shown
        self.downloads_table.setHorizontalHeaderLabels([
            "Content", "S/E", "Quality", "Progress", "Status"
        ])

        # Set exact row height like the image (taller rows)
        self.downloads_table.verticalHeader().setDefaultSectionSize(50)
        self.downloads_table.verticalHeader().setVisible(False)

        # Styling to match the image exactly
        self.downloads_table.setStyleSheet("""
            QTableWidget {
                background-color: #44475a;
                color: #f8f8f2;
                border: 1px solid #6272a4;
                border-radius: 8px;
                gridline-color: #6272a4;
                font-size: 13px;
                font-weight: 500;
                selection-background-color: #6272a4;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid #6272a4;
                text-align: center;
            }
            QTableWidget::item:selected {
                background-color: #6272a4;
                color: #f8f8f2;
            }
            QHeaderView::section {
                background-color: #282a36;
                color: #f8f8f2;
                padding: 12px 8px;
                border: 1px solid #6272a4;
                font-weight: 600;
                font-size: 12px;
                text-align: center;
            }
        """)

        # Column widths to match the image proportions
        header = self.downloads_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.downloads_table.setColumnWidth(0, 350)  # Content - wider like image
        self.downloads_table.setColumnWidth(1, 80)   # S/E - medium width
        self.downloads_table.setColumnWidth(2, 100)  # Quality - medium width
        self.downloads_table.setColumnWidth(3, 200)  # Progress - wider for progress bar
        self.downloads_table.setColumnWidth(4, 120)  # Status - medium width

        self.downloads_layout.addWidget(self.downloads_table)

        # Connect button signals
        self.start_download_btn.clicked.connect(self.start_all_downloads)
        self.clear_completed_btn.clicked.connect(self.clear_completed_downloads)
        self.clear_all_btn.clicked.connect(self.clear_all_downloads)

        # Downloads list to track download items
        self.download_items = []

        self.content_tabs.addTab(self.downloads_tab, "Downloads")

        # Initialize batch download variables
        self.current_episodes_list_widget = None  # QListWidget for UI interactions
        self.current_episodes_data = []  # List of episode data
        self.current_season_data = None
        self.batch_download_settings = {
            'quality': None,
            'audio': None,
            'subtitle': None,
            'stream_data': None
        }
        self.batch_download_queue = []
        self.current_batch_index = 0
        self.is_batch_downloading = False

        # Multi-episode sequential download variables
        self.multi_episode_settings = None
        self.current_episode_download_index = 0
        self.is_sequential_downloading = False

    def clear_content_data(self):
        """Clear previous content data and reset UI state"""
        try:
            # Hide content tabs initially
            self.content_tabs.setVisible(False)

            # Reset to first tab (Content Info)
            self.content_tabs.setCurrentIndex(0)

            # Clear ALL content data variables
            self.current_content = None
            self.current_seasons = []
            self.current_series_info = {}
            self.current_episode_streams = []
            self.current_selected_episode = None
            self.selected_stream_data = None

            # Clear any cached episode data
            if hasattr(self, 'current_episodes_list'):
                self.current_episodes_list = []
            if hasattr(self, 'current_season_ids'):
                self.current_season_ids = []
            if hasattr(self, 'current_qualities'):
                self.current_qualities = []

            # Clear UI elements
            self.title_label.setText("Title")
            self.type_label.setText("Type")
            self.year_label.setText("Year")
            self.episodes_count_label.setText("")
            self.genres_label.setText("")
            self.cast_label.setText("")
            self.description_text.setPlainText("")
            self.poster_label.setText("Poster")

            # Clear lists
            if hasattr(self, 'seasons_list'):
                self.seasons_list.clear()
            if hasattr(self, 'episodes_list'):
                self.episodes_list.clear()

            # Remove episodes section if it exists
            if hasattr(self, 'episodes_section'):
                self.episodes_section.setParent(None)
                self.episodes_section.deleteLater()
                delattr(self, 'episodes_section')

            # Clear streams container
            self.clear_streams_container()

            # Clear streams table
            if hasattr(self, 'streams_table'):
                self.streams_table.setRowCount(0)
                self.selected_episode_label.setText("Select an episode to view available streams")
                self.select_stream_button.setEnabled(False)

            # Reset buttons
            if hasattr(self, 'play_button'):
                self.play_button.setEnabled(False)
            if hasattr(self, 'continue_button'):
                self.continue_button.setVisible(False)

            # Clear any search-related flags
            if hasattr(self, '_tried_series_fallback'):
                delattr(self, '_tried_series_fallback')
            if hasattr(self, '_tried_movie_fallback'):
                delattr(self, '_tried_movie_fallback')

            print("🧹 Complete content data cleared and UI reset")

            # Update status to inform user
            self.status_updated.emit("🧹 Content data cleared - Ready for new search")

        except Exception as e:
            print(f"❌ Error clearing content data: {str(e)}")

    def clear_streams_container(self):
        """Clear all stream cards from the streams container"""
        try:
            # Clear existing stream cards
            for i in reversed(range(self.streams_container_layout.count())):
                child = self.streams_container_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)
                    child.deleteLater()
            print("🧹 Streams container cleared")
        except Exception as e:
            print(f"❌ Error clearing streams container: {str(e)}")

    def handle_search(self):
        """Handle search button click - supports both URL and ID input"""
        try:
            # Get search input from input field
            search_input = self.widgets.url_input.text().strip()
            if not search_input:
                self.show_message("Error", "Please enter a valid OSN+ URL or content ID")
                return

            # Check if the input matches a saved item exactly
            exact_match = self.find_exact_match_in_saved(search_input)
            if exact_match:
                # Extract ID from the saved item
                if " - " in exact_match:
                    content_id = exact_match.split(" - ")[0].strip()
                    print(f"🎯 Found exact match in saved items: {exact_match}")
                    print(f"🔍 Using ID: {content_id}")
                    # Update the search input to use the ID
                    search_input = content_id
                    self.widgets.url_input.setText(content_id)



            # Clear previous content and reset UI state COMPLETELY
            self.clear_content_data()

            # Also clear any cached data and reset variables
            self.current_content = None
            self.current_seasons = []
            self.current_series_info = {}
            self.current_episode_streams = []
            self.current_selected_episode = None
            self.selected_stream_data = None

            # Clear any fallback flags from previous searches
            if hasattr(self, '_tried_series_fallback'):
                delattr(self, '_tried_series_fallback')
            if hasattr(self, '_tried_movie_fallback'):
                delattr(self, '_tried_movie_fallback')

            print("🧹 Complete data reset for new search")

            # Show loading state
            self.widgets.search_button.setText("Searching...")
            self.widgets.search_button.setEnabled(False)

            # Determine if input is URL or ID and process accordingly
            content_id, content_type = self.parse_search_input(search_input)

            if not content_id or not content_type:
                self.show_message("Error", "Invalid input. Please enter a valid OSN+ URL or content ID")
                return

            print(f"🔍 Searching for {content_type} with ID: {content_id}")

            # Store the original input for later use
            self.current_search_input = search_input
            self.current_content_id = content_id

            # Reset fallback flags for new search
            if hasattr(self, '_tried_series_fallback'):
                delattr(self, '_tried_series_fallback')
            if hasattr(self, '_tried_movie_fallback'):
                delattr(self, '_tried_movie_fallback')

            # Search based on content type
            if content_type == "movie":
                print(f"🎬 Searching for movie with ID: {content_id}")
                self.search_movie(content_id)
            elif content_type == "series":
                print(f"📺 Searching for series with ID: {content_id}")
                self.search_series(content_id)
            elif content_type == "unknown":
                # ID-only input - check content type first to avoid errors
                print(f"🔄 ID-only input, checking content type for ID: {content_id}")
                self.check_and_search_content(content_id)
            else:
                # If we can't determine the type, try movie first (more common)
                print(f"🔄 Unknown content type, trying movie first for ID: {content_id}")
                self.search_movie(content_id)  # Try movie first, will fallback to series if needed

        except Exception as e:
            self.show_message("Error", f"Search failed: {str(e)}")
        finally:
            self.widgets.search_button.setText("Search")
            self.widgets.search_button.setEnabled(True)

    def parse_search_input(self, search_input):
        """Parse search input to determine content ID and type"""
        try:
            # Check if input is a URL
            if "osnplus.com" in search_input:
                print(f"🔍 Parsing URL: {search_input}")

                # Extract content type and ID from URL
                if "/movie/" in search_input or "/movies/" in search_input:
                    # Handle different URL formats:
                    # https://osnplus.com/en-ae/movies/1035538-al-abqari
                    # https://osnplus.com/en-ae/movie/1035538-al-abqari
                    # https://osnplus.com/en-eg/movie/the-fall-guy-43820

                    # Split by '/' and find the last part that contains the ID
                    parts = search_input.split("/")
                    last_part = parts[-1] if parts else ""

                    # Extract ID from the last part
                    content_id = None
                    if "-" in last_part:
                        # Format: name-id or id-name
                        segments = last_part.split("-")
                        for segment in segments:
                            if segment.isdigit():
                                content_id = segment
                                break
                    elif last_part.isdigit():
                        # Format: just ID
                        content_id = last_part

                    if content_id:
                        print(f"✅ Extracted movie ID: {content_id}")
                        return content_id, "movie"
                    else:
                        print(f"❌ Could not extract movie ID from: {last_part}")
                        return None, None

                elif "/series/" in search_input or "/shows/" in search_input:
                    # Handle series URLs similarly
                    parts = search_input.split("/")
                    last_part = parts[-1] if parts else ""

                    # Extract ID from the last part
                    content_id = None
                    if "-" in last_part:
                        # Format: name-id or id-name
                        segments = last_part.split("-")
                        for segment in segments:
                            if segment.isdigit():
                                content_id = segment
                                break
                    elif last_part.isdigit():
                        # Format: just ID
                        content_id = last_part

                    if content_id:
                        print(f"✅ Extracted series ID: {content_id}")
                        return content_id, "series"
                    else:
                        print(f"❌ Could not extract series ID from: {last_part}")
                        return None, None
                else:
                    print(f"❌ Unknown URL format: {search_input}")
                    return None, None
            else:
                # Input is likely just an ID - we need to determine if it's movie or series
                # For now, we'll try both and see which one works
                if search_input.isdigit():
                    print(f"🔍 Input is ID: {search_input}")
                    # Return "unknown" type so the system will try movie first, then series automatically
                    return search_input, "unknown"  # Will trigger fallback logic
                else:
                    print(f"❌ Invalid input format: {search_input}")
                    return None, None

        except Exception as e:
            print(f"❌ Error parsing search input: {str(e)}")
            return None, None

    def check_and_search_content(self, content_id):
        """Check content type first, then search accordingly"""
        try:
            print(f"🔍 Checking content type for ID: {content_id}")
            self.status_updated.emit(f"Checking content type for: {content_id}")

            # Use the API's check_content_type method
            content_type = self.main_window.osn_api.check_content_type(content_id)

            if content_type == "movie":
                print(f"✅ Confirmed as movie, searching...")
                self.search_movie(content_id)
            elif content_type == "series":
                print(f"✅ Confirmed as series, searching...")
                self.search_series(content_id)
            else:
                print(f"❌ Content type could not be determined")
                self.show_message("Error", f"Content with ID {content_id} not found. Please check the ID and try again.")

        except Exception as e:
            print(f"❌ Error checking content type: {str(e)}")
            # Fallback to old method if check fails
            print(f"🔄 Falling back to movie search first")
            self.search_movie(content_id)

    def search_movie(self, movie_id):
        """Search for movie details using movie ID"""
        try:
            print(f"🎬 Searching for movie with ID: {movie_id}")
            self.status_updated.emit(f"Searching for movie: {movie_id}")

            # Store current search info for fallback
            self.current_content_id = movie_id
            self.current_search_input = movie_id

            # Clear previous data only if this is not a fallback search
            if not hasattr(self, '_tried_movie_fallback'):
                self.clear_content_data()

            # Start movie search - use silent_fallback=True for ID-only searches to avoid showing error messages
            # The fallback logic will handle trying series if movie fails
            is_id_only_search = (hasattr(self, 'current_search_input') and
                               self.current_search_input and
                               self.current_search_input.isdigit())

            # Always use silent fallback for ID-only searches or when this is a fallback attempt
            silent_fallback = is_id_only_search or hasattr(self, '_tried_movie_fallback')

            print(f"🔍 Movie search - ID only: {is_id_only_search}, Silent fallback: {silent_fallback}")
            self.main_window.osn_api.get_movie_details(movie_id, silent_fallback=silent_fallback)

        except Exception as e:
            print(f"❌ Error in search_movie: {str(e)}")
            self.show_message("Error", f"Failed to search movie: {str(e)}")

    def search_series(self, series_id):
        """Search for series details using series ID"""
        try:
            print(f"📺 Searching for series with ID: {series_id}")
            self.status_updated.emit(f"Searching for series: {series_id}")

            # Store current search info for fallback
            self.current_content_id = series_id
            self.current_search_input = series_id

            # Clear previous data only if this is not a fallback search
            if not hasattr(self, '_tried_series_fallback'):
                self.clear_content_data()

            # Start series search - use silent_fallback if this is a fallback attempt
            is_fallback = hasattr(self, '_tried_series_fallback')
            self.main_window.osn_api.get_series_details(series_id, silent_fallback=is_fallback)

        except Exception as e:
            print(f"❌ Error in search_series: {str(e)}")
            self.show_message("Error", f"Failed to search series: {str(e)}")

    def display_movie_info(self, movie_data, movie_id):
        """Display movie information in UI like series - show poster and data first"""
        try:
            # Extract movie details
            title = movie_data.get('title', {}).get('en', 'Unknown Title')
            year = movie_data.get('year', 'Unknown Year')
            imdb_rating = movie_data.get('imdbRating', {}).get('rating', 'N/A')

            # Fix genres extraction - they are dict objects with name.en
            genres_raw = movie_data.get('genres', [])
            genres = []
            for genre in genres_raw:
                if isinstance(genre, dict):
                    genre_name = genre.get('name', {}).get('en', 'Unknown Genre')
                    genres.append(genre_name)
                else:
                    genres.append(str(genre))

            # Fix cast extraction - get from credits.actors
            cast_raw = movie_data.get('credits', {}).get('actors', [])
            cast = []
            for actor in cast_raw:
                if isinstance(actor, dict):
                    actor_name = actor.get('fullName', {}).get('en', 'Unknown Actor')
                    cast.append(actor_name)
                else:
                    cast.append(str(actor))

            description = movie_data.get('description', {}).get('en', '')

            # Update title
            self.title_label.setText(title)

            # Update type
            self.type_label.setText("Type: MOVIE")

            # Update year
            self.year_label.setText(f"Year: {year}")

            # Update IMDb rating if available
            if imdb_rating != 'N/A':
                self.episodes_count_label.setText(f"IMDb Rating: {imdb_rating}")
            else:
                self.episodes_count_label.setText("")

            # Update genres
            if genres:
                self.genres_label.setText(f"Genres: {', '.join(genres)}")
            else:
                self.genres_label.setText("")

            # Update cast
            if cast:
                self.cast_label.setText(f"Cast: {', '.join(cast[:5])}")  # Show first 5 cast members
            else:
                self.cast_label.setText("")

            # Update description
            if description:
                self.description_text.setPlainText(description)
            else:
                self.description_text.setPlainText("")

            # Load poster if available
            poster_url = movie_data.get('images', {}).get('longImageWithTitleUrl')
            if poster_url:
                self.load_poster(poster_url)

            # Store current content with streams for later use
            self.current_content = {
                'type': 'movie',
                'data': movie_data,
                'id': movie_id,
                'streams': movie_data.get('streams', [])  # Store streams for later
            }

            # Show the content tabs now that we have data
            self.content_tabs.setVisible(True)
            print("✅ Content tabs are now visible")

            # Hide batch download options for movies
            self.hide_batch_download_options()

            # For movies: Show in seasons tab as a single item like series
            self.setup_movie_as_season(title, movie_data)

            # Show and enable continue button for movies (like series)
            self.continue_button.setVisible(True)
            self.continue_button.setText("🎬 Load Movie Streams")
            self.continue_button.setEnabled(True)

            # Add to recent URLs with content info (always save, regardless of input type)
            if hasattr(self, 'current_search_input'):
                content_info = f"{movie_id} - {title} ({year})"
                self.add_to_recent_urls(content_info)
                print(f"✅ Added to recent: {content_info}")

            self.status_updated.emit("Movie information loaded successfully")

        except Exception as e:
            self.show_message("Error", f"Failed to display movie info: {str(e)}")

    def setup_movie_as_season(self, movie_title, movie_data):
        """Setup movie in seasons tab like a series with one season"""
        try:
            # Clear seasons list
            self.seasons_list.clear()

            # Add movie as a single "season" item
            item = QListWidgetItem(f"🎬 {movie_title}")
            item.setData(Qt.UserRole, {
                'type': 'movie',
                'title': movie_title,
                'data': movie_data,
                'streams': movie_data.get('streams', [])
            })

            # Style the item to look like a movie
            item.setToolTip(f"Click to view available streams for {movie_title}")

            self.seasons_list.addItem(item)

            # Connect selection to show streams directly
            self.seasons_list.itemClicked.connect(self.handle_movie_selection)

            print(f"✅ Added movie '{movie_title}' to seasons list")

        except Exception as e:
            print(f"❌ Error setting up movie as season: {str(e)}")

    def handle_movie_selection(self, item):
        """Handle when movie item is clicked in seasons list"""
        try:
            item_data = item.data(Qt.UserRole)
            if item_data and item_data.get('type') == 'movie':
                movie_title = item_data.get('title')
                streams = item_data.get('streams', [])

                print(f"🎬 Movie '{movie_title}' selected, showing {len(streams)} streams")

                if streams:
                    # Display movie streams directly
                    self.display_movie_streams(streams, movie_title)
                    # Switch to Available Streams tab
                    self.content_tabs.setCurrentIndex(2)  # Available Streams tab
                    print("✅ Switched to Available Streams tab for movie")
                else:
                    self.show_message("Error", "No streams available for this movie")

        except Exception as e:
            print(f"❌ Error handling movie selection: {str(e)}")
            self.show_message("Error", f"Failed to load movie streams: {str(e)}")

    def display_movie_streams(self, streams, movie_title):
        """Display movie streams in Available Streams tab"""
        try:
            print(f"🎬 Displaying {len(streams)} streams for movie: {movie_title}")

            # Clear existing streams
            self.clear_streams_container()

            # Filter duplicate streams (keep only DASH streams, remove HLS duplicates)
            unique_streams = self.filter_duplicate_streams(streams)
            print(f"🔧 Filtered from {len(streams)} to {len(unique_streams)} unique streams")

            # Create stream cards for each unique stream
            for i, stream in enumerate(unique_streams):
                stream_card = self.create_stream_card(stream, i + 1)
                self.streams_container_layout.addWidget(stream_card)

            # Add stretch to push cards to top
            self.streams_container_layout.addStretch()

            print(f"✅ Displayed {len(unique_streams)} stream cards for movie")

        except Exception as e:
            print(f"❌ Error displaying movie streams: {str(e)}")
            self.show_message("Error", f"Failed to display movie streams: {str(e)}")

    def filter_duplicate_streams(self, streams):
        """Filter duplicate streams - keep only DASH streams, remove HLS duplicates"""
        try:
            unique_streams = []
            seen_qualities = set()

            # Sort streams to prioritize DASH over HLS
            sorted_streams = sorted(streams, key=lambda x: (
                x.get('highestImageResolution', ''),
                x.get('isHdr', False),
                x.get('isDolbyVision', False),
                0 if x.get('manifestType') == 'MANIFEST_TYPE_DASH' else 1  # DASH first
            ))

            for stream in sorted_streams:
                # Create a unique key based on quality characteristics
                resolution = stream.get('highestImageResolution', 'UNKNOWN')
                is_hdr = stream.get('isHdr', False)
                is_dolby_vision = stream.get('isDolbyVision', False)

                # Create quality key
                quality_key = (resolution, is_hdr, is_dolby_vision)

                # Only add if we haven't seen this quality combination before
                if quality_key not in seen_qualities:
                    seen_qualities.add(quality_key)
                    unique_streams.append(stream)

                    # Debug info
                    manifest_type = stream.get('manifestType', 'UNKNOWN')
                    print(f"🎯 Keeping stream: {resolution} HDR:{is_hdr} DV:{is_dolby_vision} Type:{manifest_type}")
                else:
                    # Debug info for skipped streams
                    manifest_type = stream.get('manifestType', 'UNKNOWN')
                    print(f"⏭️ Skipping duplicate: {resolution} HDR:{is_hdr} DV:{is_dolby_vision} Type:{manifest_type}")

            return unique_streams

        except Exception as e:
            print(f"❌ Error filtering duplicate streams: {str(e)}")
            return streams  # Return original streams if filtering fails

    def display_series_info(self, series_data, series_id):
        """Display series information in UI using new Shahid-like design"""
        try:
            # DEBUG: Print received data structure
            print("\n" + "="*100)
            print("🔍 RECEIVED DATA IN display_series_info:")
            print("="*100)
            import json
            print(json.dumps(series_data, indent=2, ensure_ascii=False))
            print("="*100)

            # Extract series details from the correct structure
            series_info = series_data.get('series', {})
            print(f"📺 Series info extracted: {bool(series_info)}")

            title = series_info.get('title', {}).get('en', 'Unknown Series')
            year = series_info.get('year', 'Unknown Year')

            # Fix genres extraction - they are dict objects, not strings
            genres_raw = series_info.get('genres', [])
            genres = []
            for genre in genres_raw:
                if isinstance(genre, dict):
                    genre_name = genre.get('name', {}).get('en', 'Unknown Genre')
                    genres.append(genre_name)
                else:
                    genres.append(str(genre))

            # Fix cast extraction - get from credits.actors
            cast_raw = series_info.get('credits', {}).get('actors', [])
            cast = []
            for actor in cast_raw[:5]:  # First 5 actors
                if isinstance(actor, dict):
                    actor_name = actor.get('fullName', {}).get('en', 'Unknown Actor')
                    cast.append(actor_name)
                else:
                    cast.append(str(actor))

            description = series_info.get('description', {}).get('en', '')
            seasons = series_info.get('seasons', [])

            print(f"📺 Extracted data:")
            print(f"  - Title: {title}")
            print(f"  - Year: {year}")
            print(f"  - Seasons count: {len(seasons)}")
            print(f"  - Genres: {genres}")
            print("="*50)

            # Calculate total episodes
            total_episodes = 0
            for season in seasons:
                total_episodes += season.get('episodesCount', 0)

            # Update title
            self.title_label.setText(title)

            # Update type
            self.type_label.setText("Type: SERIES")

            # Update year
            self.year_label.setText(f"Year: {year}")

            # Update episodes count
            self.episodes_count_label.setText(f"Seasons: {len(seasons)} | Episodes: {total_episodes}")

            # Update genres
            if genres:
                self.genres_label.setText(f"Genres: {', '.join(genres)}")
            else:
                self.genres_label.setText("")

            # Update cast
            if cast:
                self.cast_label.setText(f"Cast: {', '.join(cast[:5])}")  # Show first 5 cast members
            else:
                self.cast_label.setText("")

            # Update description
            if description:
                self.description_text.setPlainText(description)
            else:
                self.description_text.setPlainText("")

            # Load poster if available
            poster_url = series_info.get('images', {}).get('longImageWithTitleUrl')
            if poster_url:
                self.load_poster(poster_url)

            # Show the content tabs now that we have data
            self.content_tabs.setVisible(True)
            print("✅ Content tabs are now visible")

            # Store seasons data but DON'T display them yet
            self.current_seasons = seasons
            self.current_series_info = series_info

            # Show Continue button to load seasons
            self.continue_button.setVisible(True)
            self.continue_button.setText("🎬 Load Seasons & Episodes")
            self.continue_button.setEnabled(True)

            print(f"\n✅ Series info loaded. Found {len(seasons)} seasons.")
            print("📌 Click 'Load Seasons & Episodes' button to continue.")

            # Store current content
            self.current_content = {
                'type': 'series',
                'data': series_data,
                'id': series_id
            }

            # Add to recent URLs with content info (always save, regardless of input type)
            if hasattr(self, 'current_search_input'):
                content_info = f"{series_id} - {title} ({year})"
                self.add_to_recent_urls(content_info)
                print(f"✅ Added to recent: {content_info}")

            self.status_updated.emit("Series information loaded successfully")

        except Exception as e:
            self.show_message("Error", f"Failed to display series info: {str(e)}")

    def populate_quality_options(self, streams):
        """Populate quality dropdown with available streams"""
        try:
            if not hasattr(self.widgets, 'quality_combo'):
                return

            self.widgets.quality_combo.clear()
            self.current_qualities = []

            for stream in streams:
                if stream.get('manifestType') == 'MANIFEST_TYPE_DASH':
                    resolution = stream.get('highestImageResolution', 'Unknown')
                    stream_id = stream.get('streamId', '')

                    display_text = f"{resolution} - {stream_id}"
                    self.widgets.quality_combo.addItem(display_text)
                    self.current_qualities.append(stream)

        except Exception as e:
            self.show_message("Error", f"Failed to populate quality options: {str(e)}")

    def populate_season_options(self, seasons):
        """Populate season dropdown"""
        try:
            if not hasattr(self.widgets, 'season_combo'):
                return

            self.widgets.season_combo.clear()

            for season in seasons:
                season_number = season.get('seasonNumber', 1)
                episode_count = len(season.get('episodes', []))

                display_text = f"Season {season_number} ({episode_count} episodes)"
                self.widgets.season_combo.addItem(display_text)

        except Exception as e:
            self.show_message("Error", f"Failed to populate season options: {str(e)}")

    def load_poster(self, poster_url):
        """Load and display poster image"""
        try:
            if not poster_url:
                print("❌ No poster URL provided")
                self.poster_label.setText("Poster\nNot Available")
                return

            print(f"🔄 Loading poster from: {poster_url}")

            # Download poster with timeout and headers
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            response = requests.get(poster_url, headers=headers, timeout=10)
            response.raise_for_status()

            # Create pixmap and set to label
            pixmap = QPixmap()
            pixmap.loadFromData(response.content)

            if not pixmap.isNull():
                # Scale to fit label - Fix PySide6 syntax
                scaled_pixmap = pixmap.scaled(
                    240, 360,  # Use the label's fixed size
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )

                self.poster_label.setPixmap(scaled_pixmap)
                print(f"✅ Poster loaded successfully")
            else:
                print("❌ Failed to create pixmap from image data")
                self.poster_label.setText("Poster\nNot Available")

        except Exception as e:
            print(f"❌ Failed to load poster: {str(e)}")
            self.poster_label.setText("Poster\nNot Available")

    def handle_download(self):
        """Handle download button click"""
        try:
            if not self.current_content:
                self.show_message("Error", "No content selected for download")
                return

            # Get selected quality
            if hasattr(self.widgets, 'quality_combo'):
                selected_index = self.widgets.quality_combo.currentIndex()
                if selected_index >= 0 and selected_index < len(self.current_qualities):
                    selected_stream = self.current_qualities[selected_index]
                else:
                    self.show_message("Error", "Please select a quality")
                    return
            else:
                self.show_message("Error", "Quality selection not available")
                return

            # Start download based on content type
            if self.current_content['type'] == 'movie':
                self.start_movie_download(selected_stream)
            elif self.current_content['type'] == 'series':
                self.start_series_download(selected_stream)

        except Exception as e:
            self.show_message("Error", f"Download failed: {str(e)}")

    def handle_continue(self):
        """Handle continue button click - Load seasons/episodes for series or streams for movies"""
        try:
            # Check if this is a movie
            if hasattr(self, 'current_content') and self.current_content:
                content_type = self.current_content.get('type')

                if content_type == 'movie':
                    print(f"\n🎬 Loading movie streams...")

                    # Get movie streams
                    movie_data = self.current_content.get('data', {})
                    streams = movie_data.get('streams', [])
                    movie_title = movie_data.get('title', {}).get('en', 'Unknown Movie')

                    if streams:
                        # Display movie streams directly
                        self.display_movie_streams(streams, movie_title)
                        # Switch to Available Streams tab
                        self.content_tabs.setCurrentIndex(2)  # Available Streams tab
                        print("✅ Switched to Available Streams tab for movie")
                    else:
                        self.show_message("Error", "No streams available for this movie")

                    # Hide continue button
                    self.continue_button.setVisible(False)

                elif content_type == 'series':
                    # Handle series - load seasons and episodes
                    if hasattr(self, 'current_seasons') and self.current_seasons:
                        print(f"\n🎬 Loading seasons and episodes data...")

                        # Populate seasons list in the Seasons tab
                        self.populate_seasons_list(self.current_seasons)

                        # Switch to Seasons tab
                        self.content_tabs.setCurrentWidget(self.seasons_tab)

                        # Hide continue button
                        self.continue_button.setVisible(False)

                        print(f"✅ Seasons loaded successfully!")
                        self.status_updated.emit("Seasons loaded. Select a season to view episodes.")

                    else:
                        self.show_message("Warning", "No seasons data available")
                else:
                    self.show_message("Warning", "Unknown content type")
            else:
                self.show_message("Warning", "No content data available")

        except Exception as e:
            print(f"❌ Error in handle_continue: {str(e)}")
            self.show_message("Error", f"Failed to continue: {str(e)}")

    def populate_seasons_list(self, seasons):
        """Populate seasons list in the Seasons tab"""
        try:
            self.seasons_list.clear()

            for season in seasons:
                season_number = season.get('seasonNumber', 1)
                episode_count = season.get('episodesCount', 0)

                display_text = f"Season {season_number} ({episode_count} episodes)"
                self.seasons_list.addItem(display_text)

            # Connect season selection
            self.seasons_list.itemClicked.connect(self.handle_season_selection)

        except Exception as e:
            self.show_message("Error", f"Failed to populate seasons list: {str(e)}")

    def handle_season_selection(self, item):
        """Handle season selection - Load episodes in the same tab"""
        try:
            season_index = self.seasons_list.row(item)
            if season_index < len(self.current_seasons):
                selected_season = self.current_seasons[season_index]
                season_id = selected_season.get('contentId')
                season_number = selected_season.get('seasonNumber', 1)

                print(f"🎬 Loading episodes for Season {season_number}...")

                # Call API to get episodes for this season
                if hasattr(self.main_window, 'osn_api'):
                    self.main_window.osn_api.get_episodes_by_season(
                        season_id,
                        self.current_series_info.get('title', {}).get('en', 'Unknown'),
                        season_number
                    )

        except Exception as e:
            self.show_message("Error", f"Failed to handle season selection: {str(e)}")

    def get_season_episodes(self, season):
        """Get episodes for selected season"""
        try:
            season_id = season.get('id')
            season_number = season.get('seasonNumber', 1)

            print(f"🎬 Getting episodes for Season {season_number}...")

            # This would call the API to get episodes
            # For now, we'll use the episodes data if available
            episodes = season.get('episodes', [])

            if episodes:
                # Add episodes to the same seasons tab
                self.add_episodes_to_seasons_tab(episodes, season_number)
            else:
                # Call API to get episodes
                if hasattr(self.main_window, 'osn_api'):
                    self.main_window.osn_api.get_episodes_by_season(
                        season_id,
                        self.current_series_info.get('title', {}).get('en', 'Unknown'),
                        season_number
                    )

        except Exception as e:
            self.show_message("Error", f"Failed to get season episodes: {str(e)}")

    def populate_episodes_list(self, episodes, season_number):
        """Populate episodes list in the Episodes tab"""
        try:
            self.episodes_list.clear()

            for i, episode in enumerate(episodes, 1):
                episode_title = episode.get('title', {}).get('en', f'Episode {i}')
                episode_number = episode.get('episodeNumber', i)

                display_text = f"Episode {episode_number}: {episode_title}"
                self.episodes_list.addItem(display_text)

            # Update range spinboxes
            self.range_from.setMaximum(len(episodes))
            self.range_to.setMaximum(len(episodes))
            self.range_to.setValue(len(episodes))

            # Connect episode controls
            self.select_all_button.clicked.connect(self.select_all_episodes)
            self.select_none_button.clicked.connect(self.select_no_episodes)
            self.select_range_button.clicked.connect(self.select_episode_range)

            print(f"✅ Episodes loaded for Season {season_number}")

        except Exception as e:
            self.show_message("Error", f"Failed to populate episodes list: {str(e)}")

    def select_all_episodes(self):
        """Select all episodes"""
        for i in range(self.episodes_list.count()):
            self.episodes_list.item(i).setSelected(True)

    def select_no_episodes(self):
        """Deselect all episodes"""
        self.episodes_list.clearSelection()

    def select_episode_range(self):
        """Select episode range"""
        try:
            start = self.range_from.value() - 1  # Convert to 0-based index
            end = self.range_to.value()  # End is exclusive

            self.episodes_list.clearSelection()
            for i in range(start, min(end, self.episodes_list.count())):
                self.episodes_list.item(i).setSelected(True)

        except Exception as e:
            self.show_message("Error", f"Failed to select episode range: {str(e)}")

    def display_episodes_list(self, episodes_data):
        """Display episodes list in the same Seasons tab"""
        try:
            series_title = episodes_data.get('series_title')
            season_number = episodes_data.get('season_number')
            episodes = episodes_data.get('episodes', [])

            print(f"🎬 Displaying episodes for {series_title} Season {season_number}")

            # Add episodes section to the seasons tab
            self.add_episodes_to_seasons_tab(episodes, season_number)

        except Exception as e:
            self.show_message("Error", f"Failed to display episodes list: {str(e)}")

    def add_episodes_to_seasons_tab(self, episodes, season_number):
        """Add episodes list to the seasons tab with selection controls"""
        try:
            # Remove existing episodes section if it exists
            if hasattr(self, 'episodes_section'):
                self.episodes_section.setParent(None)
                self.episodes_section.deleteLater()

            # Create episodes section
            self.episodes_section = QWidget()
            episodes_layout = QVBoxLayout(self.episodes_section)
            episodes_layout.setContentsMargins(0, 10, 0, 0)

            # Episodes label
            episodes_label = QLabel(f"Episodes (Season {season_number}):")
            episodes_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #ff79c6;")
            episodes_layout.addWidget(episodes_label)

            # Episode controls
            controls_layout = QHBoxLayout()

            # Select All button
            select_all_button = QPushButton("Select All")
            select_all_button.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    padding: 6px 12px;
                    border: none;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)

            # Select None button
            select_none_button = QPushButton("Select None")
            select_none_button.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    padding: 6px 12px;
                    border: none;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)

            # Range selection
            range_label = QLabel("Select Range:")
            range_label.setStyleSheet("color: #f8f8f2; font-size: 12px;")

            from PySide6.QtWidgets import QSpinBox
            range_from = QSpinBox()
            range_from.setMinimum(1)
            range_from.setMaximum(len(episodes))
            range_from.setValue(1)
            range_from.setStyleSheet("""
                QSpinBox {
                    padding: 4px;
                    border: 1px solid #44475a;
                    border-radius: 3px;
                    background-color: #282a36;
                    color: #f8f8f2;
                    font-size: 12px;
                    width: 60px;
                }
            """)

            to_label = QLabel("to")
            to_label.setStyleSheet("color: #f8f8f2; font-size: 12px;")

            range_to = QSpinBox()
            range_to.setMinimum(1)
            range_to.setMaximum(len(episodes))
            range_to.setValue(len(episodes))
            range_to.setStyleSheet("""
                QSpinBox {
                    padding: 4px;
                    border: 1px solid #44475a;
                    border-radius: 3px;
                    background-color: #282a36;
                    color: #f8f8f2;
                    font-size: 12px;
                    width: 60px;
                }
            """)

            select_range_button = QPushButton("Select")
            select_range_button.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    padding: 6px 12px;
                    border: none;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)

            # Add controls to layout
            controls_layout.addWidget(select_all_button)
            controls_layout.addWidget(select_none_button)
            controls_layout.addWidget(range_label)
            controls_layout.addWidget(range_from)
            controls_layout.addWidget(to_label)
            controls_layout.addWidget(range_to)
            controls_layout.addWidget(select_range_button)
            controls_layout.addStretch()  # Push everything to the left

            episodes_layout.addLayout(controls_layout)

            # Episodes list with multi-selection
            episodes_list = QListWidget()
            episodes_list.setSelectionMode(QListWidget.MultiSelection)
            episodes_list.setStyleSheet("""
                QListWidget {
                    background-color: #282a36;
                    border: 1px solid #44475a;
                    border-radius: 5px;
                }
                QListWidget::item {
                    padding: 10px;
                    border-bottom: 1px solid #44475a;
                    color: #f8f8f2;
                }
                QListWidget::item:selected {
                    background-color: #00bcd4;
                    color: white;
                }
                QListWidget::item:hover {
                    background-color: #44475a;
                }
            """)

            # Populate episodes
            for i, episode in enumerate(episodes, 1):
                episode_title = episode.get('title', {}).get('en', f'Episode {i}')
                episode_text = f"Episode {i}: {episode_title}"

                item = QListWidgetItem(episode_text)
                item.setData(Qt.UserRole, episode)
                episodes_list.addItem(item)

            episodes_layout.addWidget(episodes_list)

            # Download buttons
            download_layout = QHBoxLayout()
            download_layout.addStretch()  # Push buttons to the right

            # View Streams button - الزر الوحيد المطلوب
            view_streams_button = QPushButton("📺 View Streams")
            view_streams_button.setStyleSheet("""
                QPushButton {
                    background-color: #9c27b0;
                    color: white;
                    border: none;
                    padding: 12px 30px;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 16px;
                }
                QPushButton:hover {
                    background-color: #8e24aa;
                }
            """)

            download_layout.addWidget(view_streams_button)
            episodes_layout.addLayout(download_layout)

            # Connect button events
            select_all_button.clicked.connect(lambda: self.select_all_episodes(episodes_list))
            select_none_button.clicked.connect(lambda: self.select_none_episodes(episodes_list))
            select_range_button.clicked.connect(lambda: self.select_range_episodes(episodes_list, range_from.value(), range_to.value()))
            view_streams_button.clicked.connect(self.handle_view_streams)

            # Connect episode selection to show streams
            episodes_list.itemClicked.connect(self.handle_episode_selection)

            # Store references for later use
            self.current_episodes_list_widget = episodes_list  # QListWidget for UI interactions
            self.current_episodes_data = episodes  # List of episode data

            # Add episodes section to seasons layout
            self.seasons_layout.addWidget(self.episodes_section)

            # Show batch download options for series
            series_data = getattr(self, 'current_series_info', {})
            self.show_batch_download_options_for_series(series_data, episodes)

            print(f"✅ Added {len(episodes)} episodes to seasons tab with selection controls")

        except Exception as e:
            self.show_message("Error", f"Failed to add episodes to seasons tab: {str(e)}")

    def select_all_episodes(self, episodes_list):
        """Select all episodes in the list"""
        try:
            for i in range(episodes_list.count()):
                item = episodes_list.item(i)
                item.setSelected(True)
            print(f"✅ Selected all {episodes_list.count()} episodes")
        except Exception as e:
            print(f"❌ Error selecting all episodes: {str(e)}")

    def select_none_episodes(self, episodes_list):
        """Deselect all episodes in the list"""
        try:
            episodes_list.clearSelection()
            print(f"✅ Deselected all episodes")
        except Exception as e:
            print(f"❌ Error deselecting episodes: {str(e)}")

    def select_range_episodes(self, episodes_list, start, end):
        """Select a range of episodes"""
        try:
            # Clear current selection
            episodes_list.clearSelection()

            # Ensure valid range
            start = max(1, start)
            end = min(episodes_list.count(), end)

            if start > end:
                start, end = end, start

            # Select range (convert to 0-based indexing)
            for i in range(start - 1, end):
                item = episodes_list.item(i)
                if item:
                    item.setSelected(True)

            print(f"✅ Selected episodes {start} to {end}")
        except Exception as e:
            print(f"❌ Error selecting episode range: {str(e)}")

    def download_selected_episodes(self, episodes_list):
        """Download selected episodes"""
        try:
            selected_items = episodes_list.selectedItems()
            if not selected_items:
                self.show_message("Warning", "Please select at least one episode to download")
                return

            selected_episodes = []
            for item in selected_items:
                episode_data = item.data(Qt.UserRole)
                if episode_data:
                    selected_episodes.append(episode_data)

            if selected_episodes:
                episode_count = len(selected_episodes)
                self.show_message("Info", f"Selected {episode_count} episodes for download.\nDownload functionality will be implemented soon.")

                print(f"\n🎬 Selected {episode_count} episodes for download:")
                for ep in selected_episodes:
                    episode_number = ep.get('episodeNumber', 'N/A')
                    episode_title = ep.get('title', {}).get('en', 'No Title')
                    print(f"  - Episode {episode_number}: {episode_title}")

        except Exception as e:
            self.show_message("Error", f"Failed to process selected episodes: {str(e)}")

    def handle_episode_selection(self, item):
        """Handle episode selection to show available streams"""
        try:
            episode_data = item.data(Qt.UserRole)
            if not episode_data:
                return

            episode_number = episode_data.get('episodeNumber', 'N/A')
            episode_title = episode_data.get('title', {}).get('en', 'No Title')
            streams = episode_data.get('streams', [])

            print(f"\n🎬 Episode selected: {episode_number} - {episode_title}")
            print(f"📺 Found {len(streams)} streams")

            # Episode info is now shown in the cards directly

            # Store streams for later use when user clicks "View Streams" button
            self.current_episode_streams = streams
            self.current_selected_episode = episode_data

            # Don't automatically switch tabs - let user choose when to view streams

        except Exception as e:
            print(f"❌ Error handling episode selection: {str(e)}")

    def handle_view_streams(self):
        """Handle View Streams button click - now handles multiple selected episodes"""
        try:
            # Get currently selected episodes from the list widget
            if not hasattr(self, 'current_episodes_list_widget') or not self.current_episodes_list_widget:
                self.show_message("Warning", "No episodes list available")
                return

            selected_items = self.current_episodes_list_widget.selectedItems()
            if not selected_items:
                self.show_message("Warning", "Please select at least one episode to view streams")
                return

            # Get the first selected episode for streams display
            first_selected_item = selected_items[0]
            episode_data = first_selected_item.data(Qt.UserRole)

            if not episode_data:
                self.show_message("Warning", "No episode data available")
                return

            streams = episode_data.get('streams', [])
            if not streams:
                self.show_message("Info", "No streams available for the selected episode")
                return

            # Display streams for the first selected episode
            self.display_episode_streams(streams, episode_data)

            # Switch to Available Streams tab
            self.content_tabs.setCurrentIndex(2)  # Available Streams tab index

            episode_number = episode_data.get('episodeNumber', 'N/A')
            episode_title = episode_data.get('title', {}).get('en', 'No Title')

            # Show info about all selected episodes
            selected_count = len(selected_items)
            if selected_count > 1:
                print(f"✅ Showing streams for Episode {episode_number} (first of {selected_count} selected episodes)")
                self.status_updated.emit(f"Showing streams for Episode {episode_number} (first of {selected_count} selected)")
            else:
                print(f"✅ Switched to streams view for Episode {episode_number}: {episode_title}")
                self.status_updated.emit(f"Showing streams for Episode {episode_number}")

        except Exception as e:
            print(f"❌ Error handling view streams: {str(e)}")
            self.show_message("Error", f"Failed to view streams: {str(e)}")

    def display_episode_streams(self, streams, episode_data):
        """Display available streams for selected episode using cards"""
        try:
            # Clear existing stream cards
            for i in reversed(range(self.streams_container_layout.count())):
                child = self.streams_container_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)
                    child.deleteLater()

            if not streams:
                # Show no streams message
                no_streams_label = QLabel("No streams available for this episode")
                no_streams_label.setStyleSheet("color: #f8f8f2; font-size: 14px; text-align: center; padding: 20px;")
                self.streams_container_layout.addWidget(no_streams_label)
                return

            # Filter streams (ignore HLS like in original code)
            filtered_streams = []
            for stream in streams:
                manifest_type = stream.get("manifestType", "N/A")
                if manifest_type != "MANIFEST_TYPE_HLS":
                    filtered_streams.append(stream)

            if not filtered_streams:
                # Show no compatible streams message
                no_streams_label = QLabel("No compatible streams available for this episode")
                no_streams_label.setStyleSheet("color: #f8f8f2; font-size: 14px; text-align: center; padding: 20px;")
                self.streams_container_layout.addWidget(no_streams_label)
                return

            # Create stream cards
            for index, stream in enumerate(filtered_streams):
                stream_card = self.create_stream_card(stream, index + 1)
                self.streams_container_layout.addWidget(stream_card)

            # Add stretch to push cards to top
            self.streams_container_layout.addStretch()

            # Store current episode data
            self.current_selected_episode = episode_data

            print(f"✅ Displayed {len(filtered_streams)} stream cards in Available Streams tab")

        except Exception as e:
            print(f"❌ Error displaying episode streams: {str(e)}")

    def create_stream_card(self, stream, index):
        """Create a beautiful stream card"""
        try:
            # Get stream info
            stream_id = stream.get("streamId", "N/A")
            resolution = stream.get("highestImageResolution", "N/A")
            is_hdr = stream.get("isHdr", False)
            is_dolby_vision = stream.get("isDolbyVision", False)

            # Simplify resolution names
            resolution_display = "HD"
            if "4K" in resolution:
                resolution_display = "4K"
            elif "HD" in resolution:
                resolution_display = "HD"

            # Create quality label - أبسط وأوضح
            if is_hdr and is_dolby_vision:
                quality_label = f"{resolution_display}\nHDR+DV"
            elif is_hdr:
                quality_label = f"{resolution_display}\nHDR"
            elif is_dolby_vision:
                quality_label = f"{resolution_display}\nDV"
            else:
                quality_label = resolution_display

            # Create card frame
            card = QFrame()
            card.setFixedHeight(110)
            card.setStyleSheet("""
                QFrame {
                    background-color: #1a1d29;
                    border: 3px solid #ffffff;
                    border-radius: 15px;
                    margin: 8px;
                }
                QFrame:hover {
                    border-color: #00bcd4;
                    background-color: #252836;
                    box-shadow: 0px 4px 8px rgba(0, 188, 212, 0.3);
                }
            """)

            # Card layout
            card_layout = QHBoxLayout(card)
            card_layout.setContentsMargins(15, 10, 15, 10)

            # Quality badge - إطار أكبر وأوضح
            quality_badge = QLabel(quality_label)
            quality_badge.setFixedSize(120, 70)
            quality_badge.setStyleSheet(f"""
                QLabel {{
                    background-color: {'#e74c3c' if is_hdr or is_dolby_vision else '#27ae60'};
                    color: #ffffff;
                    border-radius: 35px;
                    font-weight: bold;
                    font-size: 15px;
                    text-align: center;
                    padding: 12px;
                    border: 3px solid #ffffff;
                    margin: 4px;
                }}
            """)
            quality_badge.setAlignment(Qt.AlignCenter)

            # Stream info
            info_layout = QVBoxLayout()
            info_layout.setSpacing(2)

            # Stream title - نص أبيض واضح مع خلفية صلبة
            if is_hdr and is_dolby_vision:
                stream_title = QLabel(f"{resolution_display} HDR + Dolby Vision")
            elif is_hdr:
                stream_title = QLabel(f"{resolution_display} HDR")
            elif is_dolby_vision:
                stream_title = QLabel(f"{resolution_display} Dolby Vision")
            else:
                stream_title = QLabel(f"{resolution_display} Quality")

            stream_title.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-weight: bold;
                    font-size: 16px;
                    background-color: #000000;
                    padding: 6px 12px;
                    border-radius: 8px;
                    border: 2px solid #FFFFFF;
                    margin: 2px;
                }
            """)

            # Stream details - نص أبيض واضح مع خلفية صلبة
            stream_details = QLabel(f"Stream ID: {stream_id}")
            stream_details.setStyleSheet("""
                QLabel {
                    color: #FFFFFF;
                    font-size: 13px;
                    background-color: #333333;
                    padding: 4px 8px;
                    border-radius: 6px;
                    border: 1px solid #FFFFFF;
                    margin: 2px;
                }
            """)

            info_layout.addWidget(stream_title)
            info_layout.addWidget(stream_details)

            # Select button
            select_btn = QPushButton("Select")
            select_btn.setFixedSize(90, 40)
            select_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: #ffffff;
                    border: 2px solid #2980b9;
                    border-radius: 20px;
                    font-weight: bold;
                    font-size: 13px;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                    border-color: #1f618d;
                }
                QPushButton:pressed {
                    background-color: #1f618d;
                }
            """)

            # Connect button to selection
            select_btn.clicked.connect(lambda checked, s=stream: self.select_stream_card(s, card))

            # Add Play button
            play_btn = QPushButton("▶️ Play")
            play_btn.setFixedSize(90, 40)
            play_btn.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: #ffffff;
                    border: 2px solid #229954;
                    border-radius: 20px;
                    font-weight: bold;
                    font-size: 13px;
                    padding: 5px;
                }
                QPushButton:hover {
                    background-color: #229954;
                    border-color: #1e8449;
                }
                QPushButton:pressed {
                    background-color: #1e8449;
                }
            """)

            # Connect play button to open player
            play_btn.clicked.connect(lambda checked, s=stream: self.open_player(s))

            # Add to card layout
            card_layout.addWidget(quality_badge)
            card_layout.addLayout(info_layout)
            card_layout.addStretch()

            # Add buttons in horizontal layout
            button_layout = QHBoxLayout()
            button_layout.setSpacing(5)
            button_layout.addWidget(select_btn)
            button_layout.addWidget(play_btn)
            card_layout.addLayout(button_layout)

            # Store stream data in card
            card.setProperty("stream_data", stream)
            card.setProperty("selected", False)

            return card

        except Exception as e:
            print(f"❌ Error creating stream card: {str(e)}")
            return QFrame()

    def select_stream_card(self, stream_data, selected_card):
        """Handle stream card selection and fetch detailed stream information"""
        try:
            # Reset all cards to unselected state
            for i in range(self.streams_container_layout.count()):
                item = self.streams_container_layout.itemAt(i)
                if item and item.widget():
                    card = item.widget()
                    if hasattr(card, 'setProperty'):
                        card.setProperty("selected", False)
                        card.setStyleSheet("""
                            QFrame {
                                background-color: #44475a;
                                border: 2px solid #6272a4;
                                border-radius: 10px;
                                margin: 5px;
                            }
                            QFrame:hover {
                                border-color: #00bcd4;
                                background-color: #4a4d5a;
                            }
                        """)

            # Mark selected card
            selected_card.setProperty("selected", True)
            selected_card.setStyleSheet("""
                QFrame {
                    background-color: #00bcd4;
                    border: 2px solid #00acc1;
                    border-radius: 10px;
                    margin: 5px;
                }
            """)

            # Store selected stream
            self.selected_stream_data = stream_data

            # Get stream info for display
            stream_id = stream_data.get("streamId", "N/A")
            resolution = stream_data.get("highestImageResolution", "N/A")

            print(f"✅ Selected Stream: ID={stream_id}, Quality={resolution}")
            print(f"🔄 Fetching detailed stream information...")

            # Fetch detailed stream information (MPD, License URL, etc.)
            self.fetch_stream_details(stream_data)

        except Exception as e:
            print(f"❌ Error selecting stream card: {str(e)}")

    def fetch_stream_details(self, stream_data):
        """Fetch detailed stream information including MPD, License URL, PSSH, KID, Keys"""
        try:
            # Get currently selected episodes from the list widget (for multi-selection support)
            selected_episodes = []
            if hasattr(self, 'current_episodes_list_widget') and self.current_episodes_list_widget:
                selected_items = self.current_episodes_list_widget.selectedItems()
                for item in selected_items:
                    episode_data = item.data(Qt.UserRole)
                    if episode_data:
                        selected_episodes.append(episode_data)

            # Get content info - could be episode(s) or movie
            if selected_episodes:
                # For multiple episodes - use first episode for stream details
                content_data = selected_episodes[0]
                content_id = content_data.get('contentId')
                content_type = 'episode'
                print(f"📺 Processing {len(selected_episodes)} selected episodes")
            elif hasattr(self, 'current_selected_episode') and self.current_selected_episode:
                # For single episode (fallback)
                content_data = self.current_selected_episode
                content_id = content_data.get('contentId')
                content_type = 'episode'
                selected_episodes = [content_data]
            elif hasattr(self, 'current_content') and self.current_content:
                # For movies
                content_data = self.current_content.get('data', {})
                content_id = self.current_content.get('id')
                content_type = 'movie'
                selected_episodes = []
            else:
                self.show_message("Error", "No content selected")
                return

            stream_id = stream_data.get('streamId')

            print(f"📡 Fetching stream details for {content_type.title()} - Content ID: {content_id}, Stream ID: {stream_id}")

            # Get access token
            access_token = self.main_window.osn_api.get_access_token()
            if not access_token:
                self.show_message("Error", "No access token available. Please login first.")
                return

            # Use OSN DRM module to get stream details
            drm_info = self.main_window.osn_drm.get_stream_details(content_id, stream_id, access_token)

            if drm_info:
                print(f"✅ Stream details fetched successfully!")
                print(f"📺 MPD URL: {drm_info.get('mpd_url', 'N/A')}")
                print(f"🔐 License URL: {drm_info.get('license_url', 'N/A')}")
                print(f"🎫 DRM Token: {drm_info.get('drm_token', 'N/A')}")

                # Store selected episodes for later use
                self.current_selected_episodes = selected_episodes

                # Parse MPD to get qualities, audio tracks, and subtitles
                self.parse_mpd_and_extract_drm(drm_info, content_data, stream_data)
            else:
                self.show_message("Error", "Failed to fetch stream details")

        except Exception as e:
            print(f"❌ Error fetching stream details: {str(e)}")
            self.show_message("Error", f"Failed to fetch stream details: {str(e)}")

    def parse_mpd_and_extract_drm(self, drm_info, content_data, stream_data):
        """Parse MPD file and extract DRM information"""
        try:
            mpd_url = drm_info.get('mpd_url')
            license_url = drm_info.get('license_url')
            drm_token = drm_info.get('drm_token')

            if not mpd_url:
                self.show_message("Error", "MPD URL not available")
                return

            print(f"🔍 Parsing MPD file and extracting DRM info...")

            # Extract PSSH and KID from MPD
            pssh = self.main_window.osn_drm.get_pssh_from_mpd(mpd_url)
            kid = self.main_window.osn_drm.get_kid_from_mpd(mpd_url)

            print(f"🔑 PSSH: {pssh}")
            print(f"🆔 KID: {kid}")

            # Extract DRM keys using pywidevine
            if pssh and license_url and drm_token:
                keys = self.extract_drm_keys(license_url, pssh, drm_token)
                print(f"🔓 Extracted {len(keys) if keys else 0} DRM keys")
                # Save keys immediately
                if keys:
                    self.save_keys_to_file_direct(keys, "OSN Stream")
            else:
                keys = []
                print("⚠️ Missing DRM information for key extraction")

            # Parse MPD for available qualities, audio, and subtitles
            mpd_data = self.parse_mpd_file(mpd_url)

            # Create comprehensive stream information
            detailed_stream_info = {
                'content_data': content_data,  # Changed from episode_data to content_data
                'stream_data': stream_data,
                'drm_info': drm_info,
                'mpd_url': mpd_url,
                'license_url': license_url,
                'drm_token': drm_token,
                'pssh': pssh,
                'kid': kid,
                'keys': keys,
                'mpd_data': mpd_data
            }

            # Display detailed information in Download Options tab
            self.display_detailed_stream_info(detailed_stream_info)

            # Switch to Download Options tab
            self.content_tabs.setCurrentIndex(3)  # Download Options tab index

        except Exception as e:
            print(f"❌ Error parsing MPD and extracting DRM: {str(e)}")
            self.show_message("Error", f"Failed to parse stream data: {str(e)}")

    def extract_drm_keys(self, license_url, pssh, drm_token):
        """Extract DRM keys using unified pywidevine"""
        try:
            # Add unified pywidevine to path
            import sys
            from pathlib import Path

            # Use the unified pywidevine in binaries folder
            yango_root = Path(__file__).parent.parent.parent
            binaries_path = yango_root / "binaries"
            if str(binaries_path) not in sys.path:
                sys.path.insert(0, str(binaries_path))

            from pywidevine.decrypt.wvdecrypt import WvDecrypt
            from base64 import b64encode
            import requests

            print(f"🔐 Extracting DRM keys using unified pywidevine...")

            # Setup headers for license request
            headers = {
                'x-dt-custom-data': drm_token,
                'User-Agent': 'Dalvik/2.1.0 (Linux; Android 11; SHIELD Android TV Build/RQ1A.210105.003; wv)',
                'Content-Type': 'application/octet-stream'
            }

            # Use WvDecrypt to extract keys
            wvdecrypt = WvDecrypt(pssh)
            raw_challenge = wvdecrypt.get_challenge()

            # Send license request
            response = requests.post(url=license_url, headers=headers, data=raw_challenge)

            if response.status_code != 200:
                print(f"❌ License request failed. Status code: {response.status_code}")
                return []

            # Process license response
            license_b64 = b64encode(response.content)
            wvdecrypt.update_license(license_b64)
            keys = wvdecrypt.start_process()

            print(f"✅ Successfully extracted {len(keys)} DRM keys")
            for key in keys:
                print(f"🔑 Key: {key}")

            # Save keys to file immediately like original OSN.py
            self.save_keys_to_file_direct(keys, "OSN Content")

            return keys

        except Exception as e:
            print(f"❌ Error extracting DRM keys: {str(e)}")
            return []

    def save_keys_to_file_direct(self, keys, title="OSN Content"):
        """Save DRM keys to file directly like original OSN.py"""
        try:
            import os
            from pathlib import Path

            # Create KEYS directory like original OSN.py
            keys_dir = "KEYS"
            os.makedirs(keys_dir, exist_ok=True)

            save_filename = 'KEYS.txt'
            save_path = os.path.join(keys_dir, save_filename)

            print(f"🔑 DEBUG: Saving keys to: {save_path}")
            print(f"🔑 DEBUG: Keys to save: {keys}")

            # Read existing keys
            existing_keys = []
            if os.path.exists(save_path):
                with open(save_path, 'r', encoding='utf-8') as f:
                    existing_keys = [line.strip() for line in f if line.strip() and not line.startswith('#')]

            # Filter new keys
            new_keys = [key for key in keys if key not in existing_keys]

            if new_keys:
                print(f"🔑 OBTAINED KEYS for {title}:")
                for key in keys:
                    print(f"🔑 --key {key}")

                # Append new keys to file like original OSN.py
                with open(save_path, 'a', encoding='utf-8') as f:
                    f.write(f'\n\n# {title}\n')
                    for key in new_keys:
                        f.write(f'{key}\n')

                print(f"✅ Success Saving KEYS to {save_path}")
            else:
                print("🔑 KEYS Already Saved!")

        except Exception as e:
            print(f"❌ Error saving keys directly: {str(e)}")
            import traceback
            print(f"❌ Full traceback: {traceback.format_exc()}")

    def parse_mpd_file(self, mpd_url):
        """Parse MPD file to extract available qualities, audio tracks, and subtitles"""
        try:
            import requests
            import xml.etree.ElementTree as ET

            print(f"📄 Parsing MPD file: {mpd_url}")

            # Download MPD file
            response = requests.get(mpd_url)
            response.raise_for_status()

            # Parse XML
            root = ET.fromstring(response.content)
            namespaces = {'mpd': 'urn:mpeg:dash:schema:mpd:2011'}

            # Extract information
            qualities = []
            audio_tracks = []
            subtitle_tracks = []

            # Find all AdaptationSets
            for adaptation_set in root.findall('.//mpd:AdaptationSet', namespaces):
                mime_type = adaptation_set.get('mimeType', '')
                content_type = adaptation_set.get('contentType', '')
                lang = adaptation_set.get('lang', 'und')

                # Video qualities
                if 'video' in mime_type or content_type == 'video':
                    for representation in adaptation_set.findall('.//mpd:Representation', namespaces):
                        width = representation.get('width')
                        height = representation.get('height')
                        bandwidth = representation.get('bandwidth')
                        codecs = representation.get('codecs', '')
                        uuid = representation.get('id')  # This is the crucial UUID for N_m3u8DL-RE

                        print(f"🎬 Found video representation: {width}x{height} - {bandwidth} bps - UUID: {uuid} - Codecs: {codecs}")

                        if width and height and uuid:  # Make sure we have UUID
                            quality_label = self.get_quality_label(int(height)) if height.isdigit() else 'Unknown'
                            quality_info = {
                                'resolution': f"{width}x{height}",
                                'bandwidth': bandwidth,
                                'codecs': codecs,
                                'uuid': uuid,  # Store the UUID for download command
                                'id': uuid,    # Also store as 'id' for compatibility
                                'quality_label': quality_label
                            }
                            print(f"🎬 Creating quality: {quality_label} ({width}x{height}) - UUID: {uuid} - Bandwidth: {bandwidth}")
                            qualities.append(quality_info)

                # Audio tracks
                elif 'audio' in mime_type or content_type == 'audio':
                    for representation in adaptation_set.findall('.//mpd:Representation', namespaces):
                        codecs = representation.get('codecs', '')
                        bandwidth = representation.get('bandwidth')
                        audio_id = representation.get('id', '')  # Get unique ID for this audio track

                        audio_info = {
                            'language': lang,
                            'codecs': codecs,
                            'bandwidth': bandwidth,
                            'id': audio_id  # Store the unique ID for precise selection
                        }
                        audio_tracks.append(audio_info)

                # Subtitle tracks
                elif 'text' in mime_type or content_type == 'text':
                    subtitle_info = {
                        'language': lang,
                        'mime_type': mime_type
                    }
                    subtitle_tracks.append(subtitle_info)

            # Remove duplicates and sort
            qualities = self.remove_duplicate_qualities(qualities)
            audio_tracks = self.remove_duplicate_audio(audio_tracks)
            subtitle_tracks = self.remove_duplicate_subtitles(subtitle_tracks)

            mpd_data = {
                'qualities': qualities,
                'audio_tracks': audio_tracks,
                'subtitle_tracks': subtitle_tracks
            }

            print(f"📊 Found {len(qualities)} video qualities, {len(audio_tracks)} audio tracks, {len(subtitle_tracks)} subtitle tracks")

            # Print detailed quality information
            print(f"📋 DETAILED QUALITY LIST:")
            for i, quality in enumerate(qualities):
                print(f"📋   Quality {i+1}: {quality.get('quality_label', 'Unknown')} ({quality.get('resolution', 'Unknown')}) - UUID: {quality.get('uuid', quality.get('id', 'No UUID'))} - Bandwidth: {quality.get('bandwidth', 'Unknown')}")

            return mpd_data

        except Exception as e:
            print(f"❌ Error parsing MPD file: {str(e)}")
            return {'qualities': [], 'audio_tracks': [], 'subtitle_tracks': []}

    def get_quality_label(self, height):
        """Convert height to quality label"""
        if height >= 2160:
            return "4K"
        elif height >= 1440:
            return "1440p"
        elif height >= 1080:
            return "1080p"
        elif height >= 720:
            return "720p"
        elif height >= 480:
            return "480p"
        else:
            return f"{height}p"

    def remove_duplicate_qualities(self, qualities):
        """Remove duplicate video qualities - keep all bitrates for same resolution"""
        seen = set()
        unique_qualities = []
        for quality in qualities:
            # Include bandwidth in the key to keep different bitrates for same resolution
            try:
                bandwidth = quality.get('bandwidth', 0)
                bandwidth_int = int(bandwidth) if bandwidth else 0
                key = (quality['resolution'], quality['codecs'], bandwidth_int)
                if key not in seen:
                    seen.add(key)
                    unique_qualities.append(quality)
            except (ValueError, TypeError):
                # If bandwidth is not a valid number, use 0
                key = (quality['resolution'], quality['codecs'], 0)
                if key not in seen:
                    seen.add(key)
                    unique_qualities.append(quality)

        # Sort by resolution height (descending), then by bandwidth (descending)
        def sort_key(x):
            try:
                height = int(x['resolution'].split('x')[1])
                bandwidth = x.get('bandwidth', 0)
                # Ensure bandwidth is an integer
                bandwidth_int = int(bandwidth) if bandwidth else 0
                return (height, bandwidth_int)
            except (ValueError, TypeError, IndexError):
                return (0, 0)

        return sorted(unique_qualities, key=sort_key, reverse=True)

    def remove_duplicate_audio(self, audio_tracks):
        """Remove duplicate audio tracks - keep all unique language codes"""
        seen = set()
        unique_audio = []
        for audio in audio_tracks:
            # Use ID as primary key since each representation has unique ID
            audio_id = audio.get('id', '')
            if audio_id and audio_id not in seen:
                seen.add(audio_id)
                unique_audio.append(audio)
                enhanced_name = self.get_enhanced_audio_name(audio)
                print(f"🎵 Keeping audio track: {enhanced_name} (ID: {audio_id})")
            elif not audio_id:
                # Fallback to old method if no ID
                bandwidth = audio.get('bandwidth', '0')
                key = (audio['language'], audio['codecs'], bandwidth)
                if key not in seen:
                    seen.add(key)
                    unique_audio.append(audio)
                    print(f"🎵 Keeping audio track: {audio['language']} - {audio['codecs']} ({bandwidth})")
        return unique_audio

    def remove_duplicate_subtitles(self, subtitle_tracks):
        """Remove duplicate subtitle tracks"""
        seen = set()
        unique_subtitles = []
        for subtitle in subtitle_tracks:
            key = (subtitle['language'], subtitle['mime_type'])
            if key not in seen:
                seen.add(key)
                unique_subtitles.append(subtitle)
        return unique_subtitles

    def display_detailed_stream_info(self, detailed_info):
        """Display stream selection options in Download Options tab"""
        try:
            # Clear existing content in Download Options tab
            download_tab = self.content_tabs.widget(3)  # Download Options tab
            if download_tab.layout():
                # Clear existing layout
                while download_tab.layout().count():
                    child = download_tab.layout().takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()
            else:
                # Create new layout if none exists
                layout = QVBoxLayout(download_tab)
                download_tab.setLayout(layout)

            layout = download_tab.layout()

            # Title - handle both episodes and movies
            content_data = detailed_info['content_data']

            # Check if this is an episode or movie
            if 'episodeNumber' in content_data:
                # This is an episode
                episode_title = content_data.get('title', {}).get('en', 'Unknown Episode')
                episode_number = content_data.get('episodeNumber', 'N/A')
                title_text = f"📺 Episode {episode_number}: {episode_title}"
            else:
                # This is a movie
                movie_title = content_data.get('title', {}).get('en', 'Unknown Movie')
                movie_year = content_data.get('year', 'Unknown Year')
                title_text = f"🎬 {movie_title} ({movie_year})"

            title_label = QLabel(title_text)
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 14px;
                    font-weight: bold;
                    color: #50fa7b;
                    margin: 5px 0;
                    padding: 8px;
                    background-color: #44475a;
                    border-radius: 6px;
                }
            """)
            layout.addWidget(title_label)

            # Store detailed info for later use
            self.current_detailed_info = detailed_info

            # Video Quality Selection
            mpd_data = detailed_info.get('mpd_data', {})
            qualities = mpd_data.get('qualities', [])

            if qualities:
                quality_group = QGroupBox("🎬 Select Video Quality")
                quality_group.setStyleSheet("""
                    QGroupBox {
                        font-size: 14px;
                        font-weight: bold;
                        color: #8be9fd;
                        border: 2px solid #6272a4;
                        border-radius: 6px;
                        margin: 5px 0;
                        padding-top: 12px;
                    }
                    QGroupBox::title {
                        subcontrol-origin: margin;
                        left: 8px;
                        padding: 0 4px 0 4px;
                    }
                """)
                quality_layout = QVBoxLayout(quality_group)
                quality_layout.setContentsMargins(8, 5, 8, 8)
                quality_layout.setSpacing(5)



                # Create quality selection dropdown
                quality_dropdown = QComboBox()
                quality_dropdown.setStyleSheet("""
                    QComboBox {
                        background-color: #44475a;
                        border: 2px solid #6272a4;
                        border-radius: 6px;
                        padding: 8px 12px;
                        color: #f8f8f2;
                        font-size: 12px;
                        font-weight: 500;
                        min-height: 25px;
                    }
                    QComboBox:hover {
                        border-color: #8be9fd;
                        background-color: #4a4d5a;
                    }
                    QComboBox:focus {
                        border-color: #8be9fd;
                        background-color: #4a4d5a;
                    }
                    QComboBox::drop-down {
                        border: none;
                        width: 20px;
                    }
                    QComboBox::down-arrow {
                        image: none;
                        border-left: 5px solid transparent;
                        border-right: 5px solid transparent;
                        border-top: 5px solid #f8f8f2;
                        margin-right: 5px;
                    }
                    QComboBox QAbstractItemView {
                        background-color: #44475a;
                        border: 2px solid #6272a4;
                        border-radius: 6px;
                        color: #f8f8f2;
                        selection-background-color: #8be9fd;
                        selection-color: #282a36;
                        padding: 4px;
                    }
                    QComboBox QAbstractItemView::item {
                        padding: 8px 12px;
                        border-radius: 4px;
                        margin: 1px;
                    }
                    QComboBox QAbstractItemView::item:hover {
                        background-color: #8be9fd;
                        color: #282a36;
                    }
                """)

                # Add placeholder
                quality_dropdown.addItem("🎬 Select Video Quality...")
                quality_dropdown.setCurrentIndex(0)

                self.quality_dropdown = quality_dropdown
                quality_layout.addWidget(quality_dropdown)

                # Populate quality dropdown
                for i, quality in enumerate(qualities):
                    # Create more detailed quality text
                    quality_label = quality['quality_label']
                    resolution = quality['resolution']
                    bandwidth = quality.get('bandwidth', 0)
                    codecs = quality.get('codecs', '')

                    # Format bandwidth in a more readable way
                    try:
                        bandwidth_int = int(bandwidth) if bandwidth else 0
                        if bandwidth_int > 0:
                            if bandwidth_int >= 1000000:  # >= 1 Mbps
                                bandwidth_text = f"{bandwidth_int//1000} kbps"
                            else:
                                bandwidth_text = f"{bandwidth_int//1000} kbps"
                        else:
                            bandwidth_text = "Unknown bitrate"
                    except (ValueError, TypeError):
                        bandwidth_text = "Unknown bitrate"

                    # Create detailed text with emoji based on quality
                    if '4K' in quality_label or '3840' in resolution:
                        emoji = "🔥"
                    elif '1440p' in quality_label or '2560' in resolution:
                        emoji = "⭐"
                    elif '1080p' in quality_label or '1920' in resolution:
                        emoji = "✨"
                    elif '720p' in quality_label or '1280' in resolution:
                        emoji = "💫"
                    else:
                        emoji = "📺"

                    # Create comprehensive quality text
                    quality_text = f"{emoji} {quality_label} ({resolution}) - {bandwidth_text}"
                    if codecs:
                        quality_text += f" [{codecs}]"

                    # Add to dropdown
                    quality_dropdown.addItem(quality_text)

                    # Store quality data in dropdown item
                    item_index = quality_dropdown.count() - 1
                    quality_dropdown.setItemData(item_index, quality)

                # Set default selection to highest quality (index 1, since 0 is placeholder)
                if quality_dropdown.count() > 1:
                    quality_dropdown.setCurrentIndex(1)
                layout.addWidget(quality_group)

            # Audio Tracks Selection
            audio_tracks = mpd_data.get('audio_tracks', [])

            if audio_tracks:
                audio_group = QGroupBox("🔊 Select Audio Tracks")
                audio_group.setStyleSheet("""
                    QGroupBox {
                        font-size: 14px;
                        font-weight: bold;
                        color: #ffb86c;
                        border: 2px solid #6272a4;
                        border-radius: 6px;
                        margin: 5px 0;
                        padding-top: 12px;
                    }
                    QGroupBox::title {
                        subcontrol-origin: margin;
                        left: 8px;
                        padding: 0 4px 0 4px;
                    }
                """)
                audio_layout = QVBoxLayout(audio_group)
                audio_layout.setContentsMargins(8, 5, 8, 8)
                audio_layout.setSpacing(3)

                self.audio_checkboxes = []
                for i, audio in enumerate(audio_tracks):
                    # Get enhanced audio display name
                    audio_text = self.get_enhanced_audio_name(audio)
                    try:
                        bandwidth = audio.get('bandwidth', 0)
                        if bandwidth and int(bandwidth) > 0:
                            audio_text += f" ({int(bandwidth)//1000} kbps)"
                    except (ValueError, TypeError):
                        pass  # Skip bandwidth if it's not a valid number

                    checkbox = QCheckBox(audio_text)
                    checkbox.setStyleSheet("""
                        QCheckBox {
                            color: #f8f8f2;
                            font-size: 12px;
                            padding: 3px;
                        }
                        QCheckBox::indicator {
                            width: 16px;
                            height: 16px;
                        }
                        QCheckBox::indicator:unchecked {
                            background-color: #44475a;
                            border: 2px solid #6272a4;
                            border-radius: 3px;
                        }
                        QCheckBox::indicator:checked {
                            background-color: #50fa7b;
                            border: 2px solid #50fa7b;
                            border-radius: 3px;
                        }
                    """)
                    # Only check the first audio track by default
                    checkbox.setChecked(i == 0)
                    checkbox.audio_data = audio

                    # Connect signal to handle multiple selection (no longer exclusive)
                    checkbox.stateChanged.connect(lambda state, cb=checkbox: self.handle_audio_selection(cb, state))

                    self.audio_checkboxes.append(checkbox)
                    audio_layout.addWidget(checkbox)

                layout.addWidget(audio_group)

            # Subtitle Tracks Selection
            subtitle_tracks = mpd_data.get('subtitle_tracks', [])

            if subtitle_tracks:
                subtitle_group = QGroupBox("📝 Select Subtitles")
                subtitle_group.setStyleSheet("""
                    QGroupBox {
                        font-size: 14px;
                        font-weight: bold;
                        color: #f1fa8c;
                        border: 2px solid #6272a4;
                        border-radius: 6px;
                        margin: 5px 0;
                        padding-top: 12px;
                    }
                    QGroupBox::title {
                        subcontrol-origin: margin;
                        left: 8px;
                        padding: 0 4px 0 4px;
                    }
                """)
                subtitle_layout = QVBoxLayout(subtitle_group)
                subtitle_layout.setContentsMargins(8, 5, 8, 8)
                subtitle_layout.setSpacing(3)

                self.subtitle_checkboxes = []
                for subtitle in subtitle_tracks:
                    subtitle_text = f"{subtitle['language'].upper()}"

                    checkbox = QCheckBox(subtitle_text)
                    checkbox.setStyleSheet("""
                        QCheckBox {
                            color: #f8f8f2;
                            font-size: 12px;
                            padding: 3px;
                        }
                        QCheckBox::indicator {
                            width: 16px;
                            height: 16px;
                        }
                        QCheckBox::indicator:unchecked {
                            background-color: #44475a;
                            border: 2px solid #6272a4;
                            border-radius: 3px;
                        }
                        QCheckBox::indicator:checked {
                            background-color: #f1fa8c;
                            border: 2px solid #f1fa8c;
                            border-radius: 3px;
                        }
                    """)
                    checkbox.subtitle_data = subtitle
                    self.subtitle_checkboxes.append(checkbox)
                    subtitle_layout.addWidget(checkbox)

                layout.addWidget(subtitle_group)

            # Download Button
            download_button = QPushButton("⬇️ Start Download")
            download_button.setStyleSheet("""
                QPushButton {
                    background-color: #50fa7b;
                    color: #282a36;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 14px;
                    margin: 10px 0;
                    max-height: 40px;
                }
                QPushButton:hover {
                    background-color: #5af78e;
                }
                QPushButton:pressed {
                    background-color: #4caf50;
                }
            """)
            download_button.clicked.connect(self.start_download_with_selections)
            layout.addWidget(download_button)

            # Add stretch to push content to top
            layout.addStretch()

            print(f"✅ Download options displayed successfully!")

        except Exception as e:
            print(f"❌ Error displaying download options: {str(e)}")

    def handle_audio_selection(self, clicked_checkbox, state):
        """Handle multiple audio selection - multiple audio tracks can be selected"""
        try:
            # Log the selection with enhanced name
            audio_data = clicked_checkbox.audio_data
            enhanced_name = self.get_enhanced_audio_name(audio_data)

            if state == 2:  # Checked
                print(f"🎵 Audio selected: {enhanced_name}")
            else:  # Unchecked
                print(f"🎵 Audio deselected: {enhanced_name}")

        except Exception as e:
            print(f"❌ Error handling audio selection: {str(e)}")

    def get_enhanced_audio_name(self, audio):
        """Convert audio track info to enhanced display name"""
        try:
            language = audio.get('language', 'unknown').lower()
            codecs = audio.get('codecs', '').lower()

            # Language mapping
            language_map = {
                'en': 'English',
                'en-ddp': 'English',
                'en-da': 'English',
                'ar': 'Arabic',
                'fr': 'French',
                'tr': 'Turkish',
                'es': 'Spanish',
                'de': 'German',
                'it': 'Italian',
                'pt': 'Portuguese',
                'ru': 'Russian',
                'ja': 'Japanese',
                'ko': 'Korean',
                'zh': 'Chinese',
                'hi': 'Hindi',
                'ur': 'Urdu'
            }

            # Audio type mapping based on codec and language code
            audio_type_map = {
                'ec-3': 'Dolby Digital Plus',
                'eac3': 'Dolby Digital Plus',
                'ac-3': 'Dolby Digital',
                'mp4a.40.2': 'AAC',
                'mp4a': 'AAC'
            }

            # Special handling for specific language codes
            if language == 'en-ddp':
                display_language = 'English'
                audio_type = 'Dolby Digital Plus'
            elif language == 'en-da':
                display_language = 'English'
                audio_type = 'Audio Description'
            elif language == 'en-atmos':
                display_language = 'English'
                audio_type = 'Dolby Atmos'
            else:
                # Get display language
                display_language = language_map.get(language, language.upper())

                # Get audio type from codec
                audio_type = 'Unknown'
                for codec_key, type_name in audio_type_map.items():
                    if codec_key in codecs:
                        audio_type = type_name
                        break

                # If still unknown, try to determine from codec
                if audio_type == 'Unknown':
                    if 'dolby' in codecs or 'ddp' in language:
                        audio_type = 'Dolby Digital Plus'
                    elif 'atmos' in codecs or 'atmos' in language:
                        audio_type = 'Dolby Atmos'
                    elif 'aac' in codecs or 'mp4a' in codecs:
                        audio_type = 'AAC'
                    else:
                        audio_type = 'Standard'

            # Create enhanced display name
            if audio_type in ['Audio Description']:
                return f"{display_language} - {audio_type}"
            elif audio_type == 'Unknown' or audio_type == 'Standard':
                return f"{display_language} - Standard"
            else:
                return f"{display_language} - {audio_type}"

        except Exception as e:
            print(f"❌ Error creating enhanced audio name: {str(e)}")
            # Fallback to original format
            language = audio.get('language', 'unknown')
            codecs = audio.get('codecs', 'unknown')
            return f"{language.upper()} - {codecs}"

    def create_info_row(self, label, value):
        """Create an information row widget"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(5, 2, 5, 2)

        # Label
        label_widget = QLabel(f"{label}:")
        label_widget.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #bd93f9;
                min-width: 100px;
            }
        """)
        label_widget.setFixedWidth(120)

        # Value (with text selection enabled)
        value_widget = QLabel(str(value))
        value_widget.setStyleSheet("""
            QLabel {
                color: #f8f8f2;
                background-color: #44475a;
                padding: 5px;
                border-radius: 3px;
                border: 1px solid #6272a4;
            }
        """)
        value_widget.setTextInteractionFlags(Qt.TextSelectableByMouse)
        value_widget.setWordWrap(True)

        layout.addWidget(label_widget)
        layout.addWidget(value_widget)

        return widget

    def start_download_with_selections(self):
        """Start download with user selections"""
        try:
            if not hasattr(self, 'current_detailed_info'):
                self.show_message("Error", "No stream information available")
                return

            # Get selected quality from dropdown
            selected_quality = None
            if hasattr(self, 'quality_dropdown'):
                current_index = self.quality_dropdown.currentIndex()
                if current_index > 0:  # Skip placeholder (index 0)
                    selected_quality = self.quality_dropdown.itemData(current_index)

            if not selected_quality:
                self.show_message("Error", "Please select a video quality")
                return

            # Get selected audio tracks
            selected_audio = []
            if hasattr(self, 'audio_checkboxes'):
                for checkbox in self.audio_checkboxes:
                    if checkbox.isChecked():
                        selected_audio.append(checkbox.audio_data)

            # Get selected subtitles
            selected_subtitles = []
            if hasattr(self, 'subtitle_checkboxes'):
                for checkbox in self.subtitle_checkboxes:
                    if checkbox.isChecked():
                        selected_subtitles.append(checkbox.subtitle_data)

            # Get DRM information
            detailed_info = self.current_detailed_info
            content_data = detailed_info['content_data']

            # Create summary based on content type
            if 'episodeNumber' in content_data:
                # Episode
                content_title = f"Episode {content_data.get('episodeNumber', 'N/A')} - {content_data.get('title', {}).get('en', 'Unknown')}"
                content_icon = "📺"
            else:
                # Movie
                movie_title = content_data.get('title', {}).get('en', 'Unknown Movie')
                movie_year = content_data.get('year', 'Unknown Year')
                content_title = f"{movie_title} ({movie_year})"
                content_icon = "🎬"

            # Display selection summary
            summary = f"""
{content_icon} Content: {content_title}

🎬 Selected Quality: {selected_quality['quality_label'] if selected_quality else 'None'} ({selected_quality['resolution'] if selected_quality else 'N/A'})

🔊 Selected Audio Tracks: {len(selected_audio)}
{chr(10).join([f"  • {audio['language'].upper()} - {audio['codecs']}" for audio in selected_audio])}

📝 Selected Subtitles: {len(selected_subtitles)}
{chr(10).join([f"  • {sub['language'].upper()}" for sub in selected_subtitles])}

🔐 DRM Information:
  • MPD URL: {detailed_info.get('mpd_url', 'N/A')[:50]}...
  • License URL: {detailed_info.get('license_url', 'N/A')}
  • KID: {detailed_info.get('kid', 'N/A')}
  • PSSH: {'Available' if detailed_info.get('pssh') else 'Not Found'}

Ready to download?
            """

            # Show confirmation dialog
            reply = QMessageBox.question(
                None,
                "Confirm Download",
                summary,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if reply == QMessageBox.Yes:
                print("🚀 Starting download with selections...")
                print(f"📺 Quality: {selected_quality['quality_label'] if selected_quality else 'None'}")
                print(f"🔊 Audio tracks: {len(selected_audio)}")
                print(f"📝 Subtitles: {len(selected_subtitles)}")

                # Add to downloads table
                self.add_download_to_table(detailed_info, selected_quality, selected_audio, selected_subtitles)

                # Switch to Downloads tab
                self.content_tabs.setCurrentIndex(4)  # Downloads tab index

                # Show success message
                self.show_message("Success", f"'{content_title}' added to downloads queue!")
            else:
                print("❌ Download cancelled by user")

        except Exception as e:
            print(f"❌ Error starting download: {str(e)}")
            self.show_message("Error", f"Failed to start download: {str(e)}")

    def add_download_to_table(self, detailed_info, selected_quality, selected_audio, selected_subtitles):
        """Add download item(s) to downloads table - supports multiple episodes with sequential download"""
        try:
            # Check if we have multiple selected episodes
            if hasattr(self, 'current_selected_episodes') and len(self.current_selected_episodes) > 1:
                # Add multiple episodes to queue for sequential download
                print(f"📥 Adding {len(self.current_selected_episodes)} episodes to download table")

                # Store the settings for all episodes - save preferences, not specific IDs
                # Extract preferences from selected items
                quality_preference = {
                    'resolution': selected_quality.get('resolution'),
                    'quality_label': selected_quality.get('quality_label'),
                    'uuid': selected_quality.get('uuid')
                }

                # Extract audio language preferences
                audio_preferences = []
                for audio in selected_audio:
                    if isinstance(audio, dict):
                        audio_preferences.append({
                            'language': audio.get('language', 'en'),
                            'codec': audio.get('codec', 'mp4a.40.2')
                        })
                    else:
                        audio_preferences.append({'language': str(audio), 'codec': 'mp4a.40.2'})

                # Extract subtitle language preferences
                subtitle_preferences = []
                for subtitle in selected_subtitles:
                    if isinstance(subtitle, dict):
                        subtitle_preferences.append(subtitle.get('language', 'en'))
                    else:
                        subtitle_preferences.append(str(subtitle))

                # Sort episodes by episode number before storing and adding to table
                episodes_sorted = sorted(self.current_selected_episodes, key=lambda ep: ep.get('episodeNumber', 0))
                print(f"📋 Episodes sorted by episode number for table:")
                for ep in episodes_sorted:
                    print(f"   - Episode {ep.get('episodeNumber', 'Unknown')}: {ep.get('title', {}).get('en', 'Unknown Title')}")

                self.multi_episode_settings = {
                    'quality_preference': quality_preference,
                    'audio_preferences': audio_preferences,
                    'subtitle_preferences': subtitle_preferences,
                    'episodes': episodes_sorted.copy()  # Store sorted episodes
                }

                print(f"💾 Saved preferences - Quality: {quality_preference.get('quality_label')}, Audio: {[a.get('language') for a in audio_preferences]}, Subtitles: {subtitle_preferences}")

                # Add all episodes to table first (as queued) without starting download - IN SORTED ORDER
                for episode_data in episodes_sorted:
                    # Create basic info for each episode (without stream details)
                    episode_basic_info = {
                        'content_data': episode_data,
                        'needs_stream_fetch': True  # Flag to indicate we need to fetch stream details
                    }

                    # Add each episode to table (but don't start download yet)
                    self.add_single_download_to_table(episode_basic_info, selected_quality, selected_audio, selected_subtitles, start_download=False)

                # DON'T start download automatically - wait for user to click "Start Downloads"
                print(f"✅ Added {len(self.current_selected_episodes)} episodes to download table - ready to start when user clicks 'Start Downloads'")
            else:
                # Add single episode or movie and start download immediately
                self.add_single_download_to_table(detailed_info, selected_quality, selected_audio, selected_subtitles, start_download=True)

        except Exception as e:
            print(f"❌ Error adding download to table: {str(e)}")
            self.show_message("Error", f"Failed to add download: {str(e)}")

    def add_single_download_to_table(self, detailed_info, selected_quality, selected_audio, selected_subtitles, start_download=True):
        """Add single download item to downloads table"""
        try:
            content_data = detailed_info.get('content_data', {})

            # Determine content type and get appropriate info
            if 'episodeNumber' in content_data:
                # This is an episode
                season_number = content_data.get('seasonNumber', 'N/A')
                episode_number = content_data.get('episodeNumber', 'N/A')
                episode_title = content_data.get('title', {}).get('en', 'Unknown Episode')

                # Get series title from series info or default
                series_title = "Unknown Series"
                if hasattr(self, 'current_series_info') and self.current_series_info:
                    series_title = self.current_series_info.get('title', {}).get('en', 'Unknown Series')

                content_title = f"{series_title} S{season_number}E{episode_number}"
            else:
                # This is a movie
                movie_title = content_data.get('title', {}).get('en', 'Unknown Movie')
                movie_year = content_data.get('year', 'Unknown Year')
                content_title = f"{movie_title} ({movie_year})"

            # Get quality info - use stream quality instead of MPD quality
            stream_data = detailed_info.get('stream_data', {})
            resolution_type = stream_data.get('highestImageResolution', 'HD')
            is_hdr = stream_data.get('isHdr', False)
            is_dolby_vision = stream_data.get('isDolbyVision', False)

            # Create codec description based on stream type
            if resolution_type == 'IMAGE_RESOLUTION_4K':
                if is_hdr and is_dolby_vision:
                    codec = "4K HDR+DV"
                elif is_hdr:
                    codec = "4K HDR"
                elif is_dolby_vision:
                    codec = "4K DV"
                else:
                    codec = "4K"
            else:
                codec = "HD"

            # Get actual resolution from selected quality if available and format it properly
            if selected_quality:
                raw_resolution = selected_quality.get('resolution', 'N/A')
                # Convert resolution like 640x360 to 360p, 1920x1080 to 1080p, etc.
                if 'x' in str(raw_resolution):
                    try:
                        width, height = raw_resolution.split('x')
                        actual_resolution = f"{height}p"
                    except:
                        actual_resolution = raw_resolution
                else:
                    actual_resolution = raw_resolution
            else:
                actual_resolution = 'N/A'

            # Create download item
            download_item = {
                'detailed_info': detailed_info,
                'episode_data': content_data,  # Store episode data for easy access
                'selected_quality': selected_quality,
                'selected_audio': selected_audio,
                'selected_subtitles': selected_subtitles,
                'status': 'Queued',
                'progress': 0
            }

            # Add to downloads list
            self.download_items.append(download_item)

            # Add row to table
            row_position = self.downloads_table.rowCount()
            self.downloads_table.insertRow(row_position)

            # Set table items with compact design
            # Column 0: Content title (compact)
            title_item = QTableWidgetItem(content_title)
            title_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            title_item.setFont(QFont("Arial", 10, QFont.Weight.Medium))
            self.downloads_table.setItem(row_position, 0, title_item)

            # Column 1: Season/Episode or Movie indicator (very compact)
            if 'episodeNumber' in content_data:
                se_text = f"S{season_number}E{episode_number}"
            else:
                se_text = "🎬"

            se_item = QTableWidgetItem(se_text)
            se_item.setTextAlignment(Qt.AlignCenter)
            se_item.setFont(QFont("Arial", 9, QFont.Weight.Bold))
            self.downloads_table.setItem(row_position, 1, se_item)

            # Column 2: Quality (compact)
            quality_text = actual_resolution
            if is_hdr:
                quality_text += " HDR"
            if is_dolby_vision:
                quality_text += " DV"

            quality_item = QTableWidgetItem(quality_text)
            quality_item.setTextAlignment(Qt.AlignCenter)
            quality_item.setFont(QFont("Arial", 9))
            self.downloads_table.setItem(row_position, 2, quality_item)

            # Column 3: Progress bar (matching image style)
            progress_bar = QProgressBar()
            progress_bar.setValue(0)
            progress_bar.setMinimumHeight(25)
            progress_bar.setMaximumHeight(30)
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 2px solid #6272a4;
                    border-radius: 6px;
                    text-align: center;
                    font-size: 11px;
                    font-weight: 600;
                    color: #f8f8f2;
                    background-color: #282a36;
                    height: 25px;
                }
                QProgressBar::chunk {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #50fa7b, stop:1 #8be9fd);
                    border-radius: 4px;
                    margin: 1px;
                }
            """)
            self.downloads_table.setCellWidget(row_position, 3, progress_bar)

            # Column 4: Status (compact with emoji)
            status_item = QTableWidgetItem("⏳ Queued")
            status_item.setTextAlignment(Qt.AlignCenter)
            status_item.setFont(QFont("Arial", 9))
            self.downloads_table.setItem(row_position, 4, status_item)

            # Store download item reference for progress updates and all necessary data
            download_item['row'] = row_position
            download_item['progress_bar'] = progress_bar
            download_item['content_data'] = detailed_info.get('content_data', {})
            download_item['mpd_url'] = detailed_info.get('mpd_url')
            download_item['license_url'] = detailed_info.get('license_url')
            download_item['drm_token'] = detailed_info.get('drm_token')
            download_item['keys'] = detailed_info.get('keys', [])
            download_item['pssh'] = detailed_info.get('pssh')
            download_item['kid'] = detailed_info.get('kid')
            download_item['selected_quality'] = selected_quality
            download_item['selected_audio'] = selected_audio
            download_item['selected_subtitles'] = selected_subtitles

            print(f"✅ Added download to table: {content_title}")

            # Don't start download immediately - wait for user to click "Start Downloads"
            if start_download:
                # Check if this is a movie or single episode
                content_data = detailed_info.get('content_data', {})
                is_movie = 'episodeNumber' not in content_data

                if is_movie:
                    print(f"🎬 Movie added to downloads table: {content_title}")
                    print(f"⏳ Click 'Start Downloads' button to begin download")
                else:
                    print(f"📺 Episode added to downloads table: {content_title}")
                    print(f"⏳ Click 'Start Downloads' button to begin download")

        except Exception as e:
            print(f"❌ Error adding single download to table: {str(e)}")
            self.show_message("Error", f"Failed to add download: {str(e)}")

    def start_movie_download_from_table(self, detailed_info, selected_quality, selected_audio, selected_subtitles):
        """Start movie download from downloads table"""
        try:
            print("🎬 Starting movie download from table...")

            # Get movie data
            content_data = detailed_info.get('content_data', {})
            mpd_url = detailed_info.get('mpd_url')
            drm_info = {
                'license_url': detailed_info.get('license_url'),
                'drm_token': detailed_info.get('drm_token'),
                'keys': detailed_info.get('keys', []),
                'pssh': detailed_info.get('pssh'),
                'kid': detailed_info.get('kid'),
                'mpd_url': mpd_url
            }

            # Start movie download using osn_downloader
            self.main_window.osn_downloader.download_movie(
                mpd_url=mpd_url,
                movie_data=content_data,
                selected_quality=selected_quality,
                audio_tracks=selected_audio,
                subtitle_tracks=selected_subtitles,
                drm_info=drm_info
            )

        except Exception as e:
            print(f"❌ Error starting movie download from table: {str(e)}")
            self.show_message("Error", f"Failed to start movie download: {str(e)}")

    def start_episode_download_from_table(self, detailed_info, selected_quality, selected_audio, selected_subtitles):
        """Start episode download from downloads table"""
        try:
            print("📺 Starting episode download from table...")

            # Get episode data
            content_data = detailed_info.get('content_data', {})
            mpd_url = detailed_info.get('mpd_url')
            drm_info = {
                'license_url': detailed_info.get('license_url'),
                'drm_token': detailed_info.get('drm_token'),
                'keys': detailed_info.get('keys', []),
                'pssh': detailed_info.get('pssh'),
                'kid': detailed_info.get('kid'),
                'mpd_url': mpd_url
            }

            # Get series data (if available)
            series_data = getattr(self, 'current_series_info', {})

            # Start episode download using osn_downloader
            self.main_window.osn_downloader.download_episode(
                mpd_url=mpd_url,
                series_data=series_data,
                episode_data=content_data,
                selected_quality=selected_quality,
                audio_tracks=selected_audio,
                subtitle_tracks=selected_subtitles,
                drm_info=drm_info
            )

        except Exception as e:
            print(f"❌ Error starting episode download from table: {str(e)}")
            self.show_message("Error", f"Failed to start episode download: {str(e)}")

    def start_movie_download_from_stored_item(self, download_item):
        """Start movie download from stored download item"""
        try:
            print("🎬 Starting movie download from stored item...")

            # Extract stored information from download item
            content_data = download_item.get('content_data', {})

            # Get the detailed info that was stored when the item was added
            detailed_info = {
                'content_data': content_data,
                'mpd_url': download_item.get('mpd_url'),
                'license_url': download_item.get('license_url'),
                'drm_token': download_item.get('drm_token'),
                'keys': download_item.get('keys', []),
                'pssh': download_item.get('pssh'),
                'kid': download_item.get('kid')
            }

            # Get stored quality and audio selections
            selected_quality = download_item.get('selected_quality')
            selected_audio = download_item.get('selected_audio', [])
            selected_subtitles = download_item.get('selected_subtitles', [])

            # Start the movie download
            self.start_movie_download_from_table(detailed_info, selected_quality, selected_audio, selected_subtitles)

        except Exception as e:
            print(f"❌ Error starting movie download from stored item: {str(e)}")
            self.show_message("Error", f"Failed to start movie download: {str(e)}")

    def start_sequential_download(self):
        """Start sequential download using osn_downloader.py"""
        try:
            # Check if we have multi-episode settings or need to create them from download_items
            if not hasattr(self, 'multi_episode_settings') or not self.multi_episode_settings:
                print("⚠️ No multi-episode settings found, creating from download_items...")

                # Create multi-episode settings from download_items
                if hasattr(self, 'download_items') and self.download_items:
                    episodes_data = []
                    for item in self.download_items:
                        if item.get('status') == 'Queued':
                            content_data = item.get('content_data', {})
                            if 'episodeNumber' in content_data:  # It's an episode
                                episodes_data.append(content_data)

                    if episodes_data:
                        # Create temporary multi_episode_settings
                        self.multi_episode_settings = {
                            'episodes': episodes_data,
                            'quality_preference': self.download_items[0].get('selected_quality', {}),
                            'audio_preferences': self.download_items[0].get('selected_audio', []),
                            'subtitle_preferences': self.download_items[0].get('selected_subtitles', [])
                        }
                        print(f"✅ Created multi-episode settings for {len(episodes_data)} episodes")
                    else:
                        print("❌ No episode data found in download_items")
                        return
                else:
                    print("❌ No download_items found")
                    return

            self.is_sequential_downloading = True
            episodes = self.multi_episode_settings['episodes']
            print(f"🚀 Starting sequential download of {len(episodes)} episodes using osn_downloader.py")

            # Sort episodes by episode number to ensure correct order
            episodes_sorted = sorted(episodes, key=lambda ep: ep.get('episodeNumber', 0))
            print(f"📋 Episodes sorted by episode number:")
            for ep in episodes_sorted:
                print(f"   - Episode {ep.get('episodeNumber', 'Unknown')}: {ep.get('title', {}).get('en', 'Unknown Title')}")

            # Ensure all episodes are marked as "Queued" initially
            for i in range(len(self.download_items)):
                if i < self.downloads_table.rowCount():
                    status_item = self.downloads_table.item(i, 5)
                    if status_item:
                        status_item.setText("Queued")

            # Prepare episodes data for osn_downloader.py - FETCH DETAILED INFO FOR EACH EPISODE
            episodes_data = []
            for i, episode in enumerate(episodes_sorted):
                try:
                    print(f"🔄 Fetching detailed info for episode {episode.get('episodeNumber', i + 1)}")

                    # Fetch stream details for this episode
                    episode_id = episode.get('contentId')
                    if not episode_id:
                        print(f"❌ No contentId found for episode {i + 1}")
                        continue

                    # Get stream ID for this episode
                    stream_id = self.get_stream_id_for_episode(episode)
                    if not stream_id:
                        print(f"❌ No stream ID found for episode {episode.get('episodeNumber', i + 1)}")
                        continue

                    print(f"✅ Found stream ID {stream_id} for episode {episode.get('episodeNumber', i + 1)}")

                    # Fetch MPD and DRM info for this episode
                    mpd_url, drm_info = self.fetch_mpd_and_drm_for_episode(episode_id, stream_id)
                    if not mpd_url:
                        print(f"❌ Failed to fetch MPD for episode {episode.get('episodeNumber', i + 1)}")
                        continue

                    print(f"✅ Fetched MPD and DRM for episode {episode.get('episodeNumber', i + 1)}")

                    episode_data = {
                        'episode_number': episode.get('episodeNumber', i + 1),
                        'mpd_url': mpd_url,
                        'series_data': {
                            'title': {'en': self.current_series_info.get('title', {}).get('en', 'Unknown Series') if hasattr(self, 'current_series_info') else 'Unknown Series'},
                            'images': self.current_series_info.get('images', {}) if hasattr(self, 'current_series_info') else {}
                        },
                        'episode_data': episode,
                        'drm_info': drm_info
                    }
                    episodes_data.append(episode_data)
                    print(f"📋 Prepared episode {episode.get('episodeNumber', i + 1)} for sequential download")

                except Exception as e:
                    print(f"❌ Error preparing episode {episode.get('episodeNumber', i + 1)}: {str(e)}")
                    continue

            if episodes_data:
                # Get preferences from first episode
                quality_preference = self.multi_episode_settings.get('quality_preference', {})
                audio_preferences = self.multi_episode_settings.get('audio_preferences', [])
                subtitle_preferences = self.multi_episode_settings.get('subtitle_preferences', [])

                print(f"🎯 Using shared preferences - Quality: {quality_preference.get('quality_label', 'Unknown')}")
                print(f"🎯 Audio: {[a.get('language') for a in audio_preferences]}")
                print(f"🎯 Subtitles: {subtitle_preferences}")

                # Start sequential download using osn_downloader.py
                self.main_window.osn_downloader.download_series_sequential(
                    episodes_data=episodes_data,
                    quality_preferences=quality_preference,
                    audio_preferences=audio_preferences,
                    subtitle_preferences=subtitle_preferences
                )
            else:
                print("❌ No episodes data prepared")
                self.is_sequential_downloading = False

        except Exception as e:
            print(f"❌ Error starting sequential download: {str(e)}")
            self.is_sequential_downloading = False

    def get_stream_id_for_episode(self, episode):
        """Get stream ID for episode"""
        try:
            streams = episode.get('streams', [])
            for stream in streams:
                if stream.get('manifestType') == 'MANIFEST_TYPE_DASH':
                    return stream.get('streamId')
            return None
        except Exception as e:
            print(f"❌ Error getting stream ID: {str(e)}")
            return None

    def fetch_mpd_and_drm_for_episode(self, content_id, stream_id):
        """Fetch MPD URL and DRM info for episode"""
        try:
            # Get access token
            access_token = self.main_window.osn_api.get_access_token()
            if not access_token:
                print(f"❌ No access token available for stream {stream_id}")
                return None, None

            print(f"🔄 Fetching stream details for content {content_id}, stream {stream_id}")

            # Use OSN DRM module to get stream details (this is the correct function)
            stream_response = self.main_window.osn_drm.get_stream_details(content_id, stream_id, access_token)
            if not stream_response:
                print(f"❌ Failed to get stream details for stream {stream_id}")
                return None, None

            mpd_url = stream_response.get('mpd_url')
            license_url = stream_response.get('license_url')
            drm_token = stream_response.get('drm_token')

            if not mpd_url:
                print(f"❌ No MPD URL found for stream {stream_id}")
                return None, None

            print(f"✅ Got MPD URL for stream {stream_id}: {mpd_url}")

            # Extract DRM info
            drm_info = {
                'license_url': license_url,
                'drm_token': drm_token,
                'pssh': None,
                'keys': []
            }

            if license_url and drm_token:
                try:
                    # Get PSSH from MPD
                    pssh = self.main_window.osn_drm.get_pssh_from_mpd(mpd_url)
                    if pssh:
                        drm_info['pssh'] = pssh

                        # Extract DRM keys using the correct method from osn_ui.py
                        keys = self.extract_drm_keys(license_url, pssh, drm_token)
                        if keys:
                            drm_info['keys'] = keys
                            print(f"✅ Extracted {len(keys)} DRM keys for stream {stream_id}")
                            # Save keys immediately
                            self.save_keys_to_file_direct(keys, f"Stream {stream_id}")
                        else:
                            print(f"⚠️ No DRM keys extracted for stream {stream_id}")
                    else:
                        print(f"⚠️ No PSSH found for stream {stream_id}")
                except Exception as e:
                    print(f"⚠️ Error extracting DRM info for stream {stream_id}: {str(e)}")

            return mpd_url, drm_info

        except Exception as e:
            print(f"❌ Error fetching MPD and DRM for stream {stream_id}: {str(e)}")
            return None, None

    def download_next_episode(self):
        """DISABLED - osn_downloader.py handles the queue automatically"""
        print("⚠️ download_next_episode called but disabled - osn_downloader.py handles queue")
        return

    def fetch_and_start_next_episode(self):
        """Fetch details for next episode and start its download"""
        try:
            if not hasattr(self, 'multi_episode_settings') or not self.multi_episode_settings:
                print(f"✅ No more episodes to download - all completed!")
                self.is_sequential_downloading = False
                return

            print(f"🔍 Current multi_episode_settings type: {type(self.multi_episode_settings)}")
            print(f"🔍 Current multi_episode_settings length: {len(self.multi_episode_settings)}")

            # Check if multi_episode_settings is a list or dict
            if isinstance(self.multi_episode_settings, list):
                # It's a list - use list operations
                if len(self.multi_episode_settings) > 1:
                    # Remove the first episode (already downloaded)
                    completed_episode = self.multi_episode_settings.pop(0)
                    print(f"✅ Completed episode {completed_episode.get('episodeNumber', 'Unknown')}")

                    # Get the next episode (now at index 0)
                    if self.multi_episode_settings:
                        next_episode = self.multi_episode_settings[0]
                        episode_number = next_episode.get('episodeNumber', 'Unknown')
                        print(f"🔄 Fetching details for next episode: {episode_number}")

                        # Find the correct download row for this episode
                        download_row = -1
                        for row in range(self.downloads_table.rowCount()):
                            episode_data_in_row = self.downloads_table.item(row, 0).data(Qt.UserRole)
                            if episode_data_in_row and episode_data_in_row.get('episodeNumber') == next_episode.get('episodeNumber'):
                                download_row = row
                                break

                        if download_row >= 0:
                            print(f"🎯 Found download row {download_row} for episode {next_episode.get('episodeNumber')}")
                            # Fetch stream details for the next episode
                            self.fetch_episode_stream_details_for_download(next_episode, download_row)
                        else:
                            print(f"❌ Could not find download row for episode {next_episode.get('episodeNumber')}")
                            self.is_sequential_downloading = False
                    else:
                        print(f"✅ All episodes completed!")
                        self.is_sequential_downloading = False
                        self.multi_episode_settings = []
                else:
                    print(f"✅ All episodes completed!")
                    self.is_sequential_downloading = False
                    self.multi_episode_settings = []

            elif isinstance(self.multi_episode_settings, dict):
                # It's a dict - check if it has 'episodes' key
                episode_keys = list(self.multi_episode_settings.keys())
                print(f"🔍 Episode keys: {episode_keys}")

                if 'episodes' in self.multi_episode_settings:
                    # The episodes are stored in the 'episodes' key
                    episodes_list = self.multi_episode_settings['episodes']
                    print(f"🔍 Found episodes list with {len(episodes_list)} episodes")

                    if len(episodes_list) > 1:
                        # Remove the first episode (already downloaded)
                        completed_episode = episodes_list.pop(0)
                        print(f"✅ Completed episode {completed_episode.get('episodeNumber', 'Unknown')}")

                        # Get the next episode (now at index 0)
                        if episodes_list:
                            next_episode = episodes_list[0]
                            episode_number = next_episode.get('episodeNumber', 'Unknown')
                            print(f"🔄 Fetching details for next episode: {episode_number}")

                            # Find the correct download row for this episode
                            download_row = -1
                            target_episode_number = next_episode.get('episodeNumber')
                            print(f"🔍 Looking for episode {target_episode_number} in {self.downloads_table.rowCount()} rows")

                            for row in range(self.downloads_table.rowCount()):
                                # Check if this row corresponds to our target episode
                                # Method 1: Check download_items list
                                if row < len(self.download_items):
                                    download_item = self.download_items[row]
                                    stored_episode_data = download_item.get('episode_data', {})
                                    stored_episode_number = stored_episode_data.get('episodeNumber')
                                    print(f"🔍 Row {row}: stored episode number = {stored_episode_number}")

                                    if stored_episode_number == target_episode_number:
                                        download_row = row
                                        print(f"✅ Found match in download_items at row {row}")
                                        break

                                # Method 2: Check table item data as fallback
                                title_item = self.downloads_table.item(row, 0)
                                if title_item:
                                    episode_data_in_row = title_item.data(Qt.UserRole)
                                    if episode_data_in_row and episode_data_in_row.get('episodeNumber') == target_episode_number:
                                        download_row = row
                                        print(f"✅ Found match in table data at row {row}")
                                        break

                            if download_row >= 0:
                                print(f"🎯 Found download row {download_row} for episode {next_episode.get('episodeNumber')}")
                                # Fetch stream details for the next episode
                                self.fetch_episode_stream_details_for_download(next_episode, download_row)
                            else:
                                print(f"❌ Could not find download row for episode {next_episode.get('episodeNumber')}")
                                self.is_sequential_downloading = False
                        else:
                            print(f"✅ All episodes completed!")
                            self.is_sequential_downloading = False
                            self.multi_episode_settings = {}
                    else:
                        print(f"✅ All episodes completed!")
                        self.is_sequential_downloading = False
                        self.multi_episode_settings = {}
                else:
                    # Legacy dict format - treat keys as episodes
                    if len(episode_keys) > 1:
                        # Remove the first episode (already downloaded)
                        first_key = episode_keys[0]
                        completed_episode = self.multi_episode_settings.pop(first_key)
                        print(f"✅ Completed episode {getattr(completed_episode, 'episodeNumber', 'Unknown') if hasattr(completed_episode, 'get') else 'Unknown'}")

                        # Get the next episode
                        remaining_keys = list(self.multi_episode_settings.keys())
                        if remaining_keys:
                            next_key = remaining_keys[0]
                            next_episode = self.multi_episode_settings[next_key]
                            episode_number = getattr(next_episode, 'episodeNumber', 'Unknown') if hasattr(next_episode, 'get') else 'Unknown'
                            print(f"🔄 Fetching details for next episode: {episode_number}")

                            # Find the correct download row for this episode
                            download_row = -1
                            for row in range(self.downloads_table.rowCount()):
                                episode_data_in_row = self.downloads_table.item(row, 0).data(Qt.UserRole)
                                if episode_data_in_row and episode_data_in_row.get('episodeNumber') == getattr(next_episode, 'episodeNumber', None):
                                    download_row = row
                                    break

                            if download_row >= 0:
                                print(f"🎯 Found download row {download_row} for episode {episode_number}")
                                # Fetch stream details for the next episode
                                self.fetch_episode_stream_details_for_download(next_episode, download_row)
                            else:
                                print(f"❌ Could not find download row for episode {episode_number}")
                                self.is_sequential_downloading = False
                        else:
                            print(f"✅ All episodes completed!")
                            self.is_sequential_downloading = False
                            self.multi_episode_settings = {}
                    else:
                        print(f"✅ All episodes completed!")
                        self.is_sequential_downloading = False
                        self.multi_episode_settings = {}

        except Exception as e:
            print(f"❌ Error fetching next episode: {str(e)}")
            print(f"🔍 Debug info: multi_episode_settings type: {type(getattr(self, 'multi_episode_settings', None))}")
            print(f"🔍 Debug info: multi_episode_settings length: {len(getattr(self, 'multi_episode_settings', []))}")
            import traceback
            print(f"🔍 Full traceback: {traceback.format_exc()}")
            self.is_sequential_downloading = False

    def fetch_episode_stream_details_for_download(self, episode_data, download_row):
        """Fetch stream details for a specific episode and start its download"""
        try:
            content_id = episode_data.get('contentId')
            streams = episode_data.get('streams', [])

            print(f"📋 EPISODE STREAMS DEBUG:")
            print(f"📋 Episode Number: {episode_data.get('episodeNumber')}")
            print(f"📋 Content ID: {content_id}")
            print(f"📋 Total Streams: {len(streams)}")

            # Print detailed stream information like the original script
            if streams:
                print(f"📋 [+] Available Streams for the Selected Episode:")
                print(f"📋 {'Index':<8} {'Stream ID':<12} {'Resolution':<20} {'HDR':<8} {'Manifest Type'}")
                print(f"📋 {'-'*70}")

                for i, stream in enumerate(streams, 1):
                    stream_id = stream.get('streamId', 'N/A')
                    resolution = stream.get('highestImageResolution', 'N/A')
                    hdr = 'Yes' if stream.get('hdr', False) else 'No'
                    manifest_type = stream.get('manifestType', 'N/A')

                    print(f"📋 {i:<8} {stream_id:<12} {resolution:<20} {hdr:<8} {manifest_type}")

                print(f"📋 {'-'*70}")

            if not streams:
                print(f"❌ No streams found for episode {episode_data.get('episodeNumber')}")
                return

            # Get the first DASH stream (same logic as original)
            selected_stream = None
            for stream in streams:
                if stream.get('manifestType') == 'MANIFEST_TYPE_DASH':
                    selected_stream = stream
                    break

            if not selected_stream:
                print(f"❌ No DASH stream found for episode {episode_data.get('episodeNumber')}")
                return

            stream_id = selected_stream.get('streamId')

            print(f"📡 Fetching stream details for Episode {episode_data.get('episodeNumber')} - Content ID: {content_id}, Stream ID: {stream_id}")

            # Get access token
            access_token = self.main_window.osn_api.get_access_token()
            if not access_token:
                print("❌ No access token available")
                return

            # Use OSN DRM module to get stream details
            print(f"🔄 Calling get_stream_details with Content ID: {content_id}, Stream ID: {stream_id}")
            drm_info = self.main_window.osn_drm.get_stream_details(content_id, stream_id, access_token)

            if drm_info:
                print(f"✅ Stream details fetched for Episode {episode_data.get('episodeNumber')}")

                # Parse MPD and start download for this episode
                self.parse_mpd_and_start_episode_download(drm_info, episode_data, selected_stream, download_row)
            else:
                print(f"❌ Failed to fetch stream details for episode {episode_data.get('episodeNumber')}")
                return

        except Exception as e:
            print(f"❌ Error fetching episode stream details: {str(e)}")
            return

    def move_to_next_episode(self):
        """DISABLED - osn_downloader.py handles the queue automatically"""
        print("⚠️ move_to_next_episode called but disabled - osn_downloader.py handles queue")
        return

    def parse_mpd_and_start_episode_download(self, drm_info, episode_data, stream_data, download_row):
        """Parse MPD and start download for a specific episode"""
        try:
            mpd_url = drm_info.get('mpd_url')
            license_url = drm_info.get('license_url')
            drm_token = drm_info.get('drm_token')

            if not mpd_url:
                print(f"❌ No MPD URL for episode {episode_data.get('episodeNumber')}")
                return

            print(f"📺 Parsing MPD for Episode {episode_data.get('episodeNumber')}: {mpd_url}")

            # Parse MPD to get qualities, audio tracks, and subtitles
            mpd_data = self.parse_mpd_file(mpd_url)

            if not mpd_data:
                print(f"❌ Failed to parse MPD for episode {episode_data.get('episodeNumber')}")
                return

            # Extract PSSH and KID for DRM
            pssh = None
            keys = []

            if license_url and drm_token:
                try:
                    pssh = self.main_window.osn_drm.get_pssh_from_mpd(mpd_url)

                    if pssh:
                        # Extract DRM keys using pywidevine
                        keys = self.extract_drm_keys(license_url, pssh, drm_token)
                        print(f"🔐 DRM keys extracted for Episode {episode_data.get('episodeNumber')}: {len(keys)} keys")
                        # Save keys immediately
                        if keys:
                            episode_title = episode_data.get('title', {}).get('en', f"Episode {episode_data.get('episodeNumber')}")
                            self.save_keys_to_file_direct(keys, episode_title)
                    else:
                        keys = []
                        print(f"⚠️ No PSSH found in MPD for episode {episode_data.get('episodeNumber')}")

                except Exception as e:
                    print(f"❌ Error extracting DRM info for episode {episode_data.get('episodeNumber')}: {str(e)}")

            # Create detailed info for this episode
            detailed_info = {
                'content_data': episode_data,
                'stream_data': stream_data,
                'mpd_url': mpd_url,
                'license_url': license_url,
                'drm_token': drm_token,
                'pssh': pssh,
                'keys': keys,
                'mpd_data': mpd_data
            }

            # Use the same preferences as selected for the first episode
            settings = self.multi_episode_settings
            quality_preference = settings.get('quality_preference', {})
            audio_preferences = settings.get('audio_preferences', [])
            subtitle_preferences = settings.get('subtitle_preferences', [])

            # Update the download item with complete details
            if download_row < len(self.download_items):
                self.download_items[download_row]['detailed_info'] = detailed_info
                self.download_items[download_row]['quality_preference'] = quality_preference
                self.download_items[download_row]['audio_preferences'] = audio_preferences
                self.download_items[download_row]['subtitle_preferences'] = subtitle_preferences

                # Update status to show we're starting
                status_item = self.downloads_table.item(download_row, 5)
                if status_item:
                    status_item.setText("Starting...")

                print(f"🚀 Starting download for Episode {episode_data.get('episodeNumber')}")

                # Track the active download row
                self.active_download_row = download_row
                # DON'T update current_download_row here to avoid interfering with active downloads

                # Start download for ANY episode (not just the first one)
                if True:  # Always start download for any episode
                    print(f"🚀 Starting download for FIRST episode: {episode_data.get('episodeNumber')}")

                    # Get required data for download
                    content_data = detailed_info.get('content_data', {})
                    mpd_url = detailed_info.get('mpd_url')

                    # Get the actual DRM keys (list of strings like "kid:key")
                    actual_keys = detailed_info.get('keys', [])
                    print(f"🔑 DEBUG: actual_keys from detailed_info: {actual_keys}")
                    print(f"🔑 DEBUG: type of actual_keys: {type(actual_keys)}")

                    # The keys should already be a list of strings like "kid:key" from extract_drm_keys
                    # But if they're not, we need to handle this properly
                    final_keys = []
                    if isinstance(actual_keys, list) and actual_keys:
                        # Check if the list contains actual key strings (not placeholders)
                        for key in actual_keys:
                            if isinstance(key, str) and ':' in key and 'placeholder' not in key:
                                final_keys.append(key)
                        print(f"🔑 DEBUG: Filtered final_keys: {final_keys}")
                    else:
                        print(f"🔑 DEBUG: No valid keys found or wrong format")

                    drm_info = {
                        'license_url': detailed_info.get('license_url'),
                        'drm_token': detailed_info.get('drm_token'),
                        'pssh': detailed_info.get('pssh'),
                        'kid': detailed_info.get('kid'),
                        'keys': final_keys,  # Use the filtered keys list
                        'formatted_key': self.format_drm_keys(final_keys)
                    }

                    # Get series data
                    series_data = {
                        'title': {'en': self.current_series_info.get('title', {}).get('en', 'Unknown Series') if hasattr(self, 'current_series_info') else 'Unknown Series'}
                    }

                    # Use the same preferences as selected for the first episode
                    settings = self.multi_episode_settings
                    quality_preference = settings.get('quality_preference', {})
                    audio_preferences = settings.get('audio_preferences', [])
                    subtitle_preferences = settings.get('subtitle_preferences', [])

                    print(f"🎯 Using preferences - Quality: {quality_preference.get('quality_label', 'Unknown')}, Audio: {[a.get('language') for a in audio_preferences]}, Subtitles: {subtitle_preferences}")

                    # Get MPD data for this episode to find matching tracks
                    mpd_data = detailed_info.get('mpd_data', {})
                    print(f"🔍 MPD data keys: {list(mpd_data.keys()) if mpd_data else 'None'}")
                    available_audio_tracks = mpd_data.get('audio_tracks', [])
                    available_subtitle_tracks = mpd_data.get('subtitle_tracks', [])
                    available_video_qualities = mpd_data.get('qualities', [])  # Fixed: use 'qualities' not 'video_qualities'
                    print(f"🔍 Extracted from MPD - Qualities: {len(available_video_qualities)}, Audio: {len(available_audio_tracks)}, Subtitles: {len(available_subtitle_tracks)}")

                    # Find matching quality based on resolution preference
                    selected_quality = None
                    target_resolution = quality_preference.get('resolution')
                    target_quality_label = quality_preference.get('quality_label')

                    print(f"🔍 Looking for quality - Resolution: {target_resolution}, Label: {target_quality_label}")
                    print(f"🔍 Available qualities: {len(available_video_qualities)}")

                    # Print available qualities for debugging
                    for i, quality in enumerate(available_video_qualities):
                        print(f"🔍   Quality {i}: {quality.get('quality_label', 'Unknown')} - {quality.get('resolution')} - UUID: {quality.get('uuid', quality.get('id', 'No UUID'))}")

                    # Strategy 1: Try exact resolution match first (most reliable)
                    if target_resolution:
                        for quality in available_video_qualities:
                            if quality.get('resolution') == target_resolution:
                                selected_quality = quality
                                print(f"📺 ✅ Found EXACT resolution match: {quality.get('quality_label', 'Unknown')} ({quality.get('resolution')}) - UUID: {quality.get('uuid', quality.get('id', 'No UUID'))}")
                                break

                    # Strategy 2: Try quality label match (e.g., "360p")
                    if not selected_quality and target_quality_label:
                        for quality in available_video_qualities:
                            if quality.get('quality_label') == target_quality_label:
                                selected_quality = quality
                                print(f"📺 ✅ Found quality label match: {quality.get('quality_label', 'Unknown')} ({quality.get('resolution')}) - UUID: {quality.get('uuid', quality.get('id', 'No UUID'))}")
                                break

                    # Strategy 3: Try to find by height match (e.g., 640x360 matches 360p)
                    if not selected_quality and target_quality_label:
                        try:
                            # Extract height from quality label (e.g., "360p" -> "360")
                            target_height = target_quality_label.replace('p', '').replace('K', '').replace('4', '2160').replace('1440', '1440').replace('1080', '1080').replace('720', '720').replace('480', '480').replace('360', '360')

                            for quality in available_video_qualities:
                                quality_resolution = quality.get('resolution', '')
                                if 'x' in str(quality_resolution):
                                    width, height = str(quality_resolution).split('x')
                                    if height == target_height:
                                        selected_quality = quality
                                        print(f"📺 ✅ Found height match: {quality.get('quality_label', 'Unknown')} ({quality.get('resolution')}) - UUID: {quality.get('uuid', quality.get('id', 'No UUID'))}")
                                        break
                        except Exception as e:
                            print(f"⚠️ Error in height matching: {e}")

                    # Strategy 4: Use first available quality as fallback
                    if not selected_quality and available_video_qualities:
                        selected_quality = available_video_qualities[0]
                        print(f"📺 ⚠️ Using FALLBACK quality: {selected_quality.get('quality_label', 'Unknown')} ({selected_quality.get('resolution')}) - UUID: {selected_quality.get('uuid', selected_quality.get('id', 'No UUID'))}")

                    # Final check
                    if not selected_quality:
                        print(f"❌ No quality found! This should not happen.")
                        return

                    # Find matching audio tracks based on language preferences
                    audio_tracks = []
                    for audio_pref in audio_preferences:
                        target_language = audio_pref.get('language', 'en')
                        target_codec = audio_pref.get('codec', 'mp4a.40.2')

                        # Find matching audio track in this episode's MPD
                        best_match = None
                        for available_track in available_audio_tracks:
                            if available_track.get('language') == target_language:
                                # Prefer exact codec match
                                if available_track.get('codec') == target_codec:
                                    best_match = available_track
                                    break
                                # Otherwise, use any track with same language
                                elif not best_match:
                                    best_match = available_track

                        if best_match:
                            audio_tracks.append(best_match)
                            print(f"🎵 Found matching audio track: {best_match.get('language')} - {best_match.get('codec', 'Unknown')} (ID: {best_match.get('id', 'Unknown')})")

                    # Find matching subtitle tracks based on language preferences
                    subtitle_tracks = []
                    for target_language in subtitle_preferences:
                        # Find matching subtitle track in this episode's MPD
                        for available_track in available_subtitle_tracks:
                            if available_track.get('language') == target_language:
                                subtitle_tracks.append(target_language)
                                print(f"📝 Found matching subtitle track: {target_language}")
                                break

                    # If no audio tracks found, use the first available one
                    if not audio_tracks and available_audio_tracks:
                        audio_tracks.append(available_audio_tracks[0])
                        print(f"🎵 Using fallback audio track: {available_audio_tracks[0].get('language')} - {available_audio_tracks[0].get('codec', 'Unknown')}")

                    print(f"🎯 Final selection - Quality: {selected_quality.get('quality_label', 'Unknown') if selected_quality else 'None'}, Audio: {len(audio_tracks)}, Subtitles: {len(subtitle_tracks)}")

                    # Start the actual download using OSNDownloader
                    self.main_window.osn_downloader.download_episode(
                        mpd_url=mpd_url,
                        series_data=series_data,
                        episode_data=episode_data,
                        selected_quality=selected_quality,
                        audio_tracks=audio_tracks,
                        subtitle_tracks=subtitle_tracks,
                        drm_info=drm_info
                    )
                    print(f"📺 Started episode download: {series_data.get('title', {}).get('en', 'Unknown')} S{episode_data.get('seasonNumber')}E{episode_data.get('episodeNumber')}")
            else:
                print(f"❌ Invalid download row {download_row}")
                return

        except Exception as e:
            print(f"❌ Error parsing MPD and starting episode download: {str(e)}")
            return

    def start_individual_download(self, row):
        """DISABLED - Causes multiple downloads. Use sequential download only."""
        print(f"⚠️ start_individual_download called for row {row} but DISABLED to prevent multiple downloads")
        print(f"🔄 Use 'Start Downloads (Sequential)' button instead")
        return

    def format_drm_keys(self, keys):
        """Format DRM keys for N_m3u8DL-RE"""
        try:
            if not keys:
                return None

            # Format keys as expected by N_m3u8DL-RE
            formatted_keys = []
            for key in keys:
                if isinstance(key, str) and ':' in key:
                    formatted_keys.append(key)

            return formatted_keys[0] if formatted_keys else None

        except Exception as e:
            print(f"❌ Error formatting DRM keys: {str(e)}")
            return None

    def connect_download_progress_signals(self, row):
        """DISABLED - Progress system removed to prevent multiple downloads"""
        print(f"⚠️ Progress signals connection DISABLED for row {row}")
        return

    def handle_download_progress_signal(self, percentage, message):
        """Handle download progress updates for sequential downloads"""
        try:
            print(f"📊 Progress signal: {percentage}% - {message}")

            # Update progress bar for current episode in sequential download
            if hasattr(self, 'is_sequential_downloading') and self.is_sequential_downloading:
                self.update_progress_bar(percentage, message)

        except Exception as e:
            print(f"❌ Error handling progress signal: {str(e)}")

    def mark_episode_completed(self, episode_number):
        """Mark specific episode as completed in downloads table"""
        try:
            if hasattr(self, 'downloads_table') and self.downloads_table.rowCount() > 0:
                # Find the row for this episode number
                target_row = episode_number - 1  # Episode numbers start from 1, rows start from 0

                if target_row < self.downloads_table.rowCount():
                    # Update progress bar to 100% (Column 3 in new design)
                    progress_bar = self.downloads_table.cellWidget(target_row, 3)
                    if progress_bar:
                        progress_bar.setValue(100)
                        print(f"✅ Set episode {episode_number} progress to 100%")

                    # Update status to "Completed" (Column 4 in new design)
                    status_item = self.downloads_table.item(target_row, 4)
                    if status_item:
                        status_item.setText("✅ Done")
                        print(f"✅ Set episode {episode_number} status to 'Completed'")
                        # Play completion sound when episode is completed
                        self.play_completion_sound()

        except Exception as e:
            print(f"❌ Error marking episode {episode_number} as completed: {str(e)}")

    def mark_episode_failed(self, episode_number):
        """Mark specific episode as failed in downloads table"""
        try:
            if hasattr(self, 'downloads_table') and self.downloads_table.rowCount() > 0:
                # Find the row for this episode number
                target_row = episode_number - 1  # Episode numbers start from 1, rows start from 0

                if target_row < self.downloads_table.rowCount():
                    # Update status to "Failed"
                    status_item = self.downloads_table.item(target_row, 5)
                    if status_item:
                        status_item.setText("Failed")
                        print(f"❌ Set episode {episode_number} status to 'Failed'")

        except Exception as e:
            print(f"❌ Error marking episode {episode_number} as failed: {str(e)}")

    def handle_download_completion_signal(self, file_path, success):
        """Handle download completion for sequential downloads"""
        try:
            print(f"✅ Download completion signal: {success} - {file_path}")

            # For sequential downloads, this will be handled by episode_completed signal
            if hasattr(self, 'is_sequential_downloading') and self.is_sequential_downloading:
                print(f"📺 Sequential download completion handled by episode_completed signal")
            else:
                # For single downloads, update the progress bar to 100%
                self.update_progress_bar(100, "✅ Download completed!")
                # Play completion sound for single downloads
                self.play_completion_sound()

        except Exception as e:
            print(f"❌ Error handling completion signal: {str(e)}")

    def handle_download_error_signal(self, error_msg):
        """DISABLED - Progress system removed to prevent multiple downloads"""
        print(f"⚠️ Error signal DISABLED: {error_msg}")
        return

    def handle_video_progress_signal(self, percentage):
        """DISABLED - Progress system removed to prevent multiple downloads"""
        print(f"⚠️ Video progress signal DISABLED: {percentage}%")
        return

    def handle_status_update_signal(self, status):
        """DISABLED - Progress system removed to prevent multiple downloads"""
        print(f"⚠️ Status update signal DISABLED: {status}")
        return

    def update_download_progress(self, row, percentage, message):
        """DISABLED - Progress system removed to prevent multiple downloads"""
        print(f"⚠️ Progress update DISABLED for row {row}: {percentage}% - {message}")
        return

    def handle_download_completion(self, row, file_path, success):
        """DISABLED - Progress system removed to prevent multiple downloads"""
        print(f"⚠️ Download completion DISABLED for row {row}: {success} - {file_path}")
        return

    def handle_download_error(self, row, error_message):
        """DISABLED - Progress system removed to prevent multiple downloads"""
        print(f"⚠️ Download error DISABLED for row {row}: {error_message}")
        return



    def remove_download(self, row):
        """Remove download from table"""
        try:
            # Remove from table
            self.downloads_table.removeRow(row)

            # Remove from download items list
            if row < len(self.download_items):
                self.download_items.pop(row)

            print(f"🗑️ Removed download from row {row}")

        except Exception as e:
            print(f"❌ Error removing download: {str(e)}")

    def start_all_downloads(self):
        """Start all queued downloads - supports sequential download for multiple episodes"""
        try:
            queued_count = 0
            for i, item in enumerate(self.download_items):
                if item['status'] == 'Queued':
                    queued_count += 1

            if queued_count == 0:
                self.show_message("Info", "No queued downloads to start")
                return

            # Check if we have multiple episodes that need sequential download
            if hasattr(self, 'multi_episode_settings') and self.multi_episode_settings and queued_count > 1:
                # Start sequential download for multiple episodes - ONLY use sequential method
                print(f"🚀 Starting sequential download for {queued_count} episodes")
                self.start_sequential_download()
                self.show_message("Info", f"Started sequential download for {queued_count} episodes")
            elif queued_count == 1:
                # For single download, check if it's a movie or episode
                print(f"🚀 Single download detected")

                # Find the single queued item
                single_item = None
                for item in self.download_items:
                    if item['status'] == 'Queued':
                        single_item = item
                        break

                if single_item:
                    # Check if it's a movie or episode
                    content_data = single_item.get('content_data', {})
                    is_movie = 'episodeNumber' not in content_data

                    if is_movie:
                        print(f"🎬 Starting single movie download")
                        # Start movie download using the stored information
                        self.start_movie_download_from_stored_item(single_item)
                        self.show_message("Info", f"Movie download started")
                    else:
                        print(f"📺 Single episode download - using direct method")
                        self.start_single_episode_download(single_item)
                        self.show_message("Info", f"Episode download started")
                else:
                    print(f"❌ No queued item found")
                    self.show_message("Error", "No queued downloads found")
            else:
                # Multiple downloads but no multi_episode_settings - force sequential
                print(f"🚀 Multiple downloads detected, forcing sequential download")
                self.start_sequential_download()
                self.show_message("Info", f"Started sequential download for {queued_count} downloads")

        except Exception as e:
            print(f"❌ Error starting all downloads: {str(e)}")

    def start_single_episode_download(self, download_item):
        """Start download for a single episode"""
        try:
            print(f"📺 Starting single episode download...")

            # Extract stored information from download item
            content_data = download_item.get('content_data', {})

            # Get the detailed info that was stored when the item was added
            detailed_info = {
                'content_data': content_data,
                'mpd_url': download_item.get('mpd_url'),
                'license_url': download_item.get('license_url'),
                'drm_token': download_item.get('drm_token'),
                'keys': download_item.get('keys', []),
                'pssh': download_item.get('pssh'),
                'kid': download_item.get('kid')
            }

            # Get stored quality and audio selections
            selected_quality = download_item.get('selected_quality')
            selected_audio = download_item.get('selected_audio', [])
            selected_subtitles = download_item.get('selected_subtitles', [])

            # Start the episode download
            self.start_episode_download_from_table(detailed_info, selected_quality, selected_audio, selected_subtitles)

        except Exception as e:
            print(f"❌ Error starting single episode download: {str(e)}")
            self.show_message("Error", f"Failed to start episode download: {str(e)}")

    def clear_completed_downloads(self):
        """Clear completed downloads with modern feedback"""
        try:
            completed_count = 0
            # Remove completed items from back to front to avoid index issues
            for i in range(len(self.download_items) - 1, -1, -1):
                if self.download_items[i]['status'] in ['Completed', '✅ Done']:
                    self.downloads_table.removeRow(i)
                    self.download_items.pop(i)
                    completed_count += 1

            if completed_count > 0:
                self.show_message("Success", f"🧹 Cleared {completed_count} completed downloads")
                # Update progress label
                self.update_downloads_status()
            else:
                self.show_message("Info", "No completed downloads to clear")

        except Exception as e:
            print(f"❌ Error clearing completed downloads: {str(e)}")

    def clear_all_downloads(self):
        """Clear all downloads with confirmation"""
        try:
            if len(self.download_items) == 0:
                self.show_message("Info", "No downloads to clear")
                return

            total_count = len(self.download_items)

            # Clear table
            self.downloads_table.setRowCount(0)

            # Clear download items
            self.download_items.clear()

            self.show_message("Success", f"🗑️ Cleared all {total_count} downloads")

            # Update progress label
            self.update_downloads_status()

        except Exception as e:
            print(f"❌ Error clearing all downloads: {str(e)}")

    def update_downloads_status(self):
        """Update downloads status in header"""
        try:
            if hasattr(self, 'overall_progress_label'):
                total = len(self.download_items)
                if total == 0:
                    self.overall_progress_label.setText("📥 Downloads: 0 active")
                else:
                    active = sum(1 for item in self.download_items
                               if item.get('status') in ['Downloading', 'Processing', '⏳ Queued'])
                    completed = sum(1 for item in self.download_items
                                  if item.get('status') in ['Completed', '✅ Done'])

                    if active > 0:
                        self.overall_progress_label.setText(f"📥 Downloads: {active} active")
                    else:
                        self.overall_progress_label.setText(f"📥 Downloads: {completed}/{total} completed")
        except Exception as e:
            print(f"❌ Error updating downloads status: {str(e)}")

    def start_movie_download(self, selected_stream):
        """Start movie download"""
        try:
            content_id = self.current_content['id']
            stream_id = selected_stream.get('streamId')

            # Get access token
            access_token = self.main_window.osn_api.get_access_token()
            if not access_token:
                self.show_message("Error", "No access token available. Please login first.")
                return

            # Process DRM and start download
            drm_info = self.main_window.osn_drm.process_content_drm(content_id, stream_id, access_token)

            # Start download
            self.main_window.osn_downloader.download_movie(
                mpd_url=drm_info.get('mpd_url') if drm_info else None,
                movie_data=self.current_content['data'],
                selected_quality={'resolution': '720p', 'uuid': 'placeholder'},  # This should be parsed from stream
                drm_info=drm_info
            )

            self.status_updated.emit("Movie download started...")

        except Exception as e:
            self.show_message("Error", f"Failed to start movie download: {str(e)}")

    def start_series_download(self, selected_stream):
        """Start series download (placeholder)"""
        self.show_message("Info", "Series download functionality will be implemented")

    def toggle_episode_range(self, checked):
        """Toggle episode range inputs based on download all checkbox"""
        try:
            # Enable/disable range inputs based on checkbox
            self.episode_from_input.setEnabled(not checked)
            self.episode_to_input.setEnabled(not checked)
            self.episode_range_label.setEnabled(not checked)
            self.episode_to_label.setEnabled(not checked)

            print(f"📋 Download all episodes: {checked}")

        except Exception as e:
            print(f"❌ Error toggling episode range: {str(e)}")

    def populate_episode_range_options(self, episodes):
        """Populate episode range dropdowns with available episodes"""
        try:
            self.episode_from_input.clear()
            self.episode_to_input.clear()

            for i, episode in enumerate(episodes, 1):
                episode_number = episode.get('episodeNumber', i)
                episode_title = episode.get('title', {}).get('en', f'Episode {i}')
                display_text = f"{episode_number}: {episode_title[:30]}..."

                self.episode_from_input.addItem(display_text, episode_number)
                self.episode_to_input.addItem(display_text, episode_number)

            # Set default selection (from 1 to last episode)
            if episodes:
                self.episode_from_input.setCurrentIndex(0)
                self.episode_to_input.setCurrentIndex(len(episodes) - 1)

            print(f"✅ Populated episode range with {len(episodes)} episodes")

        except Exception as e:
            print(f"❌ Error populating episode range: {str(e)}")

    def populate_batch_quality_options(self, streams):
        """Populate batch quality dropdown with available streams"""
        try:
            self.batch_quality_combo.clear()

            # Filter unique qualities from streams
            unique_qualities = {}
            for stream in streams:
                if stream.get('manifestType') == 'MANIFEST_TYPE_DASH':
                    resolution = stream.get('highestImageResolution', 'Unknown')
                    stream_id = stream.get('streamId', '')

                    if resolution not in unique_qualities:
                        unique_qualities[resolution] = stream

            # Add quality options
            for resolution, stream in unique_qualities.items():
                display_text = f"{resolution}"
                self.batch_quality_combo.addItem(display_text, stream)

            # Set default to first quality
            if unique_qualities:
                self.batch_quality_combo.setCurrentIndex(0)

            print(f"✅ Populated batch quality with {len(unique_qualities)} options")

        except Exception as e:
            print(f"❌ Error populating batch quality: {str(e)}")

    def populate_batch_audio_options(self, mpd_data):
        """Populate batch audio dropdown with available audio tracks"""
        try:
            self.batch_audio_combo.clear()

            if mpd_data and 'audio_tracks' in mpd_data:
                audio_tracks = mpd_data['audio_tracks']

                # Add audio options
                for track in audio_tracks:
                    self.batch_audio_combo.addItem(track, track)

                # Set default to first audio track
                if audio_tracks:
                    self.batch_audio_combo.setCurrentIndex(0)

                print(f"✅ Populated batch audio with {len(audio_tracks)} options")
            else:
                # Add default options
                self.batch_audio_combo.addItem("Arabic", "ar")
                self.batch_audio_combo.addItem("English", "en")
                self.batch_audio_combo.setCurrentIndex(0)

        except Exception as e:
            print(f"❌ Error populating batch audio: {str(e)}")

    def populate_batch_subtitle_options(self, mpd_data):
        """Populate batch subtitle dropdown with available subtitle tracks"""
        try:
            self.batch_subtitle_combo.clear()

            # Add "No Subtitles" option
            self.batch_subtitle_combo.addItem("No Subtitles", "none")

            if mpd_data and 'subtitle_tracks' in mpd_data:
                subtitle_tracks = mpd_data['subtitle_tracks']

                # Add subtitle options
                for track in subtitle_tracks:
                    self.batch_subtitle_combo.addItem(track, track)

                print(f"✅ Populated batch subtitles with {len(subtitle_tracks)} options")
            else:
                # Add default options
                self.batch_subtitle_combo.addItem("Arabic", "ar")
                self.batch_subtitle_combo.addItem("English", "en")

        except Exception as e:
            print(f"❌ Error populating batch subtitles: {str(e)}")

    def start_batch_download(self):
        """Start batch download of selected episodes - DISABLED to prevent confusion with sequential download"""
        try:
            # REDIRECT to sequential download to prevent confusion
            self.show_message("Info", "Please use 'Start Downloads (Sequential)' button in the Downloads tab for proper sequential downloading.")
            return
            if not self.current_episodes_list:
                self.show_message("Error", "No episodes available for batch download")
                return

            # Get selected range
            download_all = self.download_all_episodes_cb.isChecked()

            if download_all:
                # Download all episodes
                episodes_to_download = self.current_episodes_list.copy()
                print(f"📥 Starting batch download for ALL {len(episodes_to_download)} episodes")
            else:
                # Download selected range
                from_index = self.episode_from_input.currentIndex()
                to_index = self.episode_to_input.currentIndex()

                if from_index > to_index:
                    self.show_message("Error", "From episode cannot be greater than To episode")
                    return

                episodes_to_download = self.current_episodes_list[from_index:to_index + 1]
                print(f"📥 Starting batch download for episodes {from_index + 1} to {to_index + 1} ({len(episodes_to_download)} episodes)")

            # Get selected quality, audio, and subtitle settings
            selected_quality = self.batch_quality_combo.currentData()
            selected_audio = self.batch_audio_combo.currentData()
            selected_subtitle = self.batch_subtitle_combo.currentData()

            if not selected_quality:
                self.show_message("Error", "Please select a quality for batch download")
                return

            # Store batch settings
            self.batch_download_settings = {
                'quality': selected_quality,
                'audio': selected_audio,
                'subtitle': selected_subtitle,
                'stream_data': None  # Will be populated for each episode
            }

            # Prepare download queue
            self.batch_download_queue = episodes_to_download.copy()
            self.current_batch_index = 0
            self.is_batch_downloading = True

            # Disable batch download button
            self.start_batch_download_btn.setEnabled(False)
            self.start_batch_download_btn.setText("Downloading...")

            # Start downloading first episode
            self.download_next_episode_in_batch()

        except Exception as e:
            print(f"❌ Error starting batch download: {str(e)}")
            self.show_message("Error", f"Failed to start batch download: {str(e)}")

    def download_next_episode_in_batch(self):
        """DISABLED - Use sequential download instead"""
        self.show_message("Info", "Please use 'Start Downloads (Sequential)' button in the Downloads tab for proper sequential downloading.")
        return

    def get_episode_stream_and_download(self, episode_data):
        """DISABLED - Use sequential download instead"""
        self.show_message("Info", "Please use 'Start Downloads (Sequential)' button in the Downloads tab for proper sequential downloading.")
        return

    def download_single_episode_in_batch(self, episode_data, stream_data):
        """DISABLED - Use sequential download instead"""
        self.show_message("Info", "Please use 'Start Downloads (Sequential)' button in the Downloads tab for proper sequential downloading.")
        return

    def handle_batch_download_error(self, error_message):
        """DISABLED - Use sequential download instead"""
        self.show_message("Info", "Please use 'Start Downloads (Sequential)' button in the Downloads tab for proper sequential downloading.")
        return

    def finish_batch_download(self):
        """DISABLED - Use sequential download instead"""
        self.show_message("Info", "Please use 'Start Downloads (Sequential)' button in the Downloads tab for proper sequential downloading.")
        return

    def handle_episode_download_completed(self, file_path, success):
        """DISABLED - Use sequential download instead"""
        self.show_message("Info", "Please use 'Start Downloads (Sequential)' button in the Downloads tab for proper sequential downloading.")
        return

    def show_batch_download_options_for_series(self, series_data, episodes):
        """Show batch download options when series episodes are loaded"""
        try:
            # Store current episodes and series data
            self.current_episodes_data = episodes  # Store episode data list
            self.current_season_data = series_data

            # DON'T show batch download group to prevent confusion with sequential download
            # self.batch_download_group.setVisible(True)  # DISABLED to prevent confusion

            # Populate episode range options
            self.populate_episode_range_options(episodes)

            # Get first episode streams to populate quality options
            if episodes and len(episodes) > 0:
                first_episode = episodes[0]
                streams = first_episode.get('streams', [])

                if streams:
                    # Populate quality options
                    self.populate_batch_quality_options(streams)

                    # Get MPD data for audio/subtitle options (use first stream)
                    first_stream = None
                    for stream in streams:
                        if stream.get('manifestType') == 'MANIFEST_TYPE_DASH':
                            first_stream = stream
                            break

                    if first_stream:
                        # Try to get MPD data for audio/subtitle options
                        try:
                            # This is a simplified approach - in real implementation,
                            # you might want to fetch actual MPD data
                            mpd_data = {
                                'audio_tracks': ['ar', 'en'],  # Default options
                                'subtitle_tracks': ['ar', 'en']  # Default options
                            }

                            self.populate_batch_audio_options(mpd_data)
                            self.populate_batch_subtitle_options(mpd_data)

                        except Exception as e:
                            print(f"⚠️ Could not get MPD data, using defaults: {str(e)}")
                            # Use default options
                            self.populate_batch_audio_options(None)
                            self.populate_batch_subtitle_options(None)

            # Enable batch download button
            self.start_batch_download_btn.setEnabled(True)

            print(f"✅ Batch download options shown for {len(episodes)} episodes")

        except Exception as e:
            print(f"❌ Error showing batch download options: {str(e)}")

    def hide_batch_download_options(self):
        """Hide batch download options (for movies or when no content)"""
        try:
            self.batch_download_group.setVisible(False)
            self.current_episodes_list_widget = None
            self.current_episodes_data = []
            self.current_season_data = None

        except Exception as e:
            print(f"❌ Error hiding batch download options: {str(e)}")

    def open_player(self, stream_data):
        """Open video player with stream data"""
        try:
            print(f"🎬 Opening player for stream: {stream_data.get('streamId', 'Unknown')}")

            # Get content info - could be episode or movie
            if hasattr(self, 'current_selected_episode') and self.current_selected_episode:
                # For episodes
                content_data = self.current_selected_episode
                content_id = content_data.get('contentId')
                content_type = 'episode'
            elif hasattr(self, 'current_content') and self.current_content:
                # For movies
                content_data = self.current_content.get('data', {})
                content_id = self.current_content.get('id')
                content_type = 'movie'
            else:
                self.show_message("Error", "No content selected")
                return

            stream_id = stream_data.get('streamId')

            print(f"📡 Getting stream details for player - Content ID: {content_id}, Stream ID: {stream_id}")

            # Get access token
            access_token = self.main_window.osn_api.get_access_token()
            if not access_token:
                self.show_message("Error", "No access token available. Please login first.")
                return

            # Get stream details including MPD URL and DRM info
            drm_info = self.main_window.osn_drm.get_stream_details(content_id, stream_id, access_token)

            if not drm_info:
                self.show_message("Error", "Failed to get stream details")
                return

            mpd_url = drm_info.get('mpd_url')
            license_url = drm_info.get('license_url')
            drm_token = drm_info.get('drm_token')

            if not mpd_url:
                self.show_message("Error", "MPD URL not available")
                return

            print(f"✅ Stream details obtained:")
            print(f"📺 MPD URL: {mpd_url}")
            print(f"🔐 License URL: {license_url}")
            print(f"🎫 DRM Token: {drm_token}")

            # Extract PSSH and KID for DRM
            pssh = None
            kid = None
            keys = []

            if license_url and drm_token:
                try:
                    pssh = self.main_window.osn_drm.get_pssh_from_mpd(mpd_url)
                    kid = self.main_window.osn_drm.get_kid_from_mpd(mpd_url)

                    if pssh:
                        # Extract DRM keys
                        keys = self.extract_drm_keys(license_url, pssh, drm_token)
                        print(f"🔓 Extracted {len(keys)} DRM keys for player")
                        # Save keys immediately
                        if keys:
                            content_title = content_data.get('title', {}).get('en', 'OSN Player Content')
                            self.save_keys_to_file_direct(keys, content_title)

                except Exception as e:
                    print(f"⚠️ Error extracting DRM info: {str(e)}")

            # Launch OSN+ Player
            self.launch_osn_player(mpd_url, keys, content_data, content_type)

        except Exception as e:
            print(f"❌ Error opening player: {str(e)}")
            self.show_message("Error", f"Failed to open player: {str(e)}")

    def launch_osn_player(self, mpd_url, keys, content_data, content_type):
        """Launch OSN+ Player using the separate player module"""
        try:
            from .osn_player import play_osn_content

            # Launch player without showing success message
            success = play_osn_content(
                mpd_url=mpd_url,
                keys=keys,
                content_data=content_data,
                content_type=content_type,
                parent=self.main_window
            )

            if not success:
                self.show_message("Error", "Failed to start OSN+ Player")

        except Exception as e:
            print(f"❌ Error launching OSN+ player: {str(e)}")
            self.show_message("Error", f"Failed to launch OSN+ player: {str(e)}")

    def update_status_bar(self, message):
        """Update status bar with message"""
        if hasattr(self.widgets, 'status_label'):
            self.widgets.status_label.setText(message)

    def update_progress_bar(self, percentage, message):
        """Update progress bar - both general and downloads table"""
        # Update general progress bar if exists
        if hasattr(self.widgets, 'progress_bar'):
            self.widgets.progress_bar.setValue(percentage)

        # Update overall progress label in downloads tab with modern design
        if hasattr(self, 'overall_progress_label'):
            active_downloads = sum(1 for item in getattr(self, 'download_items', [])
                                 if item.get('status') in ['Downloading', 'Processing'])
            total_downloads = len(getattr(self, 'download_items', []))

            if active_downloads > 0:
                self.overall_progress_label.setText(f"📥 Downloads: {active_downloads} active • {percentage}%")
            elif total_downloads > 0:
                completed = sum(1 for item in getattr(self, 'download_items', [])
                              if item.get('status') == 'Completed')
                self.overall_progress_label.setText(f"📥 Downloads: {completed}/{total_downloads} completed")
            else:
                self.overall_progress_label.setText("📥 Downloads: 0 active")

        # Update the CURRENT EPISODE row in downloads table (for sequential downloads)
        if hasattr(self, 'downloads_table') and self.downloads_table.rowCount() > 0:
            current_row = None

            if hasattr(self, 'is_sequential_downloading') and self.is_sequential_downloading:
                # For sequential downloads, use the current_episode_index from the downloader
                if hasattr(self.main_window, 'osn_downloader'):
                    downloader_index = getattr(self.main_window.osn_downloader, 'current_episode_index', 0)
                    if downloader_index < self.downloads_table.rowCount():
                        current_row = downloader_index
                        print(f"🎯 Sequential download: Using downloader index {downloader_index} as current row")
                    else:
                        # Fallback: find first non-completed row
                        for row in range(self.downloads_table.rowCount()):
                            status_item = self.downloads_table.item(row, 5)
                            if status_item and status_item.text() != "Completed":
                                current_row = row
                                print(f"🎯 Sequential download: Found first non-completed row {row}")
                                break

                # If still no current row found, use first non-completed
                if current_row is None:
                    for row in range(self.downloads_table.rowCount()):
                        status_item = self.downloads_table.item(row, 5)
                        if status_item and status_item.text() != "Completed":
                            current_row = row
                            break
            else:
                # For single downloads, use the last row
                current_row = self.downloads_table.rowCount() - 1

            # Update progress for the current row (Column 3 in new design)
            if current_row is not None:
                progress_bar = self.downloads_table.cellWidget(current_row, 3)
                if progress_bar:
                    progress_bar.setValue(percentage)
                    print(f"🎯 Updated downloads table progress for row {current_row}: {percentage}%")

                # Update status for the current row (Column 4 in new design)
                status_item = self.downloads_table.item(current_row, 4)
                if status_item:
                    if percentage == 100:
                        status_item.setText("✅ Done")
                        # Play completion sound when download reaches 100%
                        self.play_completion_sound()
                    elif percentage > 0:
                        status_item.setText(f"📥 {percentage}%")
                    else:
                        status_item.setText("🔄 Starting")

        self.update_status_bar(message)
        print(f"🎯 UI Progress Updated: {percentage}% - {message}")

    def show_message(self, title, message):
        """Show message box"""
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.exec_()

    def handle_clear(self):
        """Handle clear button click"""
        try:
            # Clear URL input
            self.widgets.url_input.clear()

            # Clear recent URLs
            if hasattr(self.widgets, 'recent_combo'):
                self.widgets.recent_combo.clear()

            # Use the comprehensive clear function
            self.clear_content_data()

            print("🧹 All content data cleared via Clear button")

        except Exception as e:
            self.show_message("Error", f"Clear failed: {str(e)}")

    def handle_play(self):
        """Handle play button click"""
        try:
            if not self.current_content:
                self.show_message("Warning", "No content selected")
                return

            # Switch to Download Options tab
            self.content_tabs.setCurrentWidget(self.download_options_tab)

        except Exception as e:
            self.show_message("Error", f"Play failed: {str(e)}")



    def handle_recent_selection_by_index(self, index):
        """Handle recent selection by index (when user clicks on dropdown item)"""
        try:
            if hasattr(self.widgets, 'recent_combo') and index >= 0:
                selected_item = self.widgets.recent_combo.itemText(index)
                print(f"🔍 Selected by index {index}: {selected_item}")

                # Only proceed if this is a user-initiated selection, not automatic loading
                if hasattr(self, '_loading_recent_data') and self._loading_recent_data:
                    print("⏭️ Skipping auto-search during data loading")
                    return

                if selected_item and selected_item.strip():
                    # Check if the selected item contains ID and name (format: "ID - Name (Year)")
                    if " - " in selected_item:
                        # Extract just the ID part
                        content_id = selected_item.split(" - ")[0].strip()
                        self.widgets.url_input.setText(content_id)
                        print(f"✅ Selected from recent: {content_id}")

                        # Automatically trigger search
                        self.handle_search()
                    else:
                        # It's a regular URL or ID
                        self.widgets.url_input.setText(selected_item.strip())

                        # Automatically trigger search
                        self.handle_search()

        except Exception as e:
            print(f"❌ Error in recent selection by index: {str(e)}")
            self.show_message("Error", f"Recent selection failed: {str(e)}")





    def add_to_recent_urls(self, content_info):
        """Add content info to recent URLs list"""
        try:
            if not content_info or not content_info.strip():
                return

            # Skip placeholder text
            if content_info.startswith("Select from recent"):
                return

            if hasattr(self.widgets, 'recent_combo'):
                # Check if content info already exists and remove it first
                for i in range(self.widgets.recent_combo.count() - 1, -1, -1):
                    item_text = self.widgets.recent_combo.itemText(i)
                    if item_text == content_info or item_text.startswith("Select from recent"):
                        self.widgets.recent_combo.removeItem(i)

                # Add to top of list (after placeholder)
                self.widgets.recent_combo.insertItem(1, content_info)

                # Limit to 10 recent items (plus placeholder)
                while self.widgets.recent_combo.count() > 11:
                    self.widgets.recent_combo.removeItem(self.widgets.recent_combo.count() - 1)

                print(f"📝 Added to recent: {content_info}")

                # Save to file immediately
                self.save_recent_urls_to_file()

        except Exception as e:
            print(f"❌ Error adding to recent URLs: {str(e)}")

    def save_recent_urls_to_file(self):
        """Save recent URLs to file for persistence"""
        try:
            # Create config directory in YANGO root (3 levels up from modules/OSN/osn_ui.py)
            config_dir = os.path.join(os.path.dirname(__file__), "..", "..", "config")
            os.makedirs(config_dir, exist_ok=True)

            # Get all items from recent combo, excluding placeholder text
            recent_items = []
            if hasattr(self.widgets, 'recent_combo'):
                for i in range(self.widgets.recent_combo.count()):
                    item_text = self.widgets.recent_combo.itemText(i)
                    # Skip placeholder text and empty items
                    if (item_text.strip() and
                        not item_text.startswith("Select from recent") and
                        item_text not in recent_items):  # Avoid duplicates
                        recent_items.append(item_text.strip())

            # Save to JSON file
            config_file = os.path.join(config_dir, "osn_recent_urls.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(recent_items, f, ensure_ascii=False, indent=2)

            print(f"💾 Saved {len(recent_items)} recent URLs to {config_file}")

        except Exception as e:
            print(f"❌ Error saving recent URLs to file: {str(e)}")

    def load_recent_urls_from_file(self):
        """Load recent URLs from file"""
        try:
            # Set loading flag to prevent auto-search
            self._loading_recent_data = True

            config_file = os.path.join(os.path.dirname(__file__), "..", "..", "config", "osn_recent_urls.json")

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    recent_items = json.load(f)

                # Filter out placeholder text and duplicates
                filtered_items = []
                for item in recent_items:
                    if (item.strip() and
                        not item.startswith("Select from recent") and
                        item not in filtered_items):
                        filtered_items.append(item.strip())

                # Add items to recent combo (already made editable in setup_ui_connections)
                if hasattr(self.widgets, 'recent_combo'):
                    # Set placeholder text
                    self.widgets.recent_combo.lineEdit().setPlaceholderText("Search in saved content...")

                    # Clear and populate
                    self.widgets.recent_combo.clear()
                    # Add placeholder first
                    self.widgets.recent_combo.addItem("Select from recent searches...")

                    # Add filtered items
                    for item in filtered_items:
                        self.widgets.recent_combo.addItem(item)

                    print(f"📂 Loaded {len(filtered_items)} recent URLs from {config_file} (searchable)")

                    # Update autocomplete for main search
                    self.update_search_autocomplete()
                else:
                    print("📂 No recent URLs file found or empty")
            else:
                print("📂 No recent URLs file found")

        except Exception as e:
            print(f"❌ Error loading recent URLs from file: {str(e)}")
        finally:
            # Clear loading flag after a short delay to allow UI to settle
            if hasattr(self, '_loading_recent_data'):
                delattr(self, '_loading_recent_data')
            # Make sure dropdown is closed after loading
            if hasattr(self.widgets, 'recent_combo'):
                self.widgets.recent_combo.hidePopup()

    def populate_quality_options(self, streams):
        """Populate quality dropdown with available streams"""
        try:
            # This would populate quality options if we had a quality combo
            # For now, just store the streams
            self.current_qualities = streams
            print(f"✅ Found {len(streams)} quality options")

        except Exception as e:
            self.show_message("Error", f"Failed to populate quality options: {str(e)}")

    def add_episodes_tab(self):
        """Add episodes/seasons tab to content tabs"""
        try:
            if not hasattr(self.widgets, 'content_tabs'):
                return

            # Check if tab already exists
            for i in range(self.widgets.content_tabs.count()):
                if self.widgets.content_tabs.tabText(i) == "Episodes":
                    self.widgets.content_tabs.setCurrentIndex(i)
                    return

            # Create episodes tab (placeholder for now)
            from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel

            episodes_tab = QWidget()
            episodes_layout = QVBoxLayout(episodes_tab)

            episodes_label = QLabel("Episodes functionality will be implemented here")
            episodes_label.setStyleSheet("color: #ffffff; padding: 20px;")
            episodes_layout.addWidget(episodes_label)

            self.widgets.content_tabs.addTab(episodes_tab, "Episodes")
            self.widgets.content_tabs.setCurrentWidget(episodes_tab)

        except Exception as e:
            self.show_message("Error", f"Failed to add episodes tab: {str(e)}")

    def add_download_options_tab(self):
        """Add download options tab to content tabs"""
        try:
            if not hasattr(self.widgets, 'content_tabs'):
                return

            # Check if tab already exists
            for i in range(self.widgets.content_tabs.count()):
                if self.widgets.content_tabs.tabText(i) == "Download Options":
                    self.widgets.content_tabs.setCurrentIndex(i)
                    return

            # Create download options tab (placeholder for now)
            from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel

            download_tab = QWidget()
            download_layout = QVBoxLayout(download_tab)

            download_label = QLabel("Download options functionality will be implemented here")
            download_label.setStyleSheet("color: #ffffff; padding: 20px;")
            download_layout.addWidget(download_label)

            self.widgets.content_tabs.addTab(download_tab, "Download Options")
            self.widgets.content_tabs.setCurrentWidget(download_tab)

        except Exception as e:
            self.show_message("Error", f"Failed to add download options tab: {str(e)}")

    def handle_api_error(self, error_message):
        """Handle API errors and try alternative search if needed"""
        # Check if this is a silent fallback message
        is_silent = error_message.startswith("SILENT_FALLBACK:")
        original_error_message = error_message

        try:
            if is_silent:
                # Remove the silent prefix for processing
                error_message = error_message.replace("SILENT_FALLBACK:", "")
                # For silent fallbacks, don't show any error dialog - just handle the fallback logic
                print(f"🔇 Silent fallback triggered: {error_message}")

            # Check if this was a movie search that failed and we should try series
            if (hasattr(self, 'current_content_id') and
                hasattr(self, 'current_search_input') and
                ("This might be a series ID" in error_message or
                 "Movie not found" in error_message or
                 "Invalid series link" in error_message) and
                not hasattr(self, '_tried_series_fallback')):

                print(f"🔄 Movie search failed, trying as series for ID: {self.current_content_id}")
                self._tried_series_fallback = True  # Prevent infinite loop
                # Don't show error message yet, try series search first
                self.search_series(self.current_content_id)
                return

            # If this is a silent fallback and we've already tried both, just return without showing error
            if is_silent:
                print(f"🔇 Silent fallback completed, no error dialog shown")
                return

            # Check if this was a series search that failed and we should try movie
            elif (hasattr(self, 'current_content_id') and
                  hasattr(self, 'current_search_input') and
                  ("Series not found" in error_message) and
                  not hasattr(self, '_tried_movie_fallback')):

                print(f"🔄 Series search failed, trying as movie for ID: {self.current_content_id}")
                self._tried_movie_fallback = True  # Prevent infinite loop
                # Don't show error message yet, try movie search first
                self.search_movie(self.current_content_id)
                return

            # Reset search button state only if we're not trying fallback
            if hasattr(self.widgets, 'search_button'):
                self.widgets.search_button.setText("Search")
                self.widgets.search_button.setEnabled(True)

            # Reset fallback flags for next search
            if hasattr(self, '_tried_series_fallback'):
                delattr(self, '_tried_series_fallback')
            if hasattr(self, '_tried_movie_fallback'):
                delattr(self, '_tried_movie_fallback')

            # Show error message only if both searches failed and it's not a silent fallback
            if not is_silent:
                print(f"❌ Final API Error: {error_message}")

                # Create a more user-friendly error message
                if "Movie not found" in error_message and "series ID" in error_message:
                    final_message = "Content not found. Please check the URL or ID and try again."
                elif "Series not found" in error_message:
                    final_message = "Content not found. Please check the URL or ID and try again."
                else:
                    final_message = error_message

                self.show_message("Search Error", final_message)

                # Update status
                self.status_updated.emit(f"Error: {final_message}")
            else:
                print(f"🔇 Silent fallback error (not shown to user): {error_message}")

        except Exception as e:
            print(f"❌ Error in handle_api_error: {str(e)}")
            # Only show error dialog if it's not a silent fallback
            if not is_silent:
                self.show_message("API Error", original_error_message)

    def handle_content_found(self, content_data):
        """Handle content found signals"""
        try:
            # Reset search button state
            if hasattr(self.widgets, 'search_button'):
                self.widgets.search_button.setText("Search")
                self.widgets.search_button.setEnabled(True)

            # Reset fallback flags since we found content successfully
            if hasattr(self, '_tried_series_fallback'):
                delattr(self, '_tried_series_fallback')
            if hasattr(self, '_tried_movie_fallback'):
                delattr(self, '_tried_movie_fallback')

            # Close any existing error dialogs
            self.close_error_dialogs()

            # Display content based on type
            if content_data['type'] == 'movie':
                print(f"✅ Movie found successfully!")
                self.display_movie_info(content_data['data'], content_data.get('movie_id'))
            elif content_data['type'] == 'series':
                print(f"✅ Series found successfully!")
                self.display_series_info(content_data['data'], content_data.get('series_id'))

            # Update status
            self.status_updated.emit("Content loaded successfully")

        except Exception as e:
            self.show_message("Error", f"Failed to display content: {str(e)}")

    def close_error_dialogs(self):
        """Close any existing error dialogs"""
        try:
            # Find and close any QMessageBox dialogs
            from PySide6.QtWidgets import QApplication, QMessageBox

            for widget in QApplication.topLevelWidgets():
                if isinstance(widget, QMessageBox) and widget.isVisible():
                    print("🔄 Closing existing error dialog...")
                    widget.close()

        except Exception as e:
            print(f"⚠️ Error closing dialogs: {str(e)}")

    def handle_login_status(self, is_logged_in, message):
        """Handle login status changes"""
        try:
            if is_logged_in:
                self.status_updated.emit(f"✅ {message}")
            else:
                self.status_updated.emit(f"❌ {message}")

        except Exception as e:
            print(f"Error handling login status: {str(e)}")

    def display_seasons_table(self, seasons, series_info):
        """Display seasons table like the original code"""
        try:
            from tabulate import tabulate

            # Prepare table data like the original code
            table_data = []
            season_ids = []

            series_title = series_info.get('title', {}).get('en', 'Unknown Series')

            for season in seasons:
                season_number = season.get("seasonNumber", "N/A")
                episodes_count = season.get("episodesCount", "N/A")
                season_id = season.get("contentId", "N/A")
                season_year = season.get("year", series_info.get("year", "N/A"))

                # Extract IMDb rating if available
                imdb_rating = series_info.get("imdbRating", {}).get("rating", "N/A")

                table_data.append([
                    f"{series_title} ({season_year})",
                    season_number,
                    episodes_count,
                    season_id,
                    imdb_rating
                ])
                season_ids.append(season_id)

            # Display table in console like original code
            print("\n" + "="*80)
            print("📺 SERIES DETAILS AND AVAILABLE SEASONS:")
            print("="*80)

            headers = ["Title & Year", "Season", "Episodes", "Season ID", "IMDb Rating"]
            table_output = tabulate(table_data, headers=headers, tablefmt="fancy_grid", colalign=("left", "center", "center", "center", "center"))
            print(table_output)

            print("\n" + "="*80)
            print("🎬 Select a season to view episodes:")
            print("="*80)

            # Store season data for later use
            self.current_seasons = seasons
            self.current_season_ids = season_ids
            self.current_series_info = series_info

            # Add Episodes tab to show seasons (don't auto-load episodes)
            self.add_episodes_tab(seasons, series_info)

        except Exception as e:
            print(f"Error displaying seasons table: {str(e)}")
            self.show_message("Error", f"Failed to display seasons: {str(e)}")

    def add_episodes_tab(self, seasons, series_info):
        """Add Episodes tab with seasons list"""
        try:
            # Create Episodes tab content
            episodes_widget = self.create_episodes_widget(seasons, series_info)

            # Add tab to content tabs
            if hasattr(self.widgets, 'content_tabs'):
                # Remove existing Episodes tab if exists
                for i in range(self.widgets.content_tabs.count()):
                    if self.widgets.content_tabs.tabText(i) == "Episodes":
                        self.widgets.content_tabs.removeTab(i)
                        break

                self.widgets.content_tabs.addTab(episodes_widget, "Episodes")
                self.widgets.content_tabs.setCurrentWidget(episodes_widget)

        except Exception as e:
            self.show_message("Error", f"Failed to add episodes tab: {str(e)}")

    def create_episodes_widget(self, seasons, series_info):
        """Create episodes widget with seasons list"""
        try:
            from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QListWidget, QListWidgetItem, QPushButton
            from PySide6.QtCore import Qt

            widget = QWidget()
            layout = QVBoxLayout(widget)

            # Title
            title_label = QLabel("📺 Available Seasons")
            title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #ff79c6; margin: 10px;")
            layout.addWidget(title_label)

            # Seasons list
            seasons_list = QListWidget()
            seasons_list.setStyleSheet("""
                QListWidget {
                    background-color: #44475a;
                    border: 1px solid #6272a4;
                    border-radius: 5px;
                    color: #f8f8f2;
                    font-size: 14px;
                    padding: 5px;
                }
                QListWidget::item {
                    padding: 10px;
                    border-bottom: 1px solid #6272a4;
                }
                QListWidget::item:hover {
                    background-color: #6272a4;
                }
                QListWidget::item:selected {
                    background-color: #ff79c6;
                    color: #282a36;
                }
            """)

            # Add seasons to list
            for season in seasons:
                season_number = season.get("seasonNumber", "N/A")
                episodes_count = season.get("episodesCount", "N/A")
                season_year = season.get("year", series_info.get("year", "N/A"))

                item_text = f"Season {season_number} ({season_year}) - {episodes_count} Episodes"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, season)  # Store season data
                seasons_list.addItem(item)

            layout.addWidget(seasons_list)

            # Connect selection
            seasons_list.itemDoubleClicked.connect(lambda item: self.on_season_selected(item))

            # Select season button
            select_button = QPushButton("🎬 View Episodes")
            select_button.setStyleSheet("""
                QPushButton {
                    background-color: #ff79c6;
                    color: #282a36;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #ff92d0;
                }
                QPushButton:pressed {
                    background-color: #ff5cc6;
                }
            """)
            select_button.clicked.connect(lambda: self.on_season_selected(seasons_list.currentItem()))
            layout.addWidget(select_button)

            return widget

        except Exception as e:
            self.show_message("Error", f"Failed to create episodes widget: {str(e)}")
            return QWidget()

    def on_season_selected(self, item):
        """Handle season selection"""
        try:
            if not item:
                self.show_message("Warning", "Please select a season first")
                return

            season_data = item.data(Qt.UserRole)
            if not season_data:
                return

            season_id = season_data.get("contentId")
            season_number = season_data.get("seasonNumber", 1)

            if season_id:
                # Get episodes for selected season
                print(f"\n🎬 Loading episodes for Season {season_number}...")
                self.status_updated.emit(f"Loading episodes for Season {season_number}...")

                # Call API to get episodes
                if hasattr(self.main_window, 'osn_api'):
                    self.main_window.osn_api.get_episodes_by_season(
                        season_id,
                        self.current_series_info.get('title', {}).get('en', 'Unknown'),
                        season_number
                    )

        except Exception as e:
            self.show_message("Error", f"Failed to load season episodes: {str(e)}")

    # Episodes are now displayed in the seasons tab - no separate episodes tab needed
