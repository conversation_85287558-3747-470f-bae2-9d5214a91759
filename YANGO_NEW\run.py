#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Quick run script for YANGO PLAY application
This script provides a simple way to launch the application with error handling
"""

import sys
import os

def main():
    """Main function to run the YANGO PLAY application"""
    
    print("🚀 Starting YANGO PLAY - Modern GUI...")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    
    # Add current directory to path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    try:
        # Import and run the main application
        from main import QApplication, MainWindow
        
        print("✅ Modules imported successfully")
        print("🖥️ Creating application window...")
        
        # Create QApplication
        app = QApplication(sys.argv)
        app.setApplicationName("YANGO PLAY")
        app.setApplicationVersion("1.0.0")
        
        # Set application icon if available
        try:
            from PySide6.QtGui import QIcon
            icon_path = os.path.join(current_dir, "icon.ico")
            if os.path.exists(icon_path):
                app.setWindowIcon(QIcon(icon_path))
        except:
            pass  # Icon is optional
        
        # Create main window
        window = MainWindow()
        
        print("✅ Application window created")
        print("🌟 YANGO PLAY is now running!")
        print("\n💡 Tips:")
        print("   • Enter a YANGO URL or content ID in the search box")
        print("   • Example: https://play.yango.com/en-eg/movie/content-id")
        print("   • Or just use the content ID directly")
        print("   • Recent URLs are saved automatically")
        print("\n" + "=" * 50)
        
        # Run the application
        return app.exec_()
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n💡 Solutions:")
        print("   • Install PySide6: pip install PySide6")
        print("   • Install requests: pip install requests")
        print("   • Run: pip install -r requirements.txt")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")
        print("\n💡 Try:")
        print("   • Run the test script: python test_app.py")
        print("   • Check the console for detailed error messages")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n👋 Application closed by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Fatal Error: {e}")
        sys.exit(1)
