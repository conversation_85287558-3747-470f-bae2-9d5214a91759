"""
Test script for enhanced progress monitoring in OSN_NEW
"""

import sys
import os
from pathlib import Path

# Add the modules directory to the path
sys.path.insert(0, str(Path(__file__).parent / "modules"))

from PySide6.QtCore import QCoreApplication, QTimer
from modules.osn_downloader import OSNDownloader

def test_enhanced_progress():
    """Test the enhanced progress monitoring system"""
    
    app = QCoreApplication(sys.argv)
    
    # Create downloader instance
    downloader = OSNDownloader()
    
    # Connect signals to test functions
    downloader.download_progress.connect(lambda progress, status: print(f"📊 Progress: {progress}% - {status}"))
    downloader.status_update.connect(lambda status: print(f"📝 Status: {status}"))
    downloader.video_progress.connect(lambda progress: print(f"📹 Video: {progress}%"))
    downloader.audio_progress.connect(lambda progress: print(f"🔊 Audio: {progress}%"))
    downloader.subtitle_progress.connect(lambda progress: print(f"📝 Subtitle: {progress}%"))
    downloader.download_completed.connect(lambda path, success: print(f"✅ Completed: {path} - Success: {success}"))
    downloader.download_error.connect(lambda error: print(f"❌ Error: {error}"))
    
    print("🎉 Enhanced OSN Progress Monitoring System Initialized!")
    print("📋 Available features:")
    print("   ✅ Enhanced subtitle progress tracking (0-15%)")
    print("   ✅ Enhanced video progress tracking (15-70%)")
    print("   ✅ Enhanced audio progress tracking (70-85%)")
    print("   ✅ Enhanced merging phase tracking (85-100%)")
    print("   ✅ Smooth progress animation")
    print("   ✅ Phase-based status messages")
    print("   ✅ aria2c download support")
    print("   ✅ Real-time progress monitoring")
    
    print("\n🔧 System ready for downloads!")
    print("   Use the OSN_NEW UI to start downloads and see the enhanced progress in action.")
    
    # Exit after showing info
    QTimer.singleShot(2000, app.quit)
    
    return app.exec()

if __name__ == "__main__":
    test_enhanced_progress()
