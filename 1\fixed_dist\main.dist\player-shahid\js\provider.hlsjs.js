(self.webpackChunkjwplayer=self.webpackChunkjwplayer||[]).push([[98],{8377:(t,e,r)=>{"use strict";r.d(e,{M:()=>i,_:()=>n});const i=function(t,e){var i=t.kind||"cc";return t.default||t.defaulttrack?"default":t._id||t.file||i+e},n=function(t,e){let r=t.label||t.name||t.language;return r||(r="CC",1<(e+=1)&&(r+=` [${e}]`)),{label:r,unknownCount:e}}},6103:(t,e,r)=>{"use strict";r.d(e,{VS:()=>g,xl:()=>f});var i=r(7477),n=r(2894),a=r(6886),s=r(7941),o=r(7387),l=r(2957),u=r(4446);function c(t,e,i,a){let c,f,g=t.responseXML?t.responseXML.firstChild:null;if(g)for("xml"===(0,s.r1)(g)&&(g=g.nextSibling);g&&g.nodeType===g.COMMENT_NODE;)g=g.nextSibling;try{if(g&&"tt"===(0,s.r1)(g)){if(!t.responseXML)throw new Error("Empty XML response");c=function(t){t||h(306007);var e=[];let r=t.getElementsByTagName("p"),i=30;const n=t.getElementsByTagName("tt");if(null!=n&&n[0]){const t=parseFloat(n[0].getAttribute("ttp:frameRate")||"");isNaN(t)||(i=t)}r||h(306005),r.length||(r=t.getElementsByTagName("tt:p")).length||(r=t.getElementsByTagName("tts:p"));for(let n=0;n<r.length;n++){var a=r[n],s=a.getElementsByTagName("br");for(let e=0;e<s.length;e++){const r=s[e];null!=r&&r.parentNode&&r.parentNode.replaceChild(t.createTextNode("\r\n"),r)}var o=a.innerHTML||a.textContent||a.text||"",o=(0,l.fy)(o).replace(/>\s+</g,"><").replace(/(<\/?)tts?:/g,"$1").replace(/<br.*?\/>/g,"\r\n");if(o){const t=a.getAttribute("begin")||"",r=a.getAttribute("dur")||"",n=a.getAttribute("end")||"",s={begin:(0,l.m9)(t,i),text:o};n?s.end=(0,l.m9)(n,i):r&&(s.end=(s.begin||0)+(0,l.m9)(r,i)),e.push(s)}}return e.length||h(306005),e}(t.responseXML),f=d(c),delete e.xhr,i(f)}else{const s=t.responseText;0<=s.indexOf("WEBVTT")?r.e(347).then(function(t){return r(2776).default}.bind(null,r)).catch((0,n.Jt)(301131)).then(t=>{t=new t(window);f=[],t.oncue=function(t){f.push(t)},t.onflush=function(){delete e.xhr,i(f)},t.parse(s)}).catch(t=>{delete e.xhr,a((0,u.Mm)(null,u.Y7,t))}):(c=(0,o.Z)(s),f=d(c),delete e.xhr,i(f))}}catch(t){delete e.xhr,a((0,u.Mm)(null,u.Y7,t))}}const h=t=>{throw new u.rG(null,t)},d=function(t){return t.map(t=>new i.Z(t.begin,t.end,t.text))},f=function(t,e,r){t.xhr=(0,a.h)(t.file,function(i){c(i,t,e,r)},(t,e,i,n)=>{r((0,u.l9)(n,u.Y7))})},g=function(t){t&&t.forEach(t=>{var e=t.xhr;e&&(e.onload=null,e.onreadystatechange=null,e.onerror=null,"abort"in e)&&e.abort(),delete t.xhr})}},7387:(t,e,r)=>{"use strict";r.d(e,{Z:()=>function(t){var e=[];let r=(t=(0,i.fy)(t)).split("\r\n\r\n");1===r.length&&(r=t.split("\n\n"));for(let t=0;t<r.length;t++)if("WEBVTT"!==r[t]){const i=n(r[t]);i.text&&e.push(i)}return e}});var i=r(2957);const n=t=>{var e={};let r=t.split("\r\n"),n=1;if(0<(r=1===r.length?t.split("\n"):r)[0].indexOf(" --\x3e ")&&(n=0),r.length>n+1&&r[n+1]){const t=r[n],a=t.indexOf(" --\x3e ");0<a&&(e.begin=(0,i.m9)(t.substr(0,a)),e.end=(0,i.m9)(t.substr(a+5)),e.text=r.slice(n+1).join("\r\n"))}return e}},7477:(t,e,r)=>{"use strict";r.d(e,{Z:()=>a});let i=window.VTTCue;const n=t=>"string"==typeof t&&!!{start:!0,middle:!0,end:!0,left:!0,right:!0}[t.toLowerCase()]&&t.toLowerCase();if(!i){const t="auto";(i=function(e,r,i){var a=this;a.hasBeenReset=!1;let s="",o=!1,l=e,u=r,h=i,d=null,c="",f=!0,g=t,v="start",m=t,p=100,y="middle";Object.defineProperty(a,"id",{enumerable:!0,get:()=>s,set(t){s=""+t}}),Object.defineProperty(a,"pauseOnExit",{enumerable:!0,get:()=>o,set(t){o=Boolean(t)}}),Object.defineProperty(a,"startTime",{enumerable:!0,get:()=>l,set(t){if("number"!=typeof t)throw new TypeError("Start time must be set to a number.");l=t,this.hasBeenReset=!0}}),Object.defineProperty(a,"endTime",{enumerable:!0,get:()=>u,set(t){if("number"!=typeof t)throw new TypeError("End time must be set to a number.");u=t,this.hasBeenReset=!0}}),Object.defineProperty(a,"text",{enumerable:!0,get:()=>h,set(t){h=""+t,this.hasBeenReset=!0}}),Object.defineProperty(a,"region",{enumerable:!0,get:()=>d,set(t){d=t,this.hasBeenReset=!0}}),Object.defineProperty(a,"vertical",{enumerable:!0,get:()=>c,set(t){t=(t=>"string"==typeof t&&!!{"":!0,lr:!0,rl:!0}[t.toLowerCase()]&&t.toLowerCase())(t);if(!1===t)throw new SyntaxError("An invalid or illegal string was specified.");c=t,this.hasBeenReset=!0}}),Object.defineProperty(a,"snapToLines",{enumerable:!0,get:()=>f,set(t){f=Boolean(t),this.hasBeenReset=!0}}),Object.defineProperty(a,"line",{enumerable:!0,get:()=>g,set(e){if("number"!=typeof e&&e!==t)throw new SyntaxError("An invalid number or illegal string was specified.");g=e,this.hasBeenReset=!0}}),Object.defineProperty(a,"lineAlign",{enumerable:!0,get:()=>v,set(t){t=n(t);if(!t)throw new SyntaxError("An invalid or illegal string was specified.");v=t,this.hasBeenReset=!0}}),Object.defineProperty(a,"position",{enumerable:!0,get:()=>m,set(t){if(t<0||100<t)throw new Error("Position must be between 0 and 100.");m=t,this.hasBeenReset=!0}}),Object.defineProperty(a,"size",{enumerable:!0,get:()=>p,set(t){if(t<0||100<t)throw new Error("Size must be between 0 and 100.");p=t,this.hasBeenReset=!0}}),Object.defineProperty(a,"align",{enumerable:!0,get:()=>y,set(t){t=n(t);if(!t)throw new SyntaxError("An invalid or illegal string was specified.");y=t,this.hasBeenReset=!0}}),a.displayState=void 0}).prototype.getCueAsHTML=function(){return window.WebVTT.convertCueToDOMTree(window,this.text)}}const a=i},2728:(t,e,r)=>{"use strict";r.d(e,{Z:()=>function(t,e){let r,a=e;return{start(){this.stop(),r=window.setInterval(()=>{var e=t.getBandwidthEstimate();(0,n.qh)(e)&&(a=e,t.trigger(i.qG,{bandwidthEstimate:a}))},1e3)},stop(){clearInterval(r)},getEstimate:()=>a}}});var i=r(1643),n=r(6042)},548:(t,e,r)=>{"use strict";r.d(e,{Zv:()=>i,i0:()=>a,pR:()=>n});const i=1,n=i+1,a=25},4506:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});const i=t=>({bitrate:t.bitrate,label:t.label,width:t.width,height:t.height})},3328:(t,e,r)=>{"use strict";r.d(e,{Z:()=>L});var i=r(6103),n=r(8377);const a={TIT2:"title",TT2:"title",WXXX:"url",TPE1:"artist",TP1:"artist",TALB:"album",TAL:"album"},s=(t,e)=>{var r=t.length;let i,n,a,s="",o=e||0;for(;o<r;)if(0!==(i=t[o++])&&3!==i)switch(i>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:s+=String.fromCharCode(i);break;case 12:case 13:n=t[o++],s+=String.fromCharCode((31&i)<<6|63&n);break;case 14:n=t[o++],a=t[o++],s+=String.fromCharCode((15&i)<<12|(63&n)<<6|(63&a)<<0)}return s},o=t=>{t=(t=>{let e="0x";for(let r=0;r<t.length;r++)t[r]<16&&(e+="0"),e+=t[r].toString(16);return parseInt(e,16)})(t);return 127&t|(32512&t)>>1|(8323072&t)>>2|(2130706432&t)>>3};var u=r(8348),h=r(1643),d=r(6042);function v(t){const e=t.target,{activeCues:r,cues:i}=e,n=e._id,a=this._cues,s=this._activeCues;if(null!=i&&i.length){const t=a[n];a[n]=Array.prototype.slice.call(i),this.parseNativeID3Cues(i,t)}else delete a[n];if(null!=r&&r.length){const t=s[n],e=s[n]=Array.prototype.slice.call(r);this.triggerActiveCues(e,t)}else delete s[n]}const c=(t,e,r)=>{null!=e&&e.length&&(0,d.S6)(e,function(e){var i=e._id||"";if(r&&(e._id=void 0),!u.Browser.ie&&!u.Browser.safari||!t||!/^(native|subtitle|cc)/.test(i)){if(u.Browser.ie&&"disabled"===e.mode||(e.mode="disabled",e.mode="hidden"),e.cues)for(let t=e.cues.length;t--;)e.removeCue(e.cues[t]);e.embedded||(e.mode="disabled"),e.inuse=!1}})},f=t=>/^native(?:captions|subtitles)/.test(t),g=t=>"captions"===t||"subtitles"===t,m=(t,e,r)=>{if(u.Browser.ie){let i=r;(t||"metadata"===e.kind)&&(i=new window.TextTrackCue(r.startTime,r.endTime,r.text),r.value)&&(i.value=r.value),((t,e)=>{var r=[],i=t.mode,n=(t.mode="hidden",t.cues);if(n)for(let i=n.length-1;0<=i&&n[i].startTime>e.startTime;i--)r.unshift(n[i]),t.removeCue(n[i]);try{t.addCue(e),r.forEach(e=>t.addCue(e))}catch(t){console.error(t)}t.mode=i})(e,i)}else try{e.addCue(r)}catch(t){console.error(t)}},E=(t,e)=>t.startTime===e.startTime&&t.endTime===e.endTime&&t.text===e.text&&t.data===e.data&&JSON.stringify(t.value)===JSON.stringify(e.value),S=t=>{var e=(t=>{const e={};if(!("value"in t)&&"data"in t&&t.data instanceof ArrayBuffer){const e=new Uint8Array(t.data);let r=e.length,i=(t={value:{key:"",data:""}},10);for(;i<14&&i<e.length&&0!==e[i];)t.value.key+=String.fromCharCode(e[i]),i++;let n=19,a=e[n],l=(3!==a&&0!==a||(a=e[++n],r--),0);if(1!==a&&2!==a)for(let t=n+1;t<r;t++)if(0===e[t]){l=t-n;break}if(0<l){const r=s(e.subarray(n,n+=l),0);if("PRIV"===t.value.key){if("com.apple.streaming.transportStreamTimestamp"===r){const r=1&o(e.subarray(n,n+=4)),i=o(e.subarray(n,n+=4))+(r?4294967296:0);t.value.data=i}else t.value.data=s(e,n+1);t.value.info=r}else t.value.info=r,t.value.data=s(e,n+1)}else{const r=e[n];t.value.data=(1===r||2===r?(t,e)=>{var r=t.length-1;let i="",n=e||0;for(;n<r;)254===t[n]&&255===t[n+1]||(i+=String.fromCharCode((t[n]<<8)+t[n+1])),n+=2;return i}:s)(e,n+1)}}if(function(t,e){if(null==t)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(Object(t),e)}(a,t.value.key)&&(e[a[t.value.key]]=t.value.data),t.value.info){let r=e[t.value.key];r!==Object(r)&&(r={},e[t.value.key]=r),r[t.value.info]=t.value.data}else e[t.value.key]=t.value.data;return e})(t);return{metadataType:"id3",metadataTime:t.startTime,metadata:e}},L={_itemTracks:null,_textTracks:null,_currentTextTrackIndex:-1,_tracksById:null,_cuesByTrackId:null,_cachedVTTCues:null,_metaCuesByTextTime:null,_unknownCount:0,_activeCues:null,_cues:null,textTrackChangeHandler:null,addTrackHandler:null,cueChangeHandler:null,renderNatively:!1,_initTextTracks(){this._textTracks=[],this._tracksById={},this._metaCuesByTextTime={},this._cuesByTrackId={},this._cachedVTTCues={},this._cues={},this._activeCues={},this._unknownCount=0},addTracksListener(t,e,r){t&&(this.removeTracksListener(t,e,r),this.instreamMode||(t.addEventListener?t.addEventListener(e,r):t["on"+e]=r))},removeTracksListener(t,e,r){t&&(t.removeEventListener&&r?t.removeEventListener(e,r):t["on"+e]=null)},clearTracks(){(0,i.VS)(this._itemTracks);const t=this["_tracksById"];if(t&&Object.keys(t).forEach(e=>{0===e.indexOf("nativemetadata")&&(e=t[e],this.cueChangeHandler&&e.removeEventListener("cuechange",this.cueChangeHandler),c(this.renderNatively,[e],!0))}),this._itemTracks=null,this._textTracks=null,this._tracksById=null,this._cuesByTrackId=null,this._metaCuesByTextTime=null,this._unknownCount=0,this._currentTextTrackIndex=-1,this._activeCues={},this._cues={},this.renderNatively){const t=this.video.textTracks;this.textTrackChangeHandler&&this.removeTracksListener(t,"change",this.textTrackChangeHandler),c(this.renderNatively,t,!0)}},clearMetaCues(){const{_tracksById:t,_cachedVTTCues:e}=this;t&&e&&Object.keys(t).forEach(r=>{0===r.indexOf("nativemetadata")&&(r=t[r],c(this.renderNatively,[r],!1),r.mode="hidden",r.inuse=!0,r._id)&&(e[r._id]={})})},clearCueData(t){var e=this._cachedVTTCues;null!=e&&e[t]&&(e[t]={},this._tracksById)&&(this._tracksById[t].data=[])},disableTextTrack(){var e,t=this.getCurrentTextTrack();t&&(t.mode="disabled",(e=t._id)&&f(e)||this.renderNatively&&u.OS.iOS)&&(t.mode="hidden")},enableTextTrack(){var t=this.getCurrentTextTrack();t&&(t.mode="showing")},getCurrentTextTrack(){if(this._textTracks)return this._textTracks[this._currentTextTrackIndex]},getSubtitlesTrack(){return this._currentTextTrackIndex},addTextTracks(t){const r=[];return t&&(this._textTracks||this._initTextTracks(),t.forEach(t=>{if(!(t.includedInManifest||t.kind&&!g(t.kind))){const e=this._createTrack(t);this._addTrackToList(e),r.push(e),t.file&&(t.data=[],(0,i.xl)(t,t=>{e.sideloaded=!0,this.addVTTCuesToTrack(e,t)},t=>{this.trigger(h.cM,t)}))}}),null!=this)&&null!=(t=this._textTracks)&&t.length&&this.trigger(h.jt,{tracks:this._textTracks}),r},setTextTracks(t){var e;if(this._currentTextTrackIndex=-1,t){if(this._textTracks){const t=this._tracksById;this._activeCues={},this._cues={},this._unknownCount=0,this._textTracks=this._textTracks.filter(e=>{var r=e._id;return this.renderNatively&&r&&f(r)?(delete t[r],!1):(e.name&&0===e.name.indexOf("CC")&&this._unknownCount++,0===r.indexOf("nativemetadata")&&"com.apple.streaming"===e.inBandMetadataTrackDispatchType&&delete t[r],!0)},this)}else this._initTextTracks();if(t.length){let e=0;for(var r=t.length,i=this._tracksById,a=this._cuesByTrackId;e<r;e++){const r=t[e];let s=r._id||"";if(!s){if(!1===r.inuse&&g(r.kind)&&this.renderNatively){r._id="native"+r.kind+e;continue}if(g(r.kind)||"metadata"===r.kind){if(s=r._id="native"+r.kind+e,!r.label&&"captions"===r.kind){const t=(0,n._)(r,this._unknownCount);r.name=t.label,this._unknownCount=t.unknownCount}}else s=r._id=(0,n.M)(r,this._textTracks?this._textTracks.length:0);if(i[s])continue;r.inuse=!0}if(r.inuse&&!i[s])if("metadata"===r.kind){r.mode="hidden";const t=this.cueChangeHandler=this.cueChangeHandler||v.bind(this);r.removeEventListener("cuechange",t),r.addEventListener("cuechange",t),i[s]=r}else if(g(r.kind)){const t=r.mode;let e;if(r.mode="hidden",r.cues&&r.cues.length||!r.embedded){if("disabled"===t&&!f(s)||(r.mode=t),a[s]&&!a[s].loaded){const i=a[s].cues;for(;e=i.shift();)m(this.renderNatively,r,e);r.mode=t,a[s].loaded=!0}this._addTrackToList(r)}}}}this.renderNatively&&this.addTrackListeners(t),null!=this&&null!=(e=this._textTracks)&&e.length&&this.trigger(h.jt,{tracks:this._textTracks})}},addTrackListeners(t){var e=this.textTrackChangeHandler=this.textTrackChangeHandler||function(){var t=this.video.textTracks,e=(0,d.hX)(t,function(t){return(t.inuse||!t._id)&&g(t.kind)});if(!this._textTracks||function(t){var e=this._textTracks,r=this._tracksById;if(t.length>e.length)return!0;for(let e=0;e<t.length;e++){var i=t[e];if(!i._id||!r[i._id])return!0}return!1}.call(this,e))this.setTextTracks(t);else{let r=-1;for(let t=0;t<this._textTracks.length;t++)if("showing"===this._textTracks[t].mode){r=t;break}r!==this._currentTextTrackIndex&&this.setSubtitlesTrack(r+1)}}.bind(this);this.removeTracksListener(t,"change",e),this.addTracksListener(t,"change",e),(u.Browser.edge&&u.Browser.ie||u.Browser.firefox)&&(e=this.addTrackHandler=this.addTrackHandler||function(t){t=t.track;null!=t&&t._id||this.setTextTracks(this.video.textTracks)}.bind(this),this.removeTracksListener(t,"addtrack",e),this.addTracksListener(t,"addtrack",e))},setupSideloadedTracks(t){var e;this.renderNatively&&((e=(t=t||null)===this._itemTracks)||(0,i.VS)(this._itemTracks),this._itemTracks=t)&&!e&&(this.disableTextTrack(),this._clearSideloadedTextTracks(),this.addTextTracks(t))},setSubtitlesTrack(t){var e;this.renderNatively?this._textTracks&&(0===t&&this._textTracks.forEach(t=>{t.mode=t.embedded?"hidden":"disabled"}),this._currentTextTrackIndex!==t-1)&&(this.disableTextTrack(),this._currentTextTrackIndex=t-1,(e=this.getCurrentTextTrack())&&(e.mode="showing"),this.trigger(h.UF,{currentTrack:this._currentTextTrackIndex+1,tracks:this._textTracks})):this.setCurrentSubtitleTrack&&this.setCurrentSubtitleTrack(t-1)},createCue:(t,e,r)=>new(window.VTTCue||window.TextTrackCue)(t,Math.max(e||0,t+.25),r),addVTTCue(t,e){this._tracksById||this._initTextTracks();var r=t.track||"native"+t.type;let i=this._tracksById[r];var n="captions"===t.type?"CC":"ID3 Metadata",a=t.cue;if(!i){const e={kind:t.type,_id:r,label:n,default:!1};this.renderNatively||"metadata"===e.kind?((i=this._createTrack(e)).embedded=!0,this.setTextTracks(this.video.textTracks)):i=this.addTextTracks([e])[0]}if(this._cacheVTTCue(i,a,e)){const t=this.renderNatively||"metadata"===i.kind;return t?m(t,i,a):i.data.push(a),a}return null},addVTTCuesToTrack(t,e){if(this.renderNatively){var t=t._id,a=this._tracksById;let s=this._cuesByTrackId;var l,o=a[t];if(o){if(null==(a=s)||null==(a=a[t])||!a.loaded)for(s[t]={cues:e,loaded:!0};l=e.shift();)m(this.renderNatively,o,l)}else(s=s||(this._cuesByTrackId={}))[t]={cues:e,loaded:!1}}},parseNativeID3Cues(t,e){const r=t[t.length-1];if(!e||e.length!==t.length||!r._parsed&&!E(e[e.length-1],r)){var i=[],n=[];let a=-1,s=-1,o;for(let e=0;e<t.length;e++){const r=t[e];if(!r._extended&&Boolean(r.data||r.value)){if(r.startTime!==s||null===r.endTime){o=s,s=r.startTime;const t=i[a];if(i[++a]=[],n[a]=[],t&&0<s-o)for(let e=0;e<t.length;e++){const r=t[e];r.endTime=s,r._extended=!0}}i[a].push(r),r._parsed||(n[a].push(r),r.endTime-s<.25&&(r.endTime=s+.25),r._parsed=!0)}}for(let t=0;t<n.length;t++)n[t].length&&n[t].forEach(t=>{t=S(t);this.trigger(h.O1,t)})}},triggerActiveCues(t,e){t=t.filter(t=>{if(null==e||!e.some(e=>E(t,e))){if(t.data)return!0;var r=t.text?(t=>{let e;try{e=JSON.parse(t.text)}catch(t){return null}var r={metadataType:e.metadataType,metadataTime:t.startTime,metadata:e};return e.programDateTime&&(r.programDateTime=e.programDateTime),r})(t):null;if(r)"emsg"===r.metadataType&&(r.metadata=r.metadata||{},r.metadata.messageData=t.value),this.trigger(h.rx,r);else if(t.value)return!0}return!1});t.length&&t.forEach(t=>{t=S(t);this.trigger(h.rx,t)})},ensureMetaTracksActive(){const t=this.video.textTracks,e=t.length;for(let r=0;r<e;r++){const e=t[r];"metadata"===e.kind&&"disabled"===e.mode&&(e.mode="hidden")}},_cacheVTTCue(t,e,r){const i=t.kind,n=t._id,a=this._cachedVTTCues;a[n]||(a[n]={});var s=a[n];let o;switch(i){case"captions":case"subtitles":{o=r||Math.floor(20*e.startTime);const t="_"+(e.line||"auto"),i=Math.floor(20*e.endTime),n=s[o+t]||s[o+1+t]||s[o-1+t];return!(n&&Math.abs(n-i)<=1||(s[o+t]=i,0))}case"metadata":{const t=e.data?new Uint8Array(e.data).join(""):e.text;return!s[o=r||e.startTime+t]&&(s[o]=e.endTime,!0)}default:return!1}},_addTrackToList(t){this._textTracks.push(t),this._tracksById[t._id]=t},_createTrack(t){let e;const r=(0,n._)(t,this._unknownCount),i=r.label;if(this._unknownCount=r.unknownCount,this.renderNatively||"metadata"===t.kind){const r=this.video.textTracks;(e=(e=(0,d._e)(r,{label:i}))||this.video.addTextTrack(t.kind,i,t.language||"")).default=t.default,e.mode="disabled",e.inuse=!0}else(e=t).data=e.data||[];return e._id||(e._id=(0,n.M)(t,this._textTracks?this._textTracks.length:0)),e},_clearSideloadedTextTracks(){if(this._textTracks){var t=this._textTracks.filter(t=>t.embedded||"subs"===t.groupid);this._initTextTracks();const e=this._tracksById;t.forEach(t=>{e[t._id]=t}),this._textTracks=t}}}},9601:(t,e,r)=>{"use strict";r.d(e,{E:()=>n,Z:()=>function(t,e,r){let a=t+1e3,s=i.ul;return 0<e?(403===e&&(s=i.H4),a+=n(e)):"http:"===(""+r).substring(0,5)&&"https:"===document.location.protocol?a+=12:0===e&&(a+=11),{code:a,key:s}}});var i=r(4446);const n=t=>400<=t&&t<600?t:6},5099:(t,e,r)=>{"use strict";r.d(e,{Z:()=>function(t){return new Promise(function(e,r){if(t.paused)return r(i("NotAllowedError",0,"play() failed."));let n;const a=function(t){if(n(),"playing"!==t.type){const e=`The play() request was interrupted by a "${t.type}" event.`;return"error"===t.type?r(i("NotSupportedError",9,e)):r(i("AbortError",20,e))}e()},s=function(){t.addEventListener("playing",a),t.addEventListener("abort",a),t.addEventListener("error",a),t.addEventListener("pause",a)};n=function(){t.removeEventListener("play",s),t.removeEventListener("playing",a),t.removeEventListener("pause",a),t.removeEventListener("abort",a),t.removeEventListener("error",a)},t.addEventListener("play",s)})}});const i=(t,e,r)=>{r=new Error(r);return r.name=t,r.code=e,r}},5256:(t,e,r)=>{"use strict";r.d(e,{_T:()=>i,c3:()=>o,ji:()=>a,s_:()=>l});function s(t,e){return t.some(function(t){let r;for(let i=0;i<e.length&&!(r=t[e[i]]);i++);var i;return!!r&&(i=this[r]||!1,this[r]=!0,i)},{})}const i=t=>Math.floor(t/1e3),a=(t,e,r)=>{return t?((t,e)=>{let r=null;var n;return r=e&&t&&(n=Object.keys(t)).length&&(n=((t,e)=>{let r,i=null,n=1/0;return t.forEach(t=>{(r=Math.abs(parseFloat(t)-e))<n&&(i=t,n=r)}),i})(n,i(e)))?t[n]:r})(e,e=t.bitrate||t.bandwidth)||t.label||((t,e,r)=>{if(!t&&!e)return"";let n="",a="";return e&&(a=i(e)+" kbps",n=a),t&&(n=t+"p",e)&&r&&(n+=` (${a})`),n})(t.height,e,r):""},o=t=>!!Array.isArray(t)&&s(t,["height","bitrate","bandwidth"]),l=t=>!!Array.isArray(t)&&s(t,["label"])},686:(t,e,r)=>{"use strict";r.d(e,{s:()=>n,v:()=>a});const i=t=>void 0===t?120:Math.max(t,0),n=(t,e)=>t!==1/0&&Math.abs(t)>=Math.max(i(e),0),a=(t,e)=>{let r="VOD";return t===1/0?r="LIVE":t<0&&(r=n(t,i(e))?"DVR":"LIVE"),r}},3949:(t,e,r)=>{"use strict";r.d(e,{Z:()=>s});var i=r(8348),n=r(974),a=r(9974);const s={container:null,volume(t){this.video.volume=Math.min(Math.max(0,t/100),1)},mute(t){this.video.muted=Boolean(t),this.video.muted||this.video.removeAttribute("muted")},resize(t,e,r){var a=this["video"],{videoWidth:s,videoHeight:o}=a;if(t&&e&&s&&o){var l={objectFit:"",width:"",height:""},u=t/e,h=s/o;if("uniform"===r){let i;(i=h<u?t-t/(u/h):e-e/(h/u))<6&&(l.objectFit="fill",r="exactfit")}if(i.Browser.ie||i.OS.iOS&&(i.OS.version.major||0)<9||i.Browser.androidNative)if("uniform"!==r){l.objectFit="contain";let i=1,d=1;"none"===r?i=d=h<u?Math.ceil(100*o/e)/100:Math.ceil(100*s/t)/100:"fill"===r?i=d=h<u?u/h:h/u:"exactfit"===r&&(d=h<u?(i=u/h,1):(i=1,h/u)),(0,n.vs)(a,`matrix(${i.toFixed(2)}, 0, 0, ${d.toFixed(2)}, 0, 0)`)}else(l.top=l.left=l.margin="",n.vs)(a,"");(0,n.oB)(a,l)}},getContainer(){return this.container},setContainer(t){this.container=t,this.video.parentNode!==t&&t.appendChild(this.video)},removeFromContainer(){var{container:t,video:e}=this;this.container=null,t&&t===e.parentNode&&t.removeChild(e)},remove(){this.stop(),this.destroy(),this.removeFromContainer()},atEdgeOfLiveStream(){return!!this.isLive()&&(0,a.Z)(this.video.buffered)-this.video.currentTime<=2}}},186:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});const i={_eventsOn(){},_eventsOff(){},attachMedia(){this._eventsOn()},detachMedia(){return this._eventsOff()}}},8702:(t,e,r)=>{"use strict";r.d(e,{Z:()=>s});var i=r(1643),n=r(1261),a=r(5678);const s={canplay(){this.renderNatively&&this.setTextTracks(this.video.textTracks),this.trigger(i.Jl)},play(){this.stallTime=-1,this.video.paused||this.state===i._5||this.state===i.r0||this.setState(i.ik)},loadedmetadata(){var t={metadataType:"media",duration:this.getDuration(),height:this.video.videoHeight,width:this.video.videoWidth,seekRange:this.getSeekRange()},e=this.drmUsed;e&&(t.drm=e),this.trigger(i.rx,t)},timeupdate(){const t=this.video.currentTime,e=this.getCurrentTime(),r=this.getDuration();if(!isNaN(r)){this.seeking||this.video.paused||this.state!==i.nQ&&this.state!==i.ik||this.stallTime===t||(this.stallTime=-1,this.setState(i.r0),this.trigger(i.Gj));var n={position:e,duration:r,currentTime:t,seekRange:this.getSeekRange(),metadata:{currentTime:t},absolutePosition:(0,a.e)(this)},s=this.getLiveLatency();if(null!==s&&(n.latency=s,this.getTargetLatency)){const t=this.getTargetLatency();null!==t&&(n.targetLatency=t)}(this.state===i.r0||this.seeking&&this.state!==i.bc)&&this.trigger(i.R2,n)}},click(t){this.trigger(i.ot,t)},volumechange(){var t=this.video;this.trigger(i.yH,{volume:Math.round(100*t.volume)}),this.trigger(i.gy,{mute:t.muted})},seeking(){if(this.state===i.ik){var t=this.video.buffered.length?this.video.buffered.start(0):-1;if(this.video.currentTime===t)return}else if(this.state===i.bc)return;this.seeking=!0},seeked(){this.seeking&&(this.seeking=!1,this.trigger(i.aQ))},playing(){-1===this.stallTime&&this.setState(i.r0),this.trigger(i.Gj)},pause(){this.state===i.xQ||this.video.ended||this.video.error||this.video.currentTime!==this.video.duration&&this.setState(i._5)},progress(){var e,t=this.getDuration();t<=0||t===1/0||(e=this.video.buffered)&&0!==e.length&&(e=(0,n.v)(e.end(e.length-1)/t,0,1),this.trigger(i.uT,{bufferPercent:100*e,position:this.getCurrentTime(),duration:t,currentTime:this.video.currentTime,seekRange:this.getSeekRange(),absolutePosition:(0,a.e)(this)}))},ratechange(){this.trigger(i.TJ,{playbackRate:this.video.playbackRate})},ended(){this.state!==i.bc&&this.state!==i.xQ&&this.trigger(i.Ms)}}},5678:(t,e,r)=>{"use strict";r.d(e,{e:()=>i});const i=t=>{var e=1e3*(null==t||null==(e=t.video)?void 0:e.currentTime);return null!=t&&t.startDateTime&&e?new Date(t.startDateTime+e):null}},9974:(t,e,r)=>{"use strict";r.d(e,{Z:()=>function(t){return t&&t.length?t.end(t.length-1):0}})},9054:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>z});var i={},n=(r.r(i),r.d(i,{debug:()=>v,error:()=>T,info:()=>p,log:()=>m,warn:()=>y}),r(6042));class a{constructor(t,e,r,i){this.video=t,this.hlsjs=r,this.videoListeners=e,this.hlsjsListeners=i}on(){this.off(),(0,n.S6)(this.videoListeners,(t,e)=>{this.video.addEventListener(e,t,!1)}),(0,n.S6)(this.hlsjsListeners,(t,e)=>{this.hlsjs.on(e,t)})}off(){(0,n.S6)(this.videoListeners,(t,e)=>{this.video.removeEventListener(e,t)}),(0,n.S6)(this.hlsjsListeners,(t,e)=>{this.hlsjs.off(e,t)})}}var s=r(5256),o=r(8348);const l=t=>t.audioGroupIds?t.audioGroupIds[t._urlId||t.urlId]:void 0,u=(t,e)=>{const r=(0,s.c3)(t),i=(0,n.UI)(t,(t,i)=>({label:(0,s.ji)(t,e,r),level_id:t.id,hlsjsIndex:i,bitrate:t.bitrate,height:t.height,width:t.width,audioGroupId:l(t)}));return i.sort((t,e)=>t.height&&e.height&&t.height!==e.height?e.height-t.height:(e.bitrate||0)-(t.bitrate||0)),1<i.length&&i.unshift({label:"Auto",level_id:"auto",hlsjsIndex:-1}),i},h=(t,e)=>Math.max(0,(0,n.cq)(e,(0,n.sE)(e,e=>e.hlsjsIndex===t))),d=(t,e,r,i=t.length)=>{var a,s,n=(()=>{try{return window.devicePixelRatio}catch(t){}return 1})();e*=n,r*=n,o.OS.tizen&&(r=e=1/0);for(let n=0;n<i;n++){const i=t[n];if((i.width>=e||i.height>=r)&&(a=i,!(s=t[n+1])||a.width!==s.width||a.height!==s.height))return n}return i-1};var c=r(4742);const g=t=>c.Z.debug?console[t].bind(console,`[Hls.js ${t}] ->`):()=>{},v=g("debug"),m=g("log"),p=g("info"),y=g("warn"),T=g("error");var E=r(548),S=r(5083);var R=r(9601),k=r(4446);let A=function(t){return t[t.BASE_ERROR=23e4]="BASE_ERROR",t[t.ERROR_LIVE_STREAM_DOWN_OR_ENDED=230001]="ERROR_LIVE_STREAM_DOWN_OR_ENDED",t[t.MANIFEST_ERROR_CONNECTION_LOST=232002]="MANIFEST_ERROR_CONNECTION_LOST",t[t.ERROR_CONNECTION_LOST=230002]="ERROR_CONNECTION_LOST",t[t.MANIFEST_PARSING_ERROR=232600]="MANIFEST_PARSING_ERROR",t[t.LEVEL_EMPTY_ERROR=232631]="LEVEL_EMPTY_ERROR",t[t.MANIFEST_INCOMPATIBLE_CODECS_ERROR=232632]="MANIFEST_INCOMPATIBLE_CODECS_ERROR",t[t.FRAG_PARSING_ERROR=233600]="FRAG_PARSING_ERROR",t[t.FRAG_DECRYPT_ERROR=233650]="FRAG_DECRYPT_ERROR",t[t.BUFFER_STALLED_ERROR=234001]="BUFFER_STALLED_ERROR",t[t.BUFFER_APPEND_ERROR=234002]="BUFFER_APPEND_ERROR",t[t.PROTECTED_CONTENT_ACCESS_ERROR=232403]="PROTECTED_CONTENT_ACCESS_ERROR",t}({});const b=t=>{if(t){if(/^frag/.test(t))return 2e3;if(/^(manifest|level|audioTrack)/.test(t))return 1e3;if(/^key/.test(t))return 4e3}return 0};var e=r(328),I=r(3949),_=r(186),w=r(3328);class C extends e.ZP{}Object.assign(C.prototype,I.Z,_.Z,w.Z);const x=C;var P=r(4506),O=r(3343),M=r(686),F=r(1643),N=r(5004),U=r(8702),B=r(386),G=r(5099),K=r(2728),H=r(7477),V=r(1384);const j=c.Z.debug?console.warn.bind(console):()=>{},W=(t,e,r)=>{var i=t.sources[0];return(void 0!==i[r]?i:void 0!==t[r]?t:e)[r]};e=r(4560);class z extends function(t){const{MEDIA_ATTACHED:e,MEDIA_DETACHED:r,MANIFEST_PARSED:s,LEVEL_LOADED:f,LEVEL_UPDATED:g,LEVEL_PTS_UPDATED:v,FRAG_CHANGED:m,FRAG_LOADED:p,LEVEL_SWITCHED:y,FRAG_PARSING_METADATA:T,BUFFER_APPENDED:D,BUFFER_CODECS:I,FRAG_BUFFERED:_,INIT_PTS_FOUND:w,NON_NATIVE_TEXT_TRACKS_FOUND:C,CUES_PARSED:Y,AUDIO_TRACKS_UPDATED:q,ERROR:X}=t.Events,{MEDIA_ERROR:z,NETWORK_ERROR:Q}=t.ErrorTypes,Z=function(t){const e=t.ErrorTypes["NETWORK_ERROR"],{MANIFEST_PARSING_ERROR:r,LEVEL_EMPTY_ERROR:i,MANIFEST_INCOMPATIBLE_CODECS_ERROR:n,FRAG_PARSING_ERROR:a,FRAG_DECRYPT_ERROR:s,BUFFER_STALLED_ERROR:o,BUFFER_APPEND_ERROR:l,INTERNAL_EXCEPTION:u,MANIFEST_LOAD_ERROR:h,MANIFEST_LOAD_TIMEOUT:d,LEVEL_LOAD_ERROR:c,LEVEL_LOAD_TIMEOUT:f,FRAG_LOAD_ERROR:g,FRAG_LOAD_TIMEOUT:v,BUFFER_SEEK_OVER_HOLE:m,BUFFER_NUDGE_ON_STALL:p}=t.ErrorDetails,y=[h,d,r,n,c,f,g,v],T=[o,m,p],E=[i,c,f];return function(t){var{details:h,response:d,type:c}=t;let f=t.fatal,g=y.indexOf(h)<0;var v=0<=T.indexOf(h);let m=0<=E.indexOf(h),p=k.ul,S=A.BASE_ERROR;switch(h){case r:S=A.MANIFEST_PARSING_ERROR;break;case i:S=A.LEVEL_EMPTY_ERROR;break;case n:p=k.zO,S=A.MANIFEST_INCOMPATIBLE_CODECS_ERROR;break;case a:S=A.FRAG_PARSING_ERROR;break;case s:S=A.FRAG_DECRYPT_ERROR;break;case o:S=A.BUFFER_STALLED_ERROR;break;case l:S=A.BUFFER_APPEND_ERROR;break;case u:S=239e3;break;default:c===e&&(!1===navigator.onLine?(g=!1,f="manifestLoadError"===h,m=!1,S=f?A.MANIFEST_ERROR_CONNECTION_LOST:A.ERROR_CONNECTION_LOST,p=k.MD):/TimeOut$/.test(h)?S=A.BASE_ERROR+1001+b(h):d&&({code:S,key:p}=(0,R.Z)(A.BASE_ERROR,d.code,t.url),S+=b(h)))}return{key:p,code:S,recoverable:g,stalling:v,suppressLevel:m,fatal:f,error:t}}}(t);return class R extends x{constructor(t,e,r){super(),this.bandwidthMonitor=(0,K.Z)(this,e.bandwidthEstimate),this.bitrateSelection=e.bitrateSelection,this.bufferStallTimeout=1e3,this.connectionTimeoutDuration=1e4,this.dvrEnd=null,this.dvrPosition=null,this.dvrUpdatedTime=0,this.eventHandler=null,this.hlsjs=null,this.hlsjsConfig=null,this.hlsjsOptions=null,this.jwConfig=e,this.lastPosition=0,this.maxRetries=3,this.playerId=t,this.processPlaylistMetadata=O.q,this.recoveryInterval=5e3,this.renderNatively=(t=e.renderCaptionsNatively,!(!o.OS.iOS&&!o.Browser.safari)||o.Browser.chrome&&t),this.savedVideoProperties=!1,this.seeking=!1,this.staleManifestDurationMultiplier=3e3,this.state=F.bc,this.supports=R.supports,this.supportsPlaybackRate=!0,this.video=r,this.playerWidth=0,this.playerHeight=0,this.playerStretching=null,this.capLevels=!1,this.levelDuration=0,this.live=!1,this.liveEdgePosition=null,this.liveEdgeUpdated=0,this.staleManifestTimeout=-1,this.connectionTimeout=-1,this.programDateSyncTime=0,this.retryCount=0,this.stallTime=-1,this.jwLevels=[],this.audioTracks=null,this.audioTracksArray=null,this.resetLifecycleVariables()}resetLifecycleVariables(){this.resetRecovery(),this.stopStaleTimeout(),this.stopConnectionTimeout(),this.stallTime=-1,this.streamBitrate=-1,this.videoFound=!1,this.videoHeight=0,this.src=null,this.currentHlsjsLevel=null,this.currentAudioTrackIndex=null,this.currentJwItem=null,this.jwLevels=[],this.audioTracks=null,this.audioTracksArray=null,this.lastRecoveryTime=null,this.lastEndSn=null,this.levelDuration=0,this.live=!1,this.liveEdgePosition=null,this.liveEdgeUpdated=0,this.liveEdgeSn=-1,this.livePaused=!1,this.recoveringMediaError=!1,this.recoveringNetworkError=!1,this.streamType="VOD",this.lastProgramDateTime=0,this.programDateSyncTime=0}resetRecovery(){this.retryCount=0}stopStaleTimeout(){clearTimeout(this.staleManifestTimeout),this.staleManifestTimeout=-1}stopConnectionTimeout(){clearTimeout(this.connectionTimeout),this.connectionTimeout=-1}startConnectionTimeout(){-1===this.connectionTimeout&&(this.connectionTimeout=window.setTimeout(()=>{navigator.onLine?this.hlsjs.startLoad():this.handleError(A.ERROR_CONNECTION_LOST,null,k.MD)},this.connectionTimeoutDuration))}initHlsjs(e){var r=this.jwConfig.hlsjsConfig,r={withCredentials:Boolean(W(e,this.jwConfig,"withCredentials")),aesToken:W(e,this.jwConfig,"aestoken"),renderTextTracksNatively:this.renderNatively,onXhrOpen:e.sources[0].onXhrOpen,liveSyncDuration:W(e,this.jwConfig,"liveSyncDuration"),hlsjsConfig:r};this.setupSideloadedTracks(e.tracks),this.capLevels=!e.stereomode,this.hlsjs&&(0,n.wB)(this.hlsjsOptions)(r)||(this.hlsjsOptions=r,this.restoreVideoProperties(),this.stopStaleTimeout(),this.stopConnectionTimeout(),this.hlsjsConfig=function(t){var{withCredentials:t,aesToken:r,renderTextTracksNatively:a,onXhrOpen:s,liveSyncDuration:o,hlsjsConfig:l}=t,l=(0,n.ei)(l||{},["liveSyncDuration","liveSyncDurationCount","liveMaxLatencyDuration","liveMaxLatencyDurationCount","liveBackBufferLength","backBufferLength","loader","pLoader","fLoader","fragLoadingMaxRetry","fragLoadingRetryDelay","enableWorker","debug"]),a={autoStartLoad:!1,capLevelToPlayerSize:!1,captionsTextTrack1Label:"",captionsTextTrack2Label:"",captionsTextTrack1LanguageCode:"",captionsTextTrack2LanguageCode:"",debug:!!c.Z.debug&&i,fragLoadingMaxRetry:2,fragLoadingRetryDelay:4e3,maxMaxBufferLength:E.i0,renderTextTracksNatively:a,startLevel:-1,testBandwidth:!1},{liveSyncDurationCount:d,liveMaxLatencyDurationCount:f,liveMaxLatencyDuration:g}=l;return void 0!==d||void 0!==f?(l.liveSyncDuration=l.liveMaxLatencyDuration=void 0,l.liveSyncDurationCount=(0,n.xV)(d)?d:1/0,l.liveMaxLatencyDurationCount=(0,n.xV)(f)?f:1/0):void 0===o&&void 0===g||(l.liveSyncDurationCount=l.liveMaxLatencyDurationCount=void 0,a.liveSyncDuration=(0,S.G0)(o),l.liveMaxLatencyDuration=(0,n.xV)(g)?g:1/0),t||r||s?Object.assign({},a,((t,e,r)=>({xhrSetup(i,n){if(t&&(i.withCredentials=!0),e){const t=0<n.indexOf("?")?"&token=":"?token=";i.open("GET",n+t+e,!0)}"function"==typeof r&&r(i,n)},fetchSetup(r,i){if(e){const t=0<r.url.indexOf("?")?"&token=":"?token=";r.url=r.url+t+e}return t&&(i.credentials="include"),new Request(r.url,i)}}))(t,r,s),l):Object.assign({},a,l)}(r),e=Object.assign({},this.hlsjsConfig),r=this.bandwidthMonitor.getEstimate(),(0,n.qh)(r)&&(e.abrEwmaDefaultEstimate=r),e.appendErrorMaxRetry=1,this.hlsjs=new t(e),this.eventHandler=new a(this.video,this.createVideoListeners(),this.hlsjs,this.createHlsjsListeners()))}init(t){this.destroy(),this.initHlsjs(t)}preload(t){"metadata"===t.preload&&(this.maxBufferLength=E.Zv),this.load(t)}load(t){var{hlsjs:e,video:r,src:i}=this;if(e){window.hlsdata=e,document.dispatchEvent(new Event("hlsdataReady"));var n=t.sources[0].file,n=n.url&&"string"==typeof n.url?n.url:n;if(i===n&&this.videoSrc===r.src)this.maxBufferLength=E.i0;else{let s=t.starttime||-1;s<-1&&(s=this.lastPosition),this.initHlsjs(t),this.currentJwItem=t,this.src=n,this.videoHeight=0,this._eventsOn(),e.config.startPosition=s,e.loadSource(n),e.attachMedia(r),this.videoSrc=r.src}}}restartStream(t){var e=Object.assign({},this.currentJwItem);t?e.starttime=t:delete e.starttime,this.src=null,this._clearNonNativeCues(),this.clearMetaCues(),this.clearTracks(),this.init(e),this.load(e),delete e.starttime}play(){return this.livePaused&&(this.livePaused=!1,this.restartStream()),this.video.play()||(0,G.Z)(this.video)}pause(){this.stopConnectionTimeout(),this.live&&"LIVE"===this.streamType&&!this.livePaused&&this.livePause(),this.video.pause()}livePause(){this.hlsjs&&(this.livePaused=!0,this.hlsjs.stopLoad(),this.stopStaleTimeout())}stop(){this.clearTracks(),this.hlsjs&&(this._eventsOff(),this.hlsjs.stopLoad()),this.pause(),this.setState(F.bc)}seek(t){var e=this.getDuration();if(e&&e!==1/0&&!(0,n.i2)(e)){this.stopStaleTimeout(),this.stopConnectionTimeout();let r=this.dvrEnd&&t<0?this.dvrEnd+t:t;var i=this.getSeekRange(),t=("DVR"===this.streamType&&null!==this.dvrEnd&&(this.dvrPosition=r-this.dvrEnd,t<0)&&(r+=Math.min(12,((0,N.z)()-this.dvrUpdatedTime)/1e3)),this.seeking=!0,this.video.currentTime),t=(this.trigger(F.NZ,{position:this.getCurrentTime(),offset:r,duration:e,currentTime:t,seekRange:i,metadata:{currentTime:t}}),this.video.currentTime=r,this.video.currentTime),e={position:this.getCurrentTime(),duration:e,currentTime:t,seekRange:i,metadata:{currentTime:t}};this.trigger("time",e)}}getCurrentQuality(){let t=0;return t=this.hlsjs&&!this.hlsjs.autoLevelEnabled?h(this.hlsjs.manualLevel,this.jwLevels):t}getQualityLevels(){return(0,n.UI)(this.jwLevels,t=>(0,P.Z)(t))}getCurrentAudioTrack(){return(0,n.hj)(this.currentAudioTrackIndex)?this.currentAudioTrackIndex:-1}getAudioTracks(){return this.audioTracks||[]}getCurrentTime(){return this.live&&"DVR"===this.streamType?(this.dvrPosition||this.updateDvrPosition(this.getSeekRange()),this.dvrPosition):this.video.currentTime}getDuration(){var t,e;return this.live&&this.currentJwItem?(t=this.levelDuration,e=this.currentJwItem.minDvrWindow,(0,M.s)(t,e)?(this.streamType="DVR",-t):(this.streamType="LIVE",1/0)):(this.streamType="VOD",this.video.duration)}getCurrentHlsjsLevel(){let t=0;var e=this["hlsjs"];return t=e?e.streamController.loadedmetadata&&0<e.currentLevel?e.currentLevel:e.firstLevel:t}getName(){return{name:"hlsjs"}}getPlaybackRate(){return this.video.playbackRate}getSeekRange(){var{levelDuration:t,video:e}=this,{seekable:e,duration:i}=e,e=e.length?Math.max(e.end(0),e.end(e.length-1)):i;return(0,n.i2)(i)?{start:0,end:0}:{start:Math.max(0,e-t),end:e}}getBandwidthEstimate(){var t=this["hlsjs"];return t?t.bandwidthEstimate:null}getLiveLatency(){let t=null;var r,e;return this.live&&null!==this.liveEdgePosition&&(e=(0,N.z)(),t=this.liveEdgePosition+(e-this.liveEdgeUpdated)/1e3-this.video.currentTime,r=this.lastProgramDateTime)&&0<(e=e/1e3-(r/1e3+(this.video.currentTime-this.programDateSyncTime))-t)&&e<10&&(t+=e),t}getTargetLatency(){return this.hlsjs&&this.hlsjs.targetLatency||null}setCurrentQuality(t){var e;t<0||(e=((t,e)=>{let r=-1;return r=-1<t&&e[t]?e[t].hlsjsIndex:r})(t,this.jwLevels),this.hlsjs.nextLevel=e,this.trigger(F.aM,{levels:this.jwLevels,currentQuality:t}),this.bitrateSelection=this.jwLevels[t].bitrate)}setCurrentAudioTrack(t){var e=this.getCurrentHlsjsLevel(),r=this.hlsjs.levels[e],e=h(e,this.jwLevels);if(this.jwLevels&&this.jwLevels[e]&&r&&this.audioTracksArray&&(0,n.dp)(this.audioTracksArray)&&(0,n.hj)(t)){e=this.audioTracks=this.audioTracksArray;if(e&&(0,n.dp)(e)&&e[t]&&this.currentAudioTrackIndex!==t){this.trigger(F.j0,{tracks:e,currentTrack:t});let s=(e=this.audioTracks)[t];null!==this.currentAudioTrackIndex&&s.hlsjsIndex!==this.hlsjs.audioTrack&&(this.trigger(F._B,{tracks:e,currentTrack:t}),s=this.audioTracks[t]),this.currentAudioTrackIndex=t,s.hlsjsIndex!==this.hlsjs.audioTrack&&(this.hlsjs.audioTrack=s.hlsjsIndex)}}}updateAudioTrack(t){if(this.hlsjs&&this.hlsjs.audioTracks.length){let e=this.currentAudioTrackIndex;(0,n.hj)(e)?this.audioTracks&&this.audioTracks[e].hlsjsIndex===this.hlsjs.audioTrack||(this.currentAudioTrackIndex=null):e=this.audioTracksArray?((t=[])=>Math.max((0,n.cq)(t,(0,n.sE)(t,t=>t.defaulttrack)),0))(this.audioTracksArray):0,this.setCurrentAudioTrack(e)}}updateDvrPosition(t){this.dvrPosition=this.video.currentTime-t.end,this.dvrEnd=t.end,this.dvrUpdatedTime=(0,N.z)()}setCurrentSubtitleTrack(t){this.hlsjs.subtitleTrack=t}setPlaybackRate(t){this.video.playbackRate=this.video.defaultPlaybackRate=t}get maxBufferLength(){return this.hlsjs?this.hlsjs.config.maxMaxBufferLength:NaN}set maxBufferLength(t){this.hlsjs&&(this.hlsjs.config.maxMaxBufferLength=t)}isLive(){return this.live}checkAdaptation(t){var{levels:e,autoLevelEnabled:r}=this.hlsjs,i=e[t];if(i){let{width:n,height:a,bitrate:s}=i;if(n=n||this.video.videoWidth,(a=a||this.video.videoHeight)!==this.videoHeight||s!==this.streamBitrate){const l=h(t,this.jwLevels);let u="api";-1!==this.streamBitrate&&this.streamBitrate||this.videoHeight?r&&(u="auto"):u="initial choice",this.videoHeight=a,this.streamBitrate=s;const d=r?"auto":"manual",c=r&&1<e.length?"auto":this.jwLevels[l].label,f=()=>{this.trigger(F.ug,{jwLevel:this.jwLevels[l],reason:u,mode:d,level:{bitrate:s,index:l,label:c,width:n,height:a}})};o.Browser.ie?this.once("time",f,this):f()}}}createVideoListeners(){const t={waiting:()=>{this.startConnectionTimeout(),this.seeking?this.setState(F.ik):this.state===F.r0&&(this.atEdgeOfLiveStream()&&this.setPlaybackRate(1),this.stallTime=this.video.currentTime,this.setState(F.nQ))}};return Object.keys(U.Z).forEach(e=>{const r=U.Z[e];"playing"===e?t[e]=function(){var t=this.getCurrentHlsjsLevel();this.checkAdaptation(t),r.call(this)}.bind(this):"ended"===e?t[e]=function(){this.videoHeight=0,this.streamBitrate=-1,r.call(this)}.bind(this):"error"!==e&&(t[e]=r.bind(this))}),t}createHlsjsListeners(){var t={};return t[e]=()=>{this.recoveringMediaError&&(this.hlsjs.startLoad(),this.recoveringMediaError=!1,this.resetRecovery(),this.stopStaleTimeout(),this.stopConnectionTimeout())},t[r]=()=>{this._clearNonNativeCues()},t[s]=(t,e)=>{var r=e["levels"],i=this.hlsjs,{bitrateSelection:a,jwConfig:s}=this;let o=-1,l=-1;if(this.currentHlsjsLevel=null,this.jwLevels=u(r,s.qualityLabels),this.capLevels&&(this.playerWidth||this.playerHeight)&&this.playerStretching){const t=d(r,this.playerWidth,this.playerHeight,e.firstLevel+1);i.levelController.firstLevel!==t&&(i.firstLevel=t),this.resize(this.playerWidth,this.playerHeight,this.playerStretching)}(0,n.qh)(a)&&(o=((t,e)=>{if(!e)return-1;let r=Number.MAX_VALUE,i=-1;for(let n=0;n<t.length;n++){var a=t[n];if(a.bitrate){a=Math.abs(e-a.bitrate);if(a<=r&&(r=a,i=n),!a)break}}return i})(r,a),l=o),i.startLevel=o,i.nextLevel=l,i.startLoad(i.config.startPosition),this.trigger(F.UZ,{levels:this.jwLevels,currentQuality:h(o,this.jwLevels)})},t[f]=(t,e)=>{var{endSN:e,live:i,targetduration:n}=e.details;this.checkStaleManifest(e,i,n)},t[g]=(t,e)=>{var{live:e,totalduration:i}=e.details,i=(this.live=e,this.levelDuration=i,this.getSeekRange()),a=null!==this.dvrEnd&&1<Math.abs(this.dvrEnd-i.end);"DVR"===this.streamType&&a&&this.updateDvrPosition(i),e&&this.state===F.bc&&this.livePause()},t[v]=(t,e)=>{var{fragments:e,totalduration:i}=e.details;if(this.levelDuration=i,e.length){const t=e[e.length-1];t.sn!==this.liveEdgeSn&&(this.liveEdgeUpdated=(0,N.z)(),this.liveEdgeSn=t.sn,this.liveEdgePosition=t.start+t.duration)}},t[y]=(t,e)=>{e=e.level;e!==this.currentHlsjsLevel?this.setCurrentLevel(e):this.checkAdaptation(e)},t[p]=(t,e)=>{this.lastProgramDateTime=e.frag.programDateTime,this.programDateSyncTime=e.frag.start,this.lastProgramDateTime&&!this.startDateTime&&(this.startDateTime=this.lastProgramDateTime,this.trigger(F.AQ,{ready:!0,startDateTime:this.startDateTime}))},t[m]=(t,e)=>{this.lastProgramDateTime=e.frag.programDateTime,this.programDateSyncTime=e.frag.start},t[T]=(t,e)=>{e.samples&&([].some.call(this.video.textTracks,t=>!t.inuse)&&this.setTextTracks(this.video.textTracks),null!=e)&&e.samples&&e.samples.forEach(t=>{this.trigger(F.rx,{metadataType:"dai-hls",metadata:{messageData:t.data,start:t.pts,type:"ID3"}})})},t[D]=()=>{-1!==this.connectionTimeout&&this.stopConnectionTimeout(),this.atEdgeOfLiveStream()||this.stopStaleTimeout(),this.recoveringNetworkError&&(this.resetRecovery(),this.recoveringNetworkError=!1)},t[I]=(t,e)=>{e.audio&&this.videoFound||(e=e.audiovideo||e.video?"video":"audio",this.videoFound=this.videoFound||"video"==e,this.trigger(F.oZ,{mediaType:e}))},t[_]=(t,e)=>{const r=e.frag;(r.tagList||[]).forEach(([t,e])=>this.processPlaylistMetadata(t,e,r))},t[w]=(t,e)=>{var{frag:e,initPTS:i}=e;this.processPlaylistMetadata("DISCONTINUITY",i,e)},this.renderNatively||(t[C]=(t,e)=>{this.addTextTracks(e.tracks)},t[Y]=(t,e)=>{var r;if(null!=e&&null!=(r=e.cues)&&r.length){let t;const r=!(e.cues[0]instanceof H.Z);e.cues.forEach(i=>{if(r){const t=i;(i=new H.Z(t.startTime,t.endTime,t.text)).position=t.position}t=t||e.cues.filter(t=>t.startTime===i.startTime).length,i.align="center",i.line=90-5*t,i.position=50,this.addVTTCue({type:e.type,cue:i,track:e.track}),t--})}}),t[q]=(t,e)=>{const r=e["audioTracks"],i=this.hlsjs.levels,a=this.getCurrentHlsjsLevel();null!=r&&r.length&&(this.audioTracksArray=(0,n.u4)(r,(t,e,r)=>(t.push({autoselect:e.autoselect,defaulttrack:e.default,groupid:e.groupId,language:e.lang,name:e.name,hlsjsIndex:r}),t),[]),this.jwLevels.forEach(t=>{var e=0<t.hlsjsIndex?i[t.hlsjsIndex]:null;e&&(t.audioGroupId=l(e))}),this.updateAudioTrack(i[a]))},t[X]=(t,e)=>{const r=this.hlsjs,i=Z(e),n=e["type"],a=i["key"];let s;if(j(e),"DVR"===this.streamType&&n===Q){const t=this.getSeekRange();this.updateDvrPosition(t)}if(232403===i.code&&this.retryCount<this.maxRetries&&/jwpsrv.com\/.*\?token=/.test(e.url)&&(i.suppressLevel=!1,i.recoverable=!0,i.fatal=!0,s=!0,this.maxRetries=1),i.suppressLevel){const t=r.levels,n=e.context||e,s=n["level"],o=t[s];if(o&&Array.isArray(o.url)&&1===o.url.length){if(r.removeLevel(s,0),!r.levels.length)return void this.handleError(i.code,e,a);i.fatal=!1,this.jwLevels=u(r.levels,this.jwConfig.qualityLabels),this.playerWidth&&this.playerHeight&&this.playerStretching&&this.resize(this.playerWidth,this.playerHeight,this.playerStretching),r.loadLevel=0,r.currentLevel=-1,this.trigger(F.UZ,{levels:this.jwLevels,currentQuality:0})}}if(i.fatal){const t=(0,N.z)(),o=i.recoverable&&(n===Q||n===z),l=this.retryCount;if(!(o&&l<this.maxRetries))return r.stopLoad(),void this.handleError(i.code,e,a);(!this.lastRecoveryTime||t>=this.lastRecoveryTime+this.recoveryInterval)&&(j("Attempting to recover, retry count:",l),n===Q?/^manifest/.test(e.details)||s?(this.recoverManifestError(),this.retryCount=l):r.startLoad():n===z&&("bufferAppendError"===e.details?(j("Encountered a bufferAppendError in hls not attempting to recover media and destroying instance"),r.destroy()):(this.recoveringMediaError=!0,r.recoverMediaError())),this.recoveringNetworkError=!0,this.retryCount+=1,this.lastRecoveryTime=t)}this.trigger(F.cM,new k.rG(null,i.code+1e5,e))},t}resize(t,e,r){if(this.playerWidth=t,this.playerHeight=e,this.playerStretching=r,this.capLevels){const t=this.hlsjs;if(null!=t&&t.levels){const e=t.autoLevelCapping,r=d(t.levels,this.playerWidth,this.playerHeight);r!==e&&(t.autoLevelCapping=r)>e&&-1!==e&&this.state!==F.bc&&this.state!==F.xQ&&t.streamController.nextLevelSwitch()}}super.resize(t,e,r)}recoverManifestError(){var{currentTime:t,paused:e}=this.video;t||!e?(this.restartStream(t),e||this.play().catch(()=>{})):(this.hlsjs.stopLoad(),this.hlsjs.loadSource(this.src))}_eventsOn(){var{bandwidthMonitor:t,eventHandler:e,video:r}=this;e&&e.on(),t.start(),(0,V.Nm)(this,r)}_eventsOff(){var{bandwidthMonitor:t,eventHandler:e,hlsjs:r,video:i}=this;r&&e&&(this.disableTextTrack(),this.lastPosition=this.video.currentTime,r.detachMedia(),e.off()),this.off(null,null,this),t.stop(),this.resetLifecycleVariables(),(0,V.IP)(i)}handleError(t,e,r){this.resetLifecycleVariables(),this.trigger(F.Ew,new k.rG(r,t,e))}destroy(){this.hlsjs&&(this._eventsOff(),this.hlsjs.destroy(),this.hlsjs=null,this.hlsjsOptions=null)}restoreVideoProperties(){this.savedVideoProperties&&(this.volume(this.jwConfig.volume),this.mute(this.jwConfig.mute),this.savedVideoProperties=!1)}checkStaleManifest(t,e,r){r=null!==this.jwConfig.liveTimeout?1e3*this.jwConfig.liveTimeout:this.staleManifestDurationMultiplier*r;e&&this.lastEndSn===t&&0!=r?-1===this.staleManifestTimeout&&(this.staleManifestTimeout=window.setTimeout(()=>{this.checkStreamEnded()},r)):this.stopStaleTimeout(),this.lastEndSn=t,this.live=e}checkStreamEnded(){this.hlsjs&&(this.video.ended||this.atEdgeOfLiveStream())&&(this.hlsjs.stopLoad(),this.handleError(A.ERROR_LIVE_STREAM_DOWN_OR_ENDED,null,k.Sp))}setCurrentLevel(t){this.currentHlsjsLevel=t,this.checkAdaptation(t),this.updateAudioTrack(this.hlsjs.levels[t])}_clearNonNativeCues(){!this.renderNatively&&this._textTracks&&this._textTracks.forEach(t=>{this.clearCueData(t._id)})}static setEdition(t){R.supports=(0,B.Z)(t)}}}(r.n(e)()){getName(){return{name:"hlsjs"}}static getName(){return{name:"hlsjs"}}}},3343:(t,e,r)=>{"use strict";r.d(e,{q:()=>s});var i=r(6042),n=r(1643);const a=(t,e)=>{t=t[e];return(0,i.xV)(t)&&0<=t?t:null},s=function(t,e,r){var s=((t,e,r)=>{let n,s;if(null===(n=(0,i.xV)(r.startPTS)?a(r,"startPTS"):a(r,"start")))return null;switch(t){case"PROGRAM-DATE-TIME":return{metadataType:s="program-date-time",programDateTime:e,start:n,end:n+a(r,"duration")};case"EXT-X-DATERANGE":{const a={},o=e.split(",").map(t=>{var t=t.split("="),r=t[0],t=(t[1]||"").replace(/^"|"$/g,"");return{name:r,value:a[r]=t}}),l=a["START-DATE"];if(!l)return null;var u=a["END-DATE"];let h=n;if((0,i.xV)(r.programDateTime)&&(h+=(new Date(l).getTime()-new Date(r.programDateTime).getTime())/1e3),isNaN(h))return null;let d=parseFloat(a["PLANNED-DURATION"]||a.DURATION)||0;return!d&&u&&(d=(new Date(u).getTime()-new Date(l).getTime())/1e3),{metadataType:s="date-range",tag:t,content:e,attributes:o,start:h,end:h+d,startDate:l,endDate:u,duration:d}}case"EXT-X-CUE-IN":case"EXT-X-CUE-OUT":return{metadataType:s="scte-35",tag:t,content:e,start:n,end:n+(parseFloat(e)||0)};case"DISCONTINUITY":{const i=n+a(r,"duration");let o;return"cc"in r&&(o=r.cc),{metadataType:s="discontinuity",tag:t,discontinuitySequence:o,PTS:e,start:n,end:i}}default:return null}})(t,e,r);if(s&&(0,i.xV)(s.start)){const a=this.createCue(s.start,s.end,JSON.stringify(s)),o=r.sn+`_${t}_`+e;if(this.addVTTCue({type:"metadata",cue:a},o)){const t=s.metadataType;delete s.metadataType,this.trigger(n.O1,{metadataType:t,metadata:s})}}}},4560:function(t,e){!function r(i){var n=this,a=function(){"use strict";function t(t,e){var i,r=Object.keys(t);return Object.getOwnPropertySymbols&&(i=Object.getOwnPropertySymbols(t),e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,i)),r}function e(e){for(var r=1;r<arguments.length;r++){var i=null!=arguments[r]?arguments[r]:{};r%2?t(Object(i),!0).forEach(function(t){!function(t,e,r){(e=v(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r}(e,t,i[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):t(Object(i)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))})}return e}function n(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,v(i.key),i)}}function a(t,e,r){e&&n(t.prototype,e),r&&n(t,r),Object.defineProperty(t,"prototype",{writable:!1})}function o(){return(o=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i,r=arguments[e];for(i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t}).apply(this,arguments)}function l(t,e){t.prototype=Object.create(e.prototype),h(t.prototype.constructor=t,e)}function u(t){return(u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h(t,e){return(h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function d(t,e,r){return(d=function(){if("undefined"!=typeof Reflect&&Reflect.construct&&!Reflect.construct.sham){if("function"==typeof Proxy)return 1;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),1}catch(t){}}}()?Reflect.construct.bind():function(t,e,r){var i=[null],e=(i.push.apply(i,e),new(Function.bind.apply(t,i)));return r&&h(e,r.prototype),e}).apply(null,arguments)}function c(t){var e="function"==typeof Map?new Map:void 0;return(c=function(t){if(null===t||-1===Function.toString.call(t).indexOf("[native code]"))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,i)}function i(){return d(t,arguments,u(this).constructor)}return i.prototype=Object.create(t.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),h(i,t)})(t)}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,i=new Array(e);r<e;r++)i[r]=t[r];return i}function g(t,e){var i,r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,e){var r;if(t)return"string"==typeof t?f(t,e):"Map"===(r="Object"===(r=Object.prototype.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:r)||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}(t))||e&&t&&"number"==typeof t.length)return r&&(t=r),i=0,function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function v(t){t=function(t){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0===r)return String(t);r=r.call(t,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}(t);return"symbol"==typeof t?t:String(t)}function L(){}var m={exports:{}},p=(!function(){var r=/^(?=((?:[a-zA-Z0-9+\-.]+:)?))\1(?=((?:\/\/[^\/?#]*)?))\2(?=((?:(?:[^?#\/]*\/)*[^;?#\/]*)?))\3((?:;[^?#]*)?)(\?[^#]*)?(#[^]*)?$/,i=/^(?=([^\/?#]*))\1([^]*)$/,n=/(?:\/|^)\.(?=\/)/g,a=/(?:\/|^)\.\.\/(?!\.\.\/)[^\/]*(?=\/)/g,s={buildAbsoluteURL:function(t,e,r){if(r=r||{},t=t.trim(),!(e=e.trim())){if(!r.alwaysNormalize)return t;var n=s.parseURL(t);if(n)return n.path=s.normalizePath(n.path),s.buildURLFromParts(n);throw new Error("Error trying to parse base URL.")}n=s.parseURL(e);if(!n)throw new Error("Error trying to parse relative URL.");if(n.scheme)return r.alwaysNormalize?(n.path=s.normalizePath(n.path),s.buildURLFromParts(n)):e;e=s.parseURL(t);if(!e)throw new Error("Error trying to parse base URL.");!e.netLoc&&e.path&&"/"!==e.path[0]&&(t=i.exec(e.path),e.netLoc=t[1],e.path=t[2]),e.netLoc&&!e.path&&(e.path="/");var h,t={scheme:e.scheme,netLoc:n.netLoc,path:null,params:n.params,query:n.query,fragment:n.fragment};return n.netLoc||(t.netLoc=e.netLoc,"/"===n.path[0])||(n.path?(h=(h=e.path).substring(0,h.lastIndexOf("/")+1)+n.path,t.path=s.normalizePath(h)):(t.path=e.path,n.params||(t.params=e.params,n.query)||(t.query=e.query))),null===t.path&&(t.path=r.alwaysNormalize?s.normalizePath(n.path):n.path),s.buildURLFromParts(t)},parseURL:function(t){t=r.exec(t);return t?{scheme:t[1]||"",netLoc:t[2]||"",path:t[3]||"",params:t[4]||"",query:t[5]||"",fragment:t[6]||""}:null},normalizePath:function(t){for(t=t.split("").reverse().join("").replace(n,"");t.length!==(t=t.replace(a,"")).length;);return t.split("").reverse().join("")},buildURLFromParts:function(t){return t.scheme+t.netLoc+t.path+t.params+t.query+t.fragment}};m.exports=s}(),m.exports),y=Number.isFinite||function(t){return"number"==typeof t&&isFinite(t)},T=function(t){return t.MEDIA_ATTACHING="hlsMediaAttaching",t.MEDIA_ATTACHED="hlsMediaAttached",t.MEDIA_DETACHING="hlsMediaDetaching",t.MEDIA_DETACHED="hlsMediaDetached",t.BUFFER_RESET="hlsBufferReset",t.BUFFER_CODECS="hlsBufferCodecs",t.BUFFER_CREATED="hlsBufferCreated",t.BUFFER_APPENDING="hlsBufferAppending",t.BUFFER_APPENDED="hlsBufferAppended",t.BUFFER_EOS="hlsBufferEos",t.BUFFER_FLUSHING="hlsBufferFlushing",t.BUFFER_FLUSHED="hlsBufferFlushed",t.MANIFEST_LOADING="hlsManifestLoading",t.MANIFEST_LOADED="hlsManifestLoaded",t.MANIFEST_PARSED="hlsManifestParsed",t.LEVEL_SWITCHING="hlsLevelSwitching",t.LEVEL_SWITCHED="hlsLevelSwitched",t.LEVEL_LOADING="hlsLevelLoading",t.LEVEL_LOADED="hlsLevelLoaded",t.LEVEL_UPDATED="hlsLevelUpdated",t.LEVEL_PTS_UPDATED="hlsLevelPtsUpdated",t.LEVELS_UPDATED="hlsLevelsUpdated",t.AUDIO_TRACKS_UPDATED="hlsAudioTracksUpdated",t.AUDIO_TRACK_SWITCHING="hlsAudioTrackSwitching",t.AUDIO_TRACK_SWITCHED="hlsAudioTrackSwitched",t.AUDIO_TRACK_LOADING="hlsAudioTrackLoading",t.AUDIO_TRACK_LOADED="hlsAudioTrackLoaded",t.SUBTITLE_TRACKS_UPDATED="hlsSubtitleTracksUpdated",t.SUBTITLE_TRACKS_CLEARED="hlsSubtitleTracksCleared",t.SUBTITLE_TRACK_SWITCH="hlsSubtitleTrackSwitch",t.SUBTITLE_TRACK_LOADING="hlsSubtitleTrackLoading",t.SUBTITLE_TRACK_LOADED="hlsSubtitleTrackLoaded",t.SUBTITLE_FRAG_PROCESSED="hlsSubtitleFragProcessed",t.CUES_PARSED="hlsCuesParsed",t.NON_NATIVE_TEXT_TRACKS_FOUND="hlsNonNativeTextTracksFound",t.INIT_PTS_FOUND="hlsInitPtsFound",t.FRAG_LOADING="hlsFragLoading",t.FRAG_LOAD_EMERGENCY_ABORTED="hlsFragLoadEmergencyAborted",t.FRAG_LOADED="hlsFragLoaded",t.FRAG_DECRYPTED="hlsFragDecrypted",t.FRAG_PARSING_INIT_SEGMENT="hlsFragParsingInitSegment",t.FRAG_PARSING_USERDATA="hlsFragParsingUserdata",t.FRAG_PARSING_METADATA="hlsFragParsingMetadata",t.FRAG_PARSED="hlsFragParsed",t.FRAG_BUFFERED="hlsFragBuffered",t.FRAG_CHANGED="hlsFragChanged",t.FPS_DROP="hlsFpsDrop",t.FPS_DROP_LEVEL_CAPPING="hlsFpsDropLevelCapping",t.ERROR="hlsError",t.DESTROYING="hlsDestroying",t.KEY_LOADING="hlsKeyLoading",t.KEY_LOADED="hlsKeyLoaded",t.LIVE_BACK_BUFFER_REACHED="hlsLiveBackBufferReached",t.BACK_BUFFER_REACHED="hlsBackBufferReached",t}({}),E=function(t){return t.NETWORK_ERROR="networkError",t.MEDIA_ERROR="mediaError",t.KEY_SYSTEM_ERROR="keySystemError",t.MUX_ERROR="muxError",t.OTHER_ERROR="otherError",t}({}),S=function(t){return t.KEY_SYSTEM_NO_KEYS="keySystemNoKeys",t.KEY_SYSTEM_NO_ACCESS="keySystemNoAccess",t.KEY_SYSTEM_NO_SESSION="keySystemNoSession",t.KEY_SYSTEM_NO_CONFIGURED_LICENSE="keySystemNoConfiguredLicense",t.KEY_SYSTEM_LICENSE_REQUEST_FAILED="keySystemLicenseRequestFailed",t.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED="keySystemServerCertificateRequestFailed",t.KEY_SYSTEM_SERVER_CERTIFICATE_UPDATE_FAILED="keySystemServerCertificateUpdateFailed",t.KEY_SYSTEM_SESSION_UPDATE_FAILED="keySystemSessionUpdateFailed",t.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED="keySystemStatusOutputRestricted",t.KEY_SYSTEM_STATUS_INTERNAL_ERROR="keySystemStatusInternalError",t.MANIFEST_LOAD_ERROR="manifestLoadError",t.MANIFEST_LOAD_TIMEOUT="manifestLoadTimeOut",t.MANIFEST_PARSING_ERROR="manifestParsingError",t.MANIFEST_INCOMPATIBLE_CODECS_ERROR="manifestIncompatibleCodecsError",t.LEVEL_EMPTY_ERROR="levelEmptyError",t.LEVEL_LOAD_ERROR="levelLoadError",t.LEVEL_LOAD_TIMEOUT="levelLoadTimeOut",t.LEVEL_PARSING_ERROR="levelParsingError",t.LEVEL_SWITCH_ERROR="levelSwitchError",t.AUDIO_TRACK_LOAD_ERROR="audioTrackLoadError",t.AUDIO_TRACK_LOAD_TIMEOUT="audioTrackLoadTimeOut",t.SUBTITLE_LOAD_ERROR="subtitleTrackLoadError",t.SUBTITLE_TRACK_LOAD_TIMEOUT="subtitleTrackLoadTimeOut",t.FRAG_LOAD_ERROR="fragLoadError",t.FRAG_LOAD_TIMEOUT="fragLoadTimeOut",t.FRAG_DECRYPT_ERROR="fragDecryptError",t.FRAG_PARSING_ERROR="fragParsingError",t.FRAG_GAP="fragGap",t.REMUX_ALLOC_ERROR="remuxAllocError",t.KEY_LOAD_ERROR="keyLoadError",t.KEY_LOAD_TIMEOUT="keyLoadTimeOut",t.BUFFER_ADD_CODEC_ERROR="bufferAddCodecError",t.BUFFER_INCOMPATIBLE_CODECS_ERROR="bufferIncompatibleCodecsError",t.BUFFER_APPEND_ERROR="bufferAppendError",t.BUFFER_APPENDING_ERROR="bufferAppendingError",t.BUFFER_STALLED_ERROR="bufferStalledError",t.BUFFER_FULL_ERROR="bufferFullError",t.BUFFER_SEEK_OVER_HOLE="bufferSeekOverHole",t.BUFFER_NUDGE_ON_STALL="bufferNudgeOnStall",t.INTERNAL_EXCEPTION="internalException",t.INTERNAL_ABORTED="aborted",t.UNKNOWN="unknown",t}({}),R={trace:L,debug:L,log:L,warn:L,info:L,error:L},k=R;function A(t,e){if(self.console&&!0===t||"object"==typeof t){!function(t){for(var e=arguments.length,r=new Array(1<e?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];r.forEach(function(e){k[e]=t[e]?t[e].bind(t):function(t){var e=self.console[t];return e?e.bind(self.console,"["+t+"] >"):L}(e)})}(t,"debug","log","info","warn","error");try{k.log('Debug logs enabled for "'+e+'" in hls.js version 1.4.3')}catch(t){k=R}}else k=R}var b=k,D=/^(\d+)x(\d+)$/,I=/(.+?)=(".*?"|.*?)(?:,|$)/g,_=function(){function t(e){for(var r in e="string"==typeof e?t.parseAttrList(e):e)e.hasOwnProperty(r)&&("X-"===r.substring(0,2)&&(this.clientAttrs=this.clientAttrs||[],this.clientAttrs.push(r)),this[r]=e[r])}var e=t.prototype;return e.decimalInteger=function(t){t=parseInt(this[t],10);return t>Number.MAX_SAFE_INTEGER?1/0:t},e.hexadecimalInteger=function(t){if(this[t]){for(var e=(1&(e=(this[t]||"0x").slice(2)).length?"0":"")+e,r=new Uint8Array(e.length/2),i=0;i<e.length/2;i++)r[i]=parseInt(e.slice(2*i,2*i+2),16);return r}return null},e.hexadecimalIntegerAsNumber=function(t){t=parseInt(this[t],16);return t>Number.MAX_SAFE_INTEGER?1/0:t},e.decimalFloatingPoint=function(t){return parseFloat(this[t])},e.optionalFloat=function(t,e){t=this[t];return t?parseFloat(t):e},e.enumeratedString=function(t){return this[t]},e.bool=function(t){return"YES"===this[t]},e.decimalResolution=function(t){t=D.exec(this[t]);if(null!==t)return{width:parseInt(t[1],10),height:parseInt(t[2],10)}},t.parseAttrList=function(t){var e,r={};for(I.lastIndex=0;null!==(e=I.exec(t));){var i=e[2];0===i.indexOf('"')&&i.lastIndexOf('"')===i.length-1&&(i=i.slice(1,-1)),r[e[1].trim()]=i}return r},t}();var C=function(){function t(t,e){if(this.attr=void 0,this._startDate=void 0,this._endDate=void 0,this._badValueForSameId=void 0,e){var i,r=e.attr;for(i in r)if(Object.prototype.hasOwnProperty.call(t,i)&&t[i]!==r[i]){b.warn('DATERANGE tag attribute: "'+i+'" does not match for tags with ID: "'+t.ID+'"'),this._badValueForSameId=i;break}t=o(new _({}),r,t)}this.attr=t,this._startDate=new Date(t["START-DATE"]),"END-DATE"in this.attr&&(e=new Date(this.attr["END-DATE"]),y(e.getTime()))&&(this._endDate=e)}return a(t,[{key:"id",get:function(){return this.attr.ID}},{key:"class",get:function(){return this.attr.CLASS}},{key:"startDate",get:function(){return this._startDate}},{key:"endDate",get:function(){var t;return this._endDate||(null!==(t=this.duration)?new Date(this._startDate.getTime()+1e3*t):null)}},{key:"duration",get:function(){if("DURATION"in this.attr){var t=this.attr.decimalFloatingPoint("DURATION");if(y(t))return t}else if(this._endDate)return(this._endDate.getTime()-this._startDate.getTime())/1e3;return null}},{key:"plannedDuration",get:function(){return"PLANNED-DURATION"in this.attr?this.attr.decimalFloatingPoint("PLANNED-DURATION"):null}},{key:"endOnNext",get:function(){return this.attr.bool("END-ON-NEXT")}},{key:"isValid",get:function(){return!!this.id&&!this._badValueForSameId&&y(this.startDate.getTime())&&(null===this.duration||0<=this.duration)&&(!this.endOnNext||!!this.class)}}]),t}(),x=function(){this.aborted=!1,this.loaded=0,this.retry=0,this.total=0,this.chunkCount=0,this.bwEstimate=0,this.loading={start:0,first:0,end:0},this.parsing={start:0,end:0},this.buffering={start:0,first:0,end:0}},P="audio",O="video",M="audiovideo",F=function(){function t(t){var e;this._byteRange=null,this._url=null,this.baseurl=void 0,this.relurl=void 0,this.elementaryStreams=((e={})[P]=null,e[O]=null,e[M]=null,e),this.baseurl=t}return t.prototype.setByteRange=function(t,e){var t=t.split("@",2),i=[];1===t.length?i[0]=e?e.byteRangeEndOffset:0:i[0]=parseInt(t[1]),i[1]=parseInt(t[0])+i[0],this._byteRange=i},a(t,[{key:"byteRange",get:function(){return this._byteRange||[]}},{key:"byteRangeStartOffset",get:function(){return this.byteRange[0]}},{key:"byteRangeEndOffset",get:function(){return this.byteRange[1]}},{key:"url",get:function(){return!this._url&&this.baseurl&&this.relurl&&(this._url=p.buildAbsoluteURL(this.baseurl,this.relurl,{alwaysNormalize:!0})),this._url||""},set:function(t){this._url=t}}]),t}(),N=function(t){function e(e,r){return(r=t.call(this,r)||this)._decryptdata=null,r.rawProgramDateTime=null,r.programDateTime=null,r.tagList=[],r.duration=0,r.sn=0,r.levelkeys=void 0,r.type=void 0,r.loader=null,r.keyLoader=null,r.level=-1,r.cc=0,r.startPTS=void 0,r.endPTS=void 0,r.startDTS=void 0,r.endDTS=void 0,r.start=0,r.deltaPTS=void 0,r.maxStartPTS=void 0,r.minEndPTS=void 0,r.stats=new x,r.urlId=0,r.data=void 0,r.bitrateTest=!1,r.title=null,r.initSegment=null,r.endList=void 0,r.gap=void 0,r.type=e,r}l(e,t);var r=e.prototype;return r.setKeyFormat=function(t){this.levelkeys&&(t=this.levelkeys[t])&&!this._decryptdata&&(this._decryptdata=t.getDecryptData(this.sn))},r.abortRequests=function(){var t;null!=(t=this.loader)&&t.abort(),null!=(t=this.keyLoader)&&t.abort()},r.setElementaryStreamInfo=function(t,e,r,i,n,a){void 0===a&&(a=!1);var s=this.elementaryStreams,o=s[t];o?(o.startPTS=Math.min(o.startPTS,e),o.endPTS=Math.max(o.endPTS,r),o.startDTS=Math.min(o.startDTS,i),o.endDTS=Math.max(o.endDTS,n)):s[t]={startPTS:e,endPTS:r,startDTS:i,endDTS:n,partial:a}},r.clearElementaryStreamInfo=function(){var t=this.elementaryStreams;t[P]=null,t[O]=null,t[M]=null},a(e,[{key:"decryptdata",get:function(){if(!this.levelkeys&&!this._decryptdata)return null;if(!this._decryptdata&&this.levelkeys&&!this.levelkeys.NONE){var t=this.levelkeys.identity;if(t)this._decryptdata=t.getDecryptData(this.sn);else{t=Object.keys(this.levelkeys);if(1===t.length)return this._decryptdata=this.levelkeys[t[0]].getDecryptData(this.sn)}}return this._decryptdata}},{key:"end",get:function(){return this.start+this.duration}},{key:"endProgramDateTime",get:function(){var t;return null!==this.programDateTime&&y(this.programDateTime)?(t=y(this.duration)?this.duration:0,this.programDateTime+1e3*t):null}},{key:"encrypted",get:function(){if(null!=(t=this._decryptdata)&&t.encrypted)return!0;if(this.levelkeys){var t=Object.keys(this.levelkeys),r=t.length;if(1<r||1===r&&this.levelkeys[t[0]].encrypted)return!0}return!1}}]),e}(F),U=function(t){function e(e,r,i,n,a){(i=t.call(this,i)||this).fragOffset=0,i.duration=0,i.gap=!1,i.independent=!1,i.relurl=void 0,i.fragment=void 0,i.index=void 0,i.stats=new x,i.duration=e.decimalFloatingPoint("DURATION"),i.gap=e.bool("GAP"),i.independent=e.bool("INDEPENDENT"),i.relurl=e.enumeratedString("URI"),i.fragment=r,i.index=n;r=e.enumeratedString("BYTERANGE");return r&&i.setByteRange(r,a),a&&(i.fragOffset=a.fragOffset+a.duration),i}return l(e,t),a(e,[{key:"start",get:function(){return this.fragment.start+this.fragOffset}},{key:"end",get:function(){return this.start+this.duration}},{key:"loaded",get:function(){var t=this.elementaryStreams;return!!(t.audio||t.video||t.audiovideo)}}]),e}(F),B=function(){function t(t){this.PTSKnown=!1,this.alignedSliding=!1,this.averagetargetduration=void 0,this.endCC=0,this.endSN=0,this.fragments=void 0,this.fragmentHint=void 0,this.partList=null,this.dateRanges=void 0,this.live=!0,this.ageHeader=0,this.advancedDateTime=void 0,this.updated=!0,this.advanced=!0,this.availabilityDelay=void 0,this.misses=0,this.startCC=0,this.startSN=0,this.startTimeOffset=null,this.targetduration=0,this.totalduration=0,this.type=null,this.url=void 0,this.m3u8="",this.version=null,this.canBlockReload=!1,this.canSkipUntil=0,this.canSkipDateRanges=!1,this.skippedSegments=0,this.recentlyRemovedDateranges=void 0,this.partHoldBack=0,this.holdBack=0,this.partTarget=0,this.preloadHint=void 0,this.renditionReports=void 0,this.tuneInGoal=0,this.deltaUpdateFailed=void 0,this.driftStartTime=0,this.driftEndTime=0,this.driftStart=0,this.driftEnd=0,this.encryptedFragments=void 0,this.playlistParsingError=null,this.variableList=null,this.hasVariableRefs=!1,this.fragments=[],this.encryptedFragments=[],this.dateRanges={},this.url=t}return t.prototype.reloaded=function(t){var e,r;t?(e=this.lastPartSn-t.lastPartSn,r=this.lastPartIndex-t.lastPartIndex,this.updated=this.endSN!==t.endSN||!!r||!!e,this.advanced=this.endSN>t.endSN||0<e||0==e&&0<r,this.updated||this.advanced?this.misses=Math.floor(.6*t.misses):this.misses=t.misses+1,this.availabilityDelay=t.availabilityDelay):(this.advanced=!0,this.updated=!0)},a(t,[{key:"hasProgramDateTime",get:function(){return!!this.fragments.length&&y(this.fragments[this.fragments.length-1].programDateTime)}},{key:"levelTargetDuration",get:function(){return this.averagetargetduration||this.targetduration||10}},{key:"drift",get:function(){var t=this.driftEndTime-this.driftStartTime;return 0<t?1e3*(this.driftEnd-this.driftStart)/t:1}},{key:"edge",get:function(){return this.partEnd||this.fragmentEnd}},{key:"partEnd",get:function(){var t;return null!=(t=this.partList)&&t.length?this.partList[this.partList.length-1].end:this.fragmentEnd}},{key:"fragmentEnd",get:function(){var t;return null!=(t=this.fragments)&&t.length?this.fragments[this.fragments.length-1].end:0}},{key:"age",get:function(){return this.advancedDateTime?Math.max(Date.now()-this.advancedDateTime,0)/1e3:0}},{key:"lastPartIndex",get:function(){var t;return null!=(t=this.partList)&&t.length?this.partList[this.partList.length-1].index:-1}},{key:"lastPartSn",get:function(){var t;return null!=(t=this.partList)&&t.length?this.partList[this.partList.length-1].fragment.sn:this.endSN}}]),t}();function G(t){return Uint8Array.from(atob(t),function(t){return t.charCodeAt(0)})}function K(t){return Uint8Array.from(unescape(encodeURIComponent(t)),function(t){return t.charCodeAt(0)})}var H={CLEARKEY:"org.w3.clearkey",FAIRPLAY:"com.apple.fps",PLAYREADY:"com.microsoft.playready",WIDEVINE:"com.widevine.alpha"},V="org.w3.clearkey",j="com.apple.streamingkeydelivery",Y="com.microsoft.playready",W="urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed";function q(t){switch(t){case j:return H.FAIRPLAY;case Y:return H.PLAYREADY;case W:return H.WIDEVINE;case V:return H.CLEARKEY}}var X="edef8ba979d64acea3c827dcd51d21ed";function z(t){switch(t){case H.FAIRPLAY:return j;case H.PLAYREADY:return Y;case H.WIDEVINE:return W;case H.CLEARKEY:return V}}function Q(t){var e=t.drmSystems,t=t.widevineLicenseUrl,i=e?[H.FAIRPLAY,H.WIDEVINE,H.PLAYREADY,H.CLEARKEY].filter(function(t){return!!e[t]}):[];return!i[H.WIDEVINE]&&t&&i.push(H.WIDEVINE),i}var Z="undefined"!=typeof self&&self.navigator&&self.navigator.requestMediaKeySystemAccess?self.navigator.requestMediaKeySystemAccess.bind(self.navigator):null;function $(t,e,r){return Uint8Array.prototype.slice?t.slice(e,r):new Uint8Array(Array.prototype.slice.call(t,e,r))}function tt(t,e){return e+10<=t.length&&73===t[e]&&68===t[e+1]&&51===t[e+2]&&t[e+3]<255&&t[e+4]<255&&t[e+6]<128&&t[e+7]<128&&t[e+8]<128&&t[e+9]<128}function et(t,e){return e+10<=t.length&&51===t[e]&&68===t[e+1]&&73===t[e+2]&&t[e+3]<255&&t[e+4]<255&&t[e+6]<128&&t[e+7]<128&&t[e+8]<128&&t[e+9]<128}function rt(t,e){for(var r=e,i=0;tt(t,e);)i=(i+=10)+it(t,e+6),et(t,e+10)&&(i+=10),e+=i;if(0<i)return t.subarray(r,r+i)}function at(t){return t&&"PRIV"===t.key&&"com.apple.streaming.transportStreamTimestamp"===t.info}function ot(t){for(var e=0,r=[];tt(t,e);){for(var i=it(t,e+6),n=(e+=10)+i;e+8<n;){var a=function(t){var e=String.fromCharCode(t[0],t[1],t[2],t[3]),r=it(t,4);return{type:e,size:r,data:t.subarray(10,10+r)}}(t.subarray(e)),s=function(t){return"PRIV"===t.type?ut(t):"W"===t.type[0]?dt(t):ht(t)}(a);s&&r.push(s),e+=a.size+10}et(t,e)&&(e+=10)}return r}function gt(t){for(var e="",r=0;r<t.length;r++){var i=t[r].toString(16);e+=i=i.length<2?"0"+i:i}return e}var J,it=function(t,e){var r=(127&t[e])<<21;return(r|=(127&t[e+1])<<14)|(127&t[e+2])<<7|127&t[e+3]},ut=function(t){var e,r;if(!(t.size<2))return e=ft(t.data,!0),r=new Uint8Array(t.data.subarray(e.length+1)),{key:t.type,info:e,data:r.buffer}},ht=function(t){var e,r;if(!(t.size<2))return"TXXX"===t.type?(e=1,e+=(r=ft(t.data.subarray(1),!0)).length+1,e=ft(t.data.subarray(e)),{key:t.type,info:r,data:e}):(r=ft(t.data.subarray(1)),{key:t.type,data:r})},dt=function(t){var e,r;return"WXXX"===t.type?t.size<2?void 0:(e=1,e+=(r=ft(t.data.subarray(1),!0)).length+1,e=ft(t.data.subarray(e)),{key:t.type,info:r,data:e}):(r=ft(t.data),{key:t.type,data:r})},ft=function(t,e){void 0===e&&(e=!1);var i,n;J=J||void 0===self.TextDecoder?J:new self.TextDecoder("utf-8");if(J)return i=J.decode(t),e?-1!==(n=i.indexOf("\0"))?i.substring(0,n):i:i.replace(/\0/g,"");for(var a,s,o,l=t.length,u="",h=0;h<l;){if(0===(a=t[h++])&&e)return u;if(0!==a&&3!==a)switch(a>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:u+=String.fromCharCode(a);break;case 12:case 13:s=t[h++],u+=String.fromCharCode((31&a)<<6|63&s);break;case 14:s=t[h++],o=t[h++],u+=String.fromCharCode((15&a)<<12|(63&s)<<6|(63&o)<<0)}}return u},vt=Math.pow(2,32)-1,mt=[].push,pt={video:1,audio:2,id3:3,text:4};function yt(t){return String.fromCharCode.apply(null,t)}function Tt(t,e){t=t[e]<<8|t[e+1];return t<0?65536+t:t}function Et(t,e){t=St(t,e);return t<0?4294967296+t:t}function St(t,e){return t[e]<<24|t[e+1]<<16|t[e+2]<<8|t[e+3]}function Lt(t,e,r){t[e]=r>>24,t[e+1]=r>>16&255,t[e+2]=r>>8&255,t[e+3]=255&r}function Rt(t,e){var r=[];if(e.length)for(var i=t.byteLength,n=0;n<i;){var o,a=Et(t,n),a=1<a?n+a:i;yt(t.subarray(n+4,n+8))===e[0]&&(1===e.length?r.push(t.subarray(n+8,a)):(o=Rt(t.subarray(n+8,a),e.slice(1))).length&&mt.apply(r,o)),n=a}return r}function At(t){for(var e=[],r=Rt(t,["moov","trak"]),i=0;i<r.length;i++){var u,d,v,n=r[i],a=Rt(n,["tkhd"])[0];a&&(a=Et(a,0===a[0]?12:20),u=Rt(n,["mdia","mdhd"])[0])&&(u=Et(u,0===u[0]?12:20),d=Rt(n,["mdia","hdlr"])[0])&&(d=yt(d.subarray(8,12)),d={soun:P,vide:O}[d])&&(v=void 0,(n=Rt(n,["mdia","minf","stbl","stsd"])[0])&&(v=yt(n.subarray(12,16))),e[a]={timescale:u,type:d},e[d]={timescale:u,id:a,codec:v})}return Rt(t,["moov","mvex","trex"]).forEach(function(t){var r=Et(t,4),r=e[r];r&&(r.default={duration:Et(t,12),flags:Et(t,20)})}),e}function bt(t){var e=Rt(t,["schm"])[0];if(e){e=yt(e.subarray(4,8));if("cbcs"===e||"cenc"===e)return Rt(t,["schi","tenc"])[0]}return b.error("[eme] missing 'schm' box"),null}function It(t,e){var r=new Uint8Array(t.length+e.length);return r.set(t),r.set(e,t.length),r}function _t(t,e){var r=[],i=e.samples,n=e.timescale,a=e.id,s=!1;return Rt(i,["moof"]).map(function(o){var l=o.byteOffset-8;Rt(o,["traf"]).map(function(o){var u=Rt(o,["tfdt"]).map(function(t){var e=t[0],r=Et(t,4);return(r=1===e?(r*=Math.pow(2,32))+Et(t,8):r)/n})[0];return void 0!==u&&(t=u),Rt(o,["tfhd"]).map(function(u){var h=Et(u,4),d=16777215&Et(u,0),c=0,g=0,m=8;h===a&&(0!=(1&d)&&(m+=8),0!=(2&d)&&(m+=4),0!=(8&d)&&(c=Et(u,m),m+=4),0!=(16&d)&&(g=Et(u,m),m+=4),0!=(32&d)&&(m+=4),"video"===e.type&&(s=function(t){var e;return!!t&&("hvc1"===(t=(e=t.indexOf("."))<0?t:t.substring(0,e))||"hev1"===t||"dvh1"===t||"dvhe"===t)}(e.codec)),Rt(o,["trun"]).map(function(a){var o=a[0],u=16777215&Et(a,0),d=0,v=0!=(256&u),m=0,p=0!=(512&u),y=0,T=0!=(1024&u),E=0!=(2048&u),S=0,L=Et(a,4),R=8;0!=(1&u)&&(d=Et(a,R),R+=4),0!=(4&u)&&(R+=4);for(var k=d+l,A=0;A<L;A++){if(v?(m=Et(a,R),R+=4):m=c,p?(y=Et(a,R),R+=4):y=g,T&&(R+=4),E&&(S=(0===o?Et:St)(a,R),R+=4),e.type===O)for(var b=0;b<y;){var D=Et(i,k);!function(t,e){if(t)return 39==(t=e>>1&63)||40==t;return 6==(31&e)}(s,i[k+=4])||Ct(i.subarray(k,k+D),s?2:1,t+S/n,r),k+=D,b+=D+4}t+=m/n}}))})})}),r}function Ct(t,e,r,i){var n=xt(t),a=0;a+=e;for(var s=0,o=0,l=!1,u=0;a<n.length;){for(s=0;!(a>=n.length)&&(s+=u=n[a++],255===u););for(o=0;!(a>=n.length)&&(o+=u=n[a++],255===u););var h=n.length-a;if(!l&&4===s&&a<n.length){if(l=!0,181===n[a++]){var d=Tt(n,a);if(a+=2,49===d){d=Et(n,a);if(a+=4,1195456820===d){d=n[a++];if(3===d){var g=n[a++],v=64&g,m=v?2+3*(31&g):0,p=new Uint8Array(m);if(v){p[0]=g;for(var y=1;y<m;y++)p[y]=n[a++]}i.push({type:d,payloadType:s,pts:r,bytes:p})}}}}}else if(5===s&&o<h){if(l=!0,16<o){for(var T=[],E=0;E<16;E++){var S=n[a++].toString(16);T.push(1==S.length?"0"+S:S),3!==E&&5!==E&&7!==E&&9!==E||T.push("-")}for(var L=o-16,R=new Uint8Array(L),k=0;k<L;k++)R[k]=n[a++];i.push({payloadType:s,pts:r,uuid:T.join(""),userData:ft(R),userDataBytes:R})}}else if(o<h)a+=o;else if(h<o)break}}function xt(t){for(var e=t.byteLength,r=[],i=1;i<e-2;)0===t[i]&&0===t[i+1]&&3===t[i+2]?(r.push(i+2),i+=2):i++;if(0===r.length)return t;for(var n=e-r.length,a=new Uint8Array(n),s=0,i=0;i<n;s++,i++)s===r[0]&&(s++,r.shift()),a[i]=t[s];return a}var Pt={},Ot=function(){function t(t,e,r,i,n){void 0===i&&(i=[1]),void 0===n&&(n=null),this.uri=void 0,this.method=void 0,this.keyFormat=void 0,this.keyFormatVersions=void 0,this.encrypted=void 0,this.isCommonEncryption=void 0,this.iv=null,this.key=null,this.keyId=null,this.pssh=null,this.method=t,this.uri=e,this.keyFormat=r,this.keyFormatVersions=i,this.iv=n,this.encrypted=!!t&&"NONE"!==t,this.isCommonEncryption=this.encrypted&&"AES-128"!==t}t.clearKeyUriToKeyIdMap=function(){Pt={}};var e=t.prototype;return e.isSupported=function(){if(this.method){if("AES-128"===this.method||"NONE"===this.method)return!0;if("identity"===this.keyFormat)return"SAMPLE-AES"===this.method;switch(this.keyFormat){case j:case W:case Y:case V:return-1!==["ISO-23001-7","SAMPLE-AES","SAMPLE-AES-CENC","SAMPLE-AES-CTR"].indexOf(this.method)}}return!1},e.getDecryptData=function(e){if(!this.encrypted||!this.uri)return null;if("AES-128"===this.method&&this.uri&&!this.iv)return"number"!=typeof e&&("AES-128"!==this.method||this.iv||b.warn('missing IV for initialization segment with method="'+this.method+'" - compliance issue'),e=0),e=function(t){for(var e=new Uint8Array(16),r=12;r<16;r++)e[r]=t>>8*(15-r)&255;return e}(e),new t(this.method,this.uri,"identity",this.keyFormatVersions,e);var g,i=function(t){var o,s,t=t.split(":"),n=null;return"data"===t[0]&&2===t.length&&2===(s=(t=t[1].split(";"))[t.length-1].split(",")).length&&(o="base64"===s[0],s=s[1],n=o?(t.splice(-1,1),G(s)):(o=K(s).subarray(0,16),(t=new Uint8Array(16)).set(o,16-o.length),t)),n}(this.uri);if(i)switch(this.keyFormat){case W:22<=(this.pssh=i).length&&(this.keyId=i.subarray(i.length-22,i.length-6));break;case Y:var n=new Uint8Array([154,4,240,121,152,64,66,134,171,146,230,91,224,136,95,149]),n=(this.pssh=function(t,e,r){if(16!==t.byteLength)throw new RangeError("Invalid system id");i=0,n=new Uint8Array,0<i?(a=new Uint8Array(4),0<e.length&&new DataView(a.buffer).setUint32(0,e.length,!1)):a=new Uint8Array;var a,i,n,e=new Uint8Array(4);return r&&0<r.byteLength&&new DataView(e.buffer).setUint32(0,r.byteLength,!1),function(t){for(var e=arguments.length,r=new Array(1<e?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];for(var n=r.length,a=8,s=n;s--;)a+=r[s].byteLength;var o=new Uint8Array(a);for(o[0]=a>>24&255,o[1]=a>>16&255,o[2]=a>>8&255,o[3]=255&a,o.set(t,4),s=0,a=8;s<n;s++)o.set(r[s],a),a+=r[s].byteLength;return o}([112,115,115,104],new Uint8Array([i,0,0,0]),t,a,n,e,r||new Uint8Array)}(n,null,i),new Uint16Array(i.buffer,i.byteOffset,i.byteLength/2)),n=String.fromCharCode.apply(null,Array.from(n)),n=n.substring(n.indexOf("<"),n.length),n=(new DOMParser).parseFromString(n,"text/xml").getElementsByTagName("KID")[0];n&&(n=n.childNodes[0]?n.childNodes[0].nodeValue:n.getAttribute("VALUE"))&&(function(t){function e(t,e,r){var i=t[e];t[e]=t[r],t[r]=i}e(t,0,3),e(t,1,2),e(t,4,5),e(t,6,7)}(n=G(n).subarray(0,16)),this.keyId=n);break;default:var c,n=i.subarray(0,16);16!==n.length&&((c=new Uint8Array(16)).set(n,16-n.length),n=c),this.keyId=n}return this.keyId&&16===this.keyId.byteLength||((e=Pt[this.uri])||(g=Object.keys(Pt).length%Number.MAX_SAFE_INTEGER,e=new Uint8Array(16),new DataView(e.buffer,12,4).setUint32(0,g),Pt[this.uri]=e),this.keyId=e),this},t}(),Mt=/\{\$([a-zA-Z0-9-_]+)\}/g;function Ft(t){return Mt.test(t)}function Nt(t,e,r){if(null!==t.variableList||t.hasVariableRefs)for(var i=r.length;i--;){var n=r[i],a=e[n];a&&(e[n]=Ut(t,a))}}function Ut(t,e){var r;return null!==t.variableList||t.hasVariableRefs?(r=t.variableList,e.replace(Mt,function(e){var i=e.substring(2,e.length-1),n=null==r?void 0:r[i];return void 0===n?(t.playlistParsingError||(t.playlistParsingError=new Error('Missing preceding EXT-X-DEFINE tag for Variable Reference: "'+i+'"')),e):n})):e}function Bt(t,e,r){var i,n,a=t.variableList;if(a||(t.variableList=a={}),"QUERYPARAM"in e){i=e.QUERYPARAM;try{var s=new self.URL(r).searchParams;if(!s.has(i))throw new Error('"'+i+'" does not match any query parameter in URI: "'+r+'"');n=s.get(i)}catch(e){t.playlistParsingError||(t.playlistParsingError=new Error("EXT-X-DEFINE QUERYPARAM: "+e.message))}}else i=e.NAME,n=e.VALUE;i in a?t.playlistParsingError||(t.playlistParsingError=new Error('EXT-X-DEFINE duplicate Variable Name declarations: "'+i+'"')):a[i]=n||""}function Kt(){if("undefined"!=typeof self)return self.MediaSource||self.WebKitMediaSource}var Ht={audio:{a3ds:!0,"ac-3":!0,"ac-4":!0,alac:!0,alaw:!0,dra1:!0,"dts+":!0,"dts-":!0,dtsc:!0,dtse:!0,dtsh:!0,"ec-3":!0,enca:!0,g719:!0,g726:!0,m4ae:!0,mha1:!0,mha2:!0,mhm1:!0,mhm2:!0,mlpa:!0,mp4a:!0,"raw ":!0,Opus:!0,opus:!0,samr:!0,sawb:!0,sawp:!0,sevc:!0,sqcp:!0,ssmv:!0,twos:!0,ulaw:!0},video:{avc1:!0,avc2:!0,avc3:!0,avc4:!0,avcp:!0,av01:!0,drac:!0,dva1:!0,dvav:!0,dvh1:!0,dvhe:!0,encv:!0,hev1:!0,hvc1:!0,mjp2:!0,mp4v:!0,mvc1:!0,mvc2:!0,mvc3:!0,mvc4:!0,resv:!0,rv60:!0,s263:!0,svc1:!0,svc2:!0,"vc-1":!0,vp08:!0,vp09:!0},text:{stpp:!0,wvtt:!0}},Vt=Kt();function jt(t,e){return null!=(e=null==Vt?void 0:Vt.isTypeSupported((e||"video")+'/mp4;codecs="'+t+'"'))&&e}var Yt=/#EXT-X-STREAM-INF:([^\r\n]*)(?:[\r\n](?:#[^\r\n]*)?)*([^\r\n]+)|#EXT-X-(SESSION-DATA|SESSION-KEY|DEFINE|CONTENT-STEERING|START):([^\r\n]*)[\r\n]+/g,Wt=/#EXT-X-MEDIA:(.*)/g,qt=/^#EXT(?:INF|-X-TARGETDURATION):/m,Xt=new RegExp([/#EXTINF:\s*(\d*(?:\.\d+)?)(?:,(.*)\s+)?/.source,/(?!#) *(\S[\S ]*)/.source,/#EXT-X-BYTERANGE:*(.+)/.source,/#EXT-X-PROGRAM-DATE-TIME:(.+)/.source,/#.*/.source].join("|"),"g"),zt=new RegExp([/#(EXTM3U)/.source,/#EXT-X-(DATERANGE|DEFINE|KEY|MAP|PART|PART-INF|PLAYLIST-TYPE|PRELOAD-HINT|RENDITION-REPORT|SERVER-CONTROL|SKIP|START):(.+)/.source,/#EXT-X-(BITRATE|DISCONTINUITY-SEQUENCE|MEDIA-SEQUENCE|TARGETDURATION|VERSION): *(\d+)/.source,/#EXT-X-(DISCONTINUITY|ENDLIST|GAP)/.source,/(#)([^:]*):(.*)/.source,/(#)(.*)(?:.*)\r?\n?/.source].join("|")),Qt=function(){function t(){}return t.findGroup=function(t,e){for(var r=0;r<t.length;r++){var i=t[r];if(i.id===e)return i}},t.convertAVC1ToAVCOTI=function(t){var r,e=t.split(".");return 2<e.length?(r=e.shift()+".",(r+=parseInt(e.shift()).toString(16))+("000"+parseInt(e.shift()).toString(16)).slice(-4)):t},t.resolve=function(t,e){return p.buildAbsoluteURL(e,t,{alwaysNormalize:!0})},t.isMediaPlaylist=function(t){return qt.test(t)},t.parseMasterPlaylist=function(e,r){var i,n={contentSteering:null,levels:[],playlistParsingError:null,sessionData:null,sessionKeys:null,startTimeOffset:null,variableList:null,hasVariableRefs:Ft(e)},a=[];for(Yt.lastIndex=0;null!=(i=Yt.exec(e));)if(i[1]){var o=new _(i[1]),l=(Nt(n,o,["CODECS","SUPPLEMENTAL-CODECS","ALLOWED-CPC","PATHWAY-ID","STABLE-VARIANT-ID","AUDIO","VIDEO","SUBTITLES","CLOSED-CAPTIONS","NAME"]),Ut(n,i[2])),l={attrs:o,bitrate:o.decimalInteger("AVERAGE-BANDWIDTH")||o.decimalInteger("BANDWIDTH"),name:o.NAME,url:t.resolve(l,r)},h=o.decimalResolution("RESOLUTION");h&&(l.width=h.width,l.height=h.height),function(t,e){["video","audio","text"].forEach(function(r){var n,i=t.filter(function(t){return function(t,e){e=Ht[e];return!!e&&!0===e[t.slice(0,4)]}(t,r)});i.length&&(n=i.filter(function(t){return 0===t.lastIndexOf("avc1",0)||0===t.lastIndexOf("mp4a",0)}),e[r+"Codec"]=(0<n.length?n:i)[0],t=t.filter(function(t){return-1===i.indexOf(t)}))}),e.unknownCodecs=t}((o.CODECS||"").split(/[ ,]+/).filter(function(t){return t}),l),l.videoCodec&&-1!==l.videoCodec.indexOf("avc1")&&(l.videoCodec=t.convertAVC1ToAVCOTI(l.videoCodec)),null!=(h=l.unknownCodecs)&&h.length||a.push(l),n.levels.push(l)}else if(i[3]){var o=i[3],c=i[4];switch(o){case"SESSION-DATA":var f=new _(c),g=(Nt(n,f,["DATA-ID","LANGUAGE","VALUE","URI"]),f["DATA-ID"]);g&&(null===n.sessionData&&(n.sessionData={}),n.sessionData[g]=f);break;case"SESSION-KEY":g=Zt(c,r,n);g.encrypted&&g.isSupported()?(null===n.sessionKeys&&(n.sessionKeys=[]),n.sessionKeys.push(g)):b.warn('[Keys] Ignoring invalid EXT-X-SESSION-KEY tag: "'+c+'"');break;case"DEFINE":f=new _(c);Nt(n,f,["NAME","VALUE","QUERYPARAM"]),Bt(n,f,r);break;case"CONTENT-STEERING":var p=new _(c);Nt(n,p,["SERVER-URI","PATHWAY-ID"]),n.contentSteering={uri:t.resolve(p["SERVER-URI"],r),pathwayId:p["PATHWAY-ID"]||"."};break;case"START":n.startTimeOffset=$t(c)}}var y=0<a.length&&a.length<n.levels.length;return n.levels=y?a:n.levels,0===n.levels.length&&(n.playlistParsingError=new Error("no levels found in manifest")),n},t.parseMasterPlaylistMedia=function(e,r,i){var a={},s=i.levels,o={AUDIO:s.map(function(t){return{id:t.attrs.AUDIO,audioCodec:t.audioCodec}}),SUBTITLES:s.map(function(t){return{id:t.attrs.SUBTITLES,textCodec:t.textCodec}}),"CLOSED-CAPTIONS":[]},l=0;for(Wt.lastIndex=0;null!==(n=Wt.exec(e));){var d,c,n=new _(n[1]),h=n.TYPE;h&&(d=o[h],c=a[h]||[],a[h]=c,Nt(i,n,["URI","GROUP-ID","LANGUAGE","ASSOC-LANGUAGE","STABLE-RENDITION-ID","NAME","INSTREAM-ID","CHARACTERISTICS","CHANNELS"]),h={attrs:n,bitrate:0,id:l++,groupId:n["GROUP-ID"]||"",instreamId:n["INSTREAM-ID"],name:n.NAME||n.LANGUAGE||"",type:h,default:n.bool("DEFAULT"),autoselect:n.bool("AUTOSELECT"),forced:n.bool("FORCED"),lang:n.LANGUAGE,url:n.URI?t.resolve(n.URI,r):""},null!=d&&d.length&&(te(h,n=t.findGroup(d,h.groupId)||d[0],"audioCodec"),te(h,n,"textCodec")),c.push(h))}return a},t.parseLevelPlaylist=function(t,e,r,i,n,a){var s,l,u,h=new B(e),d=h.fragments,c=null,f=0,g=0,v=0,m=0,p=null,T=new N(i,e),E=-1,S=!1;for(Xt.lastIndex=0,h.m3u8=t,h.hasVariableRefs=Ft(t);null!==(s=Xt.exec(t));){S&&(S=!1,(T=new N(i,e)).start=v,T.sn=f,T.cc=m,T.level=r,c)&&(T.initSegment=c,T.rawProgramDateTime=c.rawProgramDateTime,c.rawProgramDateTime=null);var L=s[1];if(L){T.duration=parseFloat(L);var R=(" "+s[2]).slice(1);T.title=R||null,T.tagList.push(R?["INF",L,R]:["INF",L])}else if(s[3])y(T.duration)&&(T.start=v,u&&ie(T,u,h),T.sn=f,T.level=r,T.cc=m,T.urlId=n,d.push(T),R=(" "+s[3]).slice(1),T.relurl=Ut(h,R),ee(T,p),v+=(p=T).duration,f++,S=!(g=0));else if(s[4]){L=(" "+s[4]).slice(1);p?T.setByteRange(L,p):T.setByteRange(L)}else if(s[5])T.rawProgramDateTime=(" "+s[5]).slice(1),T.tagList.push(["PROGRAM-DATE-TIME",T.rawProgramDateTime]),-1===E&&(E=d.length);else if(s=s[0].match(zt)){for(l=1;l<s.length&&void 0===s[l];l++);var D=(" "+s[l]).slice(1),I=(" "+s[l+1]).slice(1),w=s[l+2]?(" "+s[l+2]).slice(1):"";switch(D){case"PLAYLIST-TYPE":h.type=I.toUpperCase();break;case"MEDIA-SEQUENCE":f=h.startSN=parseInt(I);break;case"SKIP":var x=new _(I),P=(Nt(h,x,["RECENTLY-REMOVED-DATERANGES"]),x.decimalInteger("SKIPPED-SEGMENTS"));if(y(P)){for(var O=h.skippedSegments=P;O--;)d.unshift(null);f+=P}P=x.enumeratedString("RECENTLY-REMOVED-DATERANGES");P&&(h.recentlyRemovedDateranges=P.split("\t"));break;case"TARGETDURATION":h.targetduration=Math.max(parseInt(I),1);break;case"VERSION":h.version=parseInt(I);break;case"EXTM3U":break;case"ENDLIST":h.live=!1;break;case"#":(I||w)&&T.tagList.push(w?[I,w]:[I]);break;case"DISCONTINUITY":m++,T.tagList.push(["DIS"]);break;case"GAP":T.gap=!0,T.tagList.push([D]);break;case"BITRATE":T.tagList.push([D,I]);break;case"DATERANGE":x=new _(I),P=(Nt(h,x,["ID","CLASS","START-DATE","END-DATE","SCTE35-CMD","SCTE35-OUT","SCTE35-IN"]),Nt(h,x,x.clientAttrs),new C(x,h.dateRanges[x.ID]));P.isValid||h.skippedSegments?h.dateRanges[P.id]=P:b.warn('Ignoring invalid DATERANGE tag: "'+I+'"'),T.tagList.push(["EXT-X-DATERANGE",I]);break;case"DEFINE":var K=new _(I);Nt(h,K,["NAME","VALUE","IMPORT","QUERYPARAM"]),"IMPORT"in K?function(t,e,r){var n,e=e.IMPORT;r&&e in r?((n=t.variableList)||(t.variableList=n={}),n[e]=r[e]):t.playlistParsingError||(t.playlistParsingError=new Error('EXT-X-DEFINE IMPORT attribute not found in Multivariant Playlist: "'+e+'"'))}(h,K,a):Bt(h,K,e);break;case"DISCONTINUITY-SEQUENCE":m=parseInt(I);break;case"KEY":K=Zt(I,e,h);if(K.isSupported()){if("NONE"===K.method){u=void 0;break}(u=(u=u||{})[K.keyFormat]?o({},u):u)[K.keyFormat]=K}else b.warn('[Keys] Ignoring invalid EXT-X-KEY tag: "'+I+'"');break;case"START":h.startTimeOffset=$t(I);break;case"MAP":var V=new _(I);Nt(h,V,["BYTERANGE","URI"]),T.duration?(re(j=new N(i,e),V,r,u),(T.initSegment=c=j).rawProgramDateTime&&!T.rawProgramDateTime&&(T.rawProgramDateTime=c.rawProgramDateTime)):(re(T,V,r,u),c=T,S=!0);break;case"SERVER-CONTROL":var j=new _(I);h.canBlockReload=j.bool("CAN-BLOCK-RELOAD"),h.canSkipUntil=j.optionalFloat("CAN-SKIP-UNTIL",0),h.canSkipDateRanges=0<h.canSkipUntil&&j.bool("CAN-SKIP-DATERANGES"),h.partHoldBack=j.optionalFloat("PART-HOLD-BACK",0),h.holdBack=j.optionalFloat("HOLD-BACK",0);break;case"PART-INF":V=new _(I);h.partTarget=V.decimalFloatingPoint("PART-TARGET");break;case"PART":var q=(q=h.partList)||(h.partList=[]),X=0<g?q[q.length-1]:void 0,z=g++,Q=new _(I),Q=(Nt(h,Q,["BYTERANGE","URI"]),new U(Q,T,e,z,X));q.push(Q),T.duration+=Q.duration;break;case"PRELOAD-HINT":z=new _(I);Nt(h,z,["URI"]),h.preloadHint=z;break;case"RENDITION-REPORT":X=new _(I);Nt(h,X,["URI"]),h.renditionReports=h.renditionReports||[],h.renditionReports.push(X);break;default:b.warn("line parsed but not handled: "+s)}}else b.warn("No matches on slow regex match for level playlist!")}p&&!p.relurl?(d.pop(),v-=p.duration,h.partList&&(h.fragmentHint=p)):h.partList&&(ee(T,p),T.cc=m,h.fragmentHint=T,u)&&ie(T,u,h);var tt=d.length,et=d[0],rt=d[tt-1];return 0<(v+=h.skippedSegments*h.targetduration)&&tt&&rt?(h.averagetargetduration=v/tt,tt=rt.sn,h.endSN="initSegment"!==tt?tt:0,h.live||(rt.endList=!0),et&&(h.startCC=et.cc)):(h.endSN=0,h.startCC=0),h.fragmentHint&&(v+=h.fragmentHint.duration),h.totalduration=v,h.endCC=m,0<E&&function(t,e){for(var r=t[e],i=e;i--;){var n=t[i];if(!n)return;n.programDateTime=r.programDateTime-1e3*n.duration,r=n}}(d,E),h},t}();function Zt(t,e,r){var t=new _(t),r=(Nt(r,t,["KEYFORMAT","KEYFORMATVERSIONS","URI","IV","URI"]),null!=(r=t.METHOD)?r:""),o=t.URI,l=t.hexadecimalInteger("IV"),u=t.KEYFORMATVERSIONS,n=null!=(n=t.KEYFORMAT)?n:"identity",t=(o&&t.IV&&!l&&b.error("Invalid IV: "+t.IV),o?Qt.resolve(o,e):""),o=(u||"1").split("/").map(Number).filter(Number.isFinite);return new Ot(r,t,n,o,l)}function $t(t){t=new _(t).decimalFloatingPoint("TIME-OFFSET");return y(t)?t:null}function te(t,e,r){e=e[r];e&&(t[r]=e)}function ee(t,e){t.rawProgramDateTime?t.programDateTime=Date.parse(t.rawProgramDateTime):null!=e&&e.programDateTime&&(t.programDateTime=e.endProgramDateTime),y(t.programDateTime)||(t.programDateTime=null,t.rawProgramDateTime=null)}function re(t,e,r,i){t.relurl=e.URI,e.BYTERANGE&&t.setByteRange(e.BYTERANGE),t.level=r,t.sn="initSegment",i&&(t.levelkeys=i),t.initSegment=null}function ie(t,e,r){t.levelkeys=e;r=r.encryptedFragments;r.length&&r[r.length-1].levelkeys===e||!Object.keys(e).some(function(t){return e[t].isCommonEncryption})||r.push(t)}var ne="manifest",ae="level",se="audioTrack",oe="subtitleTrack",le="main",ue="audio",he="subtitle";function de(t){switch(t.type){case se:return ue;case oe:return he;default:return le}}function ce(t,e){t=t.url;return t=void 0!==t&&0!==t.indexOf("data:")?t:e.url}var fe=function(){function t(t){this.hls=void 0,this.loaders=Object.create(null),this.variableList=null,this.hls=t,this.registerListeners()}var r=t.prototype;return r.startLoad=function(t){},r.stopLoad=function(){this.destroyInternalLoaders()},r.registerListeners=function(){var t=this.hls;t.on(T.MANIFEST_LOADING,this.onManifestLoading,this),t.on(T.LEVEL_LOADING,this.onLevelLoading,this),t.on(T.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),t.on(T.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this)},r.unregisterListeners=function(){var t=this.hls;t.off(T.MANIFEST_LOADING,this.onManifestLoading,this),t.off(T.LEVEL_LOADING,this.onLevelLoading,this),t.off(T.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),t.off(T.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this)},r.createInternalLoader=function(t){var e=this.hls.config,r=e.pLoader,i=e.loader,r=new(r||i)(e);return this.loaders[t.type]=r},r.getInternalLoader=function(t){return this.loaders[t.type]},r.resetInternalLoader=function(t){this.loaders[t]&&delete this.loaders[t]},r.destroyInternalLoaders=function(){for(var t in this.loaders){var e=this.loaders[t];e&&e.destroy(),this.resetInternalLoader(t)}},r.destroy=function(){this.variableList=null,this.unregisterListeners(),this.destroyInternalLoaders()},r.onManifestLoading=function(t,e){e=e.url;this.variableList=null,this.load({id:null,level:0,responseType:"text",type:ne,url:e,deliveryDirectives:null})},r.onLevelLoading=function(t,e){var r=e.id,i=e.level,n=e.url,e=e.deliveryDirectives;this.load({id:r,level:i,responseType:"text",type:ae,url:n,deliveryDirectives:e})},r.onAudioTrackLoading=function(t,e){var r=e.id,i=e.groupId,n=e.url,e=e.deliveryDirectives;this.load({id:r,groupId:i,level:null,responseType:"text",type:se,url:n,deliveryDirectives:e})},r.onSubtitleTrackLoading=function(t,e){var r=e.id,i=e.groupId,n=e.url,e=e.deliveryDirectives;this.load({id:r,groupId:i,level:null,responseType:"text",type:oe,url:n,deliveryDirectives:e})},r.load=function(t){var n=this,a=this.hls.config,s=this.getInternalLoader(t);if(s){var l=s.context;if(l&&l.url===t.url)return void b.trace("[playlist-loader]: playlist request ongoing");b.log("[playlist-loader]: aborting previous loader for type: "+t.type),s.abort()}l=t.type===ne?a.manifestLoadPolicy.default:o({},a.playlistLoadPolicy.default,{timeoutRetry:null,errorRetry:null}),s=this.createInternalLoader(t),null!=(a=t.deliveryDirectives)&&a.part&&(t.type===ae&&null!==t.level?i=this.hls.levels[t.level].details:t.type===se&&null!==t.id?i=this.hls.audioTracks[t.id].details:t.type===oe&&null!==t.id&&(i=this.hls.subtitleTracks[t.id].details),i)&&(a=i.partTarget,i=i.targetduration,a&&i&&(a=1e3*Math.max(3*a,.8*i),l=o({},l,{maxTimeToFirstByteMs:Math.min(a,l.maxTimeToFirstByteMs),maxLoadTimeMs:Math.min(a,l.maxTimeToFirstByteMs)})));var i=l.errorRetry||l.timeoutRetry||{},a={loadPolicy:l,timeout:l.maxLoadTimeMs,maxRetry:i.maxNumRetry||0,retryDelay:i.retryDelayMs||0,maxRetryDelay:i.maxRetryDelayMs||0};s.load(t,a,{onSuccess:function(t,e,r,i){var a=n.getInternalLoader(r),s=(n.resetInternalLoader(r.type),t.data);0===s.indexOf("#EXTM3U")?(e.parsing.start=performance.now(),Qt.isMediaPlaylist(s)?n.handleTrackOrLevelPlaylist(t,e,r,i||null,a):n.handleMasterPlaylist(t,e,r,i)):n.handleManifestParsingError(t,r,new Error("no EXTM3U delimiter"),i||null,e)},onError:function(t,e,r,i){n.handleNetworkError(e,r,!1,t,i)},onTimeout:function(t,e,r){n.handleNetworkError(e,r,!0,void 0,t)}})},r.handleMasterPlaylist=function(t,e,r,i){var h,d,c,f,p,n=this.hls,a=t.data,s=ce(t,r),o=Qt.parseMasterPlaylist(a,s);o.playlistParsingError?this.handleManifestParsingError(t,r,o.playlistParsingError,i,e):(t=o.contentSteering,r=o.levels,h=o.sessionData,d=o.sessionKeys,c=o.startTimeOffset,f=o.variableList,this.variableList=f,o=(a=Qt.parseMasterPlaylistMedia(a,s,o)).AUDIO,p=a.SUBTITLES,a=a["CLOSED-CAPTIONS"],(o=void 0===o?[]:o).length&&!o.some(function(t){return!t.url})&&r[0].audioCodec&&!r[0].attrs.AUDIO&&(b.log("[playlist-loader]: audio codec signaled in quality level, but no embedded audio track signaled, create one"),o.unshift({type:"main",name:"main",groupId:"main",default:!1,autoselect:!1,forced:!1,id:-1,attrs:new _({}),bitrate:0,url:""})),n.trigger(T.MANIFEST_LOADED,{levels:r,audioTracks:o,subtitles:p,captions:a,contentSteering:t,url:s,stats:e,networkDetails:i,sessionData:h,sessionKeys:d,startTimeOffset:c,variableList:f}))},r.handleTrackOrLevelPlaylist=function(t,e,r,i,n){var a=this.hls,s=r.id,o=r.level,l=r.type,u=ce(t,r),s=y(s)?s:0,o=y(o)?o:s,c=de(r),o=Qt.parseLevelPlaylist(t.data,u,o,c,s,this.variableList);l===ne&&(c={attrs:new _({}),bitrate:0,details:o,name:"",url:u},a.trigger(T.MANIFEST_LOADED,{levels:[c],audioTracks:[],url:u,stats:e,networkDetails:i,sessionData:null,sessionKeys:null,contentSteering:null,startTimeOffset:null,variableList:null})),e.parsing.end=performance.now(),r.levelDetails=o,this.handlePlaylistLoaded(o,t,e,r,i,n)},r.handleManifestParsingError=function(t,e,r,i,n){this.hls.trigger(T.ERROR,{type:E.NETWORK_ERROR,details:S.MANIFEST_PARSING_ERROR,fatal:e.type===ne,url:t.url,err:r,error:r,reason:r.message,response:t,context:e,networkDetails:i,stats:n})},r.handleNetworkError=function(t,r,i,n,a){var s="A network "+((i=void 0===i?!1:i)?"timeout":"error"+(n?" (status "+n.code+")":""))+" occurred while loading "+t.type,o=(t.type===ae?s+=": "+t.level+" id: "+t.id:t.type!==se&&t.type!==oe||(s+=" id: "+t.id+' group-id: "'+t.groupId+'"'),new Error(s)),l=(b.warn("[playlist-loader]: "+s),S.UNKNOWN),u=!1,s=this.getInternalLoader(t);switch(t.type){case ne:l=i?S.MANIFEST_LOAD_TIMEOUT:S.MANIFEST_LOAD_ERROR,u=!0;break;case ae:l=i?S.LEVEL_LOAD_TIMEOUT:S.LEVEL_LOAD_ERROR,u=!1;break;case se:l=i?S.AUDIO_TRACK_LOAD_TIMEOUT:S.AUDIO_TRACK_LOAD_ERROR,u=!1;break;case oe:l=i?S.SUBTITLE_TRACK_LOAD_TIMEOUT:S.SUBTITLE_LOAD_ERROR,u=!1}s&&this.resetInternalLoader(t.type);s={type:E.NETWORK_ERROR,details:l,fatal:u,url:t.url,loader:s,context:t,error:o,networkDetails:r,stats:a};n&&(o=(null==r?void 0:r.url)||t.url,s.response=e({url:o,data:void 0},n)),this.hls.trigger(T.ERROR,s)},r.handlePlaylistLoaded=function(t,e,r,i,n,a){var s=this.hls,o=i.type,l=i.level,u=i.id,h=i.groupId,d=i.deliveryDirectives,c=ce(e,i),f=de(i),g="number"==typeof i.level&&f===le?l:void 0;if(t.fragments.length){t.targetduration||(t.playlistParsingError=new Error("Missing Target Duration"));l=t.playlistParsingError;if(l)s.trigger(T.ERROR,{type:E.NETWORK_ERROR,details:S.LEVEL_PARSING_ERROR,fatal:!1,url:c,error:l,reason:l.message,response:e,context:i,level:g,parent:f,networkDetails:n,stats:r});else switch(t.live&&a&&(a.getCacheAge&&(t.ageHeader=a.getCacheAge()||0),a.getCacheAge&&!isNaN(t.ageHeader)||(t.ageHeader=0)),o){case ne:case ae:s.trigger(T.LEVEL_LOADED,{details:t,level:g||0,id:u||0,stats:r,networkDetails:n,deliveryDirectives:d});break;case se:s.trigger(T.AUDIO_TRACK_LOADED,{details:t,id:u||0,groupId:h||"",stats:r,networkDetails:n,deliveryDirectives:d});break;case oe:s.trigger(T.SUBTITLE_TRACK_LOADED,{details:t,id:u||0,groupId:h||"",stats:r,networkDetails:n,deliveryDirectives:d})}}else{l=new Error("No Segments found in Playlist");s.trigger(T.ERROR,{type:E.NETWORK_ERROR,details:S.LEVEL_EMPTY_ERROR,fatal:!1,url:c,error:l,reason:l.message,response:e,context:i,level:g,parent:f,networkDetails:n,stats:r})}},t}();function ge(t,e){var r;try{r=new Event("addtrack")}catch(t){(r=document.createEvent("Event")).initEvent("addtrack",!1,!1)}r.track=t,e.dispatchEvent(r)}function ve(t,e){var r=t.mode;if("disabled"===r&&(t.mode="hidden"),t.cues&&!t.cues.getCueById(e.id))try{if(t.addCue(e),!t.cues.getCueById(e.id))throw new Error("addCue is failed for: "+e)}catch(r){b.debug("[texttrack-utils]: "+r);var i=new self.TextTrackCue(e.startTime,e.endTime,e.text);i.id=e.id,t.addCue(i)}"disabled"===r&&(t.mode=r)}function me(t){var e=t.mode;if("disabled"===e&&(t.mode="hidden"),t.cues)for(var r=t.cues.length;r--;)t.removeCue(t.cues[r]);"disabled"===e&&(t.mode=e)}function pe(t,e,r,i){var n=t.mode;if("disabled"===n&&(t.mode="hidden"),t.cues&&0<t.cues.length)for(var a=function(t,e,r){var i=[],n=function(t,e){if(e<t[0].startTime)return 0;var r=t.length-1;if(e>t[r].endTime)return-1;for(var i=0,n=r;i<=n;){var a=Math.floor((n+i)/2);if(e<t[a].startTime)n=a-1;else{if(!(e>t[a].startTime&&i<r))return a;i=a+1}}return t[i].startTime-e<e-t[n].startTime?i:n}(t,e);if(-1<n)for(var a=n,s=t.length;a<s;a++){var o=t[a];if(o.startTime>=e&&o.endTime<=r)i.push(o);else if(o.startTime>r)return i}return i}(t.cues,e,r),s=0;s<a.length;s++)i&&!i(a[s])||t.removeCue(a[s]);"disabled"===n&&(t.mode=n)}var ye="org.id3",Te="https://aomedia.org/emsg/ID3";function Ee(){if("undefined"!=typeof self)return self.WebKitDataCue||self.VTTCue||self.TextTrackCue}var Se=function(){var t=Ee();try{new t(0,Number.POSITIVE_INFINITY,"")}catch(t){return Number.MAX_VALUE}return Number.POSITIVE_INFINITY}();function Le(t,e){return t.getTime()/1e3-e}var Re=function(){function t(t){this.hls=void 0,this.id3Track=null,this.media=null,this.dateRangeCuesAppended={},this.hls=t,this._registerListeners()}var e=t.prototype;return e.destroy=function(){this._unregisterListeners(),this.id3Track=null,this.media=null,this.dateRangeCuesAppended={},this.hls=null},e._registerListeners=function(){var t=this.hls;t.on(T.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(T.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(T.MANIFEST_LOADING,this.onManifestLoading,this),t.on(T.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),t.on(T.BUFFER_FLUSHING,this.onBufferFlushing,this),t.on(T.LEVEL_UPDATED,this.onLevelUpdated,this)},e._unregisterListeners=function(){var t=this.hls;t.off(T.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(T.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(T.MANIFEST_LOADING,this.onManifestLoading,this),t.off(T.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),t.off(T.BUFFER_FLUSHING,this.onBufferFlushing,this),t.off(T.LEVEL_UPDATED,this.onLevelUpdated,this)},e.onMediaAttached=function(t,e){this.media=e.media},e.onMediaDetaching=function(){this.id3Track&&(me(this.id3Track),this.id3Track=null,this.media=null,this.dateRangeCuesAppended={})},e.onManifestLoading=function(){this.dateRangeCuesAppended={}},e.createTrack=function(t){t=this.getID3Track(t.textTracks);return t.mode="hidden",t},e.getID3Track=function(t){if(this.media){for(var e=0;e<t.length;e++){var r=t[e];if("metadata"===r.kind&&"id3"===r.label)return ge(r,this.media),r}return this.media.addTextTrack("metadata","id3")}},e.onFragParsingMetadata=function(t,e){if(this.media){var r=this.hls.config,i=r.enableEmsgMetadataCues,n=r.enableID3MetadataCues;if(i||n){var a=e.samples;this.id3Track||(this.id3Track=this.createTrack(this.media));for(var s=Ee(),o=0;o<a.length;o++){var l=a[o].type;if((l!==Te||i)&&n){var u=ot(a[o].data);if(u){var h=a[o].pts,d=h+a[o].duration;(d=Se<d?Se:d)-h<=0&&(d=h+.25);for(var c=0;c<u.length;c++){var g,f=u[c];at(f)||(this.updateId3CueEnds(h),(g=new s(h,d,"")).value=f,l&&(g.type=l),this.id3Track.addCue(g))}}}}}}},e.updateId3CueEnds=function(t){var e,r=null==(e=this.id3Track)?void 0:e.cues;if(r)for(var i=r.length;i--;){var n=r[i];n.startTime<t&&n.endTime===Se&&(n.endTime=t)}},e.onBufferFlushing=function(t,e){var l,u,r=e.startOffset,i=e.endOffset,e=e.type,a=this.id3Track,s=this.hls;s&&(s=s.config,l=s.enableEmsgMetadataCues,u=s.enableID3MetadataCues,a)&&(l||u)&&pe(a,r,i,"audio"===e?function(t){return t.type===ye&&u}:"video"===e?function(t){return t.type===Te&&l}:function(t){return t.type===ye&&u||t.type===Te&&l})},e.onLevelUpdated=function(t,e){var r=this,e=e.details;if(this.media&&e.hasProgramDateTime&&this.hls.config.enableDateRangeMetadataCues){var n=this.dateRangeCuesAppended,a=this.id3Track,s=e.dateRanges,o=Object.keys(s);if(a)for(var l=Object.keys(n).filter(function(t){return!o.includes(t)}),h=l.length;h--;)!function(){var t=l[h];Object.keys(n[t].cues).forEach(function(e){a.removeCue(n[t].cues[e])}),delete n[t]}();e=e.fragments[e.fragments.length-1];if(0!==o.length&&y(null==e?void 0:e.programDateTime)){this.id3Track||(this.id3Track=this.createTrack(this.media));for(var c=e.programDateTime/1e3-e.start,f=Ee(),v=0;v<o.length;v++)!function(){var t=o[v],e=s[t],i=n[t],a=(null==i?void 0:i.cues)||{},l=(null==i?void 0:i.durationKnown)||!1,u=Le(e.startDate,c),h=Se,d=e.endDate;d?(h=Le(d,c),l=!0):e.endOnNext&&!l&&(d=o.reduce(function(t,r){var i=s[r];return i.class===e.class&&i.id!==r&&i.startDate>e.startDate&&t.push(i),t},[]).sort(function(t,e){return t.startDate.getTime()-e.startDate.getTime()})[0])&&(h=Le(d.startDate,c),l=!0);for(var m,y=Object.keys(e.attr),T=0;T<y.length;T++){var L,S,E=y[T];"ID"!==E&&"CLASS"!==E&&"START-DATE"!==E&&"DURATION"!==E&&"END-DATE"!==E&&"END-ON-NEXT"!==E&&((S=a[E])?l&&!i.durationKnown&&(S.endTime=h):(L=e.attr[E],S=new f(u,h,""),function(t){return"SCTE35-OUT"===t||"SCTE35-IN"===t}(E)&&(m=L,L=Uint8Array.from(m.replace(/^0x/,"").replace(/([\da-fA-F]{2}) ?/g,"0x$1 ").replace(/ +$/,"").split(" ")).buffer),S.value={key:E,data:L},S.type="com.apple.quicktime.HLS",S.id=t,r.id3Track.addCue(S),a[E]=S))}n[t]={cues:a,dateRange:e,durationKnown:l}}()}}},t}(),ke=function(){function t(t){var e=this;this.hls=void 0,this.config=void 0,this.media=null,this.levelDetails=null,this.currentTime=0,this.stallCount=0,this._latency=null,this.timeupdateHandler=function(){return e.timeupdate()},this.hls=t,this.config=t.config,this.registerListeners()}var e=t.prototype;return e.destroy=function(){this.unregisterListeners(),this.onMediaDetaching(),this.levelDetails=null,this.hls=this.timeupdateHandler=null},e.registerListeners=function(){this.hls.on(T.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.on(T.MEDIA_DETACHING,this.onMediaDetaching,this),this.hls.on(T.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.on(T.LEVEL_UPDATED,this.onLevelUpdated,this),this.hls.on(T.ERROR,this.onError,this)},e.unregisterListeners=function(){this.hls.off(T.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.off(T.MEDIA_DETACHING,this.onMediaDetaching,this),this.hls.off(T.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.off(T.LEVEL_UPDATED,this.onLevelUpdated,this),this.hls.off(T.ERROR,this.onError,this)},e.onMediaAttached=function(t,e){this.media=e.media,this.media.addEventListener("timeupdate",this.timeupdateHandler)},e.onMediaDetaching=function(){this.media&&(this.media.removeEventListener("timeupdate",this.timeupdateHandler),this.media=null)},e.onManifestLoading=function(){this.levelDetails=null,this._latency=null,this.stallCount=0},e.onLevelUpdated=function(t,e){e=e.details;(this.levelDetails=e).advanced&&this.timeupdate(),!e.live&&this.media&&this.media.removeEventListener("timeupdate",this.timeupdateHandler)},e.onError=function(t,e){e.details===S.BUFFER_STALLED_ERROR&&(this.stallCount++,null!=(e=this.levelDetails))&&e.live&&b.warn("[playback-rate-controller]: Stall detected, adjusting target latency")},e.timeupdate=function(){var i,r,n,t=this.media,e=this.levelDetails;t&&e&&(this.currentTime=t.currentTime,null!==(r=this.computeLatency()))&&(this._latency=r,n=(i=this.config).lowLatencyMode,i=i.maxLiveSyncPlaybackRate,n)&&1!==i&&null!==(n=this.targetLatency)&&(n=(r=r-n)<Math.min(this.maxLatency,n+e.targetduration),e.live&&n&&.05<r&&1<this.forwardBufferLength?(e=Math.min(2,Math.max(1,i)),n=Math.round(2/(1+Math.exp(-.75*r-this.edgeStalled))*20)/20,t.playbackRate=Math.min(e,Math.max(1,n))):1!==t.playbackRate&&0!==t.playbackRate&&(t.playbackRate=1))},e.estimateLiveEdge=function(){var t=this.levelDetails;return null===t?null:t.edge+t.age},e.computeLatency=function(){var t=this.estimateLiveEdge();return null===t?null:t-this.currentTime},a(t,[{key:"latency",get:function(){return this._latency||0}},{key:"maxLatency",get:function(){var t=this.config,e=this.levelDetails;return void 0!==t.liveMaxLatencyDuration?t.liveMaxLatencyDuration:e?t.liveMaxLatencyDurationCount*e.targetduration:0}},{key:"targetLatency",get:function(){var e,a,s,r,n,t=this.levelDetails;return null===t?null:(e=t.holdBack,r=t.partHoldBack,t=t.targetduration,a=(n=this.config).liveSyncDuration,s=n.liveSyncDurationCount,n=n.lowLatencyMode&&r||e,(n=(r=this.hls.userConfig).liveSyncDuration||r.liveSyncDurationCount||0===n?void 0!==a?a:s*t:n)+Math.min(+this.stallCount,t))}},{key:"liveSyncPosition",get:function(){var i,t=this.estimateLiveEdge(),e=this.targetLatency,r=this.levelDetails;return null===t||null===e||null===r?null:(i=r.edge,t=t-e-this.edgeStalled,e=i-r.totalduration,i=i-(this.config.lowLatencyMode&&r.partTarget||r.targetduration),Math.min(Math.max(e,t),i))}},{key:"drift",get:function(){var t=this.levelDetails;return null===t?1:t.drift}},{key:"edgeStalled",get:function(){var e,t=this.levelDetails;return null===t?0:(e=3*(this.config.lowLatencyMode&&t.partTarget||t.targetduration),Math.max(t.age-e,0))}},{key:"forwardBufferLength",get:function(){var r,t=this.media,e=this.levelDetails;return t&&e?((r=t.buffered.length)?t.buffered.end(r-1):e.edge)-this.currentTime:0}}]),t}(),Ae=["NONE","TYPE-0","TYPE-1",null],be=function(){function t(t,e,r){this.msn=void 0,this.part=void 0,this.skip=void 0,this.msn=t,this.part=e,this.skip=r}return t.prototype.addDirectives=function(t){t=new self.URL(t);return void 0!==this.msn&&t.searchParams.set("_HLS_msn",this.msn.toString()),void 0!==this.part&&t.searchParams.set("_HLS_part",this.part.toString()),this.skip&&t.searchParams.set("_HLS_skip",this.skip),t.href},t}(),De=function(){function t(t){this._attrs=void 0,this.audioCodec=void 0,this.bitrate=void 0,this.codecSet=void 0,this.height=void 0,this.id=void 0,this.name=void 0,this.videoCodec=void 0,this.width=void 0,this.unknownCodecs=void 0,this.audioGroupIds=void 0,this.details=void 0,this.fragmentError=0,this.loadError=0,this.loaded=void 0,this.realBitrate=0,this.textGroupIds=void 0,this.url=void 0,this._urlId=0,this.url=[t.url],this._attrs=[t.attrs],this.bitrate=t.bitrate,t.details&&(this.details=t.details),this.id=t.id||0,this.name=t.name,this.width=t.width||0,this.height=t.height||0,this.audioCodec=t.audioCodec,this.videoCodec=t.videoCodec,this.unknownCodecs=t.unknownCodecs,this.codecSet=[t.videoCodec,t.audioCodec].filter(function(t){return t}).join(",").replace(/\.[^.,]+/g,"")}return t.prototype.addFallback=function(t){this.url.push(t.url),this._attrs.push(t.attrs)},a(t,[{key:"maxBitrate",get:function(){return Math.max(this.realBitrate,this.bitrate)}},{key:"attrs",get:function(){return this._attrs[this._urlId]}},{key:"pathwayId",get:function(){return this.attrs["PATHWAY-ID"]||"."}},{key:"uri",get:function(){return this.url[this._urlId]||""}},{key:"urlId",get:function(){return this._urlId},set:function(t){t%=this.url.length;this._urlId!==t&&(this.fragmentError=0,this.loadError=0,this.details=void 0,this._urlId=t)}},{key:"audioGroupId",get:function(){var t;return null==(t=this.audioGroupIds)?void 0:t[this.urlId]}},{key:"textGroupId",get:function(){var t;return null==(t=this.textGroupIds)?void 0:t[this.urlId]}}]),t}();function Ie(t,e){var n,r=e.startPTS;y(r)?(n=0,(r=e.sn>t.sn?(n=r-t.start,t):(n=t.start-r,e)).duration!==n&&(r.duration=n)):e.sn>t.sn?t.cc===e.cc&&t.minEndPTS?e.start=t.start+(t.minEndPTS-t.start):e.start=t.start+t.duration:e.start=Math.max(t.start-e.duration,0)}function _e(t,e,r,i,n,a){i-r<=0&&(b.warn("Fragment should have a positive duration",e),i=r+e.duration,a=n+e.duration);var s=r,o=i,l=e.startPTS,u=e.endPTS,h=(y(l)&&(h=Math.abs(l-r),y(e.deltaPTS)?e.deltaPTS=Math.max(h,e.deltaPTS):e.deltaPTS=h,s=Math.max(r,l),r=Math.min(r,l),n=Math.min(n,e.startDTS),o=Math.min(i,u),i=Math.max(i,u),a=Math.max(a,e.endDTS)),r-e.start);0!==e.start&&(e.start=r),e.duration=i-e.start,e.startPTS=r,e.maxStartPTS=s,e.startDTS=n,e.endPTS=i,e.minEndPTS=o,e.endDTS=a;var c,l=e.sn;if(!t||l<t.startSN||l>t.endSN)return 0;var u=l-t.startSN,v=t.fragments;for(v[u]=e,c=u;0<c;c--)Ie(v[c],v[c-1]);for(c=u;c<v.length-1;c++)Ie(v[c],v[c+1]);return t.fragmentHint&&Ie(v[v.length-1],t.fragmentHint),t.PTSKnown=t.alignedSliding=!0,h}function we(t,e){for(var r=null,i=t.fragments,n=i.length-1;0<=n;n--){var a=i[n].initSegment;if(a){r=a;break}}t.fragmentHint&&delete t.fragmentHint.endPTS;var s,u,d,c=0;if(function(t,e,r){for(var i=e.skippedSegments,n=Math.max(t.startSN,e.startSN)-e.startSN,a=(t.fragmentHint?1:0)+(i?e.endSN:Math.min(t.endSN,e.endSN))-e.startSN,s=e.startSN-t.startSN,o=e.fragmentHint?e.fragments.concat(e.fragmentHint):e.fragments,l=t.fragmentHint?t.fragments.concat(t.fragmentHint):t.fragments,u=n;u<=a;u++){var h=l[s+u],d=o[u];i&&!d&&u<i&&(d=e.fragments[u]=h),h&&d&&r(h,d)}}(t,e,function(t,i){t.relurl&&(c=t.cc-i.cc),y(t.startPTS)&&y(t.endPTS)&&(i.start=i.startPTS=t.startPTS,i.startDTS=t.startDTS,i.maxStartPTS=t.maxStartPTS,i.endPTS=t.endPTS,i.endDTS=t.endDTS,i.minEndPTS=t.minEndPTS,i.duration=t.endPTS-t.startPTS,i.duration&&(s=i),e.PTSKnown=e.alignedSliding=!0),i.elementaryStreams=t.elementaryStreams,i.loader=t.loader,i.stats=t.stats,i.urlId=t.urlId,t.initSegment&&(i.initSegment=t.initSegment,r=t.initSegment)}),r&&(e.fragmentHint?e.fragments.concat(e.fragmentHint):e.fragments).forEach(function(t){t.initSegment&&t.initSegment.relurl!==(null==r?void 0:r.relurl)||(t.initSegment=r)}),e.skippedSegments)if(e.deltaUpdateFailed=e.fragments.some(function(t){return!t}),e.deltaUpdateFailed){b.warn("[level-helper] Previous playlist missing segments skipped in delta playlist");for(var f=e.skippedSegments;f--;)e.fragments.shift();e.startSN=e.fragments[0].sn,e.startCC=e.fragments[0].cc}else e.canSkipDateRanges&&(e.dateRanges=(l=t.dateRanges,u=e.dateRanges,h=e.recentlyRemovedDateranges,d=o({},l),h&&h.forEach(function(t){delete d[t]}),Object.keys(u).forEach(function(t){var e=new C(u[t].attr,d[t]);e.isValid?d[t]=e:b.warn('Ignoring invalid Playlist Delta Update DATERANGE tag: "'+JSON.stringify(u[t].attr)+'"')}),d));var g=e.fragments;if(c){b.warn("discontinuity sliding from playlist, take drift into account");for(var v=0;v<g.length;v++)g[v].cc+=c}e.skippedSegments&&(e.startCC=e.fragments[0].cc),function(t,e){if(t&&e)for(var i=0,n=0,a=t.length;n<=a;n++){var s=t[n],o=e[n+i];s&&o&&s.index===o.index&&s.fragment.sn===o.fragment.sn?function(t,e){e.elementaryStreams=t.elementaryStreams,e.stats=t.stats}(s,o):i--}}(t.partList,e.partList),s?_e(e,s,s.startPTS,s.endPTS,s.startDTS,s.endDTS):Ce(t,e),g.length&&(e.totalduration=e.edge-g[0].start),e.driftStartTime=t.driftStartTime,e.driftStart=t.driftStart;var h,l=e.advancedDateTime;e.advanced&&l?(h=e.edge,e.driftStart||(e.driftStartTime=l,e.driftStart=h),e.driftEndTime=l,e.driftEnd=h):(e.driftEndTime=t.driftEndTime,e.driftEnd=t.driftEnd,e.advancedDateTime=t.advancedDateTime)}function Ce(t,e){var r=e.startSN+e.skippedSegments-t.startSN,t=t.fragments;r<0||r>=t.length||xe(e,t[r].start)}function xe(t,e){if(e){for(var r=t.fragments,i=t.skippedSegments;i<r.length;i++)r[i].start+=e;t.fragmentHint&&(t.fragmentHint.start+=e)}}function Pe(t,e,r){return null!=t&&t.details?Oe(null==(t=t.details)?void 0:t.partList,e,r):null}function Oe(t,e,r){if(t)for(var i=t.length;i--;){var n=t[i];if(n.index===r&&n.fragment.sn===e)return n}return null}function Me(t){switch(t.details){case S.FRAG_LOAD_TIMEOUT:case S.KEY_LOAD_TIMEOUT:case S.LEVEL_LOAD_TIMEOUT:case S.MANIFEST_LOAD_TIMEOUT:return!0}return!1}function Fe(t,e){e=Me(e);return t.default[(e?"timeout":"error")+"Retry"]}function Ne(t,e){e="linear"===t.backoff?1:Math.pow(2,e);return Math.min(e*t.retryDelayMs,t.maxRetryDelayMs)}function Ue(t){return e(e({},t),{errorRetry:null,timeoutRetry:null})}function Be(t,e,r,i){return t&&e<t.maxNumRetry&&(0===i&&!1===navigator.onLine||i&&(i<400||499<i)||r)}function Ge(t,e){for(var n,a,r=0,i=t.length-1;r<=i;){var s=e(a=t[n=(r+i)/2|0]);if(0<s)r=1+n;else{if(!(s<0))return a;i=n-1}}return null}function Ke(t,e,r,i){void 0===r&&(r=0),void 0===i&&(i=0);var n=null;return t?n=e[t.sn-e[0].sn+1]||null:0===r&&0===e[0].start&&(n=e[0]),n&&0===He(r,i,n)||!(e=Ge(e,He.bind(null,r,i)))||e===t&&n?n:e}function He(t,e,r){return void 0===e&&(e=0),r.start<=(t=void 0===t?0:t)&&r.start+r.duration>t?0:(e=Math.min(e,r.duration+(r.deltaPTS||0)),r.start+r.duration-e<=t?1:r.start-e>t&&r.start?-1:0)}var je,F=function(){function t(t){this.hls=void 0,this.playlistError=0,this.penalizedRenditions={},this.log=void 0,this.warn=void 0,this.error=void 0,this.hls=t,this.log=b.log.bind(b,"[info]:"),this.warn=b.warn.bind(b,"[warning]:"),this.error=b.error.bind(b,"[error]:"),this.registerListeners()}var e=t.prototype;return e.registerListeners=function(){var t=this.hls;t.on(T.ERROR,this.onError,this),t.on(T.MANIFEST_LOADING,this.onManifestLoading,this)},e.unregisterListeners=function(){var t=this.hls;t&&(t.off(T.ERROR,this.onError,this),t.off(T.ERROR,this.onErrorOut,this),t.off(T.MANIFEST_LOADING,this.onManifestLoading,this))},e.destroy=function(){this.unregisterListeners(),this.hls=null,this.penalizedRenditions={}},e.startLoad=function(t){this.playlistError=0},e.stopLoad=function(){},e.getVariantLevelIndex=function(t){return(null==t?void 0:t.type)===le?t.level:this.hls.loadLevel},e.onManifestLoading=function(){this.playlistError=0,this.penalizedRenditions={}},e.onError=function(t,e){if(!e.fatal){var d,i=this.hls,n=e.context;switch(e.details){case S.FRAG_LOAD_ERROR:case S.FRAG_LOAD_TIMEOUT:case S.KEY_LOAD_ERROR:case S.KEY_LOAD_TIMEOUT:return void(e.errorAction=this.getFragRetryOrSwitchAction(e));case S.FRAG_GAP:case S.FRAG_PARSING_ERROR:case S.FRAG_DECRYPT_ERROR:return e.errorAction=this.getFragRetryOrSwitchAction(e),void(e.errorAction.action=2);case S.LEVEL_EMPTY_ERROR:case S.LEVEL_PARSING_ERROR:var o=e.parent===le?e.level:i.loadLevel;return void(e.details===S.LEVEL_EMPTY_ERROR&&null!=(a=e.context)&&null!=(a=a.levelDetails)&&a.live?e.errorAction=this.getPlaylistRetryOrSwitchAction(e,o):(e.levelRetry=!1,e.errorAction=this.getLevelSwitchAction(e,o)));case S.LEVEL_LOAD_ERROR:case S.LEVEL_LOAD_TIMEOUT:return void("number"==typeof(null==n?void 0:n.level)&&(e.errorAction=this.getPlaylistRetryOrSwitchAction(e,n.level)));case S.AUDIO_TRACK_LOAD_ERROR:case S.AUDIO_TRACK_LOAD_TIMEOUT:case S.SUBTITLE_LOAD_ERROR:case S.SUBTITLE_TRACK_LOAD_TIMEOUT:if(n){var a=i.levels[i.loadLevel];if(a&&(n.type===se&&n.groupId===a.audioGroupId||n.type===oe&&n.groupId===a.textGroupId))return e.errorAction=this.getPlaylistRetryOrSwitchAction(e,i.loadLevel),e.errorAction.action=2,void(e.errorAction.flags=1)}return;case S.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED:var o=i.levels[i.loadLevel],a=null==o?void 0:o.attrs["HDCP-LEVEL"];return void(a&&(e.errorAction={action:2,flags:2,hdcpLevel:a}));case S.BUFFER_ADD_CODEC_ERROR:case S.REMUX_ALLOC_ERROR:return void(e.errorAction=this.getLevelSwitchAction(e,null!=(o=e.level)?o:i.loadLevel));case S.INTERNAL_EXCEPTION:case S.BUFFER_APPENDING_ERROR:case S.BUFFER_APPEND_ERROR:case S.BUFFER_FULL_ERROR:case S.LEVEL_SWITCH_ERROR:case S.BUFFER_STALLED_ERROR:case S.BUFFER_SEEK_OVER_HOLE:case S.BUFFER_NUDGE_ON_STALL:return void(e.errorAction={action:0,flags:0})}e.type===E.KEY_SYSTEM_ERROR&&(d=this.getVariantLevelIndex(e.frag),e.levelRetry=!1,e.errorAction=this.getLevelSwitchAction(e,d))}},e.getPlaylistRetryOrSwitchAction=function(t,e){var i=Fe(this.hls.config.playlistLoadPolicy,t),n=this.playlistError++,r=null==(r=t.response)?void 0:r.code;return Be(i,n,Me(t),r)?{action:5,flags:0,retryConfig:i,retryCount:n}:(r=this.getLevelSwitchAction(t,e),i&&(r.retryConfig=i,r.retryCount=n),r)},e.getFragRetryOrSwitchAction=function(t){var e=this.hls,r=this.getVariantLevelIndex(t.frag),i=e.levels[r],n=e.config,a=n.fragLoadPolicy,n=n.keyLoadPolicy,n=Fe(t.details.startsWith("key")?n:a,t),a=e.levels.reduce(function(t,e){return t+e.fragmentError},0);if(i){t.details!==S.FRAG_GAP&&i.fragmentError++;i=null==(e=t.response)?void 0:e.code;if(Be(n,a,Me(t),i))return{action:5,flags:0,retryConfig:n,retryCount:a}}e=this.getLevelSwitchAction(t,r);return n&&(e.retryConfig=n,e.retryCount=a),e},e.getLevelSwitchAction=function(t,e){var r=this.hls,i=(null==e&&(e=r.loadLevel),this.hls.levels[e]);if(i&&(i.loadError++,r.autoLevelEnabled)){for(var s=-1,o=r.levels,l=null==(e=t.frag)?void 0:e.type,e=null!=(e=t.context)?e:{},h=e.type,d=e.groupId,c=o.length;c--;){var f=(c+r.loadLevel)%o.length;if(f!==r.loadLevel&&0===o[f].loadError){var g=o[f];if(t.details===S.FRAG_GAP&&t.frag){var v=o[f].details;if(v){v=Ke(t.frag,v.fragments,t.frag.start);if(null!=v&&v.gap)continue}}else{if(h===se&&d===g.audioGroupId||h===oe&&d===g.textGroupId)continue;if(l===ue&&i.audioGroupId===g.audioGroupId||l===he&&i.textGroupId===g.textGroupId)continue}s=f;break}}if(-1<s&&r.loadLevel!==s)return t.levelRetry=!0,{action:2,flags:0,nextAutoLevel:s}}return{action:2,flags:1}},e.onErrorOut=function(t,e){var r;switch(null==(r=e.errorAction)?void 0:r.action){case 0:break;case 2:this.sendAlternateToPenaltyBox(e),e.errorAction.resolved||e.details===S.FRAG_GAP||(e.fatal=!0)}e.fatal&&this.hls.stopLoad()},e.sendAlternateToPenaltyBox=function(t){var e=this.hls,r=t.errorAction;if(r){var i=r.flags,n=r.hdcpLevel,a=r.nextAutoLevel;switch(i){case 0:this.switchLevel(t,a);break;case 1:r.resolved||(r.resolved=this.redundantFailover(t));break;case 2:n&&(e.maxHdcpLevel=Ae[Ae.indexOf(n)-1],r.resolved=!0),this.warn('Restricting playback to HDCP-LEVEL of "'+e.maxHdcpLevel+'" or lower')}r.resolved||this.switchLevel(t,a)}},e.switchLevel=function(t,e){void 0!==e&&t.errorAction&&(this.warn("switching to level "+e+" after "+t.details),this.hls.nextAutoLevel=e,t.errorAction.resolved=!0,this.hls.nextLoadLevel=this.hls.nextAutoLevel)},e.redundantFailover=function(t){var e=this,r=this.hls,i=this.penalizedRenditions,n=t.parent===le?t.level:r.loadLevel,a=r.levels[n],s=a.url.length,o=(t.frag||a).urlId;a.urlId!==o||t.frag&&!a.details||this.penalizeRendition(a,t);for(var u=1;u<s;u++){var h=function(){var l=(o+u)%s,h=i[l];if(!h||function(t,e,r){if(3e5<performance.now()-t.lastErrorPerfMs)return 1;var i=t.details;if(e.details===S.FRAG_GAP&&i&&e.frag){var n=e.frag.start,n=Ke(null,i.fragments,n);if(n&&!n.gap)return 1}if(r&&t.errors.length<r.errors.length){n=t.errors[t.errors.length-1];if(i&&n.frag&&e.frag&&Math.abs(n.frag.start-e.frag.start)>3*i.targetduration)return 1}}(h,t,i[o]))return e.warn("Switching to Redundant Stream "+(1+l)+"/"+s+': "'+a.url[l]+'" after '+t.details),e.playlistError=0,r.levels.forEach(function(t){t.urlId=l}),r.nextLoadLevel=n,{v:!0}}();if("object"==typeof h)return h.v}return!1},e.penalizeRendition=function(t,e){var r=this.penalizedRenditions,i=r[t.urlId]||{lastErrorPerfMs:0,errors:[],details:void 0};i.lastErrorPerfMs=performance.now(),i.errors.push(e),i.details=t.details,r[t.urlId]=i},t}(),We=function(){function t(t,e){this.hls=void 0,this.timer=-1,this.requestScheduled=-1,this.canLoad=!1,this.log=void 0,this.warn=void 0,this.log=b.log.bind(b,e+":"),this.warn=b.warn.bind(b,e+":"),this.hls=t}var e=t.prototype;return e.destroy=function(){this.clearTimer(),this.hls=this.log=this.warn=null},e.clearTimer=function(){clearTimeout(this.timer),this.timer=-1},e.startLoad=function(){this.canLoad=!0,this.requestScheduled=-1,this.loadPlaylist()},e.stopLoad=function(){this.canLoad=!1,this.clearTimer()},e.switchParams=function(t,e){var r=null==e?void 0:e.renditionReports;if(r){for(var l,o,h,i=-1,n=0;n<r.length;n++){var a=r[n],s=void 0;try{s=new self.URL(a.URI,e.url).href}catch(t){b.warn("Could not construct new URL for Rendition Report: "+t),s=a.URI||""}if(s===t){i=n;break}s===t.substring(0,s.length)&&(i=n)}if(-1!==i)return o=r[i],l=parseInt(o["LAST-MSN"])||(null==e?void 0:e.lastPartSn),o=parseInt(o["LAST-PART"])||(null==e?void 0:e.lastPartIndex),this.hls.config.lowLatencyMode&&(h=Math.min(e.age-e.partTarget,e.targetduration),0<=o)&&h>e.partTarget&&(o+=1),new be(l,0<=o?o:void 0,"")}},e.loadPlaylist=function(t){-1===this.requestScheduled&&(this.requestScheduled=self.performance.now())},e.shouldLoadPlaylist=function(t){return this.canLoad&&!!t&&!!t.url&&(!t.details||t.details.live)},e.shouldReloadPlaylist=function(t){return-1===this.timer&&-1===this.requestScheduled&&this.shouldLoadPlaylist(t)},e.playlistLoaded=function(t,e,r){var i=this,n=e.details,a=e.stats,s=self.performance.now(),o=a.loading.first?Math.max(0,s-a.loading.first):0;if(n.advancedDateTime=Date.now()-o,n.live||null!=r&&r.live){if(n.reloaded(r),r&&this.log("live playlist "+t+" "+(n.advanced?"REFRESHED "+n.lastPartSn+"-"+n.lastPartIndex:"MISSED")),r&&0<n.fragments.length&&we(r,n),this.canLoad&&n.live){var l,o=void 0,h=void 0;if(n.canBlockReload&&n.endSN&&n.advanced){var d=this.hls.config.lowLatencyMode,c=n.lastPartSn,f=n.endSN,g=n.lastPartIndex,v=c===f,c=(-1!==g?(o=v?f+1:c,h=v?d?0:g:g+1):o=f+1,n.age),g=c+n.ageHeader,f=Math.min(g-n.partTarget,1.5*n.targetduration);if(0<f&&(r&&f>r.tuneInGoal?(this.warn("CDN Tune-in goal increased from: "+r.tuneInGoal+" to: "+f+" with playlist age: "+n.age),f=0):(o+=g=Math.floor(f/n.targetduration),void 0!==h&&(h+=Math.round(f%n.targetduration/n.partTarget)),this.log("CDN Tune-in age: "+n.ageHeader+"s last advanced "+c.toFixed(2)+"s goal: "+f+" skip sn "+g+" to part "+h)),n.tuneInGoal=f),l=this.getDeliveryDirectives(n,e.deliveryDirectives,o,h),d||!v)return void this.loadPlaylist(l)}else n.canBlockReload&&(l=this.getDeliveryDirectives(n,e.deliveryDirectives,o,h));r=this.hls.mainForwardBufferInfo,c=r?r.end-r.len:0,g=function(t,e){void 0===e&&(e=1/0);var r=1e3*t.targetduration;return t.updated?(t=t.fragments).length&&e<4*r&&(e=1e3*t[t.length-1].duration)<r&&(r=e):r/=2,Math.round(r)}(n,1e3*(n.edge-c)),f=(n.updated&&s>this.requestScheduled+g&&(this.requestScheduled=a.loading.start),void 0!==o&&n.canBlockReload?this.requestScheduled=a.loading.first+g-(1e3*n.partTarget||1e3):-1===this.requestScheduled||this.requestScheduled+g<s?this.requestScheduled=s:this.requestScheduled-s<=0&&(this.requestScheduled+=g),this.requestScheduled-s),f=Math.max(0,f);this.log("reload live playlist "+t+" in "+Math.round(f)+" ms"),this.timer=self.setTimeout(function(){return i.loadPlaylist(l)},f)}}else this.clearTimer()},e.getDeliveryDirectives=function(t,e,r,i){var n=function(t,e){var r=t.canSkipUntil,i=t.canSkipDateRanges,t=t.endSN;return r&&(void 0!==e?e-t:0)<r?i?"v2":"YES":""}(t,r);return null!=e&&e.skip&&t.deltaUpdateFailed&&(r=e.msn,i=e.part,n=""),new be(r,i,n)},e.checkRetry=function(t){var e=this,r=t.details,i=Me(t),n=t.errorAction,a=n||{},s=a.action,o=a.retryCount,o=void 0===o?0:o,a=a.retryConfig,s=!!n&&!!a&&(5===s||!n.resolved&&2===s);return s&&(this.requestScheduled=-1,i&&null!=(i=t.context)&&i.deliveryDirectives?(this.warn("Retrying playlist loading "+(o+1)+"/"+a.maxNumRetry+' after "'+r+'" without delivery-directives'),this.loadPlaylist()):(i=Ne(a,o),this.timer=self.setTimeout(function(){return e.loadPlaylist()},i),this.warn("Retrying playlist loading "+(o+1)+"/"+a.maxNumRetry+' after "'+r+'" in '+i+"ms")),t.levelRetry=!0,n.resolved=!0),s},t}(),qe=function(t){function e(e,r){return(e=t.call(this,e,"[level-controller]")||this)._levels=[],e._firstLevel=-1,e._startLevel=void 0,e.currentLevel=null,e.currentLevelIndex=-1,e.manualLevelIndex=-1,e.steering=void 0,e.onParsedComplete=void 0,e.steering=r,e._registerListeners(),e}l(e,t);var r=e.prototype;return r._registerListeners=function(){var t=this.hls;t.on(T.MANIFEST_LOADING,this.onManifestLoading,this),t.on(T.MANIFEST_LOADED,this.onManifestLoaded,this),t.on(T.LEVEL_LOADED,this.onLevelLoaded,this),t.on(T.LEVELS_UPDATED,this.onLevelsUpdated,this),t.on(T.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),t.on(T.FRAG_LOADED,this.onFragLoaded,this),t.on(T.ERROR,this.onError,this)},r._unregisterListeners=function(){var t=this.hls;t.off(T.MANIFEST_LOADING,this.onManifestLoading,this),t.off(T.MANIFEST_LOADED,this.onManifestLoaded,this),t.off(T.LEVEL_LOADED,this.onLevelLoaded,this),t.off(T.LEVELS_UPDATED,this.onLevelsUpdated,this),t.off(T.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),t.off(T.FRAG_LOADED,this.onFragLoaded,this),t.off(T.ERROR,this.onError,this)},r.destroy=function(){this._unregisterListeners(),this.steering=null,this.resetLevels(),t.prototype.destroy.call(this)},r.startLoad=function(){this._levels.forEach(function(t){t.loadError=0,t.fragmentError=0}),t.prototype.startLoad.call(this)},r.resetLevels=function(){this._startLevel=void 0,this.manualLevelIndex=-1,this.currentLevelIndex=-1,this.currentLevel=null,this._levels=[]},r.onManifestLoading=function(t,e){this.resetLevels()},r.onManifestLoaded=function(t,e){var r,i=[],n={};e.levels.forEach(function(t){var a=t.attrs,e=(-1!==(null==(e=t.audioCodec)?void 0:e.indexOf("mp4a.40.34"))&&(je=je||/chrome|firefox/i.test(navigator.userAgent))&&(t.audioCodec=void 0),a.AUDIO),o=a.CODECS,l=a["FRAME-RATE"],u=a["PATHWAY-ID"],h=a.RESOLUTION,a=a.SUBTITLES,u=(u||".")+"-"+t.bitrate+"-"+h+"-"+l+"-"+o;(r=n[u])?r.addFallback(t):(r=new De(t),n[u]=r,i.push(r)),Xe(r,"audio",e),Xe(r,"text",a)}),this.filterAndSortMediaOptions(i,e)},r.filterAndSortMediaOptions=function(t,e){var r=this,i=[],n=[],a=!1,s=!1,o=!1,l=t.filter(function(t){var e=t.audioCodec,r=t.videoCodec,i=t.width,n=t.height,t=t.unknownCodecs;return a=a||!(!i||!n),s=s||!!r,o=o||!!e,!(null!=t&&t.length)&&(!e||jt(e,"audio"))&&(!r||jt(r,"video"))});if(0!==(l=(a||s)&&o?l.filter(function(t){var e=t.videoCodec,r=t.width,t=t.height;return!!e||!(!r||!t)}):l).length){e.audioTracks&&ze(i=e.audioTracks.filter(function(t){return!t.audioCodec||jt(t.audioCodec,"audio")})),e.subtitles&&ze(n=e.subtitles);var u=l.slice(0),h=(l.sort(function(t,e){return t.attrs["HDCP-LEVEL"]!==e.attrs["HDCP-LEVEL"]?(t.attrs["HDCP-LEVEL"]||"")>(e.attrs["HDCP-LEVEL"]||"")?1:-1:t.bitrate!==e.bitrate?t.bitrate-e.bitrate:t.attrs["FRAME-RATE"]!==e.attrs["FRAME-RATE"]?t.attrs.decimalFloatingPoint("FRAME-RATE")-e.attrs.decimalFloatingPoint("FRAME-RATE"):t.attrs.SCORE!==e.attrs.SCORE?t.attrs.decimalFloatingPoint("SCORE")-e.attrs.decimalFloatingPoint("SCORE"):a&&t.height!==e.height?t.height-e.height:0}),u[0]);if(this.steering&&(l=this.steering.filterParsedLevels(l)).length!==u.length)for(var d=0;d<u.length;d++)if(u[d].pathwayId===l[0].pathwayId){h=u[d];break}this._levels=l;for(var c=0;c<l.length;c++)if(l[c]===h){this._firstLevel=c,this.log("manifest loaded, "+l.length+" level(s) found, first bitrate: "+h.bitrate);break}t=o&&!s,n={levels:l,audioTracks:i,subtitleTracks:n,sessionData:e.sessionData,sessionKeys:e.sessionKeys,firstLevel:this._firstLevel,stats:e.stats,audio:o,video:s,altAudio:!t&&i.some(function(t){return!!t.url})};this.hls.trigger(T.MANIFEST_PARSED,n),(this.hls.config.autoStartLoad||this.hls.forceStartLoad)&&this.hls.startLoad(this.hls.config.startPosition)}else Promise.resolve().then(function(){var t;r.hls&&(t=new Error("no level with compatible codecs found in manifest"),r.hls.trigger(T.ERROR,{type:E.MEDIA_ERROR,details:S.MANIFEST_INCOMPATIBLE_CODECS_ERROR,fatal:!0,url:e.url,error:t,reason:t.message}))})},r.onError=function(t,e){!e.fatal&&e.context&&e.context.type===ae&&e.context.level===this.level&&this.checkRetry(e)},r.onFragLoaded=function(t,e){var e=e.frag;void 0!==e&&e.type===le&&void 0!==(e=this._levels[e.level])&&(e.loadError=0)},r.onLevelLoaded=function(t,e){var n=e.level,a=e.details,s=this._levels[n];s?n===this.currentLevelIndex?(0===s.fragmentError&&(s.loadError=0),this.playlistLoaded(n,e,s.details)):null!=(s=e.deliveryDirectives)&&s.skip&&(a.deltaUpdateFailed=!0):(this.warn("Invalid level index "+n),null!=(s=e.deliveryDirectives)&&s.skip&&(a.deltaUpdateFailed=!0))},r.onAudioTrackSwitched=function(t,e){var r=this.currentLevel;if(r){var i=this.hls.audioTracks[e.id].groupId;if(r.audioGroupIds&&r.audioGroupId!==i){for(var n=-1,a=0;a<r.audioGroupIds.length;a++)if(r.audioGroupIds[a]===i){n=a;break}-1!==n&&n!==r.urlId&&(r.urlId=n,this.canLoad)&&this.startLoad()}}},r.loadPlaylist=function(e){t.prototype.loadPlaylist.call(this);var r=this.currentLevelIndex,i=this.currentLevel;if(i&&this.shouldLoadPlaylist(i)){var n=i.urlId,a=i.uri;if(e)try{a=e.addDirectives(a)}catch(t){this.warn("Could not construct new URL with HLS Delivery Directives: "+t)}var s=i.attrs["PATHWAY-ID"];this.log("Loading level index "+r+(void 0!==(null==e?void 0:e.msn)?" at sn "+e.msn+" part "+e.part:"")+" with"+(s?" Pathway "+s:"")+" URI "+(n+1)+"/"+i.url.length+" "+a),this.clearTimer(),this.hls.trigger(T.LEVEL_LOADING,{url:a,level:r,id:n,deliveryDirectives:e||null})}},r.removeLevel=function(t,e){function i(t,r){return r!==e}var r=this,n=this._levels.filter(function(n,a){return a!==t||(1<n.url.length&&void 0!==e?(n.url=n.url.filter(i),n.audioGroupIds&&(n.audioGroupIds=n.audioGroupIds.filter(i)),n.textGroupIds&&(n.textGroupIds=n.textGroupIds.filter(i)),!(n.urlId=0)):(r.steering&&r.steering.removeLevel(n),!1))});this.hls.trigger(T.LEVELS_UPDATED,{levels:n})},r.onLevelsUpdated=function(t,e){e=e.levels;e.forEach(function(t,e){t=t.details;null!=t&&t.fragments&&t.fragments.forEach(function(t){t.level=e})}),this._levels=e},a(e,[{key:"levels",get:function(){return 0===this._levels.length?null:this._levels}},{key:"level",get:function(){return this.currentLevelIndex},set:function(t){var e=this._levels;if(0!==e.length){if(t<0||t>=e.length){var r=new Error("invalid level idx"),i=t<0;if(this.hls.trigger(T.ERROR,{type:E.OTHER_ERROR,details:S.LEVEL_SWITCH_ERROR,level:t,fatal:i,error:r,reason:r.message}),i)return;t=Math.min(t,e.length-1)}var r=this.currentLevelIndex,i=this.currentLevel,s=i?i.attrs["PATHWAY-ID"]:void 0,e=e[t],u=e.attrs["PATHWAY-ID"];this.currentLevelIndex=t,this.currentLevel=e,r===t&&e.details&&i&&s===u||(this.log("Switching to level "+t+(u?" with Pathway "+u:"")+" from level "+r+(s?" with Pathway "+s:"")),delete(u=o({},e,{level:t,maxBitrate:e.maxBitrate,attrs:e.attrs,uri:e.uri,urlId:e.urlId}))._attrs,delete u._urlId,this.hls.trigger(T.LEVEL_SWITCHING,u),(r=e.details)&&!r.live)||(s=this.switchParams(e.uri,null==i?void 0:i.details),this.loadPlaylist(s))}}},{key:"manualLevel",get:function(){return this.manualLevelIndex},set:function(t){this.manualLevelIndex=t,void 0===this._startLevel&&(this._startLevel=t),-1!==t&&(this.level=t)}},{key:"firstLevel",get:function(){return this._firstLevel},set:function(t){this._firstLevel=t}},{key:"startLevel",get:function(){var t;return void 0===this._startLevel?void 0!==(t=this.hls.config.startLevel)?t:this._firstLevel:this._startLevel},set:function(t){this._startLevel=t}},{key:"nextLoadLevel",get:function(){return-1!==this.manualLevelIndex?this.manualLevelIndex:this.hls.nextAutoLevel},set:function(t){this.level=t,-1===this.manualLevelIndex&&(this.hls.nextAutoLevel=t)}}]),e}(We);function Xe(t,e,r){r&&("audio"===e?(t.audioGroupIds||(t.audioGroupIds=[]),t.audioGroupIds[t.url.length-1]=r):"text"===e&&(t.textGroupIds||(t.textGroupIds=[]),t.textGroupIds[t.url.length-1]=r))}function ze(t){var e={};t.forEach(function(t){var r=t.groupId||"";t.id=e[r]=e[r]||0,e[r]++})}var Qe="NOT_LOADED",Ze="APPENDING",$e="PARTIAL",Je="OK",tr=function(){function t(t){this.activePartLists=Object.create(null),this.endListFragments=Object.create(null),this.fragments=Object.create(null),this.timeRanges=Object.create(null),this.bufferPadding=.2,this.hls=void 0,this.hasGaps=!1,this.hls=t,this._registerListeners()}var e=t.prototype;return e._registerListeners=function(){var t=this.hls;t.on(T.BUFFER_APPENDED,this.onBufferAppended,this),t.on(T.FRAG_BUFFERED,this.onFragBuffered,this),t.on(T.FRAG_LOADED,this.onFragLoaded,this)},e._unregisterListeners=function(){var t=this.hls;t.off(T.BUFFER_APPENDED,this.onBufferAppended,this),t.off(T.FRAG_BUFFERED,this.onFragBuffered,this),t.off(T.FRAG_LOADED,this.onFragLoaded,this)},e.destroy=function(){this._unregisterListeners(),this.fragments=this.activePartLists=this.endListFragments=this.timeRanges=null},e.getAppendedFrag=function(t,e){var r=this.activePartLists[e];if(r)for(var i=r.length;i--;){var n=r[i];if(!n)break;var a=n.end;if(n.start<=t&&null!==a&&t<=a)return n}return this.getBufferedFrag(t,e)},e.getBufferedFrag=function(t,e){for(var r=this.fragments,i=Object.keys(r),n=i.length;n--;){var a=r[i[n]];if((null==a?void 0:a.body.type)===e&&a.buffered){a=a.body;if(a.start<=t&&t<=a.end)return a}}return null},e.detectEvictedFragments=function(t,e,r,i){var n=this,a=(this.timeRanges&&(this.timeRanges[t]=e),(null==i?void 0:i.fragment.sn)||-1);Object.keys(this.fragments).forEach(function(i){var s=n.fragments[i];!s||a>=s.body.sn||(s.buffered||s.loaded?(i=s.range[t])&&i.time.some(function(t){t=!n.isTimeBuffered(t.startPTS,t.endPTS,e);return t&&n.removeFragment(s.body),t}):s.body.type===r&&n.removeFragment(s.body))})},e.detectPartialFragments=function(t){var s,o,e=this,r=this.timeRanges,i=t.frag,n=t.part;r&&"initSegment"!==i.sn&&(t=rr(i),s=this.fragments[t])&&(o=!i.relurl,Object.keys(r).forEach(function(t){var l,a=i.elementaryStreams[t];a&&(l=r[t],a=o||!0===a.partial,s.range[t]=e.getBufferedTimes(i,n,a,l))}),s.loaded=null,Object.keys(s.range).length?(s.buffered=!0,s.body.endList&&(this.endListFragments[s.body.type]=s),er(s)||this.removeParts(i.sn-1,i.type)):this.removeFragment(s.body))},e.removeParts=function(t,e){var r=this.activePartLists[e];r&&(this.activePartLists[e]=r.filter(function(e){return e.fragment.sn>=t}))},e.fragBuffered=function(t,e){var r=rr(t),i=this.fragments[r];!i&&e&&(i=this.fragments[r]={body:t,appendedPTS:null,loaded:null,buffered:!1,range:Object.create(null)},t.gap)&&(this.hasGaps=!0),i&&(i.loaded=null,i.buffered=!0)},e.getBufferedTimes=function(t,e,r,i){for(var n={time:[],partial:r},a=t.start,s=t.end,o=t.minEndPTS||s,l=t.maxStartPTS||a,u=0;u<i.length;u++){var h=i.start(u)-this.bufferPadding,d=i.end(u)+this.bufferPadding;if(h<=l&&o<=d){n.time.push({startPTS:Math.max(a,i.start(u)),endPTS:Math.min(s,i.end(u))});break}if(a<d&&h<s)n.partial=!0,n.time.push({startPTS:Math.max(a,i.start(u)),endPTS:Math.min(s,i.end(u))});else if(s<=h)break}return n},e.getPartialFragment=function(t){var r,i,n=null,a=0,s=this.bufferPadding,o=this.fragments;return Object.keys(o).forEach(function(l){l=o[l];l&&er(l)&&(r=l.body.start-s,i=l.body.end+s,r<=t)&&t<=i&&(r=Math.min(t-r,i-t),a<=r)&&(n=l.body,a=r)}),n},e.isEndListAppended=function(t){t=this.endListFragments[t];return void 0!==t&&(t.buffered||er(t))},e.getState=function(t){t=rr(t),t=this.fragments[t];return t?t.buffered?er(t)?$e:Je:Ze:Qe},e.isTimeBuffered=function(t,e,r){for(var i,n,a=0;a<r.length;a++){if(i=r.start(a)-this.bufferPadding,n=r.end(a)+this.bufferPadding,i<=t&&e<=n)return!0;if(e<=i)return!1}return!1},e.onFragLoaded=function(t,e){var a,r=e.frag,i=e.part;"initSegment"===r.sn||r.bitrateTest||(a=rr(r),this.fragments[a]={body:r,appendedPTS:null,loaded:i?null:e,buffered:!1,range:Object.create(null)})},e.onBufferAppended=function(t,e){var s,r=this,i=e.frag,n=e.part,a=e.timeRanges;"initSegment"!==i.sn&&(s=i.type,n&&((e=this.activePartLists[s])||(this.activePartLists[s]=e=[]),e.push(n)),this.timeRanges=a,Object.keys(a).forEach(function(t){var e=a[t];r.detectEvictedFragments(t,e,s,n)}))},e.onFragBuffered=function(t,e){this.detectPartialFragments(e)},e.hasFragment=function(t){t=rr(t);return!!this.fragments[t]},e.hasParts=function(t){return!(null==(t=this.activePartLists[t])||!t.length)},e.removeFragmentsInRange=function(t,e,r,i,n){var a=this;i&&!this.hasGaps||Object.keys(this.fragments).forEach(function(s){var l,s=a.fragments[s];!s||(l=s.body).type!==r||i&&!l.gap||l.start<e&&l.end>t&&(s.buffered||n)&&a.removeFragment(l)})},e.removeFragment=function(t){var i,e=rr(t),r=(t.stats.loaded=0,t.clearElementaryStreamInfo(),this.activePartLists[t.type]);r&&(i=t.sn,this.activePartLists[t.type]=r.filter(function(t){return t.fragment.sn!==i})),delete this.fragments[e],t.endList&&delete this.endListFragments[t.type]},e.removeAllFragments=function(){this.fragments=Object.create(null),this.endListFragments=Object.create(null),this.activePartLists=Object.create(null),this.hasGaps=!1},t}();function er(t){var e;return t.buffered&&(t.body.gap||(null==(e=t.range.video)?void 0:e.partial)||(null==(e=t.range.audio)?void 0:e.partial)||(null==(e=t.range.audiovideo)?void 0:e.partial))}function rr(t){return t.type+"_"+t.level+"_"+t.urlId+"_"+t.sn}var ir=Math.pow(2,17),nr=function(){function t(t){this.config=void 0,this.loader=null,this.partLoadTimeout=-1,this.config=t}var r=t.prototype;return r.destroy=function(){this.loader&&(this.loader.destroy(),this.loader=null)},r.abort=function(){this.loader&&this.loader.abort()},r.load=function(t,r){var i=this,n=t.url;if(!n)return Promise.reject(new or({type:E.NETWORK_ERROR,details:S.FRAG_LOAD_ERROR,fatal:!1,frag:t,error:new Error("Fragment does not have a "+(n?"part list":"url")),networkDetails:null}));this.abort();var a=this.config,s=a.fLoader,o=a.loader;return new Promise(function(l,u){var h,d,c,f;i.loader&&i.loader.destroy(),t.gap?u(sr(t)):(h=i.loader=t.loader=new(s||o)(a),d=ar(t),c=Ue(a.fragLoadPolicy.default),f={loadPolicy:c,timeout:c.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0,highWaterMark:"initSegment"===t.sn?1/0:ir},t.stats=h.stats,h.load(d,f,{onSuccess:function(e,r,n,a){i.resetLoader(t,h);e=e.data;n.resetIV&&t.decryptdata&&(t.decryptdata.iv=new Uint8Array(e.slice(0,16)),e=e.slice(16)),l({frag:t,part:null,payload:e,networkDetails:a})},onError:function(r,a,s,o){i.resetLoader(t,h),u(new or({type:E.NETWORK_ERROR,details:S.FRAG_LOAD_ERROR,fatal:!1,frag:t,response:e({url:n,data:void 0},r),error:new Error("HTTP Error "+r.code+" "+r.text),networkDetails:s,stats:o}))},onAbort:function(e,r,n){i.resetLoader(t,h),u(new or({type:E.NETWORK_ERROR,details:S.INTERNAL_ABORTED,fatal:!1,frag:t,error:new Error("Aborted"),networkDetails:n,stats:e}))},onTimeout:function(e,r,n){i.resetLoader(t,h),u(new or({type:E.NETWORK_ERROR,details:S.FRAG_LOAD_TIMEOUT,fatal:!1,frag:t,error:new Error("Timeout after "+f.timeout+"ms"),networkDetails:n,stats:e}))},onProgress:function(e,i,n,a){r&&r({frag:t,part:null,payload:n,networkDetails:a})}}))})},r.loadPart=function(t,r,i){var n=this,a=(this.abort(),this.config),s=a.fLoader,o=a.loader;return new Promise(function(l,u){var h,d,c,f;n.loader&&n.loader.destroy(),t.gap||r.gap?u(sr(t,r)):(h=n.loader=t.loader=new(s||o)(a),d=ar(t,r),c=Ue(a.fragLoadPolicy.default),f={loadPolicy:c,timeout:c.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0,highWaterMark:ir},r.stats=h.stats,h.load(d,f,{onSuccess:function(e,a,s,o){n.resetLoader(t,h),n.updateStatsFromPart(t,r);e={frag:t,part:r,payload:e.data,networkDetails:o};i(e),l(e)},onError:function(i,a,s,o){n.resetLoader(t,h),u(new or({type:E.NETWORK_ERROR,details:S.FRAG_LOAD_ERROR,fatal:!1,frag:t,part:r,response:e({url:d.url,data:void 0},i),error:new Error("HTTP Error "+i.code+" "+i.text),networkDetails:s,stats:o}))},onAbort:function(e,i,a){t.stats.aborted=r.stats.aborted,n.resetLoader(t,h),u(new or({type:E.NETWORK_ERROR,details:S.INTERNAL_ABORTED,fatal:!1,frag:t,part:r,error:new Error("Aborted"),networkDetails:a,stats:e}))},onTimeout:function(e,i,a){n.resetLoader(t,h),u(new or({type:E.NETWORK_ERROR,details:S.FRAG_LOAD_TIMEOUT,fatal:!1,frag:t,part:r,error:new Error("Timeout after "+f.timeout+"ms"),networkDetails:a,stats:e}))}}))})},r.updateStatsFromPart=function(t,e){var r=t.stats,i=e.stats,n=i.total,t=(r.loaded+=i.loaded,n?(n=((t=Math.round(t.duration/e.duration))-(e=Math.min(Math.round(r.loaded/n),t)))*Math.round(r.loaded/e),r.total=r.loaded+n):r.total=Math.max(r.loaded,r.total),r.loading),e=i.loading;t.start?t.first+=e.first-e.start:(t.start=e.start,t.first=e.first),t.end=e.end},r.resetLoader=function(t,e){t.loader=null,this.loader===e&&(self.clearTimeout(this.partLoadTimeout),this.loader=null),e.destroy()},t}();function ar(t,e){var o,l,r=(e=void 0===e?null:e)||t,e={frag:t,part:e,responseType:"arraybuffer",url:r.url,headers:{},rangeStart:0,rangeEnd:0},n=r.byteRangeStartOffset,r=r.byteRangeEndOffset;return y(n)&&y(r)&&(o=n,l=r,"initSegment"===t.sn&&"AES-128"===(null==(t=t.decryptdata)?void 0:t.method)&&((t=r-n)%16&&(l=r+(16-t%16)),0!==n)&&(e.resetIV=!0,o=n-16),e.rangeStart=o,e.rangeEnd=l),e}function sr(t,e){var r=new Error("GAP "+(t.gap?"tag":"attribute")+" found"),r={type:E.MEDIA_ERROR,details:S.FRAG_GAP,fatal:!1,frag:t,error:r,networkDetails:null};return e&&(r.part=e),(e||t).stats.aborted=!0,new or(r)}function cr(t,e,r,i,n,a){void 0===i&&(i=0),void 0===n&&(n=-1),void 0===a&&(a=!1),this.level=void 0,this.sn=void 0,this.part=void 0,this.id=void 0,this.size=void 0,this.partial=void 0,this.transmuxing={start:0,executeStart:0,executeEnd:0,end:0},this.buffering={audio:{start:0,executeStart:0,executeEnd:0,end:0},video:{start:0,executeStart:0,executeEnd:0,end:0},audiovideo:{start:0,executeStart:0,executeEnd:0,end:0}},this.level=t,this.sn=e,this.id=r,this.size=i,this.part=n,this.partial=a}var or=function(t){function e(e){var r;return(r=t.call(this,e.error.message)||this).data=void 0,r.data=e,r}return l(e,t),e}(c(Error)),lr=function(){function t(t){this.config=void 0,this.keyUriToKeyInfo={},this.emeController=null,this.config=t}var r=t.prototype;return r.abort=function(t){for(var e in this.keyUriToKeyInfo){e=this.keyUriToKeyInfo[e].loader;if(e){if(t&&t!==e.context.frag.type)return;e.abort()}}},r.detach=function(){for(var t in this.keyUriToKeyInfo){var e=this.keyUriToKeyInfo[t];(e.mediaKeySessionContext||e.decryptdata.isCommonEncryption)&&delete this.keyUriToKeyInfo[t]}},r.destroy=function(){for(var t in this.detach(),this.keyUriToKeyInfo){t=this.keyUriToKeyInfo[t].loader;t&&t.destroy()}this.keyUriToKeyInfo={}},r.createKeyLoadError=function(t,e,r,i,n){return void 0===e&&(e=S.KEY_LOAD_ERROR),new or({type:E.NETWORK_ERROR,details:e,fatal:!1,frag:t,response:n,error:r,networkDetails:i})},r.loadClear=function(t,e){var r=this;if(this.emeController&&this.config.emeEnabled)for(var i=t.sn,n=t.cc,s=0;s<e.length&&"break"!==function(){var t=e[s];if(n<=t.cc&&("initSegment"===i||"initSegment"===t.sn||i<t.sn))return r.emeController.selectKeySystemFormat(t).then(function(e){t.setKeyFormat(e)}),"break"}();s++);},r.load=function(t){var e=this;return!t.decryptdata&&t.encrypted&&this.emeController?this.emeController.selectKeySystemFormat(t).then(function(r){return e.loadInternal(t,r)}):this.loadInternal(t)},r.loadInternal=function(t,e){e&&t.setKeyFormat(e);var n=t.decryptdata;if(!n)return e=new Error(e?"Expected frag.decryptdata to be defined after setting format "+e:"Missing decryption data on fragment in onKeyLoading"),Promise.reject(this.createKeyLoadError(t,S.KEY_LOAD_ERROR,e));e=n.uri;if(!e)return Promise.reject(this.createKeyLoadError(t,S.KEY_LOAD_ERROR,new Error('Invalid key URI: "'+e+'"')));var o,l=this.keyUriToKeyInfo[e];if(null!=l&&l.decryptdata.key)return n.key=l.decryptdata.key,Promise.resolve({frag:t,keyInfo:l});if(null!=l&&l.keyLoadPromise)switch(null==(o=l.mediaKeySessionContext)?void 0:o.keyStatus){case void 0:case"status-pending":case"usable":case"usable-in-future":return l.keyLoadPromise.then(function(e){return n.key=e.keyInfo.decryptdata.key,{frag:t,keyInfo:l}})}switch(l=this.keyUriToKeyInfo[e]={decryptdata:n,keyLoadPromise:null,loader:null,mediaKeySessionContext:null},n.method){case"ISO-23001-7":case"SAMPLE-AES":case"SAMPLE-AES-CENC":case"SAMPLE-AES-CTR":return"identity"===n.keyFormat?this.loadKeyHTTP(l,t):this.loadKeyEME(l,t);case"AES-128":return this.loadKeyHTTP(l,t);default:return Promise.reject(this.createKeyLoadError(t,S.KEY_LOAD_ERROR,new Error('Key supplied with unsupported METHOD: "'+n.method+'"')))}},r.loadKeyEME=function(t,e){var r={frag:e,keyInfo:t};if(this.emeController&&this.config.emeEnabled){e=this.emeController.loadKey(r);if(e)return(t.keyLoadPromise=e.then(function(e){return t.mediaKeySessionContext=e,r})).catch(function(e){throw t.keyLoadPromise=null,e})}return Promise.resolve(r)},r.loadKeyHTTP=function(t,r){var i=this,n=this.config,a=new n.loader(n);return r.keyLoader=t.loader=a,t.keyLoadPromise=new Promise(function(s,o){var l={keyInfo:t,frag:r,responseType:"arraybuffer",url:t.decryptdata.uri},u=n.keyLoadPolicy.default,u={loadPolicy:u,timeout:u.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0};a.load(l,u,{onSuccess:function(t,e,r,n){var a=r.frag,l=r.keyInfo,r=r.url;if(!a.decryptdata||l!==i.keyUriToKeyInfo[r])return o(i.createKeyLoadError(a,S.KEY_LOAD_ERROR,new Error("after key load, decryptdata unset or changed"),n));l.decryptdata.key=a.decryptdata.key=new Uint8Array(t.data),a.keyLoader=null,l.loader=null,s({frag:a,keyInfo:l})},onError:function(t,n,a,s){i.resetLoader(n),o(i.createKeyLoadError(r,S.KEY_LOAD_ERROR,new Error("HTTP Error "+t.code+" loading key "+t.text),a,e({url:l.url,data:void 0},t)))},onTimeout:function(t,e,n){i.resetLoader(e),o(i.createKeyLoadError(r,S.KEY_LOAD_TIMEOUT,new Error("key loading timed out"),n))},onAbort:function(t,e,n){i.resetLoader(e),o(i.createKeyLoadError(r,S.INTERNAL_ABORTED,new Error("key loading aborted"),n))}})})},r.resetLoader=function(t){var e=t.frag,r=t.keyInfo,t=t.url,n=r.loader;e.keyLoader===n&&(e.keyLoader=null,r.loader=null),delete this.keyUriToKeyInfo[t],n&&n.destroy()},t}(),ur=function(){function t(){this._boundTick=void 0,this._tickTimer=null,this._tickInterval=null,this._tickCallCount=0,this._boundTick=this.tick.bind(this)}var e=t.prototype;return e.destroy=function(){this.onHandlerDestroying(),this.onHandlerDestroyed()},e.onHandlerDestroying=function(){this.clearNextTick(),this.clearInterval()},e.onHandlerDestroyed=function(){},e.hasInterval=function(){return!!this._tickInterval},e.hasNextTick=function(){return!!this._tickTimer},e.setInterval=function(t){return!this._tickInterval&&(this._tickCallCount=0,this._tickInterval=self.setInterval(this._boundTick,t),!0)},e.clearInterval=function(){return!!this._tickInterval&&(self.clearInterval(this._tickInterval),!(this._tickInterval=null))},e.clearNextTick=function(){return!!this._tickTimer&&(self.clearTimeout(this._tickTimer),!(this._tickTimer=null))},e.tick=function(){this._tickCallCount++,1===this._tickCallCount&&(this.doTick(),1<this._tickCallCount&&this.tickImmediate(),this._tickCallCount=0)},e.tickImmediate=function(){this.clearNextTick(),this._tickTimer=self.setTimeout(this._boundTick,0)},e.doTick=function(){},t}(),hr={length:0,start:function(){return 0},end:function(){return 0}},dr=function(){function t(){}return t.isBuffered=function(e,r){try{if(e)for(var i=t.getBuffered(e),n=0;n<i.length;n++)if(r>=i.start(n)&&r<=i.end(n))return!0}catch(t){}return!1},t.bufferInfo=function(e,r,i){try{if(e){for(var a=t.getBuffered(e),s=[],n=0;n<a.length;n++)s.push({start:a.start(n),end:a.end(n)});return this.bufferedInfo(s,r,i)}}catch(t){}return{len:0,start:r,end:r,nextStart:void 0}},t.bufferedInfo=function(t,e,r){e=Math.max(0,e),t.sort(function(t,e){return t.start-e.start||e.end-t.end});var i=[];if(r)for(var n=0;n<t.length;n++){var s,a=i.length;a&&(s=i[a-1].end,t[n].start-s<r)?t[n].end>s&&(i[a-1].end=t[n].end):i.push(t[n])}else i=t;for(var o,l=0,u=e,h=e,d=0;d<i.length;d++){var c=i[d].start,f=i[d].end;if(c<=e+r&&e<f)u=c,l=(h=f)-e;else if(e+r<c){o=c;break}}return{len:l,start:u||0,end:h||0,nextStart:o}},t.getBuffered=function(t){try{return t.buffered}catch(t){return b.log("failed to get media.buffered",t),hr}},t}();function fr(t,e){for(var r=null,i=0,n=t.length;i<n;i++){var a=t[i];if(a&&a.cc===e){r=a;break}}return r}function gr(t,e){t&&(e=t.start+e,t.start=t.startPTS=e,t.endPTS=e+t.duration)}function vr(t,e){for(var r=e.fragments,i=0,n=r.length;i<n;i++)gr(r[i],t);e.fragmentHint&&gr(e.fragmentHint,t),e.alignedSliding=!0}function mr(t,e){var r,s,o;t.hasProgramDateTime&&e.hasProgramDateTime&&(r=t.fragments,e=e.fragments,r.length)&&e.length&&(r=fr(r,(e=e[Math.round(e.length/2)-1]).cc)||r[Math.round(r.length/2)-1],s=e.programDateTime,o=r.programDateTime,null!==s)&&null!==o&&vr((o-s)/1e3-(r.start-e.start),t)}var pr=function(){function t(t,e){this.subtle=void 0,this.aesIV=void 0,this.subtle=t,this.aesIV=e}return t.prototype.decrypt=function(t,e){return this.subtle.decrypt({name:"AES-CBC",iv:this.aesIV},e,t)},t}(),yr=function(){function t(t,e){this.subtle=void 0,this.key=void 0,this.subtle=t,this.key=e}return t.prototype.expandKey=function(){return this.subtle.importKey("raw",this.key,{name:"AES-CBC"},!1,["encrypt","decrypt"])},t}(),Tr=function(){function t(){this.rcon=[0,1,2,4,8,16,32,64,128,27,54],this.subMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.invSubMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.sBox=new Uint32Array(256),this.invSBox=new Uint32Array(256),this.key=new Uint32Array(0),this.ksRows=0,this.keySize=0,this.keySchedule=void 0,this.invKeySchedule=void 0,this.initTable()}var e=t.prototype;return e.uint8ArrayToUint32Array_=function(t){for(var e=new DataView(t),r=new Uint32Array(4),i=0;i<4;i++)r[i]=e.getUint32(4*i);return r},e.initTable=function(){for(var t=this.sBox,e=this.invSBox,r=this.subMix,i=r[0],n=r[1],a=r[2],s=r[3],r=this.invSubMix,l=r[0],u=r[1],h=r[2],d=r[3],c=new Uint32Array(256),f=0,g=0,v=0,v=0;v<256;v++)c[v]=v<128?v<<1:v<<1^283;for(v=0;v<256;v++){var m=g^g<<1^g<<2^g<<3^g<<4,p=(t[f]=m=m>>>8^255&m^99,c[e[m]=f]),y=c[p],T=c[y],E=257*c[m]^16843008*m;i[f]=E<<24|E>>>8,n[f]=E<<16|E>>>16,a[f]=E<<8|E>>>24,s[f]=E,l[m]=(E=16843009*T^65537*y^257*p^16843008*f)<<24|E>>>8,u[m]=E<<16|E>>>16,h[m]=E<<8|E>>>24,d[m]=E,f?(f=p^c[c[c[T^p]]],g^=c[c[g]]):f=g=1}},e.expandKey=function(t){for(var e=this.uint8ArrayToUint32Array_(t),r=!0,i=0;i<e.length&&r;)r=e[i]===this.key[i],i++;if(!r){this.key=e;var n=this.keySize=e.length;if(4!==n&&6!==n&&8!==n)throw new Error("Invalid aes key size="+n);for(var s,o,l,u=this.ksRows=4*(n+6+1),h=this.keySchedule=new Uint32Array(u),d=this.invKeySchedule=new Uint32Array(u),c=this.sBox,f=this.rcon,t=this.invSubMix,v=t[0],m=t[1],p=t[2],y=t[3],a=0;a<u;a++)a<n?o=h[a]=e[a]:(l=o,a%n==0?(l=c[(l=l<<8|l>>>24)>>>24]<<24|c[l>>>16&255]<<16|c[l>>>8&255]<<8|c[255&l],l^=f[a/n|0]<<24):6<n&&a%n==4&&(l=c[l>>>24]<<24|c[l>>>16&255]<<16|c[l>>>8&255]<<8|c[255&l]),h[a]=o=(h[a-n]^l)>>>0);for(s=0;s<u;s++)a=u-s,l=3&s?h[a]:h[a-4],d[s]=s<4||a<=4?l:v[c[l>>>24]]^m[c[l>>>16&255]]^p[c[l>>>8&255]]^y[c[255&l]],d[s]=d[s]>>>0}},e.networkToHostOrderSwap=function(t){return t<<24|(65280&t)<<8|(16711680&t)>>8|t>>>24},e.decrypt=function(t,e,r){for(var i,n,a,s,o,l,u,h,d,c,f,g,v,m,p=this.keySize+6,y=this.invKeySchedule,T=this.invSBox,E=this.invSubMix,S=E[0],L=E[1],R=E[2],k=E[3],E=this.uint8ArrayToUint32Array_(r),b=E[0],D=E[1],I=E[2],_=E[3],w=new Int32Array(t),C=new Int32Array(w.length),x=this.networkToHostOrderSwap;e<w.length;){for(d=x(w[e]),c=x(w[e+1]),f=x(w[e+2]),g=x(w[e+3]),o=d^y[0],l=g^y[1],u=f^y[2],h=c^y[3],v=4,m=1;m<p;m++)i=S[o>>>24]^L[l>>16&255]^R[u>>8&255]^k[255&h]^y[v],n=S[l>>>24]^L[u>>16&255]^R[h>>8&255]^k[255&o]^y[v+1],a=S[u>>>24]^L[h>>16&255]^R[o>>8&255]^k[255&l]^y[v+2],s=S[h>>>24]^L[o>>16&255]^R[l>>8&255]^k[255&u]^y[v+3],o=i,l=n,u=a,h=s,v+=4;i=T[o>>>24]<<24^T[l>>16&255]<<16^T[u>>8&255]<<8^T[255&h]^y[v],n=T[l>>>24]<<24^T[u>>16&255]<<16^T[h>>8&255]<<8^T[255&o]^y[v+1],a=T[u>>>24]<<24^T[h>>16&255]<<16^T[o>>8&255]<<8^T[255&l]^y[v+2],s=T[h>>>24]<<24^T[o>>16&255]<<16^T[l>>8&255]<<8^T[255&u]^y[v+3],C[e]=x(i^b),C[e+1]=x(s^D),C[e+2]=x(a^I),C[e+3]=x(n^_),b=d,D=c,I=f,_=g,e+=4}return C.buffer},t}(),Er=function(){function t(t,e){e=(void 0===e?{}:e).removePKCS7Padding,e=void 0===e||e;if(this.logEnabled=!0,this.removePKCS7Padding=void 0,this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null,this.useSoftware=void 0,this.useSoftware=t.enableSoftwareAES,this.removePKCS7Padding=e)try{var n=self.crypto;n&&(this.subtle=n.subtle||n.webkitSubtle)}catch(t){}null===this.subtle&&(this.useSoftware=!0)}var e=t.prototype;return e.destroy=function(){this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null},e.isSync=function(){return this.useSoftware},e.flush=function(){var i,n,t=this.currentResult,e=this.remainderData;return!t||e?(this.reset(),null):(e=new Uint8Array(t),this.reset(),this.removePKCS7Padding?(n=(i=(t=e).byteLength)&&new DataView(t.buffer).getUint8(i-1))?$(t,0,i-n):t:e)},e.reset=function(){this.currentResult=null,this.currentIV=null,this.remainderData=null,this.softwareDecrypter&&(this.softwareDecrypter=null)},e.decrypt=function(t,e,r){var i=this;return this.useSoftware?new Promise(function(n,a){i.softwareDecrypt(new Uint8Array(t),e,r);var s=i.flush();s?n(s.buffer):a(new Error("[softwareDecrypt] Failed to decrypt data"))}):this.webCryptoDecrypt(new Uint8Array(t),e,r)},e.softwareDecrypt=function(t,e,r){var i=this.currentIV,n=this.currentResult,a=this.remainderData,a=(this.logOnce("JS AES decrypt"),a&&(t=It(a,t),this.remainderData=null),this.getValidChunk(t));if(!a.length)return null;i&&(r=i);t=this.softwareDecrypter,(t=t||(this.softwareDecrypter=new Tr)).expandKey(e),i=n;return this.currentResult=t.decrypt(a.buffer,0,r),this.currentIV=$(a,-16).buffer,i||null},e.webCryptoDecrypt=function(t,e,r){var i=this,n=this.subtle;return this.key===e&&this.fastAesKey||(this.key=e,this.fastAesKey=new yr(n,e)),this.fastAesKey.expandKey().then(function(e){return n?(i.logOnce("WebCrypto AES decrypt"),new pr(n,new Uint8Array(r)).decrypt(t.buffer,e)):Promise.reject(new Error("web crypto not initialized"))}).catch(function(n){return b.warn("[decrypter]: WebCrypto Error, disable WebCrypto API, "+n.name+": "+n.message),i.onWebCryptoError(t,e,r)})},e.onWebCryptoError=function(t,e,r){this.useSoftware=!0,this.logEnabled=!0,this.softwareDecrypt(t,e,r);t=this.flush();if(t)return t.buffer;throw new Error("WebCrypto and softwareDecrypt: failed to decrypt data")},e.getValidChunk=function(t){var e=t,r=t.length-t.length%16;return r!==t.length&&(e=$(t,0,r),this.remainderData=$(t,r)),e},e.logOnce=function(t){this.logEnabled&&(b.log("[decrypter]: "+t),this.logEnabled=!1)},t}(),Sr="STOPPED",Lr="IDLE",Rr="KEY_LOADING",kr="FRAG_LOADING",Ar="FRAG_LOADING_WAITING_RETRY",br="WAITING_TRACK",Dr="PARSING",Ir="PARSED",_r="ENDED",wr="ERROR",Cr="WAITING_INIT_PTS",xr="WAITING_LEVEL",ur=function(t){function e(e,r,i,n,a){var s;return(s=t.call(this)||this).hls=void 0,s.fragPrevious=null,s.fragCurrent=null,s.fragmentTracker=void 0,s.transmuxer=null,s._state=Sr,s.playlistType=void 0,s.media=null,s.mediaBuffer=null,s.config=void 0,s.bitrateTest=!1,s.lastCurrentTime=0,s.nextLoadPosition=0,s.startPosition=0,s.startTimeOffset=null,s.loadedmetadata=!1,s.retryDate=0,s.levels=null,s.fragmentLoader=void 0,s.keyLoader=void 0,s.levelLastLoaded=null,s.startFragRequested=!1,s.decrypter=void 0,s.initPTS=[],s.onvseeking=null,s.onvended=null,s.logPrefix="",s.log=void 0,s.warn=void 0,s.playlistType=a,s.logPrefix=n,s.log=b.log.bind(b,n+":"),s.warn=b.warn.bind(b,n+":"),s.hls=e,s.fragmentLoader=new nr(e.config),s.keyLoader=i,s.fragmentTracker=r,s.config=e.config,s.decrypter=new Er(e.config),e.on(T.MANIFEST_LOADED,s.onManifestLoaded,function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(s)),s}l(e,t);var r=e.prototype;return r.doTick=function(){this.onTickEnd()},r.onTickEnd=function(){},r.startLoad=function(t){},r.stopLoad=function(){this.fragmentLoader.abort(),this.keyLoader.abort(this.playlistType);var t=this.fragCurrent;null!=t&&t.loader&&(t.abortRequests(),this.fragmentTracker.removeFragment(t)),this.resetTransmuxer(),this.fragCurrent=null,this.fragPrevious=null,this.clearInterval(),this.clearNextTick(),this.state=Sr},r._streamEnded=function(t,e){return!(e.live||t.nextStart||!t.end||!this.media)&&(null!=(t=e.partList)&&t.length?(t=t[t.length-1],dr.isBuffered(this.media,t.start+t.duration/2)):(t=e.fragments[e.fragments.length-1].type,this.fragmentTracker.isEndListAppended(t)))},r.getLevelDetails=function(){var t;return!this.levels||null===this.levelLastLoaded||null==(t=this.levels[this.levelLastLoaded])?void 0:t.details},r.onMediaAttached=function(t,e){e=this.media=this.mediaBuffer=e.media,this.onvseeking=this.onMediaSeeking.bind(this),this.onvended=this.onMediaEnded.bind(this),e.addEventListener("seeking",this.onvseeking),e.addEventListener("ended",this.onvended),e=this.config;this.levels&&e.autoStartLoad&&this.state===Sr&&this.startLoad(e.startPosition)},r.onMediaDetaching=function(){var t=this.media;null!=t&&t.ended&&(this.log("MSE detaching and video ended, reset startPosition"),this.startPosition=this.lastCurrentTime=0),t&&this.onvseeking&&this.onvended&&(t.removeEventListener("seeking",this.onvseeking),t.removeEventListener("ended",this.onvended),this.onvseeking=this.onvended=null),this.keyLoader&&this.keyLoader.detach(),this.media=this.mediaBuffer=null,this.loadedmetadata=!1,this.fragmentTracker.removeAllFragments(),this.stopLoad()},r.onMediaSeeking=function(){var t=this.config,e=this.fragCurrent,r=this.media,i=this.mediaBuffer,n=this.state,a=r?r.currentTime:0,i=dr.bufferInfo(i||r,a,t.maxBufferHole);this.log("media seeking to "+(y(a)?a.toFixed(3):a)+", state: "+n),this.state===_r?this.resetLoadingState():e&&(n=t.maxFragLookUpTolerance,t=e.start-n,n=e.start+e.duration+n,!i.len||n<i.start||t>i.end)&&(n=n<a,a<t||n)&&(n&&e.loader&&(this.log("seeking outside of buffer while fragment load in progress, cancel fragment load"),e.abortRequests(),this.resetLoadingState()),this.fragPrevious=null),r&&(this.fragmentTracker.removeFragmentsInRange(a,1/0,this.playlistType,!0),this.lastCurrentTime=a),this.loadedmetadata||i.len||(this.nextLoadPosition=this.startPosition=a),this.tickImmediate()},r.onMediaEnded=function(){this.startPosition=this.lastCurrentTime=0},r.onManifestLoaded=function(t,e){this.startTimeOffset=e.startTimeOffset,this.initPTS=[]},r.onHandlerDestroying=function(){this.stopLoad(),t.prototype.onHandlerDestroying.call(this)},r.onHandlerDestroyed=function(){this.state=Sr,this.fragmentLoader&&this.fragmentLoader.destroy(),this.keyLoader&&this.keyLoader.destroy(),this.decrypter&&this.decrypter.destroy(),this.hls=this.log=this.warn=this.decrypter=this.keyLoader=this.fragmentLoader=this.fragmentTracker=null,t.prototype.onHandlerDestroyed.call(this)},r.loadFragment=function(t,e,r){this._loadFragForPlayback(t,e,r)},r._loadFragForPlayback=function(t,e,r){var i=this;this._doFragLoad(t,e,r,function(e){i.fragContextChanged(t)?(i.warn("Fragment "+t.sn+(e.part?" p: "+e.part.index:"")+" of level "+t.level+" was dropped during download."),i.fragmentTracker.removeFragment(t)):(t.stats.chunkCount++,i._handleFragmentLoadProgress(e))}).then(function(e){var r;e&&(r=i.state,i.fragContextChanged(t)?r!==kr&&(i.fragCurrent||r!==Dr)||(i.fragmentTracker.removeFragment(t),i.state=Lr):("payload"in e&&(i.log("Loaded fragment "+t.sn+" of level "+t.level),i.hls.trigger(T.FRAG_LOADED,e)),i._handleFragmentLoadComplete(e)))}).catch(function(e){i.state!==Sr&&i.state!==wr&&(i.warn(e),i.resetFragmentLoading(t))})},r.clearTrackerIfNeeded=function(t){var i,r=this.fragmentTracker;r.getState(t)===Ze?(i=t.type,i=this.getFwdBufferInfo(this.mediaBuffer,i),i=Math.max(t.duration,i?i.len:this.config.maxBufferLength),this.reduceMaxBufferLength(i)&&r.removeFragment(t)):0===(null==(i=this.mediaBuffer)?void 0:i.buffered.length)?r.removeAllFragments():r.hasParts(t.type)&&(r.detectPartialFragments({frag:t,part:null,stats:t.stats,id:t.type}),r.getState(t)===$e)&&r.removeFragment(t)},r.flushMainBuffer=function(t,e,r){void 0===r&&(r=null),t-e&&this.hls.trigger(T.BUFFER_FLUSHING,{startOffset:t,endOffset:e,type:r})},r._loadInitSegment=function(t,e){var r=this;this._doFragLoad(t,e).then(function(e){if(e&&!r.fragContextChanged(t)&&r.levels)return e;throw new Error("init load aborted")}).then(function(e){var s,i=r.hls,n=e.payload,a=t.decryptdata;return n&&0<n.byteLength&&a&&a.key&&a.iv&&"AES-128"===a.method?(s=self.performance.now(),r.decrypter.decrypt(new Uint8Array(n),a.key.buffer,a.iv.buffer).catch(function(e){throw i.trigger(T.ERROR,{type:E.MEDIA_ERROR,details:S.FRAG_DECRYPT_ERROR,fatal:!1,error:e,reason:e.message,frag:t}),e}).then(function(r){var n=self.performance.now();return i.trigger(T.FRAG_DECRYPTED,{frag:t,payload:r,stats:{tstart:s,tdecrypt:n}}),e.payload=r,e})):e}).then(function(i){var n=r.fragCurrent,a=r.hls;if(!r.levels)throw new Error("init load aborted, missing levels");var s=t.stats;r.state=Lr,e.fragmentError=0,t.data=new Uint8Array(i.payload),s.parsing.start=s.buffering.start=self.performance.now(),s.parsing.end=s.buffering.end=self.performance.now(),i.frag===n&&a.trigger(T.FRAG_BUFFERED,{stats:s,frag:n,part:null,id:t.type}),r.tick()}).catch(function(e){r.state!==Sr&&r.state!==wr&&(r.warn(e),r.resetFragmentLoading(t))})},r.fragContextChanged=function(t){var e=this.fragCurrent;return!t||!e||t.level!==e.level||t.sn!==e.sn||t.urlId!==e.urlId},r.fragBufferedComplete=function(t,e){var s=this.mediaBuffer||this.media;this.log("Buffered "+t.type+" sn: "+t.sn+(e?" part: "+e.index:"")+" of "+(this.playlistType===le?"level":"track")+" "+t.level+" (frag:["+(null!=(e=t.startPTS)?e:NaN).toFixed(3)+"-"+(null!=(e=t.endPTS)?e:NaN).toFixed(3)+"] > buffer:"+(s?function(t){for(var e="",r=t.length,i=0;i<r;i++)e+="["+t.start(i).toFixed(3)+"-"+t.end(i).toFixed(3)+"]";return e}(dr.getBuffered(s)):"(detached)")+")"),this.state=Lr,s&&(!this.loadedmetadata&&t.type==le&&s.buffered.length&&(null==(e=this.fragCurrent)?void 0:e.sn)===(null==(t=this.fragPrevious)?void 0:t.sn)&&(this.loadedmetadata=!0,this.seekToStartPos()),this.tick())},r.seekToStartPos=function(){},r._handleFragmentLoadComplete=function(t){var i,r,e=this.transmuxer;e&&(r=t.frag,i=t.part,t=!(t=t.partsLoaded)||0===t.length||t.some(function(t){return!t}),r=new cr(r.level,r.sn,r.stats.chunkCount+1,0,i?i.index:-1,!t),e.flush(r))},r._handleFragmentLoadProgress=function(t){},r._doFragLoad=function(t,e,r,i){var a=this,s=(void 0===r&&(r=null),null==e?void 0:e.details);if(!this.levels||!s)throw new Error("frag load aborted, missing level"+(s?"":" detail")+"s");var o=null;if(!t.encrypted||null!=(n=t.decryptdata)&&n.key?!t.encrypted&&s.encryptedFragments.length&&this.keyLoader.loadClear(t,s.encryptedFragments):(this.log("Loading key for "+t.sn+" of ["+s.startSN+"-"+s.endSN+"], "+("[stream-controller]"===this.logPrefix?"level":"track")+" "+t.level),this.state=Rr,this.fragCurrent=t,o=this.keyLoader.load(t).then(function(t){if(!a.fragContextChanged(t.frag))return a.hls.trigger(T.KEY_LOADED,t),a.state===Rr&&(a.state=Lr),t}),this.hls.trigger(T.KEY_LOADING,{frag:t}),null===this.fragCurrent&&(o=Promise.reject(new Error("frag load aborted, context changed in KEY_LOADING")))),r=Math.max(t.start,r||0),this.config.lowLatencyMode&&"initSegment"!==t.sn){var n=s.partList;if(n&&i){r>t.end&&s.fragmentHint&&(t=s.fragmentHint);var d,u=this.getNextPart(n,t,r);if(-1<u)return d=n[u],this.log("Loading part sn: "+t.sn+" p: "+d.index+" cc: "+t.cc+" of playlist ["+s.startSN+"-"+s.endSN+"] parts [0-"+u+"-"+(n.length-1)+"] "+("[stream-controller]"===this.logPrefix?"level":"track")+": "+t.level+", target: "+parseFloat(r.toFixed(3))),this.nextLoadPosition=d.start+d.duration,this.state=kr,u=o?o.then(function(r){return!r||a.fragContextChanged(r.frag)?null:a.doFragPartsLoad(t,d,e,i)}).catch(function(t){return a.handleFragLoadError(t)}):this.doFragPartsLoad(t,d,e,i).catch(function(t){return a.handleFragLoadError(t)}),this.hls.trigger(T.FRAG_LOADING,{frag:t,part:d,targetBufferTime:r}),null===this.fragCurrent?Promise.reject(new Error("frag load aborted, context changed in FRAG_LOADING parts")):u;if(!t.url||this.loadedEndOfParts(n,r))return Promise.resolve(null)}}this.log("Loading fragment "+t.sn+" cc: "+t.cc+" "+(s?"of ["+s.startSN+"-"+s.endSN+"] ":"")+("[stream-controller]"===this.logPrefix?"level":"track")+": "+t.level+", target: "+parseFloat(r.toFixed(3))),y(t.sn)&&!this.bitrateTest&&(this.nextLoadPosition=t.start+t.duration),this.state=kr;var f=this.config.progressive,u=f&&o?o.then(function(e){return!e||a.fragContextChanged(null==e?void 0:e.frag)?null:a.fragmentLoader.load(t,i)}).catch(function(t){return a.handleFragLoadError(t)}):Promise.all([this.fragmentLoader.load(t,f?i:void 0),o]).then(function(t){t=t[0];return!f&&t&&i&&i(t),t}).catch(function(t){return a.handleFragLoadError(t)});return this.hls.trigger(T.FRAG_LOADING,{frag:t,targetBufferTime:r}),null===this.fragCurrent?Promise.reject(new Error("frag load aborted, context changed in FRAG_LOADING")):u},r.doFragPartsLoad=function(t,e,r,i){var n=this;return new Promise(function(a,s){var o,l=[],u=null==(o=r.details)?void 0:o.partList;!function e(o){n.fragmentLoader.loadPart(t,o,i).then(function(i){var s=(l[o.index]=i).part,i=(n.hls.trigger(T.FRAG_LOADED,i),Pe(r,t.sn,o.index+1)||Oe(u,t.sn,o.index+1));if(!i)return a({frag:t,part:s,partsLoaded:l});e(i)}).catch(s)}(e)})},r.handleFragLoadError=function(t){var e;return"data"in t?(e=t.data,t.data&&e.details===S.INTERNAL_ABORTED?this.handleFragLoadAborted(e.frag,e.part):this.hls.trigger(T.ERROR,e)):this.hls.trigger(T.ERROR,{type:E.OTHER_ERROR,details:S.INTERNAL_EXCEPTION,err:t,error:t,fatal:!0}),null},r._handleTransmuxerFlush=function(t){var r,i,a,e=this.getCurrentContext(t);e&&this.state===Dr?(r=e.frag,i=e.part,e=e.level,a=self.performance.now(),r.stats.parsing.end=a,i&&(i.stats.parsing.end=a),this.updateLevelTiming(r,i,e,t.partial)):this.fragCurrent||this.state===Sr||this.state===wr||(this.state=Lr)},r.getCurrentContext=function(t){var s,e=this.levels,r=this.fragCurrent,i=t.level,n=t.sn,t=t.part;return null!=e&&e[i]?(s=e[i],(t=(e=-1<t?Pe(s,n,t):null)?e.fragment:function(e,r){var i,n;return null!=s&&s.details?(i=s.details).fragments[e-i.startSN]||((n=i.fragmentHint)&&n.sn===e?n:e<i.startSN&&r&&r.sn===e?r:null):null}(n,r))?(r&&r!==t&&(t.stats=r.stats),{frag:t,part:e,level:s}):null):(this.warn("Levels object was unset while buffering fragment "+n+" of level "+i+". The current chunk will not be buffered."),null)},r.bufferFragmentData=function(t,e,r,i){var s,o,a;t&&this.state===Dr&&(a=t.data1,s=t.data2,null!=(o=(o=a)&&s?It(a,s):o))&&o.length&&(a={type:t.type,frag:e,part:r,chunkMeta:i,parent:e.type,data:o},this.hls.trigger(T.BUFFER_APPENDING,a),t.dropped)&&t.independent&&!r&&this.flushBufferGap(e)},r.flushBufferGap=function(t){var r,n,e=this.media;e&&(dr.isBuffered(e,e.currentTime)?(r=e.currentTime,e=dr.bufferInfo(e,r,0),n=t.duration,n=Math.min(2*this.config.maxFragLookUpTolerance,.25*n),e=Math.max(Math.min(t.start-n,e.end-n),r+n),t.start-e>n&&this.flushMainBuffer(e,t.start)):this.flushMainBuffer(0,t.start))},r.getFwdBufferInfo=function(t,e){var r=this.getLoadPosition();return y(r)?this.getFwdBufferInfoAtPos(t,r,e):null},r.getFwdBufferInfoAtPos=function(t,e,r){var i=this.config.maxBufferHole,n=dr.bufferInfo(t,e,i);if(0===n.len&&void 0!==n.nextStart){r=this.fragmentTracker.getBufferedFrag(e,r);if(r&&n.nextStart<r.end)return dr.bufferInfo(t,e,Math.max(n.nextStart,i))}return n},r.getMaxBufferLength=function(t){var r=this.config,t=t?Math.max(8*r.maxBufferSize/t,r.maxBufferLength):r.maxBufferLength;return Math.min(t,r.maxMaxBufferLength)},r.reduceMaxBufferLength=function(t){var e=this.config,t=t||e.maxBufferLength;return e.maxMaxBufferLength>=t&&(e.maxMaxBufferLength/=2,this.warn("Reduce max buffer length to "+e.maxMaxBufferLength+"s"),!0)},r.getAppendedFrag=function(t,e){t=this.fragmentTracker.getAppendedFrag(t,le);return t&&"fragment"in t?t.fragment:t},r.getNextFragment=function(t,e){var r=e.fragments,i=r.length;if(!i)return null;var n,a=this.config,s=r[0].start;if(e.live){var o=a.initialLiveManifestSize;if(i<o)return this.warn("Not enough fragments to start playback (have: "+i+", need: "+o+")"),null;e.PTSKnown||this.startFragRequested||-1!==this.startPosition||(n=this.getInitialLiveFragment(e,r),this.startPosition=n?this.hls.liveSyncPosition||n.start:t)}else t<=s&&(n=r[0]);return n||(i=a.lowLatencyMode?e.partEnd:e.fragmentEnd,n=this.getFragmentAtPosition(t,i,e)),this.mapToInitFragWhenRequired(n)},r.isLoopLoading=function(t,e){var r=this.fragmentTracker.getState(t);return(r===Je||r===$e&&!!t.gap)&&this.nextLoadPosition>e},r.getNextFragmentLoopLoading=function(t,e,r,i,n){var a=t.gap,e=this.getNextFragment(this.nextLoadPosition,e);if(null===e)return e;if(t=e,a&&t&&!t.gap&&r.nextStart){e=this.getFwdBufferInfoAtPos(this.mediaBuffer||this.media,r.nextStart,i);if(null!==e&&r.len+e.len>=n)return this.log('buffer full after gaps in "'+i+'" playlist starting at sn: '+t.sn),null}return t},r.mapToInitFragWhenRequired=function(t){return null==t||!t.initSegment||null!=t&&t.initSegment.data||this.bitrateTest?t:t.initSegment},r.getNextPart=function(t,e,r){for(var i=-1,n=!1,a=!0,s=0,o=t.length;s<o;s++){var l=t[s],a=a&&!l.independent;if(-1<i&&r<l.start)break;var u=l.loaded;u?i=-1:(n||l.independent||a)&&l.fragment===e&&(i=s),n=u}return i},r.loadedEndOfParts=function(t,e){t=t[t.length-1];return t&&e>t.start&&t.loaded},r.getInitialLiveFragment=function(t,e){var n,r=this.fragPrevious,i=null;return r?(t.hasProgramDateTime&&(this.log("Live playlist, switching playlist, load frag with same PDT: "+r.programDateTime),i=function(t,e,r){if(null!==e&&Array.isArray(t)&&t.length&&y(e)&&!(e<(t[0].programDateTime||0)||e>=(t[t.length-1].endProgramDateTime||0))){r=r||0;for(var i=0;i<t.length;++i){var n=t[i];if(function(t,e,r){return e=1e3*Math.min(e,r.duration+(r.deltaPTS||0)),(r.endProgramDateTime||0)-e>t}(e,r,n))return n}}return null}(e,r.endProgramDateTime,this.config.maxFragLookUpTolerance)),i||((n=r.sn+1)>=t.startSN&&n<=t.endSN&&(n=e[n-t.startSN],r.cc===n.cc)&&this.log("Live playlist, switching playlist, load frag with next SN: "+(i=n).sn),i)||(i=function(t,e){return Ge(t,function(t){return t.cc<e?1:t.cc>e?-1:0})}(e,r.cc))&&this.log("Live playlist, switching playlist, load frag with same CC: "+i.sn)):null!==(n=this.hls.liveSyncPosition)&&(i=this.getFragmentAtPosition(n,this.bitrateTest?t.fragmentEnd:t.edge,t)),i},r.getFragmentAtPosition=function(t,e,r){var n=this.config,a=this.fragPrevious,s=r.fragments,o=r.endSN,l=r.fragmentHint,u=n.maxFragLookUpTolerance,h=r.partList,n=!!(n.lowLatencyMode&&null!=h&&h.length&&l);return n&&l&&!this.bitrateTest&&(s=s.concat(l),o=l.sn),(l=t<e?Ke(a,s,t,e-u<t?0:u):s[s.length-1])&&(e=l.sn-r.startSN,a=(t=this.fragmentTracker.getState(l))===Je||t===$e&&l.gap?l:a)&&l.sn===a.sn&&(!n||h[0].fragment.sn>l.sn)&&a&&l.level===a.level&&(u=s[1+e],l=l.sn<o&&this.fragmentTracker.getState(u)!==Je?u:null),l},r.synchronizeToLiveEdge=function(t){var i,n,s,a,e=this.config,r=this.media;r&&(i=this.hls.liveSyncPosition,n=r.currentTime,a=t.fragments[0].start,s=t.edge,a=n>=a-e.maxFragLookUpTolerance&&n<=s,null!==i)&&r.duration>i&&(n<i||!a)&&(e=void 0!==e.liveMaxLatencyDuration?e.liveMaxLatencyDuration:e.liveMaxLatencyDurationCount*t.targetduration,!a&&r.readyState<4||n<s-e)&&(this.loadedmetadata||(this.nextLoadPosition=i),r.readyState)&&(this.warn("Playback: "+n.toFixed(3)+" is located too far from the end of live sliding playlist: "+s+", reset currentTime to : "+i.toFixed(3)),r.currentTime=i)},r.alignPlaylists=function(t,e){var o,u,l,r=this.levels,i=this.levelLastLoaded,n=this.fragPrevious,r=null!==i?r[i]:null,i=t.fragments.length;return i?(o=t.fragments[0].start,l=!e,u=t.alignedSliding&&y(o),l||!u&&!o?(function(e,r){e&&(function(t,e,r){(function(t,e,r){return e.details&&(r.endCC>r.startCC||t&&t.cc<r.startCC)})(t,r,e)&&(t=function(t,e){t=t.fragments,e=e.fragments;if(e.length&&t.length){t=fr(t,e[0].cc);if(t&&(!t||t.startPTS))return t;b.log("No frag in previous level to align on")}else b.log("No fragments to align")}(r.details,e))&&y(t.start)&&(b.log("Adjusting PTS using last level due to CC increase within current level "+e.url),vr(t.start,e))}(n,r,e),!r.alignedSliding&&e.details&&function(t,e){var r,i;e.fragments.length&&t.hasProgramDateTime&&e.hasProgramDateTime&&(r=e.fragments[0].programDateTime,e=((i=t.fragments[0].programDateTime)-r)/1e3+e.fragments[0].start)&&y(e)&&(b.log("Adjusting PTS using programDateTime delta "+(i-r)+"ms, sliding:"+e.toFixed(3)+" "+t.url+" "),vr(e,t))}(r,e.details),r.alignedSliding||!e.details||r.skippedSegments||Ce(e.details,r))}(r,t),l=t.fragments[0].start,this.log("Live playlist sliding: "+l.toFixed(2)+" start-sn: "+(e?e.startSN:"na")+"->"+t.startSN+" prev-sn: "+(n?n.sn:"na")+" fragments: "+i),l):o):(this.warn("No fragments in live playlist"),0)},r.waitForCdnTuneIn=function(t){return t.live&&t.canBlockReload&&t.partTarget&&t.tuneInGoal>Math.max(t.partHoldBack,3*t.partTarget)},r.setStartPosition=function(t,e){var i,n,r=this.startPosition;-1!==(r=r<e?-1:r)&&-1!==this.lastCurrentTime||(null!==(n=((i=null!==this.startTimeOffset)?this:t).startTimeOffset)&&y(n)?(r=e+n,n<0&&(r+=t.totalduration),r=Math.min(Math.max(e,r),e+t.totalduration),this.log("Start time offset "+n+" found in "+(i?"multivariant":"media")+" playlist, adjust startPosition to "+r),this.startPosition=r):t.live?r=this.hls.liveSyncPosition||e:this.startPosition=r=0,this.lastCurrentTime=r),this.nextLoadPosition=r},r.getLoadPosition=function(){var t=this.media,e=0;return this.loadedmetadata&&t?e=t.currentTime:this.nextLoadPosition&&(e=this.nextLoadPosition),e},r.handleFragLoadAborted=function(t,e){this.transmuxer&&"initSegment"!==t.sn&&t.stats.aborted&&(this.warn("Fragment "+t.sn+(e?" part "+e.index:"")+" of level "+t.level+" was aborted"),this.resetFragmentLoading(t))},r.resetFragmentLoading=function(t){this.fragCurrent&&(this.fragContextChanged(t)||this.state===Ar)||(this.state=Lr)},r.onFragmentOrKeyLoadError=function(t,e){e.chunkMeta&&!e.frag&&(r=this.getCurrentContext(e.chunkMeta))&&(e.frag=r.frag);var n,s,u,o,l,r=e.frag;r&&r.type===t&&this.levels&&(this.fragContextChanged(r)?this.warn("Frag load error must match current frag to retry "+r.url+" > "+(null==(n=this.fragCurrent)?void 0:n.url)):((n=e.details===S.FRAG_GAP)&&this.fragmentTracker.fragBuffered(r,!0),l=(o=(s=e.errorAction)||{}).action,u=void 0===(u=o.retryCount)?0:u,o=o.retryConfig,s&&5===l&&o?(this.loadedmetadata||(this.startFragRequested=!1,this.nextLoadPosition=this.startPosition),l=Ne(o,u),this.warn("Fragment "+r.sn+" of "+t+" "+r.level+" errored with "+e.details+", retrying loading "+(u+1)+"/"+o.maxNumRetry+" in "+l+"ms"),s.resolved=!0,this.retryDate=self.performance.now()+l,this.state=Ar):o&&s?(this.resetFragmentErrors(t),u<o.maxNumRetry?n||(s.resolved=!0):b.warn(e.details+" reached or exceeded max retry ("+u+")")):this.state=wr,this.tickImmediate()))},r.reduceLengthAndFlushBuffer=function(t){var e,i,r;return(this.state===Dr||this.state===Ir)&&(e=t.parent,(i=(r=this.getFwdBufferInfo(this.mediaBuffer,e))&&.5<r.len)&&this.reduceMaxBufferLength(r.len),(r=!i)&&this.warn("Buffer full error while media.currentTime is not buffered, flush "+e+" buffer"),t.frag&&(this.fragmentTracker.removeFragment(t.frag),this.nextLoadPosition=t.frag.start),this.resetLoadingState(),r)},r.resetFragmentErrors=function(t){t===ue&&(this.fragCurrent=null),this.loadedmetadata||(this.startFragRequested=!1),this.state!==Sr&&(this.state=Lr)},r.afterBufferFlushed=function(t,e,r){t&&(t=dr.getBuffered(t),this.fragmentTracker.detectEvictedFragments(e,t,r),this.state===_r)&&this.resetLoadingState()},r.resetLoadingState=function(){this.log("Reset loading state"),this.fragCurrent=null,this.fragPrevious=null,this.state=Lr},r.resetStartWhenNotLoaded=function(t){this.loadedmetadata||(this.startFragRequested=!1,null!=(t=this.levels?this.levels[t].details:null)&&t.live?(this.startPosition=-1,this.setStartPosition(t,0),this.resetLoadingState()):this.nextLoadPosition=this.startPosition)},r.resetWhenMissingContext=function(t){this.warn("The loading context changed while buffering fragment "+t.sn+" of level "+t.level+". This chunk will not be buffered."),this.removeUnbufferedFrags(),this.resetStartWhenNotLoaded(t.level),this.resetLoadingState()},r.removeUnbufferedFrags=function(t){this.fragmentTracker.removeFragmentsInRange(t=void 0===t?0:t,1/0,this.playlistType,!1,!0)},r.updateLevelTiming=function(t,e,r,i){var a=this,s=r.details;if(s){if(Object.keys(t.elementaryStreams).reduce(function(e,n){var l,o=t.elementaryStreams[n];return o?(l=o.endPTS-o.startPTS)<=0?(a.warn("Could not parse fragment "+t.sn+" "+n+" duration reliably ("+l+")"),e||!1):(l=i?0:_e(s,t,o.startPTS,o.endPTS,o.startDTS,o.endDTS),a.hls.trigger(T.LEVEL_PTS_UPDATED,{details:s,level:r,drift:l,type:n,frag:t,start:o.startPTS,end:o.endPTS}),!0):e},!1))r.fragmentError=0;else if(null===(null==(n=this.transmuxer)?void 0:n.error)){var n=new Error("Found no media in fragment "+t.sn+" of level "+r.id+" resetting transmuxer to fallback to playlist timing");if(this.warn(n.message),this.hls.trigger(T.ERROR,{type:E.MEDIA_ERROR,details:S.FRAG_PARSING_ERROR,fatal:!1,error:n,frag:t,reason:"Found no media in msn "+t.sn+' of level "'+r.url+'"'}),!this.hls)return;this.resetTransmuxer()}this.state=Ir,this.hls.trigger(T.FRAG_PARSED,{frag:t,part:e})}else this.warn("level.details undefined")},r.resetTransmuxer=function(){this.transmuxer&&(this.transmuxer.destroy(),this.transmuxer=null)},r.recoverWorkerError=function(t){"demuxerWorker"===t.event&&(this.resetTransmuxer(),this.resetLoadingState())},a(e,[{key:"state",get:function(){return this._state},set:function(t){var e=this._state;e!==t&&(this._state=t,this.log(e+"->"+t))}}]),e}(ur);function Or(){return self.SourceBuffer||self.WebKitSourceBuffer}function Mr(t,e){return{type:t=void 0===t?"":t,id:-1,pid:-1,inputTimeScale:e=void 0===e?9e4:e,sequenceNumber:-1,samples:[],dropped:0}}var Fr=function(){function t(){this._audioTrack=void 0,this._id3Track=void 0,this.frameIndex=0,this.cachedData=null,this.basePTS=null,this.initPTS=null,this.lastPTS=null}var e=t.prototype;return e.resetInitSegment=function(t,e,r,i){this._id3Track={type:"id3",id:3,pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0}},e.resetTimeStamp=function(t){this.initPTS=t,this.resetContiguity()},e.resetContiguity=function(){this.basePTS=null,this.lastPTS=null,this.frameIndex=0},e.canParse=function(t,e){return!1},e.appendFrame=function(t,e,r){},e.demux=function(t,e){this.cachedData&&(t=It(this.cachedData,t),this.cachedData=null);var r,u,i=rt(t,0),n=i?i.length:0,a=this._audioTrack,s=this._id3Track,o=i?function(t){for(var e=ot(t),r=0;r<e.length;r++){var i=e[r];if(at(i))return function(t){var r;if(8===t.data.byteLength)return r=1&(t=new Uint8Array(t.data))[3],t=(t[4]<<23)+(t[5]<<15)+(t[6]<<7)+t[7],t/=45,r&&(t+=47721858.84),Math.round(t)}(i)}}(i):void 0,l=t.length;for((null===this.basePTS||0===this.frameIndex&&y(o))&&(this.basePTS=Nr(o,e,this.initPTS),this.lastPTS=this.basePTS),null===this.lastPTS&&(this.lastPTS=this.basePTS),i&&0<i.length&&s.samples.push({pts:this.lastPTS,dts:this.lastPTS,data:i,type:ye,duration:Number.POSITIVE_INFINITY});n<l;)this.canParse(t,n)?(u=this.appendFrame(a,t,n))?(this.frameIndex++,this.lastPTS=u.sample.pts,r=n+=u.length):n=l:!function(t,e){return tt(t,e)&&it(t,e+6)+10<=t.length-e}(t,n)?n++:(i=rt(t,n),s.samples.push({pts:this.lastPTS,dts:this.lastPTS,data:i,type:ye,duration:Number.POSITIVE_INFINITY}),r=n+=i.length),n===l&&r!==l&&(u=$(t,r),this.cachedData?this.cachedData=It(this.cachedData,u):this.cachedData=u);return{audioTrack:a,videoTrack:Mr(),id3Track:s,textTrack:Mr()}},e.demuxSampleAes=function(t,e,r){return Promise.reject(new Error("["+this+"] This demuxer does not support Sample-AES decryption"))},e.flush=function(t){var e=this.cachedData;return e&&(this.cachedData=null,this.demux(e,0)),{audioTrack:this._audioTrack,videoTrack:Mr(),id3Track:this._id3Track,textTrack:Mr()}},e.destroy=function(){},t}(),Nr=function(t,e,r){return y(t)?90*t:9e4*e+(r?9e4*r.baseTime/r.timescale:0)};function Ur(t,e){return 255===t[e]&&240==(246&t[e+1])}function Br(t,e){return 1&t[e+1]?7:9}function Gr(t,e){return(3&t[e+3])<<11|t[e+4]<<3|(224&t[e+5])>>>5}function Kr(t,e){return e+1<t.length&&Ur(t,e)}function Vr(t,e,r,i,n){t.samplerate||(e=function(t,e,r,i){var o,l=navigator.userAgent.toLowerCase(),u=i,h=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350],n=1+((192&e[r+2])>>>6),d=(60&e[r+2])>>>2;return h.length-1<d?void t.trigger(T.ERROR,{type:E.MEDIA_ERROR,details:S.FRAG_PARSING_ERROR,fatal:!0,reason:"invalid ADTS sampling index:"+d}):(t=(1&e[r+2])<<2,t|=(192&e[r+3])>>>6,b.log("manifest codec:"+i+", ADTS type:"+n+", samplingIndex:"+d),e=/firefox/i.test(l)?6<=d?(n=5,o=new Array(4),d-3):(n=2,o=new Array(2),d):-1!==l.indexOf("android")?(n=2,o=new Array(2),d):(n=5,o=new Array(4),i&&(-1!==i.indexOf("mp4a.40.29")||-1!==i.indexOf("mp4a.40.5"))||!i&&6<=d?d-3:((i&&-1!==i.indexOf("mp4a.40.2")&&(6<=d&&1==t||/vivaldi/i.test(l))||!i&&1==t)&&(n=2,o=new Array(2)),d)),o[0]=n<<3,o[0]|=(14&d)>>1,o[1]|=(1&d)<<7,o[1]|=t<<3,5===n&&(o[1]|=(14&e)>>1,o[2]=(1&e)<<7,o[2]|=8,o[3]=0),{config:o,samplerate:h[d],channelCount:t,codec:"mp4a.40."+n,manifestCodec:u})}(e,r,i,n))&&(t.config=e.config,t.samplerate=e.samplerate,t.channelCount=e.channelCount,t.codec=e.codec,t.manifestCodec=e.manifestCodec,b.log("parsed codec:"+t.codec+", rate:"+e.samplerate+", channels:"+e.channelCount))}function jr(t){return 9216e4/t}function Yr(t,e,r,i,n){var a,l,d,i=i+n*jr(t.samplerate),n=function(t,e){var r=Br(t,e);if(e+r<=t.length){t=Gr(t,e)-r;if(0<t)return{headerLength:r,frameLength:t}}}(e,r);return n?(l=n.frameLength,l=(n=n.headerLength)+l,(d=Math.max(0,r+l-e.length))?(a=new Uint8Array(l-n)).set(e.subarray(r+n,e.length),0):a=e.subarray(r+n,r+l),n={unit:a,pts:i},d||t.samples.push(n),{sample:n,length:l,missing:d}):(t=e.length-r,(a=new Uint8Array(t)).set(e.subarray(r,e.length),0),{sample:{unit:a,pts:i},length:t,missing:-1})}var Wr=function(t){function e(e,r){var i;return(i=t.call(this)||this).observer=void 0,i.config=void 0,i.observer=e,i.config=r,i}l(e,t);var r=e.prototype;return r.resetInitSegment=function(e,r,i,n){t.prototype.resetInitSegment.call(this,e,r,i,n),this._audioTrack={container:"audio/adts",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"aac",samples:[],manifestCodec:r,duration:n,inputTimeScale:9e4,dropped:0}},e.probe=function(t){if(t)for(var e=(rt(t,0)||[]).length,r=t.length;e<r;e++)if(function(t,e){var i,r;return!Kr(t,e)||e+(r=Br(t,e))>=t.length?void 0:!((i=Gr(t,e))<=r)&&((r=e+i)===t.length||Kr(t,r))}(t,e))return b.log("ADTS sync word found !"),!0;return!1},r.canParse=function(t,e){return e+5<t.length&&Ur(t,e)&&Gr(t,e)<=t.length-e},r.appendFrame=function(t,e,r){Vr(t,this.observer,e,r,t.manifestCodec);t=Yr(t,e,r,this.basePTS,this.frameIndex);if(t&&0===t.missing)return t},e}(Fr),qr=/\/emsg[-/]ID3/i,Xr=function(){function t(t,e){this.remainderData=null,this.timeOffset=0,this.config=void 0,this.videoTrack=void 0,this.audioTrack=void 0,this.id3Track=void 0,this.txtTrack=void 0,this.config=e}var e=t.prototype;return e.resetTimeStamp=function(){},e.resetInitSegment=function(t,e,r,i){var u,h,l,n=this.videoTrack=Mr("video",1),a=this.audioTrack=Mr("audio",1),s=this.txtTrack=Mr("text",1);this.id3Track=Mr("id3",1),this.timeOffset=0,null!=t&&t.byteLength&&((t=At(t)).video&&(u=(l=t.video).id,h=l.timescale,l=l.codec,n.id=u,n.timescale=s.timescale=h,n.codec=l),t.audio&&(h=(u=t.audio).id,l=u.timescale,t=u.codec,a.id=h,a.timescale=l,a.codec=t),s.id=pt.text,n.sampleDuration=0,n.duration=a.duration=i)},e.resetContiguity=function(){this.remainderData=null},t.probe=function(t){return 0<Rt(t=16384<t.length?t.subarray(0,16384):t,["moof"]).length},e.demux=function(t,e){this.timeOffset=e;var r=t,i=this.videoTrack,n=this.txtTrack,t=(this.config.progressive?(t=function(t){var e={valid:null,remainder:null},r=Rt(t,["moof"]);return r&&(r.length<2?e.remainder=t:(r=r[r.length-1],e.valid=$(t,0,r.byteOffset-8),e.remainder=$(t,r.byteOffset-8))),e}(r=this.remainderData?It(this.remainderData,t):r),this.remainderData=t.remainder,i.samples=t.valid||new Uint8Array):i.samples=r,this.extractID3Track(i,e));return n.samples=_t(e,i),{videoTrack:i,audioTrack:this.audioTrack,id3Track:t,textTrack:this.txtTrack}},e.flush=function(){var t=this.timeOffset,e=this.videoTrack,r=this.txtTrack,i=(e.samples=this.remainderData||new Uint8Array,this.remainderData=null,this.extractID3Track(e,this.timeOffset));return r.samples=_t(t,e),{videoTrack:e,audioTrack:Mr(),id3Track:i,textTrack:Mr()}},e.extractID3Track=function(t,e){var r=this.id3Track;return t.samples.length&&(t=Rt(t.samples,["emsg"]))&&t.forEach(function(t){var n,a,t=function(t){var e=t[0],r="",i="",n=0,a=0,s=0,o=0,l=0,u=0;if(0===e){for(;"\0"!==yt(t.subarray(u,u+1));)r+=yt(t.subarray(u,u+1)),u+=1;for(r+=yt(t.subarray(u,u+1)),u+=1;"\0"!==yt(t.subarray(u,u+1));)i+=yt(t.subarray(u,u+1)),u+=1;i+=yt(t.subarray(u,u+1)),u+=1,n=Et(t,12),a=Et(t,16),o=Et(t,20),l=Et(t,24),u=28}else if(1===e){var n=Et(t,u+=4),e=Et(t,u+=4),d=Et(t,u+=4);for(u+=4,s=Math.pow(2,32)*e+d,Number.isSafeInteger(s)||(s=Number.MAX_SAFE_INTEGER,b.warn("Presentation time exceeds safe integer limit and wrapped to max safe integer in parsing emsg box")),o=Et(t,u),l=Et(t,u+=4),u+=4;"\0"!==yt(t.subarray(u,u+1));)r+=yt(t.subarray(u,u+1)),u+=1;for(r+=yt(t.subarray(u,u+1)),u+=1;"\0"!==yt(t.subarray(u,u+1));)i+=yt(t.subarray(u,u+1)),u+=1;i+=yt(t.subarray(u,u+1)),u+=1}return{schemeIdUri:r,value:i,timeScale:n,presentationTime:s,presentationTimeDelta:a,eventDuration:o,id:l,payload:t.subarray(u,t.byteLength)}}(t);qr.test(t.schemeIdUri)&&(n=y(t.presentationTime)?t.presentationTime/t.timeScale:e+t.presentationTimeDelta/t.timeScale,(a=4294967295===t.eventDuration?Number.POSITIVE_INFINITY:t.eventDuration/t.timeScale)<=.001&&(a=Number.POSITIVE_INFINITY),t=t.payload,r.samples.push({data:t,len:t.byteLength,dts:n,pts:n,type:Te,duration:a}))}),r},e.demuxSampleAes=function(t,e,r){return Promise.reject(new Error("The MP4 demuxer does not support SAMPLE-AES decryption"))},e.destroy=function(){},t}(),zr=null,Qr=[32,64,96,128,160,192,224,256,288,320,352,384,416,448,32,48,56,64,80,96,112,128,160,192,224,256,320,384,32,40,48,56,64,80,96,112,128,160,192,224,256,320,32,48,56,64,80,96,112,128,144,160,176,192,224,256,8,16,24,32,40,48,56,64,80,96,112,128,144,160],Zr=[44100,48e3,32e3,22050,24e3,16e3,11025,12e3,8e3],$r=[[0,72,144,12],[0,0,0,0],[0,72,144,12],[0,144,144,12]],Jr=[0,1,1,4];function ti(t,e,r,i,n){if(!(r+24>e.length)){var a=ei(e,r);if(a&&r+a.frameLength<=e.length)return i=i+n*(9e4*a.samplesPerFrame/a.sampleRate),n={unit:e.subarray(r,r+a.frameLength),pts:i,dts:i},t.config=[],t.channelCount=a.channelCount,t.samplerate=a.sampleRate,t.samples.push(n),{sample:n,length:a.frameLength,missing:0}}}function ei(t,e){var o,h,c,f,s,r=t[e+1]>>3&3,i=t[e+1]>>1&3,n=t[e+2]>>4&15,a=t[e+2]>>2&3;if(1!=r&&0!=n&&15!=n&&3!=a)return s=t[e+2]>>1&1,o=t[e+3]>>6,n=1e3*Qr[14*(3==r?3-i:3==i?3:4)+n-1],a=Zr[3*(3==r?0:2==r?1:2)+a],h=3==o?1:2,f=8*(r=$r[r][i])*(c=Jr[i]),r=Math.floor(r*n/a+s)*c,null===zr&&(s=(navigator.userAgent||"").match(/Chrome\/(\d+)/i),zr=s?parseInt(s[1]):0),zr&&zr<=87&&2==i&&224e3<=n&&0==o&&(t[e+3]=128|t[e+3]),{sampleRate:a,channelCount:h,frameLength:r,samplesPerFrame:f}}function ri(t,e){return 255===t[e]&&224==(224&t[e+1])&&0!=(6&t[e+1])}function ii(t,e){return e+1<t.length&&ri(t,e)}var ai=function(){function t(t){this.data=void 0,this.bytesAvailable=void 0,this.word=void 0,this.bitsAvailable=void 0,this.data=t,this.bytesAvailable=t.byteLength,this.word=0,this.bitsAvailable=0}var e=t.prototype;return e.loadWord=function(){var t=this.data,e=this.bytesAvailable,r=t.byteLength-e,i=new Uint8Array(4),e=Math.min(4,e);if(0===e)throw new Error("no bytes available");i.set(t.subarray(r,r+e)),this.word=new DataView(i.buffer).getUint32(0),this.bitsAvailable=8*e,this.bytesAvailable-=e},e.skipBits=function(t){var e;t=Math.min(t,8*this.bytesAvailable+this.bitsAvailable),this.bitsAvailable>t||(t=(t-=this.bitsAvailable)-((e=t>>3)<<3),this.bytesAvailable-=e,this.loadWord()),this.word<<=t,this.bitsAvailable-=t},e.readBits=function(t){var e=Math.min(this.bitsAvailable,t),r=this.word>>>32-e;if(32<t&&b.error("Cannot read more than 32 bits at a time"),this.bitsAvailable-=e,0<this.bitsAvailable)this.word<<=e;else{if(!(0<this.bytesAvailable))throw new Error("no bits available");this.loadWord()}return 0<(e=t-e)&&this.bitsAvailable?r<<e|this.readBits(e):r},e.skipLZ=function(){for(var t=0;t<this.bitsAvailable;++t)if(0!=(this.word&2147483648>>>t))return this.word<<=t,this.bitsAvailable-=t,t;return this.loadWord(),t+this.skipLZ()},e.skipUEG=function(){this.skipBits(1+this.skipLZ())},e.skipEG=function(){this.skipBits(1+this.skipLZ())},e.readUEG=function(){var t=this.skipLZ();return this.readBits(t+1)-1},e.readEG=function(){var t=this.readUEG();return 1&t?1+t>>>1:-1*(t>>>1)},e.readBoolean=function(){return 1===this.readBits(1)},e.readUByte=function(){return this.readBits(8)},e.readUShort=function(){return this.readBits(16)},e.readUInt=function(){return this.readBits(32)},e.skipScalingList=function(t){for(var e=8,r=8,i=0;i<t;i++)e=0===(r=0!==r?(e+this.readEG()+256)%256:r)?e:r},e.readSPS=function(){var t,e,r,i=0,n=0,a=0,s=0,o=this.readUByte.bind(this),l=this.readBits.bind(this),u=this.readUEG.bind(this),h=this.readBoolean.bind(this),d=this.skipBits.bind(this),c=this.skipEG.bind(this),f=this.skipUEG.bind(this),g=this.skipScalingList.bind(this),v=(o(),o());if(l(5),d(3),o(),f(),100===v||110===v||122===v||244===v||44===v||83===v||86===v||118===v||128===v){v=u();if(3===v&&d(1),f(),f(),d(1),h())for(e=3!==v?8:12,r=0;r<e;r++)h()&&g(r<6?16:64)}f();v=u();if(0===v)u();else if(1===v)for(d(1),c(),c(),t=u(),r=0;r<t;r++)c();f(),d(1);var v=u(),f=u(),l=l(1),S=(0===l&&d(1),d(1),h()&&(i=u(),n=u(),a=u(),s=u()),[1,1]);if(h()&&h())switch(o()){case 1:S=[1,1];break;case 2:S=[12,11];break;case 3:S=[10,11];break;case 4:S=[16,11];break;case 5:S=[40,33];break;case 6:S=[24,11];break;case 7:S=[20,11];break;case 8:S=[32,11];break;case 9:S=[80,33];break;case 10:S=[18,11];break;case 11:S=[15,11];break;case 12:S=[64,33];break;case 13:S=[160,99];break;case 14:S=[4,3];break;case 15:S=[3,2];break;case 16:S=[2,1];break;case 255:S=[o()<<8|o(),o()<<8|o()]}return{width:Math.ceil(16*(v+1)-2*i-2*n),height:(2-l)*(f+1)*16-(l?2:4)*(a+s),pixelRatio:S}},e.readSliceType=function(){return this.readUByte(),this.readUEG(),this.readUEG()},t}(),si=function(){function t(t,e,r){this.keyData=void 0,this.decrypter=void 0,this.keyData=r,this.decrypter=new Er(e,{removePKCS7Padding:!1})}var e=t.prototype;return e.decryptBuffer=function(t){return this.decrypter.decrypt(t,this.keyData.key.buffer,this.keyData.iv.buffer)},e.decryptAacSample=function(t,e,r){var a,i=this,n=t[e].unit;n.length<=16||(a=(a=n.subarray(16,n.length-n.length%16)).buffer.slice(a.byteOffset,a.byteOffset+a.length),this.decryptBuffer(a).then(function(a){a=new Uint8Array(a);n.set(a,16),i.decrypter.isSync()||i.decryptAacSamples(t,e+1,r)}))},e.decryptAacSamples=function(t,e,r){for(;;e++){if(e>=t.length)return void r();if(!(t[e].unit.length<32||(this.decryptAacSample(t,e,r),this.decrypter.isSync())))return}},e.getAvcEncryptedData=function(t){for(var e=16*Math.floor((t.length-48)/160)+16,r=new Int8Array(e),i=0,n=32;n<t.length-16;n+=160,i+=16)r.set(t.subarray(n,n+16),i);return r},e.getAvcDecryptedUnit=function(t,e){for(var r=new Uint8Array(e),i=0,n=32;n<t.length-16;n+=160,i+=16)t.set(r.subarray(i,i+16),n);return t},e.decryptAvcSample=function(t,e,r,i,n){var a=this,s=xt(n.data),o=this.getAvcEncryptedData(s);this.decryptBuffer(o.buffer).then(function(o){n.data=a.getAvcDecryptedUnit(s,o),a.decrypter.isSync()||a.decryptAvcSamples(t,e,r+1,i)})},e.decryptAvcSamples=function(t,e,r,i){if(t instanceof Uint8Array)throw new Error("Cannot decrypt samples of type Uint8Array");for(;;e++,r=0){if(e>=t.length)return void i();for(var n=t[e].units;!(r>=n.length);r++){var a=n[r];if(!(a.data.length<=48||1!==a.type&&5!==a.type||(this.decryptAvcSample(t,e,r,i,a),this.decrypter.isSync())))return}}},t}(),oi=188,li=function(){function t(t,e,r){this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.sampleAes=null,this.pmtParsed=!1,this.audioCodec=void 0,this.videoCodec=void 0,this._duration=0,this._pmtId=-1,this._avcTrack=void 0,this._audioTrack=void 0,this._id3Track=void 0,this._txtTrack=void 0,this.aacOverFlow=null,this.avcSample=null,this.remainderData=null,this.observer=t,this.config=e,this.typeSupported=r}t.probe=function(e){e=t.syncOffset(e);return 0<e&&b.warn("MPEG2-TS detected but first sync word found @ offset "+e),-1!==e},t.syncOffset=function(t){for(var e=t.length,r=Math.min(940,t.length-oi)+1,i=0;i<r;){for(var n=!1,a=i;a<e&&71===t[a];a+=oi)if((n=n||0!==hi(t,a)?n:!0)&&r<a+oi)return i;i++}return-1},t.createTrack=function(t,e){return{container:"video"===t||"audio"===t?"video/mp2t":void 0,type:t,id:pt[t],pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0,duration:"audio"===t?e:void 0}};var e=t.prototype;return e.resetInitSegment=function(e,r,i,n){this.pmtParsed=!1,this._pmtId=-1,this._avcTrack=t.createTrack("video"),this._audioTrack=t.createTrack("audio",n),this._id3Track=t.createTrack("id3"),this._txtTrack=t.createTrack("text"),this._audioTrack.segmentCodec="aac",this.aacOverFlow=null,this.avcSample=null,this.remainderData=null,this.audioCodec=r,this.videoCodec=i,this._duration=n},e.resetTimeStamp=function(){},e.resetContiguity=function(){var t=this._audioTrack,e=this._avcTrack,r=this._id3Track;t&&(t.pesData=null),e&&(e.pesData=null),r&&(r.pesData=null),this.aacOverFlow=null,this.avcSample=null,this.remainderData=null},e.demux=function(e,r,i,n){void 0===n&&(n=!1),(i=void 0===i?!1:i)||(this.sampleAes=null);var a,s=this._avcTrack,o=this._audioTrack,l=this._id3Track,u=this._txtTrack,h=s.pid,d=s.pesData,c=o.pid,f=l.pid,g=o.pesData,v=l.pesData,m=null,p=this.pmtParsed,y=this._pmtId,L=e.length;if(this.remainderData&&(L=(e=It(this.remainderData,e)).length,this.remainderData=null),L<oi&&!n)return this.remainderData=e,{audioTrack:o,videoTrack:s,id3Track:l,textTrack:u};var R=Math.max(0,t.syncOffset(e));(L-=(L-R)%oi)<e.byteLength&&!n&&(this.remainderData=new Uint8Array(e.buffer,L,e.buffer.byteLength-L));for(var k=0,A=R;A<L;A+=oi)if(71===e[A]){var D=!!(64&e[A+1]),I=hi(e,A),_=void 0;if(1<(48&e[A+3])>>4){if((_=A+5+e[A+4])===A+oi)continue}else _=A+4;switch(I){case h:D&&(d&&(a=fi(d))&&this.parseAVCPES(s,u,a,!1),d={data:[],size:0}),d&&(d.data.push(e.subarray(_,A+oi)),d.size+=A+oi-_);break;case c:if(D){if(g&&(a=fi(g)))switch(o.segmentCodec){case"aac":this.parseAACPES(o,a);break;case"mp3":this.parseMPEGPES(o,a)}g={data:[],size:0}}g&&(g.data.push(e.subarray(_,A+oi)),g.size+=A+oi-_);break;case f:D&&(v&&(a=fi(v))&&this.parseID3PES(l,a),v={data:[],size:0}),v&&(v.data.push(e.subarray(_,A+oi)),v.size+=A+oi-_);break;case 0:D&&(_+=e[_]+1),y=this._pmtId=function(t,e){return(31&t[e+10])<<8|t[e+11]}(e,_);break;case y:D&&(_+=e[_]+1);var w=function(t,e,r,i){var n={audio:-1,avc:-1,id3:-1,segmentCodec:"aac"},a=e+3+((15&t[e+1])<<8|t[e+2])-4;for(e+=12+((15&t[e+10])<<8|t[e+11]);e<a;){var s=hi(t,e);switch(t[e]){case 207:if(!i){b.log("ADTS AAC with AES-128-CBC frame encryption found in unencrypted stream");break}case 15:-1===n.audio&&(n.audio=s);break;case 21:-1===n.id3&&(n.id3=s);break;case 219:if(!i){b.log("H.264 with AES-128-CBC slice encryption found in unencrypted stream");break}case 27:-1===n.avc&&(n.avc=s);break;case 3:case 4:!0!==r.mpeg&&!0!==r.mp3?b.log("MPEG audio found, not supported in this browser"):-1===n.audio&&(n.audio=s,n.segmentCodec="mp3");break;case 36:b.warn("Unsupported HEVC stream type found")}e+=5+((15&t[e+3])<<8|t[e+4])}return n}(e,_,this.typeSupported,i);0<(h=w.avc)&&(s.pid=h),0<(c=w.audio)&&(o.pid=c,o.segmentCodec=w.segmentCodec),0<(f=w.id3)&&(l.pid=f),null===m||p||(b.warn("MPEG-TS PMT found at "+A+" after unknown PID '"+m+"'. Backtracking to sync byte @"+R+" to parse all TS packets."),m=null,A=R-188),p=this.pmtParsed=!0;break;case 17:case 8191:break;default:m=I}}else k++;0<k&&(C=new Error("Found "+k+" TS packet/s that do not start with 0x47"),this.observer.emit(T.ERROR,T.ERROR,{type:E.MEDIA_ERROR,details:S.FRAG_PARSING_ERROR,fatal:!1,error:C,reason:C.message})),s.pesData=d,o.pesData=g,l.pesData=v;var C={audioTrack:o,videoTrack:s,id3Track:l,textTrack:u};return n&&this.extractRemainingSamples(C),C},e.flush=function(){var e=this.remainderData;return this.remainderData=null,e=e?this.demux(e,-1,!1,!0):{videoTrack:this._avcTrack,audioTrack:this._audioTrack,id3Track:this._id3Track,textTrack:this._txtTrack},this.extractRemainingSamples(e),this.sampleAes?this.decrypt(e,this.sampleAes):e},e.extractRemainingSamples=function(t){var e,r=t.audioTrack,i=t.videoTrack,n=t.id3Track,t=t.textTrack,s=i.pesData,o=r.pesData,l=n.pesData;if(s&&(e=fi(s))?(this.parseAVCPES(i,t,e,!0),i.pesData=null):i.pesData=s,o&&(e=fi(o))){switch(r.segmentCodec){case"aac":this.parseAACPES(r,e);break;case"mp3":this.parseMPEGPES(r,e)}r.pesData=null}else null!=o&&o.size&&b.log("last AAC PES packet truncated,might overlap between fragments"),r.pesData=o;l&&(e=fi(l))?(this.parseID3PES(n,e),n.pesData=null):n.pesData=l},e.demuxSampleAes=function(t,e,r){t=this.demux(t,r,!0,!this.config.progressive),r=this.sampleAes=new si(this.observer,this.config,e);return this.decrypt(t,r)},e.decrypt=function(t,e){return new Promise(function(r){var i=t.audioTrack,n=t.videoTrack;i.samples&&"aac"===i.segmentCodec?e.decryptAacSamples(i.samples,0,function(){n.samples?e.decryptAvcSamples(n.samples,0,0,function(){r(t)}):r(t)}):n.samples&&e.decryptAvcSamples(n.samples,0,0,function(){r(t)})})},e.destroy=function(){this._duration=0},e.parseAVCPES=function(t,e,r,i){var n,a=this,s=this.parseAVCNALu(t,r.data),o=this.avcSample,l=!1;r.data=null,o&&s.length&&!t.audFound&&(gi(o,t),o=this.avcSample=ui(!1,r.pts,r.dts,"")),s.forEach(function(i){switch(i.type){case 1:n=!0,(o=o||(a.avcSample=ui(!0,r.pts,r.dts,""))).frame=!0;var s=i.data;l&&4<s.length&&(2!==(s=new ai(s).readSliceType())&&4!==s&&7!==s&&9!==s||(o.key=!0));break;case 5:n=!0,(o=o||(a.avcSample=ui(!0,r.pts,r.dts,""))).key=!0,o.frame=!0;break;case 6:n=!0,Ct(i.data,1,r.pts,e.samples);break;case 7:if(l=n=!0,!t.sps){var s=i.data,d=new ai(s).readSPS();t.width=d.width,t.height=d.height,t.pixelRatio=d.pixelRatio,t.sps=[s],t.duration=a._duration;for(var c=s.subarray(1,4),f="avc1.",g=0;g<3;g++){var v=c[g].toString(16);f+=v=v.length<2?"0"+v:v}t.codec=f}break;case 8:n=!0,t.pps||(t.pps=[i.data]);break;case 9:n=!1,t.audFound=!0,o&&gi(o,t),o=a.avcSample=ui(!1,r.pts,r.dts,"");break;case 12:n=!0;break;default:n=!1,o&&(o.debug+="unknown NAL "+i.type+" ")}o&&n&&o.units.push(i)}),i&&o&&(gi(o,t),this.avcSample=null)},e.getLastNalUnit=function(t){var r,i=this.avcSample;return r=null!=(i=i&&0!==i.units.length?i:t[t.length-1])&&i.units?(t=i.units)[t.length-1]:r},e.parseAVCNALu=function(t,e){var r,i,d,f,g,m,n=e.byteLength,a=t.naluState||0,s=a,o=[],l=0,u=-1,h=0;for(-1===a&&(h=31&e[u=0],a=0,l=1);l<n;)r=e[l++],a=a?1!==a?r?1===r?(0<=u?(d={data:e.subarray(u,l-a-1),type:h},o.push(d)):(d=this.getLastNalUnit(t.samples))&&(s&&l<=4-s&&d.state&&(d.data=d.data.subarray(0,d.data.byteLength-s)),0<(i=l-a-1))&&((f=new Uint8Array(d.data.byteLength+i)).set(d.data,0),f.set(e.subarray(0,i),d.data.byteLength),d.data=f,d.state=0),l<n?(h=31&e[u=l],0):-1):0:3:r?0:2:r?0:1;return 0<=u&&0<=a&&(g={data:e.subarray(u,n),type:h,state:a},o.push(g)),0===o.length&&(g=this.getLastNalUnit(t.samples))&&((m=new Uint8Array(g.data.byteLength+e.byteLength)).set(g.data,0),m.set(e,g.data.byteLength),g.data=m),t.naluState=a,o},e.parseAACPES=function(t,e){var r,i,a=0,s=this.aacOverFlow,o=e.data;for(s&&(this.aacOverFlow=null,l=s.missing,u=s.sample.unit.byteLength,-1===l?((h=new Uint8Array(u+o.byteLength)).set(s.sample.unit,0),h.set(o,u),o=h):(h=u-l,s.sample.unit.set(o.subarray(0,l),h),t.samples.push(s.sample),a=s.missing)),r=a,i=o.length;r<i-1&&!Kr(o,r);r++);if(r!==a){var u=r<i-1,l=u?"AAC PES did not start with ADTS header,offset:"+r:"No ADTS header found in AAC PES",h=new Error(l);if(b.warn("parsing error: "+l),this.observer.emit(T.ERROR,T.ERROR,{type:E.MEDIA_ERROR,details:S.FRAG_PARSING_ERROR,fatal:!1,levelRetry:u,error:h,reason:l}),!u)return}if(Vr(t,this.observer,o,r,this.audioCodec),void 0!==e.pts)n=e.pts;else{if(!s)return void b.warn("[tsdemuxer]: AAC PES unknown PTS");var a=jr(t.samplerate),n=s.sample.pts+a}for(var m,p=0;r<i;){if(r+=(m=Yr(t,o,r,n,p)).length,m.missing){this.aacOverFlow=m;break}for(p++;r<i-1&&!Kr(o,r);r++);}},e.parseMPEGPES=function(t,e){var r=e.data,i=r.length,n=0,a=0,s=e.pts;if(void 0!==s)for(;a<i;)if(ii(r,a)){var o=ti(t,r,a,s,n);if(!o)break;a+=o.length,n++}else a++;else b.warn("[tsdemuxer]: MPEG PES unknown PTS")},e.parseID3PES=function(t,e){void 0!==e.pts?(e=o({},e,{type:this._avcTrack?Te:ye,duration:Number.POSITIVE_INFINITY}),t.samples.push(e)):b.warn("[tsdemuxer]: ID3 PES unknown PTS")},t}();function ui(t,e,r,i){return{key:t,frame:!1,pts:e,dts:r,units:[],debug:i,length:0}}function hi(t,e){return((31&t[e+1])<<8)+t[e+2]}function fi(t){var e,r,n,a,s=0,o=t.data;if(!t||0===t.size)return null;for(;o[0].length<19&&1<o.length;){var l=new Uint8Array(o[0].length+o[1].length);l.set(o[0]),l.set(o[1],o[0].length),o[0]=l,o.splice(1,1)}if(1!==((e=o[0])[0]<<16)+(e[1]<<8)+e[2])return null;if((r=(e[4]<<8)+e[5])&&r>t.size-6)return null;var u=e[7],h=(192&u&&(n=536870912*(14&e[9])+4194304*(255&e[10])+16384*(254&e[11])+128*(255&e[12])+(254&e[13])/2,64&u?54e5<n-(a=536870912*(14&e[14])+4194304*(255&e[15])+16384*(254&e[16])+128*(255&e[17])+(254&e[18])/2)&&(b.warn(Math.round((n-a)/9e4)+"s delta between PTS and DTS, align them"),n=a):a=n),(u=e[8])+9);if(t.size<=h)return null;t.size-=h;for(var d=new Uint8Array(t.size),c=0,f=o.length;c<f;c++){var g=(e=o[c]).byteLength;if(h){if(g<h){h-=g;continue}e=e.subarray(h),g-=h,h=0}d.set(e,s),s+=g}return r&&(r-=u+3),{data:d,pts:n,dts:a,len:r}}function gi(t,e){if(t.units.length&&t.frame){if(void 0===t.pts){var r=e.samples,i=r.length;if(!i)return e.dropped++;r=r[i-1];t.pts=r.pts,t.dts=r.dts}e.samples.push(t)}t.debug.length&&b.log(t.pts+"/"+t.dts+":"+t.debug)}var Fr=function(t){function e(){return t.apply(this,arguments)||this}l(e,t);var r=e.prototype;return r.resetInitSegment=function(e,r,i,n){t.prototype.resetInitSegment.call(this,e,r,i,n),this._audioTrack={container:"audio/mpeg",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"mp3",samples:[],manifestCodec:r,duration:n,inputTimeScale:9e4,dropped:0}},e.probe=function(t){if(t)for(var e=(rt(t,0)||[]).length,r=t.length;e<r;e++)if(function(t,e){var i;if(e+1<t.length&&ri(t,e))return i=4,(e=e+(i=null!=(e=ei(t,e))&&e.frameLength?e.frameLength:i))===t.length||ii(t,e)}(t,e))return b.log("MPEG Audio sync word found !"),!0;return!1},r.canParse=function(t,e){return ri(t,e)&&4<=t.length-e},r.appendFrame=function(t,e,r){if(null!==this.basePTS)return ti(t,e,r,this.basePTS,this.frameIndex)},e}(Fr),mi=function(){function t(){}return t.getSilentFrame=function(t,e){return"mp4a.40.2"===t?1===e?new Uint8Array([0,200,0,128,35,128]):2===e?new Uint8Array([33,0,73,144,2,25,0,35,128]):3===e?new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]):4===e?new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]):5===e?new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]):6===e?new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224]):void 0:1===e?new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]):2===e||3===e?new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]):void 0},t}(),pi=Math.pow(2,32)-1,yi=function(){function t(){}return t.init=function(){for(var e in t.types={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],".mp3":[],mvex:[],mvhd:[],pasp:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]})t.types.hasOwnProperty(e)&&(t.types[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);var r=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),i=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),r=(t.HDLR_TYPES={video:r,audio:i},new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1])),i=new Uint8Array([0,0,0,0,0,0,0,0]),i=(t.STTS=t.STSC=t.STCO=i,t.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),t.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]),t.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),t.STSD=new Uint8Array([0,0,0,0,0,0,0,1]),new Uint8Array([105,115,111,109])),o=new Uint8Array([97,118,99,49]),l=new Uint8Array([0,0,0,1]);t.FTYP=t.box(t.types.ftyp,i,l,i,o),t.DINF=t.box(t.types.dinf,t.box(t.types.dref,r))},t.box=function(t){for(var e=8,r=arguments.length,i=new Array(1<r?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];for(var a=i.length,s=a;a--;)e+=i[a].byteLength;var o=new Uint8Array(e);for(o[0]=e>>24&255,o[1]=e>>16&255,o[2]=e>>8&255,o[3]=255&e,o.set(t,4),a=0,e=8;a<s;a++)o.set(i[a],e),e+=i[a].byteLength;return o},t.hdlr=function(e){return t.box(t.types.hdlr,t.HDLR_TYPES[e])},t.mdat=function(e){return t.box(t.types.mdat,e)},t.mdhd=function(e,r){r*=e;var i=Math.floor(r/(1+pi)),r=Math.floor(r%(1+pi));return t.box(t.types.mdhd,new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,e>>24&255,e>>16&255,e>>8&255,255&e,i>>24,i>>16&255,i>>8&255,255&i,r>>24,r>>16&255,r>>8&255,255&r,85,196,0,0]))},t.mdia=function(e){return t.box(t.types.mdia,t.mdhd(e.timescale,e.duration),t.hdlr(e.type),t.minf(e))},t.mfhd=function(e){return t.box(t.types.mfhd,new Uint8Array([0,0,0,0,e>>24,e>>16&255,e>>8&255,255&e]))},t.minf=function(e){return"audio"===e.type?t.box(t.types.minf,t.box(t.types.smhd,t.SMHD),t.DINF,t.stbl(e)):t.box(t.types.minf,t.box(t.types.vmhd,t.VMHD),t.DINF,t.stbl(e))},t.moof=function(e,r,i){return t.box(t.types.moof,t.mfhd(e),t.traf(i,r))},t.moov=function(e){for(var r=e.length,i=[];r--;)i[r]=t.trak(e[r]);return t.box.apply(null,[t.types.moov,t.mvhd(e[0].timescale,e[0].duration)].concat(i).concat(t.mvex(e)))},t.mvex=function(e){for(var r=e.length,i=[];r--;)i[r]=t.trex(e[r]);return t.box.apply(null,[t.types.mvex].concat(i))},t.mvhd=function(e,r){r*=e;var i=Math.floor(r/(1+pi)),r=Math.floor(r%(1+pi)),e=new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,e>>24&255,e>>16&255,e>>8&255,255&e,i>>24,i>>16&255,i>>8&255,255&i,r>>24,r>>16&255,r>>8&255,255&r,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return t.box(t.types.mvhd,e)},t.sdtp=function(e){for(var i,n=e.samples||[],a=new Uint8Array(4+n.length),r=0;r<n.length;r++)i=n[r].flags,a[r+4]=i.dependsOn<<4|i.isDependedOn<<2|i.hasRedundancy;return t.box(t.types.sdtp,a)},t.stbl=function(e){return t.box(t.types.stbl,t.stsd(e),t.box(t.types.stts,t.STTS),t.box(t.types.stsc,t.STSC),t.box(t.types.stsz,t.STSZ),t.box(t.types.stco,t.STCO))},t.avc1=function(e){for(var i,n,a=[],s=[],r=0;r<e.sps.length;r++)n=(i=e.sps[r]).byteLength,a.push(n>>>8&255),a.push(255&n),a=a.concat(Array.prototype.slice.call(i));for(r=0;r<e.pps.length;r++)n=(i=e.pps[r]).byteLength,s.push(n>>>8&255),s.push(255&n),s=s.concat(Array.prototype.slice.call(i));var o=t.box(t.types.avcC,new Uint8Array([1,a[3],a[4],a[5],255,224|e.sps.length].concat(a).concat([e.pps.length]).concat(s))),l=e.width,u=e.height,h=e.pixelRatio[0],d=e.pixelRatio[1];return t.box(t.types.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,l>>8&255,255&l,u>>8&255,255&u,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),o,t.box(t.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])),t.box(t.types.pasp,new Uint8Array([h>>24,h>>16&255,h>>8&255,255&h,d>>24,d>>16&255,d>>8&255,255&d])))},t.esds=function(t){var e=t.config.length;return new Uint8Array([0,0,0,0,3,23+e,0,1,0,4,15+e,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([e]).concat(t.config).concat([6,1,2]))},t.mp4a=function(e){var r=e.samplerate;return t.box(t.types.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,e.channelCount,0,16,0,0,0,0,r>>8&255,255&r,0,0]),t.box(t.types.esds,t.esds(e)))},t.mp3=function(e){var r=e.samplerate;return t.box(t.types[".mp3"],new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,e.channelCount,0,16,0,0,0,0,r>>8&255,255&r,0,0]))},t.stsd=function(e){return"audio"===e.type?"mp3"===e.segmentCodec&&"mp3"===e.codec?t.box(t.types.stsd,t.STSD,t.mp3(e)):t.box(t.types.stsd,t.STSD,t.mp4a(e)):t.box(t.types.stsd,t.STSD,t.avc1(e))},t.tkhd=function(e){var r=e.id,i=e.duration*e.timescale,n=e.width,e=e.height,s=Math.floor(i/(1+pi)),i=Math.floor(i%(1+pi));return t.box(t.types.tkhd,new Uint8Array([1,0,0,7,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,r>>24&255,r>>16&255,r>>8&255,255&r,0,0,0,0,s>>24,s>>16&255,s>>8&255,255&s,i>>24,i>>16&255,i>>8&255,255&i,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,n>>8&255,255&n,0,0,e>>8&255,255&e,0,0]))},t.traf=function(e,r){var i=t.sdtp(e),n=e.id,a=Math.floor(r/(1+pi)),r=Math.floor(r%(1+pi));return t.box(t.types.traf,t.box(t.types.tfhd,new Uint8Array([0,0,0,0,n>>24,n>>16&255,n>>8&255,255&n])),t.box(t.types.tfdt,new Uint8Array([1,0,0,0,a>>24,a>>16&255,a>>8&255,255&a,r>>24,r>>16&255,r>>8&255,255&r])),t.trun(e,i.length+16+20+8+16+8+8),i)},t.trak=function(e){return e.duration=e.duration||4294967295,t.box(t.types.trak,t.tkhd(e),t.mdia(e))},t.trex=function(e){e=e.id;return t.box(t.types.trex,new Uint8Array([0,0,0,0,e>>24,e>>16&255,e>>8&255,255&e,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))},t.trun=function(e,r){var i,a,s,o,n,u=e.samples||[],h=u.length,d=12+16*h,c=new Uint8Array(d);for(c.set(["video"===e.type?1:0,0,15,1,h>>>24&255,h>>>16&255,h>>>8&255,255&h,(r+=8+d)>>>24&255,r>>>16&255,r>>>8&255,255&r],0),i=0;i<h;i++)a=(n=u[i]).duration,s=n.size,o=n.flags,n=n.cts,c.set([a>>>24&255,a>>>16&255,a>>>8&255,255&a,s>>>24&255,s>>>16&255,s>>>8&255,255&s,o.isLeading<<2|o.dependsOn,o.isDependedOn<<6|o.hasRedundancy<<4|o.paddingValue<<1|o.isNonSync,61440&o.degradPrio,15&o.degradPrio,n>>>24&255,n>>>16&255,n>>>8&255,255&n],12+16*i);return t.box(t.types.trun,c)},t.initSegment=function(e){t.types||t.init();var e=t.moov(e),i=new Uint8Array(t.FTYP.byteLength+e.byteLength);return i.set(t.FTYP),i.set(e,t.FTYP.byteLength),i},t}();function Ti(t,e,r,i){t=t*e*(r=void 0===r?1:r);return(i=void 0===i?!1:i)?Math.round(t):t}function Ei(t,e){return Ti(t,1e3,1/9e4,e=void 0===e?!1:e)}yi.types=void 0,yi.HDLR_TYPES=void 0,yi.STTS=void 0,yi.STSC=void 0,yi.STCO=void 0,yi.STSZ=void 0,yi.VMHD=void 0,yi.SMHD=void 0,yi.STSD=void 0,yi.FTYP=void 0,yi.DINF=void 0;var Si=null,Li=null,Ri=function(){function t(t,e,r,i){this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.ISGenerated=!1,this._initPTS=null,this._initDTS=null,this.nextAvcDts=null,this.nextAudioPts=null,this.videoSampleDuration=null,this.isAudioContiguous=!1,this.isVideoContiguous=!1,this.observer=t,this.config=e,this.typeSupported=r,this.ISGenerated=!1,null===Si&&(t=(navigator.userAgent||"").match(/Chrome\/(\d+)/i),Si=t?parseInt(t[1]):0),null===Li&&(e=navigator.userAgent.match(/Safari\/(\d+)/i),Li=e?parseInt(e[1]):0)}var e=t.prototype;return e.destroy=function(){},e.resetTimeStamp=function(t){b.log("[mp4-remuxer]: initPTS & initDTS reset"),this._initPTS=this._initDTS=t},e.resetNextTimestamp=function(){b.log("[mp4-remuxer]: reset next timestamp"),this.isVideoContiguous=!1,this.isAudioContiguous=!1},e.resetInitSegment=function(){b.log("[mp4-remuxer]: ISGenerated flag reset"),this.ISGenerated=!1},e.getVideoStartPts=function(t){var e=!1,t=t.reduce(function(t,r){var i=r.pts-t;return i<-4294967296?(e=!0,ki(t,r.pts)):0<i?t:r.pts},t[0].pts);return e&&b.debug("PTS rollover detected"),t},e.remux=function(t,e,r,i,n,a,s,o){var l,u,h,d,c,f,k,g=n,v=n,m=-1<t.pid,p=-1<e.pid,y=e.samples.length,T=0<t.samples.length,E=s&&0<y||1<y;return(m&&!T||p&&!E)&&!this.ISGenerated&&!s||(this.ISGenerated||(h=this.generateIS(t,e,n,a)),m=this.isVideoContiguous,s=-1,E&&(s=function(t){for(var e=0;e<t.length;e++)if(t[e].key)return e;return-1}(e.samples),!m)&&this.config.forceKeyFrameOnDiscontinuity&&(f=!0,0<s?(b.warn("[mp4-remuxer]: Dropped "+s+" out of "+y+" video samples due to a missing keyframe"),k=this.getVideoStartPts(e.samples),e.samples=e.samples.slice(s),e.dropped+=s,k=v+=(e.samples[0].pts-k)/e.inputTimeScale):-1===s&&(b.warn("[mp4-remuxer]: No keyframe found out of "+y+" video samples"),f=!1)),this.ISGenerated&&(T&&E&&(y=this.getVideoStartPts(e.samples),y=(ki(t.samples[0].pts,y)-y)/e.inputTimeScale,g+=Math.max(0,y),v+=Math.max(0,-y)),T?(t.samplerate||(b.warn("[mp4-remuxer]: regenerate InitSegment as audio detected"),h=this.generateIS(t,e,n,a)),u=this.remuxAudio(t,g,this.isAudioContiguous,a,p||E||o===ue?v:void 0),E&&(y=u?u.endPTS-u.startPTS:0,e.inputTimeScale||(b.warn("[mp4-remuxer]: regenerate InitSegment as video detected"),h=this.generateIS(t,e,n,a)),l=this.remuxVideo(e,v,m,y))):E&&(l=this.remuxVideo(e,v,m,0)),l)&&(l.firstKeyFrame=s,l.independent=-1!==s,l.firstKeyFramePTS=k)),{audio:u,video:l,initSegment:h,independent:f,text:d=this.ISGenerated&&this._initPTS&&this._initDTS&&(r.samples.length&&(c=Ai(r,n,this._initPTS,this._initDTS)),i.samples.length)?bi(i,n,this._initPTS):d,id3:c}},e.generateIS=function(t,e,r,i){var s,a,n,o=t.samples,l=e.samples,u=this.typeSupported,h={},d=this._initPTS,i=!d||i,f="audio/mp4";if(i&&(n=a=1/0),t.config&&o.length&&(t.timescale=t.samplerate,"mp3"===t.segmentCodec&&(u.mpeg?(f="audio/mpeg",t.codec=""):u.mp3&&(t.codec="mp3")),h.audio={id:"audio",container:f,codec:t.codec,initSegment:"mp3"===t.segmentCodec&&u.mpeg?new Uint8Array(0):yi.initSegment([t]),metadata:{channelCount:t.channelCount}},i)&&(s=t.inputTimeScale,d&&s===d.timescale?i=!1:n=a=o[0].pts-Math.round(s*r)),e.sps&&e.pps&&l.length&&(e.timescale=e.inputTimeScale,h.video={id:"main",container:"video/mp4",codec:e.codec,initSegment:yi.initSegment([e]),metadata:{width:e.width,height:e.height}},i)&&(s=e.inputTimeScale,d&&s===d.timescale?i=!1:(f=this.getVideoStartPts(l),u=Math.round(s*r),a=Math.min(a,ki(l[0].dts,f)-u),n=Math.min(n,f-u))),Object.keys(h).length)return this.ISGenerated=!0,i?(this._initPTS={baseTime:n,timescale:s},this._initDTS={baseTime:a,timescale:s}):n=s=void 0,{tracks:h,initPTS:n,timescale:s}},e.remuxVideo=function(t,e,r,i){var s=t.inputTimeScale,l=t.samples,u=[],h=l.length,d=this._initPTS,c=this.nextAvcDts,f=8,g=this.videoSampleDuration,v=Number.POSITIVE_INFINITY,m=Number.NEGATIVE_INFINITY,p=!1;r&&null!==c||(c=e*s-(l[0].pts-ki(l[0].dts,l[0].pts)));for(var y=d.baseTime*s/d.timescale,L=0;L<h;L++){var R=l[L];R.pts=ki(R.pts-y,c),R.dts=ki(R.dts-y,c),R.dts<l[0<L?L-1:L].dts&&(p=!0)}p&&l.sort(function(t,e){var r=t.dts-e.dts,t=t.pts-e.pts;return r||t}),n=l[0].dts;var n,e=l[l.length-1].dts-n,A=e?Math.round(e/(h-1)):g||t.inputTimeScale/30;r&&(e=(d=n-c)<-1,(r=A<d)||e)&&(r?b.warn("AVC: "+Ei(d,!0)+" ms ("+d+"dts) hole between fragments detected, filling it"):b.warn("AVC: "+Ei(-d,!0)+" ms ("+d+"dts) overlapping between fragments detected"),!e||c>l[0].pts)&&(n=c,r=l[0].pts-d,l[0].dts=n,l[0].pts=r,b.log("Video: First PTS/DTS adjusted: "+Ei(r,!0)+"/"+Ei(n,!0)+", delta: "+Ei(d,!0)+" ms")),n=Math.max(0,n);for(var C=0,x=0,P=0;P<h;P++){for(var O=l[P],M=O.units,F=M.length,N=0,U=0;U<F;U++)N+=M[U].data.length;x+=N,C+=F,O.length=N,O.dts=Math.max(O.dts,n),v=Math.min(O.pts,v),m=Math.max(O.pts,m)}var B,e=l[h-1].dts,r=x+4*C+8;try{B=new Uint8Array(r)}catch(t){return void this.observer.emit(T.ERROR,T.ERROR,{type:E.MUX_ERROR,details:S.REMUX_ALLOC_ERROR,fatal:!1,error:t,bytes:r,reason:"fail allocating video mdat "+r})}var K=new DataView(B.buffer);K.setUint32(0,r),B.set(yi.types.mdat,4);for(var H=!1,V=Number.POSITIVE_INFINITY,j=Number.POSITIVE_INFINITY,Y=Number.NEGATIVE_INFINITY,W=Number.NEGATIVE_INFINITY,q=0;q<h;q++){for(var X=l[q],z=X.units,Q=0,Z=0,$=z.length;Z<$;Z++){var J=z[Z],tt=J.data,J=J.data.byteLength;K.setUint32(f,J),f+=4,B.set(tt,f),f+=J,Q+=4+J}var nt,rt=void 0,it=(q<h-1?(g=l[q+1].dts-X.dts,rt=l[q+1].pts-X.pts):(it=this.config,nt=0<q?X.dts-l[q-1].dts:A,rt=0<q?X.pts-l[q-1].pts:A,it.stretchShortVideoTrack&&null!==this.nextAudioPts&&Math.floor(it.maxBufferHole*s)<(it=(i?v+i*s:this.nextAudioPts)-X.pts)?((g=it-nt)<0?g=nt:H=!0,b.log("[mp4-remuxer]: It is approximately "+it/90+" ms to the next segment; using duration "+g/90+" ms for the last video frame.")):g=nt),Math.round(X.pts-X.dts)),V=Math.min(V,g),Y=Math.max(Y,g),j=Math.min(j,rt),W=Math.max(W,rt);u.push(new Ii(X.key,g,Q,it))}if(u.length)if(Si)Si<70&&((d=u[0].flags).dependsOn=2,d.isNonSync=0);else if(Li&&W-j<Y-V&&A/Y<.025&&0===u[0].cts){b.warn("Found irregular gaps in sample duration. Using PTS instead of DTS to determine MP4 sample duration.");for(var ut=n,ht=0,dt=u.length;ht<dt;ht++){var gt,ct=ut+u[ht].duration,ft=ut+u[ht].cts;ht<dt-1?(gt=ct+u[ht+1].cts,u[ht].duration=gt-ft):u[ht].duration=ht?u[ht-1].duration:A,u[ht].cts=0,ut=ct}}this.nextAvcDts=c=e+(g=H||!g?A:g),this.videoSampleDuration=g,this.isVideoContiguous=!0;r={data1:yi.moof(t.sequenceNumber++,n,o({},t,{samples:u})),data2:B,startPTS:v/s,endPTS:(m+g)/s,startDTS:n/s,endDTS:c/s,type:"video",hasAudio:!1,hasVideo:!0,nb:u.length,dropped:t.dropped};return t.samples=[],t.dropped=0,r},e.remuxAudio=function(t,e,r,i,n){var a=t.inputTimeScale,s=a/(t.samplerate||a),l="aac"===t.segmentCodec?1024:1152,u=l*s,h=this._initPTS,d="mp3"===t.segmentCodec&&this.typeSupported.mpeg,c=[],f=void 0!==n,g=t.samples,v=d?0:8,m=this.nextAudioPts||-1,p=e*a,y=h.baseTime*a/h.timescale;if(this.isAudioContiguous=r=r||g.length&&0<m&&(i&&Math.abs(p-m)<9e3||Math.abs(ki(g[0].pts-y,p)-m)<20*u),g.forEach(function(t){t.pts=ki(t.pts-y,p)}),!r||m<0){if(!(g=g.filter(function(t){return 0<=t.pts})).length)return;m=0===n?0:i&&!f?Math.max(0,p):g[0].pts}if("aac"===t.segmentCodec)for(var L=this.config.maxAudioFramesDrift,R=0,k=m;R<g.length;R++){var A=g[R],D=A.pts,I=D-k,_=Math.abs(1e3*I/a);if(I<=-L*u&&f)0===R&&(b.warn("Audio frame @ "+(D/a).toFixed(3)+"s overlaps nextAudioPts by "+Math.round(1e3*I/a)+" ms."),this.nextAudioPts=m=k=D);else if(L*u<=I&&_<1e4&&f){var w=Math.round(I/u);(k=D-w*u)<0&&(w--,k+=u),0===R&&(this.nextAudioPts=m=k),b.warn("[mp4-remuxer]: Injecting "+w+" audio frame @ "+(k/a).toFixed(3)+"s due to "+Math.round(1e3*I/a)+" ms gap.");for(var C=0;C<w;C++){var x=Math.max(k,0),P=mi.getSilentFrame(t.manifestCodec||t.codec,t.channelCount);P||(b.log("[mp4-remuxer]: Unable to get silent frame for given audio codec; duplicating last frame instead."),P=A.unit.subarray()),g.splice(R,0,{unit:P,pts:x}),k+=u,R++}}A.pts=k,k+=u}for(var O,M=null,F=null,N=0,U=g.length;U--;)N+=g[U].unit.byteLength;for(var B=0,G=g.length;B<G;B++){var K=g[B],H=K.unit,K=K.pts;if(null!==F)c[B-1].duration=Math.round((K-F)/s);else{if(M=K=r&&"aac"===t.segmentCodec?m:K,!(0<N))return;N+=v;try{O=new Uint8Array(N)}catch(t){return void this.observer.emit(T.ERROR,T.ERROR,{type:E.MUX_ERROR,details:S.REMUX_ALLOC_ERROR,fatal:!1,error:t,bytes:N,reason:"fail allocating audio mdat "+N})}d||(new DataView(O.buffer).setUint32(0,N),O.set(yi.types.mdat,4))}O.set(H,v);H=H.byteLength;v+=H,c.push(new Ii(!0,l,H,0)),F=K}e=c.length;if(e)return this.nextAudioPts=m=F+s*c[c.length-1].duration,i={data1:d?new Uint8Array(0):yi.moof(t.sequenceNumber++,M/s,o({},t,{samples:c})),data2:O,startPTS:h=M/a,endPTS:n=m/a,startDTS:h,endDTS:n,type:"audio",hasAudio:!0,hasVideo:!(t.samples=[]),nb:e},this.isAudioContiguous=!0,i},e.remuxEmptyAudio=function(t,e,r,i){var n=t.inputTimeScale,a=n/(t.samplerate||n),s=this.nextAudioPts,o=this._initDTS,o=9e4*o.baseTime/o.timescale,u=(null!==s?s:i.startDTS*n)+o,s=i.endDTS*n+o,d=1024*a,c=Math.ceil((s-u)/d),f=mi.getSilentFrame(t.manifestCodec||t.codec,t.channelCount);if(b.warn("[mp4-remuxer]: remux empty Audio"),f){for(var g=[],v=0;v<c;v++){var m=u+v*d;g.push({unit:f,pts:m,dts:m})}return t.samples=g,this.remuxAudio(t,e,r,!1)}b.trace("[mp4-remuxer]: Unable to remuxEmptyAudio since we were unable to get a silent frame for given audio codec")},t}();function ki(t,e){var r;if(null!==e)for(r=e<t?-8589934592:8589934592;4294967296<Math.abs(t-e);)t+=r;return t}function Ai(t,e,r,i){var n=t.samples.length;if(n){for(var a=t.inputTimeScale,s=0;s<n;s++){var o=t.samples[s];o.pts=ki(o.pts-9e4*r.baseTime/r.timescale,e*a)/a,o.dts=ki(o.dts-9e4*i.baseTime/i.timescale,e*a)/a}var l=t.samples;return t.samples=[],{samples:l}}}function bi(t,e,r){var i=t.samples.length;if(i){for(var n=t.inputTimeScale,a=0;a<i;a++){var s=t.samples[a];s.pts=ki(s.pts-9e4*r.baseTime/r.timescale,e*n)/n}t.samples.sort(function(t,e){return t.pts-e.pts});var o=t.samples;return t.samples=[],{samples:o}}}var Di,Ii=function(t,e,r,i){this.size=void 0,this.duration=void 0,this.cts=void 0,this.flags=void 0,this.duration=e,this.size=r,this.cts=i,this.flags=new _i(t)},_i=function(t){this.isLeading=0,this.isDependedOn=0,this.hasRedundancy=0,this.degradPrio=0,this.dependsOn=1,this.isNonSync=1,this.dependsOn=t?2:1,this.isNonSync=t?0:1},wi=function(){function t(){this.emitInitSegment=!1,this.audioCodec=void 0,this.videoCodec=void 0,this.initData=void 0,this.initPTS=null,this.initTracks=void 0,this.lastEndTime=null}var e=t.prototype;return e.destroy=function(){},e.resetTimeStamp=function(t){this.initPTS=t,this.lastEndTime=null},e.resetNextTimestamp=function(){this.lastEndTime=null},e.resetInitSegment=function(t,e,r,i){this.audioCodec=e,this.videoCodec=r,this.generateInitSegment(function(t){var r;return t&&i&&(r=i.keyId)&&i.isCommonEncryption&&Rt(t,["moov","trak"]).forEach(function(t){var t=Rt(t,["mdia","minf","stbl","stsd"])[0].subarray(8),i=Rt(t,["enca"]),n=0<i.length;(i=n?i:Rt(t,["encv"])).forEach(function(t){Rt(n?t.subarray(28):t.subarray(78),["sinf"]).forEach(function(t){var i,t=bt(t);t&&!(i=t.subarray(8,24)).some(function(t){return 0!==t})&&(b.log("[eme] Patching keyId in 'enc"+(n?"a":"v")+">sinf>>tenc' box: "+gt(i)+" -> "+gt(r)),t.set(r,8))})})}),t}(t)),this.emitInitSegment=!0},e.generateInitSegment=function(t){var i,n,e=this.audioCodec,r=this.videoCodec;null!=t&&t.byteLength?(i=this.initData=At(t),e=e||Ci(i.audio,P),r=r||Ci(i.video,O),n={},i.audio&&i.video?n.audiovideo={container:"video/mp4",codec:e+","+r,initSegment:t,id:"main"}:i.audio?n.audio={container:"audio/mp4",codec:e,initSegment:t,id:"audio"}:i.video?n.video={container:"video/mp4",codec:r,initSegment:t,id:"main"}:b.warn("[passthrough-remuxer.ts]: initSegment does not contain moov or trak boxes."),this.initTracks=n):(this.initTracks=void 0,this.initData=void 0)},e.remux=function(t,e,r,i,n,a){var g,f,v,l=this.initPTS,u=this.lastEndTime,h={audio:void 0,video:void 0,text:i,id3:r,initSegment:void 0},d=(y(u)||(u=this.lastEndTime=n||0),e.samples);return null!=d&&d.length&&(e={initPTS:void 0,timescale:1},null!=(f=this.initData)&&f.length||(this.generateInitSegment(d),f=this.initData),null!=f&&f.length?(this.emitInitSegment&&(e.tracks=this.initTracks,this.emitInitSegment=!1),g=function(t,e){for(var r=0,i=0,n=0,a=Rt(t,["moof","traf"]),s=0;s<a.length;s++){var o=a[s],l=Rt(o,["tfhd"])[0],u=e[Et(l,4)];if(u){var h=u.default,d=Et(l,0)|(null==h?void 0:h.flags),c=null==h?void 0:h.duration;8&d&&(c=Et(l,2&d?12:8));for(var f=u.timescale||9e4,g=Rt(o,["trun"]),v=0;v<g.length;v++)!(r=function(t){var e=Et(t,0),r=8;1&e&&(r+=4),4&e&&(r+=4);for(var i=0,n=Et(t,4),a=0;a<n;a++)256&e&&(i+=Et(t,r),r+=4),512&e&&(r+=4),1024&e&&(r+=4),2048&e&&(r+=4);return i}(g[v]))&&c&&(r=c*Et(g[v],4)),u.type===O?i+=r/f:u.type===P&&(n+=r/f)}}if(0!==i||0!==n)return i||n;for(var m=0,p=Rt(t,["sidx"]),y=0;y<p.length;y++){var T=function(t){var e=[],r=t[0],i=8,n=Et(t,i),a=t.length+0,s=Tt(t,i=(i+=4)+(0===r?8:16)+2);i+=2;for(var o=0;o<s;o++){var l=i,u=Et(t,l),h=(l+=4,2147483647&u);if(1==(2147483648&u)>>>31)return b.warn("SIDX has hierarchical references (not supported)"),null;u=Et(t,l);l+=4,e.push({referenceSize:h,subsegmentDuration:u,info:{duration:u/n,start:a,end:a+h-1}}),a+=h,i=l+=4}return{earliestPresentationTime:0,timescale:n,version:r,referencesCount:s,references:e}}(p[y]);null!=T&&T.references&&(m+=T.references.reduce(function(t,e){return t+e.info.duration||0},0))}return m}(d,f),(function(t,e,r){var n;return null===t||(n=Math.max(g,1),e=e-t.baseTime/t.timescale,Math.abs(e-r)>n)}(l,v=null===(v=function(t){return Rt(d,["moof","traf"]).reduce(function(e,r){var i=Rt(r,["tfdt"])[0],n=i[0],r=Rt(r,["tfhd"]).reduce(function(e,r){r=Et(r,4),r=t[r];if(r){var o=Et(i,4);if(1===n){if(o===vt)return b.warn("[mp4-demuxer]: Ignoring assumed invalid signed 64-bit track fragment decode time"),e;o=o*(1+vt)+Et(i,8)}o=o/(r.timescale||9e4);if(isFinite(o)&&(null===e||o<e))return o}return e},null);return null!==r&&isFinite(r)&&(null===e||r<e)?r:e},null)}(f))?n:v,n)||e.timescale!==l.timescale&&a)&&(e.initPTS=v-n,l&&1===l.timescale&&b.warn("Adjusting initPTS by "+(e.initPTS-l.baseTime)),this.initPTS=l={baseTime:e.initPTS,timescale:1}),t=(a=t?v-l.baseTime/l.timescale:u)+g,function(t,r){Rt(d,["moof","traf"]).forEach(function(e){Rt(e,["tfhd"]).forEach(function(i){var s,i=Et(i,4),i=t[i];i&&(s=i.timescale||9e4,Rt(e,["tfdt"]).forEach(function(t){var e=t[0],i=Et(t,4);0===e?(i-=r*s,Lt(t,4,i=Math.max(i,0))):(i=(i=(i*=Math.pow(2,32))+Et(t,8))-r*s,i=Math.max(i,0),e=Math.floor(i/(1+vt)),i=Math.floor(i%(1+vt)),Lt(t,4,e),Lt(t,8,i))}))})})}(f,l.baseTime/l.timescale),0<g?this.lastEndTime=t:(b.warn("Duration parsed from mp4 should be greater than zero"),this.resetNextTimestamp()),v="",(u=!!f.audio)&&(v+="audio"),(f=!!f.video)&&(v+="video"),h.audio="audio"===(a={data1:d,startPTS:a,startDTS:a,endPTS:t,endDTS:t,type:v,hasAudio:u,hasVideo:f,nb:1,dropped:0}).type?a:void 0,h.video="audio"!==a.type?a:void 0,h.initSegment=e,h.id3=Ai(r,n,l,l),i.samples.length&&(h.text=bi(i,n,l))):b.warn("[passthrough-remuxer.ts]: Failed to generate initSegment.")),h},t}();function Ci(t,e){t=null==t?void 0:t.codec;return t&&4<t.length?t:"hvc1"===t||"hev1"===t?"hvc1.1.6.L120.90":"av01"===t?"av01.0.04M.08":"avc1"===t||e===O?"avc1.42e01e":"mp4a.40.5"}try{Di=self.performance.now.bind(self.performance)}catch(t){b.debug("Unable to use Performance API on this environment"),Di="undefined"!=typeof self&&self.Date.now}var xi=[{demux:Xr,remux:wi},{demux:li,remux:Ri},{demux:Wr,remux:Ri},{demux:Fr,remux:Ri}],Pi=function(){function t(t,e,r,i,n){this.async=!1,this.observer=void 0,this.typeSupported=void 0,this.config=void 0,this.vendor=void 0,this.id=void 0,this.demuxer=void 0,this.remuxer=void 0,this.decrypter=void 0,this.probe=void 0,this.decryptionPromise=null,this.transmuxConfig=void 0,this.currentTransmuxState=void 0,this.observer=t,this.typeSupported=e,this.config=r,this.vendor=i,this.id=n}var e=t.prototype;return e.configure=function(t){this.transmuxConfig=t,this.decrypter&&this.decrypter.reset()},e.push=function(t,e,r,i){var n=this,a=r.transmuxing,s=(a.executeStart=Di(),new Uint8Array(t)),t=this.currentTransmuxState,l=this.transmuxConfig,i=(i&&(this.currentTransmuxState=i),i||t),t=i.contiguous,d=i.discontinuity,c=i.trackSwitch,f=i.accurateTimeOffset,g=i.timeOffset,i=i.initSegmentChange,m=l.audioCodec,p=l.videoCodec,y=l.defaultInitPts,L=l.duration,l=l.initSegmentData,k=function(e){var r=null;return r=0<s.byteLength&&null!=e&&null!=e.key&&null!==e.iv&&null!=e.method?e:r}(e);if(k&&"AES-128"===k.method){var A=this.getDecrypter();if(!A.isSync())return this.decryptionPromise=A.webCryptoDecrypt(s,k.key.buffer,k.iv.buffer).then(function(t){t=n.push(t,null,r);return n.decryptionPromise=null,t}),this.decryptionPromise;var D=A.softwareDecrypt(s,k.key.buffer,k.iv.buffer);if(!(D=-1<r.part?A.flush():D))return a.executeEnd=Di(),Oi(r);s=new Uint8Array(D)}A=this.needsProbing(d,c);if(A){D=this.configureTransmuxer(s);if(D)return b.warn("[transmuxer] "+D.message),this.observer.emit(T.ERROR,T.ERROR,{type:E.MEDIA_ERROR,details:S.FRAG_PARSING_ERROR,fatal:!1,error:D,reason:D.message}),a.executeEnd=Di(),Oi(r)}(d||c||i||A)&&this.resetInitSegment(l,m,p,L,e),(d||i||A)&&this.resetInitialTimestamp(y),t||this.resetContiguity();D=this.transmux(s,k,g,f,r),c=this.currentTransmuxState;return c.contiguous=!0,c.discontinuity=!1,c.trackSwitch=!1,a.executeEnd=Di(),D},e.flush=function(t){var s,e=this,r=t.transmuxing,i=(r.executeStart=Di(),this.decrypter),n=this.currentTransmuxState,a=this.decryptionPromise;return a?a.then(function(){return e.flush(t)}):(s=[],a=n.timeOffset,i&&(n=i.flush())&&s.push(this.push(n,null,t)),i=this.demuxer,n=this.remuxer,i&&n?Mi(n=i.flush(a))?n.then(function(r){return e.flushRemux(s,r,t),s}):(this.flushRemux(s,n,t),s):(r.executeEnd=Di(),[Oi(t)]))},e.flushRemux=function(t,e,r){var i=e.audioTrack,n=e.videoTrack,a=e.id3Track,e=e.textTrack,o=this.currentTransmuxState,l=o.accurateTimeOffset,o=o.timeOffset,i=(b.log("[transmuxer.ts]: Flushed fragment "+r.sn+(-1<r.part?" p: "+r.part:"")+" of level "+r.level),this.remuxer.remux(i,n,a,e,o,l,!0,this.id));t.push({remuxResult:i,chunkMeta:r}),r.transmuxing.executeEnd=Di()},e.resetInitialTimestamp=function(t){var e=this.demuxer,r=this.remuxer;e&&r&&(e.resetTimeStamp(t),r.resetTimeStamp(t))},e.resetContiguity=function(){var t=this.demuxer,e=this.remuxer;t&&e&&(t.resetContiguity(),e.resetNextTimestamp())},e.resetInitSegment=function(t,e,r,i,n){var a=this.demuxer,s=this.remuxer;a&&s&&(a.resetInitSegment(t,e,r,i),s.resetInitSegment(t,e,r,n))},e.destroy=function(){this.demuxer&&(this.demuxer.destroy(),this.demuxer=void 0),this.remuxer&&(this.remuxer.destroy(),this.remuxer=void 0)},e.transmux=function(t,e,r,i,n){return e&&"SAMPLE-AES"===e.method?this.transmuxSampleAes(t,e,r,i,n):this.transmuxUnencrypted(t,r,i,n)},e.transmuxUnencrypted=function(t,e,r,i){var t=this.demuxer.demux(t,e,!1,!this.config.progressive),a=t.audioTrack,s=t.videoTrack,o=t.id3Track,t=t.textTrack;return{remuxResult:this.remuxer.remux(a,s,o,t,e,r,!1,this.id),chunkMeta:i}},e.transmuxSampleAes=function(t,e,r,i,n){var a=this;return this.demuxer.demuxSampleAes(t,e,r).then(function(t){return{remuxResult:a.remuxer.remux(t.audioTrack,t.videoTrack,t.id3Track,t.textTrack,r,i,!1,a.id),chunkMeta:n}})},e.configureTransmuxer=function(t){for(var e,r=this.config,i=this.observer,n=this.typeSupported,a=this.vendor,s=0,o=xi.length;s<o;s++)if(xi[s].demux.probe(t)){e=xi[s];break}if(!e)return new Error("Failed to find demuxer by probing fragment data");var l=this.demuxer,u=this.remuxer,h=e.remux,d=e.demux;u&&u instanceof h||(this.remuxer=new h(i,r,n,a)),l&&l instanceof d||(this.demuxer=new d(i,r,n),this.probe=d.probe)},e.needsProbing=function(t,e){return!this.demuxer||!this.remuxer||t||e},e.getDecrypter=function(){return this.decrypter||(this.decrypter=new Er(this.config))},t}(),Oi=function(t){return{remuxResult:{},chunkMeta:t}};function Mi(t){return"then"in t&&t.then instanceof Function}function Fi(t,e,r,i,n){this.audioCodec=void 0,this.videoCodec=void 0,this.initSegmentData=void 0,this.duration=void 0,this.defaultInitPts=void 0,this.audioCodec=t,this.videoCodec=e,this.initSegmentData=r,this.duration=i,this.defaultInitPts=n||null}function Ni(t,e,r,i,n,a){this.discontinuity=void 0,this.contiguous=void 0,this.accurateTimeOffset=void 0,this.trackSwitch=void 0,this.timeOffset=void 0,this.initSegmentChange=void 0,this.discontinuity=t,this.contiguous=e,this.accurateTimeOffset=r,this.trackSwitch=i,this.timeOffset=n,this.initSegmentChange=a}var Xr={exports:{}},Bi=(!function(t){var e=Object.prototype.hasOwnProperty,r="~";function i(){}function n(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function a(t,e,i,a,s){if("function"!=typeof i)throw new TypeError("The listener must be a function");i=new n(i,a||t,s),a=r?r+e:e;return t._events[a]?t._events[a].fn?t._events[a]=[t._events[a],i]:t._events[a].push(i):(t._events[a]=i,t._eventsCount++),t}function s(t,e){0==--t._eventsCount?t._events=new i:delete t._events[e]}function o(){this._events=new i,this._eventsCount=0}Object.create&&(i.prototype=Object.create(null),(new i).__proto__||(r=!1)),o.prototype.eventNames=function(){var t,i,n=[];if(0===this._eventsCount)return n;for(i in t=this._events)e.call(t,i)&&n.push(r?i.slice(1):i);return Object.getOwnPropertySymbols?n.concat(Object.getOwnPropertySymbols(t)):n},o.prototype.listeners=function(t){var t=r?r+t:t,i=this._events[t];if(!i)return[];if(i.fn)return[i.fn];for(var n=0,a=i.length,s=new Array(a);n<a;n++)s[n]=i[n].fn;return s},o.prototype.listenerCount=function(t){t=r?r+t:t,t=this._events[t];return t?t.fn?1:t.length:0},o.prototype.emit=function(t,e,i,n,a,s){var o=r?r+t:t;if(!this._events[o])return!1;var l,h=this._events[o],d=arguments.length;if(h.fn){switch(h.once&&this.removeListener(t,h.fn,void 0,!0),d){case 1:return h.fn.call(h.context),!0;case 2:return h.fn.call(h.context,e),!0;case 3:return h.fn.call(h.context,e,i),!0;case 4:return h.fn.call(h.context,e,i,n),!0;case 5:return h.fn.call(h.context,e,i,n,a),!0;case 6:return h.fn.call(h.context,e,i,n,a,s),!0}for(u=1,l=new Array(d-1);u<d;u++)l[u-1]=arguments[u];h.fn.apply(h.context,l)}else for(var c,f=h.length,u=0;u<f;u++)switch(h[u].once&&this.removeListener(t,h[u].fn,void 0,!0),d){case 1:h[u].fn.call(h[u].context);break;case 2:h[u].fn.call(h[u].context,e);break;case 3:h[u].fn.call(h[u].context,e,i);break;case 4:h[u].fn.call(h[u].context,e,i,n);break;default:if(!l)for(c=1,l=new Array(d-1);c<d;c++)l[c-1]=arguments[c];h[u].fn.apply(h[u].context,l)}return!0},o.prototype.on=function(t,e,r){return a(this,t,e,r,!1)},o.prototype.once=function(t,e,r){return a(this,t,e,r,!0)},o.prototype.removeListener=function(t,e,i,n){t=r?r+t:t;if(this._events[t])if(e){var o=this._events[t];if(o.fn)o.fn!==e||n&&!o.once||i&&o.context!==i||s(this,t);else{for(var l=0,u=[],h=o.length;l<h;l++)(o[l].fn!==e||n&&!o[l].once||i&&o[l].context!==i)&&u.push(o[l]);u.length?this._events[t]=1===u.length?u[0]:u:s(this,t)}}else s(this,t);return this},o.prototype.removeAllListeners=function(t){return t?(t=r?r+t:t,this._events[t]&&s(this,t)):(this._events=new i,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,t.exports=o.EventEmitter=o}(Xr),function(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}(Xr.exports));function Gi(t,e){var r,a,n;return!!((r=e.remuxResult).audio||r.video||r.text||r.id3||r.initSegment)&&(r=[],a=(n=e.remuxResult).audio,n=n.video,a&&Ki(r,a),n&&Ki(r,n),t.postMessage({event:"transmuxComplete",data:e},r),!0)}function Ki(t,e){e.data1&&t.push(e.data1.buffer),e.data2&&t.push(e.data2.buffer)}function Hi(t,e,r){e.reduce(function(e,r){return Gi(t,r)||e},!1)||t.postMessage({event:"transmuxComplete",data:e[0]}),t.postMessage({event:"flush",data:r})}void 0!==i&&i&&function(t){function r(e,r){t.postMessage({event:e,data:r})}var e=new Bi;e.on(T.FRAG_DECRYPTED,r),e.on(T.ERROR,r),t.addEventListener("message",function(i){var n=i.data;switch(n.cmd){case"init":var a=JSON.parse(n.config);t.transmuxer=new Pi(e,n.typeSupported,a,n.vendor,n.id),A(a.debug,n.id),function(){for(var e in b)!function(t){b[t]=function(e){r("workerLog",{logType:t,message:e})}}(e)}(),r("init",null);break;case"configure":t.transmuxer.configure(n.config);break;case"demux":a=t.transmuxer.push(n.data,n.decryptdata,n.chunkMeta,n.state);Mi(a)?(t.transmuxer.async=!0,a.then(function(e){Gi(t,e)}).catch(function(t){r(T.ERROR,{type:E.MEDIA_ERROR,details:S.FRAG_PARSING_ERROR,chunkMeta:n.chunkMeta,fatal:!1,error:t,err:t,reason:"transmuxer-worker push error"})})):(t.transmuxer.async=!1,Gi(t,a));break;case"flush":var o=n.chunkMeta,a=t.transmuxer.flush(o);Mi(a)||t.transmuxer.async?(a=Mi(a)?a:Promise.resolve(a)).then(function(e){Hi(t,e,o)}).catch(function(t){r(T.ERROR,{type:E.MEDIA_ERROR,details:S.FRAG_PARSING_ERROR,chunkMeta:n.chunkMeta,fatal:!1,error:t,err:t,reason:"transmuxer-worker flush error"})}):Hi(t,a,o)}})}(self);var Vi=Kt()||{isTypeSupported:function(){return!1}},ji=function(){function t(t,e,i,n){function o(t,e){(e=e||{}).frag=a.frag,e.id=a.id,t===T.ERROR&&(a.error=e.error),a.hls.trigger(t,e)}var a=this,s=(this.error=null,this.hls=void 0,this.id=void 0,this.observer=void 0,this.frag=null,this.part=null,this.useWorker=void 0,this.workerContext=null,this.onwmsg=void 0,this.transmuxer=null,this.onTransmuxComplete=void 0,this.onFlush=void 0,t.config);this.hls=t,this.id=e,this.useWorker=!!s.enableWorker,this.onTransmuxComplete=i,this.onFlush=n;this.observer=new Bi,this.observer.on(T.FRAG_DECRYPTED,o),this.observer.on(T.ERROR,o);var l,u,h,d,i={mp4:Vi.isTypeSupported("video/mp4"),mpeg:Vi.isTypeSupported("audio/mpeg"),mp3:Vi.isTypeSupported('audio/mp4; codecs="mp3"')},n=navigator.vendor;if(!this.useWorker||"undefined"==typeof Worker||(s.workerPath,0))this.transmuxer=new Pi(this.observer,i,s,n,e);else try{s.workerPath?(b.log("loading Web Worker "+s.workerPath+' for "'+e+'"'),this.workerContext=(h=s.workerPath,d=new self.URL(h,self.location.href).href,{worker:new self.Worker(d),scriptURL:d})):(b.log('injecting Web Worker for "'+e+'"'),this.workerContext=(l=new self.Blob(["var exports={};var module={exports:exports};function define(f){f()};define.amd=true;("+r.toString()+")(true);"],{type:"text/javascript"}),u=self.URL.createObjectURL(l),{worker:new self.Worker(u),objectURL:u})),this.onwmsg=function(t){return a.onWorkerMessage(t)};var g=this.workerContext.worker;g.addEventListener("message",this.onwmsg),g.onerror=function(t){t=new Error(t.message+"  ("+t.filename+":"+t.lineno+")");s.enableWorker=!1,b.warn('Error in "'+e+'" Web Worker, fallback to inline'),a.hls.trigger(T.ERROR,{type:E.OTHER_ERROR,details:S.INTERNAL_EXCEPTION,fatal:!1,event:"demuxerWorker",error:t})},g.postMessage({cmd:"init",typeSupported:i,vendor:n,id:e,config:JSON.stringify(s)})}catch(t){b.warn('Error setting up "'+e+'" Web Worker, fallback to inline',t),this.resetWorker(),this.error=null,this.transmuxer=new Pi(this.observer,i,s,n,e)}}var e=t.prototype;return e.resetWorker=function(){var e,t;this.workerContext&&(e=(t=this.workerContext).worker,(t=t.objectURL)&&self.URL.revokeObjectURL(t),e.removeEventListener("message",this.onwmsg),e.onerror=null,e.terminate(),this.workerContext=null)},e.destroy=function(){this.workerContext?(this.resetWorker(),this.onwmsg=void 0):(t=this.transmuxer)&&(t.destroy(),this.transmuxer=null);var t=this.observer;t&&t.removeAllListeners(),this.frag=null,this.observer=null,this.hls=null},e.push=function(t,e,r,i,n,a,s,o,l,u){var c=this,f=(l.transmuxing.start=self.performance.now(),this.transmuxer),g=(a||n).start,v=n.decryptdata,m=this.frag,p=!(m&&n.cc===m.cc),y=!(m&&l.level===m.level),T=m?l.sn-m.sn:-1,E=this.part?l.part-this.part.index:-1,S=0==T&&1<l.id&&l.id===(null==m?void 0:m.stats.chunkCount),S=!y&&(1==T||0==T&&(1==E||S&&E<=0)),R=self.performance.now(),R=((y||T||0===n.stats.parsing.start)&&(n.stats.parsing.start=R),!a||!E&&S||(a.stats.parsing.start=R),!(m&&(null==(T=n.initSegment)?void 0:T.url)===(null==(E=m.initSegment)?void 0:E.url))),T=new Ni(p,S,o,y,g,R);S&&!p&&!R||(b.log("[transmuxer-interface, "+n.type+"]: Starting new transmux session for sn: "+l.sn+" p: "+l.part+" level: "+l.level+" id: "+l.id+"\n        discontinuity: "+p+"\n        trackSwitch: "+y+"\n        contiguous: "+S+"\n        accurateTimeOffset: "+o+"\n        timeOffset: "+g+"\n        initSegmentChange: "+R),m=new Fi(r,i,e,s,u),this.configureTransmuxer(m)),this.frag=n,this.part=a,this.workerContext?this.workerContext.worker.postMessage({cmd:"demux",data:t,decryptdata:v,chunkMeta:l,state:T},t instanceof ArrayBuffer?[t]:[]):f&&(Mi(E=f.push(t,v,l,T))?(f.async=!0,E.then(function(t){c.handleTransmuxComplete(t)}).catch(function(t){c.transmuxerError(t,l,"transmuxer-interface push error")})):(f.async=!1,this.handleTransmuxComplete(E)))},e.flush=function(t){var i,e=this,r=(t.transmuxing.start=self.performance.now(),this.transmuxer);this.workerContext?this.workerContext.worker.postMessage({cmd:"flush",chunkMeta:t}):r&&(Mi(i=r.flush(t))||r.async?(i=Mi(i)?i:Promise.resolve(i)).then(function(r){e.handleFlushResult(r,t)}).catch(function(r){e.transmuxerError(r,t,"transmuxer-interface flush error")}):this.handleFlushResult(i,t))},e.transmuxerError=function(t,e,r){this.hls&&(this.error=t,this.hls.trigger(T.ERROR,{type:E.MEDIA_ERROR,details:S.FRAG_PARSING_ERROR,chunkMeta:e,fatal:!1,error:t,err:t,reason:r}))},e.handleFlushResult=function(t,e){var r=this;t.forEach(function(t){r.handleTransmuxComplete(t)}),this.onFlush(e)},e.onWorkerMessage=function(t){var e=t.data,r=this.hls;switch(e.event){case"init":var i=null==(i=this.workerContext)?void 0:i.objectURL;i&&self.URL.revokeObjectURL(i);break;case"transmuxComplete":this.handleTransmuxComplete(e.data);break;case"flush":this.onFlush(e.data);break;case"workerLog":b[e.data.logType]&&b[e.data.logType](e.data.message);break;default:e.data=e.data||{},e.data.frag=this.frag,e.data.id=this.id,r.trigger(e.event,e.data)}},e.configureTransmuxer=function(t){var e=this.transmuxer;this.workerContext?this.workerContext.worker.postMessage({cmd:"configure",config:t}):e&&e.configure(t)},e.handleTransmuxComplete=function(t){t.chunkMeta.transmuxing.end=self.performance.now(),this.onTransmuxComplete(t)},t}(),Yi=function(){function t(t,e,r,i){this.config=void 0,this.media=null,this.fragmentTracker=void 0,this.hls=void 0,this.nudgeRetry=0,this.stallReported=!1,this.stalled=null,this.moved=!1,this.seeking=!1,this.config=t,this.media=e,this.fragmentTracker=r,this.hls=i}var e=t.prototype;return e.destroy=function(){this.media=null,this.hls=this.fragmentTracker=null},e.poll=function(t,e){var r=this.config,i=this.media,n=this.stalled;if(null!==i){var a=i.currentTime,s=i.seeking,o=this.seeking&&!s,l=!this.seeking&&s;if(this.seeking=s,a===t){if(l||o)this.stalled=null;else if(!(i.paused&&!s||i.ended||0===i.playbackRate)&&dr.getBuffered(i).length){t=dr.bufferInfo(i,a,0),l=0<t.len,o=t.nextStart||0;if(l||o){if(s){l=2<t.len,e=!o||e&&e.start<=a||2<o-a&&!this.fragmentTracker.getPartialFragment(a);if(l||e)return;this.moved=!1}if(!this.moved&&null!==this.stalled){l=Math.max(o,t.start||0)-a,e=this.hls.levels?this.hls.levels[this.hls.currentLevel]:null,o=null!=e&&null!=(o=e.details)&&o.live?2*e.details.targetduration:2,e=this.fragmentTracker.getPartialFragment(a);if(0<l&&(l<=o||e))return void this._trySkipBufferHole(e)}l=self.performance.now();null!==n?(o=l-n,!s&&250<=o&&(this._reportStall(t),!this.media)||(e=dr.bufferInfo(i,a,r.maxBufferHole),this._tryFixBufferStall(e,o))):this.stalled=l}}}else this.moved=!0,null!==n&&(this.stallReported&&(s=self.performance.now()-n,b.warn("playback not stuck anymore @"+a+", after "+Math.round(s)+"ms"),this.stallReported=!1),this.stalled=null,this.nudgeRetry=0)}},e._tryFixBufferStall=function(t,e){var r=this.config,i=this.fragmentTracker,n=this.media;null!==n&&(n=n.currentTime,!(i=i.getPartialFragment(n))||!this._trySkipBufferHole(i)&&this.media)&&(t.len>r.maxBufferHole||t.nextStart&&t.nextStart-n<r.maxBufferHole)&&e>1e3*r.highBufferWatchdogPeriod&&(b.warn("Trying to nudge playhead over buffer-hole"),this.stalled=null,this._tryNudgeBuffer())},e._reportStall=function(t){var e=this.hls,r=this.media;!this.stallReported&&r&&(this.stallReported=!0,r=new Error("Playback stalling at @"+r.currentTime+" due to low buffer ("+JSON.stringify(t)+")"),b.warn(r.message),e.trigger(T.ERROR,{type:E.MEDIA_ERROR,details:S.BUFFER_STALLED_ERROR,fatal:!1,error:r,buffer:t.len}))},e._trySkipBufferHole=function(t){var e=this.config,r=this.hls,i=this.media;if(null!==i){var n=i.currentTime,a=dr.bufferInfo(i,n,0),s=n<a.start?a.start:a.nextStart;if(s){var o=a.len<=e.maxBufferHole,a=0<a.len&&a.len<1&&i.readyState<3,u=s-n;if(0<u&&(o||a)){if(u>e.maxBufferHole){var h=this.fragmentTracker,o=!1;if(!(o=0===n&&(a=h.getAppendedFrag(0,le))&&s<a.end?!0:o)){u=t||h.getAppendedFrag(n,le);if(u){for(var g=!1,v=u.end;v<s;){var m=h.getPartialFragment(v);if(!m){g=!0;break}v+=m.duration}if(g)return 0}}}e=Math.max(s+.05,n+.1);return b.warn("skipping hole, adjusting currentTime from "+n+" to "+e),this.moved=!0,this.stalled=null,i.currentTime=e,t&&!t.gap&&(a=new Error("fragment loaded with buffer holes, seeking from "+n+" to "+e),r.trigger(T.ERROR,{type:E.MEDIA_ERROR,details:S.BUFFER_SEEK_OVER_HOLE,fatal:!1,error:a,reason:a.message,frag:t})),e}}}return 0},e._tryNudgeBuffer=function(){var n,s,t=this.config,e=this.hls,r=this.media,i=this.nudgeRetry;null!==r&&(n=r.currentTime,this.nudgeRetry++,i<t.nudgeMaxRetry?(i=n+(i+1)*t.nudgeOffset,s=new Error("Nudging 'currentTime' from "+n+" to "+i),b.warn(s.message),r.currentTime=i,e.trigger(T.ERROR,{type:E.MEDIA_ERROR,details:S.BUFFER_NUDGE_ON_STALL,error:s,fatal:!1})):(r=new Error("Playhead still not moving while enough data buffered @"+n+" after "+t.nudgeMaxRetry+" nudges"),b.error(r.message),e.trigger(T.ERROR,{type:E.MEDIA_ERROR,details:S.BUFFER_STALLED_ERROR,error:r,fatal:!0})))},t}(),Wi=function(t){function e(e,r,i){return(e=t.call(this,e,r,i,"[stream-controller]",le)||this).audioCodecSwap=!1,e.gapController=null,e.level=-1,e._forceStartLoad=!1,e.altAudio=!1,e.audioOnly=!1,e.fragPlaying=null,e.onvplaying=null,e.onvseeked=null,e.fragLastKbps=0,e.couldBacktrack=!1,e.backtrackFragment=null,e.audioCodecSwitch=!1,e.videoBuffer=null,e._registerListeners(),e}l(e,t);var r=e.prototype;return r._registerListeners=function(){var t=this.hls;t.on(T.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(T.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(T.MANIFEST_LOADING,this.onManifestLoading,this),t.on(T.MANIFEST_PARSED,this.onManifestParsed,this),t.on(T.LEVEL_LOADING,this.onLevelLoading,this),t.on(T.LEVEL_LOADED,this.onLevelLoaded,this),t.on(T.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),t.on(T.ERROR,this.onError,this),t.on(T.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),t.on(T.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),t.on(T.BUFFER_CREATED,this.onBufferCreated,this),t.on(T.BUFFER_FLUSHED,this.onBufferFlushed,this),t.on(T.LEVELS_UPDATED,this.onLevelsUpdated,this),t.on(T.FRAG_BUFFERED,this.onFragBuffered,this)},r._unregisterListeners=function(){var t=this.hls;t.off(T.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(T.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(T.MANIFEST_LOADING,this.onManifestLoading,this),t.off(T.MANIFEST_PARSED,this.onManifestParsed,this),t.off(T.LEVEL_LOADED,this.onLevelLoaded,this),t.off(T.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),t.off(T.ERROR,this.onError,this),t.off(T.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),t.off(T.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),t.off(T.BUFFER_CREATED,this.onBufferCreated,this),t.off(T.BUFFER_FLUSHED,this.onBufferFlushed,this),t.off(T.LEVELS_UPDATED,this.onLevelsUpdated,this),t.off(T.FRAG_BUFFERED,this.onFragBuffered,this)},r.onHandlerDestroying=function(){this._unregisterListeners(),this.onMediaDetaching()},r.startLoad=function(t){var e,r,i;this.levels?(e=this.lastCurrentTime,r=this.hls,this.stopLoad(),this.setInterval(100),this.level=-1,this.startFragRequested||(-1===(i=r.startLevel)&&(r.config.testBandwidth&&1<this.levels.length?this.bitrateTest=!(i=0):i=r.nextAutoLevel),this.level=r.nextLoadLevel=i,this.loadedmetadata=!1),0<e&&-1===t&&(this.log("Override startPosition with lastCurrentTime @"+e.toFixed(3)),t=e),this.state=Lr,this.nextLoadPosition=this.startPosition=this.lastCurrentTime=t,this.tick()):(this._forceStartLoad=!0,this.state=Sr)},r.stopLoad=function(){this._forceStartLoad=!1,t.prototype.stopLoad.call(this)},r.doTick=function(){switch(this.state){case xr:var e=this.levels,r=this.level,r=null==e||null==(e=e[r])?void 0:e.details;if(r&&(!r.live||this.levelLastLoaded===this.level)){if(this.waitForCdnTuneIn(r))break;this.state=Lr}break;case Ar:e=self.performance.now(),r=this.retryDate;(!r||r<=e||null!=(r=this.media)&&r.seeking)&&(this.resetStartWhenNotLoaded(this.level),this.state=Lr)}this.state===Lr&&this.doTickIdle(),this.onTickEnd()},r.onTickEnd=function(){t.prototype.onTickEnd.call(this),this.checkBuffer(),this.checkFragmentChanged()},r.doTickIdle=function(){var v,E,t=this.hls,e=this.levelLastLoaded,r=this.levels,i=this.media,n=t.config,a=t.nextLoadLevel;null===e||!(i||!this.startFragRequested&&n.startFragPrefetch)||this.altAudio&&this.audioOnly||null==r||!r[a]||(e=r[a],null!==(i=this.getMainFwdBufferInfo())&&((n=this.getLevelDetails())&&this._streamEnded(i,n)?(r={},this.altAudio&&(r.type="video"),this.hls.trigger(T.BUFFER_EOS,r),this.state=_r):(t.loadLevel!==a&&-1===t.manualLevel&&this.log("Adapting to level "+a+" from level "+this.level),this.level=t.nextLoadLevel=a,!(n=e.details)||this.state===xr||n.live&&this.levelLastLoaded!==a?(this.level=a,this.state=xr):(r=i.len,(t=this.getMaxBufferLength(e.maxBitrate))<=r||(this.backtrackFragment&&this.backtrackFragment.start>i.end&&(this.backtrackFragment=null),a=this.backtrackFragment?this.backtrackFragment.start:i.end,r=this.getNextFragment(a,n),this.couldBacktrack&&!this.fragPrevious&&r&&"initSegment"!==r.sn&&this.fragmentTracker.getState(r)!==Je?(v=(null!=(v=this.backtrackFragment)?v:r).sn-n.startSN,(v=n.fragments[v-1])&&r.cc===v.cc&&this.fragmentTracker.removeFragment(r=v)):this.backtrackFragment&&i.len&&(this.backtrackFragment=null),r&&this.isLoopLoading(r,a)&&(r.gap||(E=((v=this.audioOnly&&!this.altAudio?P:O)===O?this.videoBuffer:this.mediaBuffer)||this.media)&&this.afterBufferFlushed(E,v,le),r=this.getNextFragmentLoopLoading(r,n,i,le,t)),r&&(!r.initSegment||r.initSegment.data||this.bitrateTest||(r=r.initSegment),this.loadFragment(r,e,a)))))))},r.loadFragment=function(e,r,i){var n=this.fragmentTracker.getState(e);this.fragCurrent=e,n===Qe||n===$e?"initSegment"===e.sn?this._loadInitSegment(e,r):this.bitrateTest?(this.log("Fragment "+e.sn+" of level "+e.level+" is being downloaded to test bitrate and will not be buffered"),this._loadBitrateTestFrag(e,r)):(this.startFragRequested=!0,t.prototype.loadFragment.call(this,e,r,i)):this.clearTrackerIfNeeded(e)},r.getBufferedFrag=function(t){return this.fragmentTracker.getBufferedFrag(t,le)},r.followingBufferedFrag=function(t){return t?this.getBufferedFrag(t.end+.5):null},r.immediateLevelSwitch=function(){this.abortCurrentFrag(),this.flushMainBuffer(0,Number.POSITIVE_INFINITY)},r.nextLevelSwitch=function(){var t=this.levels,e=this.media;if(null!=e&&e.readyState){var i=this.getAppendedFrag(e.currentTime),i=(i&&1<i.start&&this.flushMainBuffer(0,i.start-1),this.getLevelDetails());if(null!=i&&i.live){var a=this.getMainFwdBufferInfo();if(!a||a.len<2*i.targetduration)return}t=!e.paused&&t&&(a=t[this.hls.nextLoadLevel],i=this.fragLastKbps)&&this.fragCurrent?this.fragCurrent.duration*a.maxBitrate/(1e3*i)+1:0;a=this.getBufferedFrag(e.currentTime+t);a&&(i=this.followingBufferedFrag(a))&&(this.abortCurrentFrag(),e=i.maxStartPTS||i.start,t=i.duration,i=Math.max(a.end,e+Math.min(Math.max(t-this.config.maxFragLookUpTolerance,.5*t),.75*t)),this.flushMainBuffer(i,Number.POSITIVE_INFINITY))}},r.abortCurrentFrag=function(){var t=this.fragCurrent;switch(this.fragCurrent=null,this.backtrackFragment=null,t&&(t.abortRequests(),this.fragmentTracker.removeFragment(t)),this.state){case Rr:case kr:case Ar:case Dr:case Ir:this.state=Lr}this.nextLoadPosition=this.getLoadPosition()},r.flushMainBuffer=function(e,r){t.prototype.flushMainBuffer.call(this,e,r,this.altAudio?"video":null)},r.onMediaAttached=function(e,r){t.prototype.onMediaAttached.call(this,e,r);e=r.media;this.onvplaying=this.onMediaPlaying.bind(this),this.onvseeked=this.onMediaSeeked.bind(this),e.addEventListener("playing",this.onvplaying),e.addEventListener("seeked",this.onvseeked),this.gapController=new Yi(this.config,e,this.fragmentTracker,this.hls)},r.onMediaDetaching=function(){var e=this.media;e&&this.onvplaying&&this.onvseeked&&(e.removeEventListener("playing",this.onvplaying),e.removeEventListener("seeked",this.onvseeked),this.onvplaying=this.onvseeked=null,this.videoBuffer=null),this.fragPlaying=null,this.gapController&&(this.gapController.destroy(),this.gapController=null),t.prototype.onMediaDetaching.call(this)},r.onMediaPlaying=function(){this.tick()},r.onMediaSeeked=function(){var t=this.media,t=t?t.currentTime:null,t=(y(t)&&this.log("Media seeked to "+t.toFixed(3)),this.getMainFwdBufferInfo());null!==t&&0!==t.len?this.tick():this.warn('Main forward buffer length on "seeked" event '+(t?t.len:"empty")+")")},r.onManifestLoading=function(){this.log("Trigger BUFFER_RESET"),this.hls.trigger(T.BUFFER_RESET,void 0),this.fragmentTracker.removeAllFragments(),this.couldBacktrack=!1,this.startPosition=this.lastCurrentTime=0,this.levels=this.fragPlaying=this.backtrackFragment=null,this.altAudio=this.audioOnly=!1},r.onManifestParsed=function(t,e){var r,n,a=!1,s=!1;e.levels.forEach(function(t){(r=t.audioCodec)&&(-1!==r.indexOf("mp4a.40.2")&&(a=!0),-1!==r.indexOf("mp4a.40.5"))&&(s=!0)}),this.audioCodecSwitch=a&&s&&!("function"==typeof(null==(n=Or())||null==(n=n.prototype)?void 0:n.changeType)),this.audioCodecSwitch&&this.log("Both AAC/HE-AAC audio found in levels; declaring level codec as HE-AAC"),this.levels=e.levels,this.startFragRequested=!1},r.onLevelLoading=function(t,e){var r=this.levels;r&&this.state===Lr&&(!(r=r[e.level]).details||r.details.live&&this.levelLastLoaded!==e.level||this.waitForCdnTuneIn(r.details))&&(this.state=xr)},r.onLevelLoaded=function(t,e){var i=this.levels,n=e.level,a=e.details,s=a.totalduration;if(i){this.log("Level "+n+" loaded ["+a.startSN+","+a.endSN+"]"+(a.lastPartSn?"[part-"+a.lastPartSn+"-"+a.lastPartIndex+"]":"")+", cc ["+a.startCC+", "+a.endCC+"] duration:"+s);var s=i[n],i=this.fragCurrent,e=(!i||this.state!==kr&&this.state!==Ar||i.level===e.level&&i.urlId===s.urlId||!i.loader||this.abortCurrentFrag(),0);if(a.live||null!=(i=s.details)&&i.live){if(a.fragments[0]||(a.deltaUpdateFailed=!0),a.deltaUpdateFailed)return;e=this.alignPlaylists(a,s.details)}if(s.details=a,this.levelLastLoaded=n,this.hls.trigger(T.LEVEL_UPDATED,{details:a,level:n}),this.state===xr){if(this.waitForCdnTuneIn(a))return;this.state=Lr}this.startFragRequested?a.live&&this.synchronizeToLiveEdge(a):this.setStartPosition(a,e),this.tick()}else this.warn("Levels were reset while loading level "+n)},r._handleFragmentLoadProgress=function(t){var o,l,u,e,c,f,m,r=t.frag,i=t.part,t=t.payload,a=this.levels;a?(o=(a=a[r.level]).details)?(l=a.videoCodec,u=o.PTSKnown||!o.live,e=null==(e=r.initSegment)?void 0:e.data,a=this._getAudioCodec(a),c=this.transmuxer=this.transmuxer||new ji(this.hls,le,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this)),f=i?i.index:-1,f=new cr(r.level,r.sn,r.stats.chunkCount,t.byteLength,f,-1!==f),m=this.initPTS[r.cc],c.push(t,e,a,l,r,i,o.totalduration,u,f,m)):(this.warn("Dropping fragment "+r.sn+" of level "+r.level+" after level details were reset"),this.fragmentTracker.removeFragment(r)):this.warn("Levels were reset while fragment load was in progress. Fragment "+r.sn+" of level "+r.level+" will not be buffered")},r.onAudioTrackSwitching=function(t,e){var i,r=this.altAudio;e.url||(this.mediaBuffer!==this.media?(this.log("Switching on main audio, use media.buffered to schedule main fragment loading"),this.mediaBuffer=this.media,(i=this.fragCurrent)&&(this.log("Switching to main audio track, cancel main fragment load"),i.abortRequests(),this.fragmentTracker.removeFragment(i)),this.resetTransmuxer(),this.resetLoadingState()):this.audioOnly&&this.resetTransmuxer(),i=this.hls,r&&(i.trigger(T.BUFFER_FLUSHING,{startOffset:0,endOffset:Number.POSITIVE_INFINITY,type:null}),this.fragmentTracker.removeAllFragments()),i.trigger(T.AUDIO_TRACK_SWITCHED,e))},r.onAudioTrackSwitched=function(t,e){var n,e=e.id,e=!!this.hls.audioTracks[e].url;e&&(n=this.videoBuffer)&&this.mediaBuffer!==n&&(this.log("Switching on alternate audio, use video.buffered to schedule main fragment loading"),this.mediaBuffer=n),this.altAudio=e,this.tick()},r.onBufferCreated=function(t,e){var s,n=e.tracks,a=!1;for(s in n){var i,r,o=n[s];"main"===o.id?(r=o,"video"===(i=s)&&(o=n[s])&&(this.videoBuffer=o.buffer)):a=!0}a&&r?(this.log("Alternate track found, use "+i+".buffered to schedule main fragment loading"),this.mediaBuffer=r.buffer):this.mediaBuffer=this.media},r.onFragBuffered=function(t,e){var n,r=e.frag,e=e.part;r&&r.type!==le||(this.fragContextChanged(r)?(this.warn("Fragment "+r.sn+(e?" p: "+e.index:"")+" of level "+r.level+" finished buffering, but was aborted. state: "+this.state),this.state===Ir&&(this.state=Lr)):(n=(e||r).stats,this.fragLastKbps=Math.round(8*n.total/(n.buffering.end-n.loading.first)),"initSegment"!==r.sn&&(this.fragPrevious=r),this.fragBufferedComplete(r,e)))},r.onError=function(t,e){var r;if(e.fatal)this.state=wr;else switch(e.details){case S.FRAG_GAP:case S.FRAG_PARSING_ERROR:case S.FRAG_DECRYPT_ERROR:case S.FRAG_LOAD_ERROR:case S.FRAG_LOAD_TIMEOUT:case S.KEY_LOAD_ERROR:case S.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError(le,e);break;case S.LEVEL_LOAD_ERROR:case S.LEVEL_LOAD_TIMEOUT:case S.LEVEL_PARSING_ERROR:e.levelRetry||this.state!==xr||(null==(r=e.context)?void 0:r.type)!==ae||(this.state=Lr);break;case S.BUFFER_FULL_ERROR:e.parent&&"main"===e.parent&&this.reduceLengthAndFlushBuffer(e)&&this.flushMainBuffer(0,Number.POSITIVE_INFINITY);break;case S.INTERNAL_EXCEPTION:this.recoverWorkerError(e)}},r.checkBuffer=function(){var r,t=this.media,e=this.gapController;t&&e&&t.readyState&&(!this.loadedmetadata&&dr.getBuffered(t).length||(r=this.state!==Lr?this.fragCurrent:null,e.poll(this.lastCurrentTime,r)),this.lastCurrentTime=t.currentTime)},r.onFragLoadEmergencyAborted=function(){this.state=Lr,this.loadedmetadata||(this.startFragRequested=!1,this.nextLoadPosition=this.startPosition),this.tickImmediate()},r.onBufferFlushed=function(t,e){var i,e=e.type;(e!==P||this.audioOnly&&!this.altAudio)&&(i=(e===O?this.videoBuffer:this.mediaBuffer)||this.media,this.afterBufferFlushed(i,e,le))},r.onLevelsUpdated=function(t,e){this.levels=e.levels},r.swapAudioCodec=function(){this.audioCodecSwap=!this.audioCodecSwap},r.seekToStartPos=function(){var e,r,i,t=this.media;t&&(e=t.currentTime,0<=(r=this.startPosition))&&e<r&&(t.seeking?this.log("could not seek to "+r+", already seeking at "+e):(0<(i=((i=dr.getBuffered(t)).length?i.start(0):0)-r)&&(i<this.config.maxBufferHole||i<this.config.maxFragLookUpTolerance)&&(this.log("adjusting start position by "+i+" to match buffer start"),this.startPosition=r+=i),this.log("seek to target start position "+r+" from current time "+e),t.currentTime=r))},r._getAudioCodec=function(t){t=this.config.defaultAudioCodec||t.audioCodec;return this.audioCodecSwap&&t&&(this.log("Swapping audio codec"),t=-1!==t.indexOf("mp4a.40.5")?"mp4a.40.2":"mp4a.40.5"),t},r._loadBitrateTestFrag=function(t,e){var r=this;t.bitrateTest=!0,this._doFragLoad(t,e).then(function(i){var a,n=r.hls;i&&!r.fragContextChanged(t)&&(e.fragmentError=0,r.state=Lr,r.startFragRequested=!1,r.bitrateTest=!1,(a=t.stats).parsing.start=a.parsing.end=a.buffering.start=a.buffering.end=self.performance.now(),n.trigger(T.FRAG_LOADED,i),t.bitrateTest=!1)})},r._handleTransmuxComplete=function(t){var r="main",i=this.hls,n=t.remuxResult,t=t.chunkMeta,s=this.getCurrentContext(t);if(s){var o=s.frag,l=s.part,s=s.level,h=n.video,d=n.text,c=n.id3,f=n.initSegment,g=s.details,v=this.altAudio?void 0:n.audio;if(this.fragContextChanged(o))this.fragmentTracker.removeFragment(o);else{if(this.state=Dr,f&&(null!=f&&f.tracks&&(m=o.initSegment||o,this._bufferInitSegment(s,f.tracks,m,t),i.trigger(T.FRAG_PARSING_INIT_SEGMENT,{frag:m,id:r,tracks:f.tracks})),s=f.initPTS,m=f.timescale,y(s))&&(this.initPTS[o.cc]={baseTime:s,timescale:m},i.trigger(T.INIT_PTS_FOUND,{frag:o,id:r,initPTS:s,timescale:m})),h&&!1!==n.independent){if(g){var f=h.startPTS,s=h.endPTS,m=h.startDTS,k=h.endDTS;if(l)l.elementaryStreams[h.type]={startPTS:f,endPTS:s,startDTS:m,endDTS:k};else if(h.firstKeyFrame&&h.independent&&1===t.id&&(this.couldBacktrack=!0),h.dropped&&h.independent){var A=this.getMainFwdBufferInfo();if((A?A.end:this.getLoadPosition())+this.config.maxBufferHole<(h.firstKeyFramePTS||f)-this.config.maxBufferHole)return void this.backtrack(o);o.setElementaryStreamInfo(h.type,o.start,s,o.start,k,!0)}o.setElementaryStreamInfo(h.type,f,s,m,k),this.backtrackFragment&&(this.backtrackFragment=o),this.bufferFragmentData(h,o,l,t)}}else if(!1===n.independent)return void this.backtrack(o);v&&(A=v.startPTS,f=v.endPTS,s=v.startDTS,m=v.endDTS,l&&(l.elementaryStreams[P]={startPTS:A,endPTS:f,startDTS:s,endDTS:m}),o.setElementaryStreamInfo(P,A,f,s,m),this.bufferFragmentData(v,o,l,t)),g&&null!=c&&null!=(k=c.samples)&&k.length&&(h={id:r,frag:o,details:g,samples:c.samples},i.trigger(T.FRAG_PARSING_METADATA,h)),g&&d&&(n={id:r,frag:o,details:g,samples:d.samples},i.trigger(T.FRAG_PARSING_USERDATA,n))}}else this.resetWhenMissingContext(t)},r._bufferInitSegment=function(t,e,r,i){var a,s,o,l,u,n=this;this.state===Dr&&(this.audioOnly=!!e.audio&&!e.video,this.altAudio&&!this.audioOnly&&delete e.audio,a=e.audio,s=e.video,o=e.audiovideo,a&&(l=t.audioCodec,u=navigator.userAgent.toLowerCase(),this.audioCodecSwitch&&(l=l&&(-1!==l.indexOf("mp4a.40.5")?"mp4a.40.2":"mp4a.40.5"),1!==a.metadata.channelCount)&&-1===u.indexOf("firefox")&&(l="mp4a.40.5"),-1!==u.indexOf("android")&&"audio/mpeg"!==a.container&&this.log("Android: force audio codec to "+(l="mp4a.40.2")),t.audioCodec&&t.audioCodec!==l&&this.log('Swapping manifest audio codec "'+t.audioCodec+'" for "'+l+'"'),a.levelCodec=l,a.id="main",this.log("Init audio buffer, container:"+a.container+", codecs[selected/level/parsed]=["+(l||"")+"/"+(t.audioCodec||"")+"/"+a.codec+"]")),s&&(s.levelCodec=t.videoCodec,s.id="main",this.log("Init video buffer, container:"+s.container+", codecs[level/parsed]=["+(t.videoCodec||"")+"/"+s.codec+"]")),o&&this.log("Init audiovideo buffer, container:"+o.container+", codecs[level/parsed]=["+(t.attrs.CODECS||"")+"/"+o.codec+"]"),this.hls.trigger(T.BUFFER_CODECS,e),Object.keys(e).forEach(function(t){var a=e[t].initSegment;null!=a&&a.byteLength&&n.hls.trigger(T.BUFFER_APPENDING,{type:t,data:a,frag:r,part:null,chunkMeta:i,parent:r.type})}),this.tick())},r.getMainFwdBufferInfo=function(){return this.getFwdBufferInfo(this.mediaBuffer||this.media,le)},r.backtrack=function(t){this.couldBacktrack=!0,this.backtrackFragment=t,this.resetTransmuxer(),this.flushBufferGap(t),this.fragmentTracker.removeFragment(t),this.fragPrevious=null,this.nextLoadPosition=t.start,this.state=Lr},r.checkFragmentChanged=function(){var r,t=this.media,e=null;t&&1<t.readyState&&!1===t.seeking&&(r=t.currentTime,dr.isBuffered(t,r)?e=this.getAppendedFrag(r):dr.isBuffered(t,r+.1)&&(e=this.getAppendedFrag(r+.1)),e)&&(this.backtrackFragment=null,t=this.fragPlaying,r=e.level,t&&e.sn===t.sn&&t.level===r&&e.urlId===t.urlId||(this.fragPlaying=e,this.hls.trigger(T.FRAG_CHANGED,{frag:e}),t&&t.level===r)||this.hls.trigger(T.LEVEL_SWITCHED,{level:r}))},a(e,[{key:"nextLevel",get:function(){var t=this.nextBufferedFrag;return t?t.level:-1}},{key:"currentFrag",get:function(){var t=this.media;return t?this.fragPlaying||this.getAppendedFrag(t.currentTime):null}},{key:"currentProgramDateTime",get:function(){var t=this.media;if(t){var t=t.currentTime,r=this.currentFrag;if(r&&y(t)&&y(r.programDateTime))return t=r.programDateTime+1e3*(t-r.start),new Date(t)}return null}},{key:"currentLevel",get:function(){var t=this.currentFrag;return t?t.level:-1}},{key:"nextBufferedFrag",get:function(){var t=this.currentFrag;return t?this.followingBufferedFrag(t):null}},{key:"forceStartLoad",get:function(){return this._forceStartLoad}}]),e}(ur),qi=function(){function t(t,e,r){void 0===e&&(e=0),void 0===r&&(r=0),this.halfLife=void 0,this.alpha_=void 0,this.estimate_=void 0,this.totalWeight_=void 0,this.halfLife=t,this.alpha_=t?Math.exp(Math.log(.5)/t):0,this.estimate_=e,this.totalWeight_=r}var e=t.prototype;return e.sample=function(t,e){var r=Math.pow(this.alpha_,t);this.estimate_=e*(1-r)+r*this.estimate_,this.totalWeight_+=t},e.getTotalWeight=function(){return this.totalWeight_},e.getEstimate=function(){if(this.alpha_){var t=1-Math.pow(this.alpha_,this.totalWeight_);if(t)return this.estimate_/t}return this.estimate_},t}(),Xi=function(){function t(t,e,r,i){void 0===i&&(i=100),this.defaultEstimate_=void 0,this.minWeight_=void 0,this.minDelayMs_=void 0,this.slow_=void 0,this.fast_=void 0,this.defaultTTFB_=void 0,this.ttfb_=void 0,this.defaultEstimate_=r,this.minWeight_=.001,this.minDelayMs_=50,this.slow_=new qi(t),this.fast_=new qi(e),this.defaultTTFB_=i,this.ttfb_=new qi(t)}var e=t.prototype;return e.update=function(t,e){var r=this.slow_,i=this.fast_,n=this.ttfb_;r.halfLife!==t&&(this.slow_=new qi(t,r.getEstimate(),r.getTotalWeight())),i.halfLife!==e&&(this.fast_=new qi(e,i.getEstimate(),i.getTotalWeight())),n.halfLife!==t&&(this.ttfb_=new qi(t,n.getEstimate(),n.getTotalWeight()))},e.sample=function(t,e){t=(t=Math.max(t,this.minDelayMs_))/1e3,e=8*e/t;this.fast_.sample(t,e),this.slow_.sample(t,e)},e.sampleTTFB=function(t){var e=t/1e3,e=Math.sqrt(2)*Math.exp(-Math.pow(e,2)/2);this.ttfb_.sample(e,Math.max(t,5))},e.canEstimate=function(){return this.fast_.getTotalWeight()>=this.minWeight_},e.getEstimate=function(){return this.canEstimate()?Math.min(this.fast_.getEstimate(),this.slow_.getEstimate()):this.defaultEstimate_},e.getEstimateTTFB=function(){return this.ttfb_.getTotalWeight()>=this.minWeight_?this.ttfb_.getEstimate():this.defaultTTFB_},e.destroy=function(){},t}(),wi=function(){function t(t){this.hls=void 0,this.lastLevelLoadSec=0,this.lastLoadedFragLevel=0,this._nextAutoLevel=-1,this.timer=-1,this.onCheck=this._abandonRulesCheck.bind(this),this.fragCurrent=null,this.partCurrent=null,this.bitrateTestDelay=0,this.bwEstimator=void 0;t=(this.hls=t).config;this.bwEstimator=new Xi(t.abrEwmaSlowVoD,t.abrEwmaFastVoD,t.abrEwmaDefaultEstimate),this.registerListeners()}var e=t.prototype;return e.registerListeners=function(){var t=this.hls;t.on(T.FRAG_LOADING,this.onFragLoading,this),t.on(T.FRAG_LOADED,this.onFragLoaded,this),t.on(T.FRAG_BUFFERED,this.onFragBuffered,this),t.on(T.LEVEL_SWITCHING,this.onLevelSwitching,this),t.on(T.LEVEL_LOADED,this.onLevelLoaded,this)},e.unregisterListeners=function(){var t=this.hls;t.off(T.FRAG_LOADING,this.onFragLoading,this),t.off(T.FRAG_LOADED,this.onFragLoaded,this),t.off(T.FRAG_BUFFERED,this.onFragBuffered,this),t.off(T.LEVEL_SWITCHING,this.onLevelSwitching,this),t.off(T.LEVEL_LOADED,this.onLevelLoaded,this)},e.destroy=function(){this.unregisterListeners(),this.clearTimer(),this.hls=this.onCheck=null,this.fragCurrent=this.partCurrent=null},e.onFragLoading=function(t,e){var i=e.frag;this.ignoreFragment(i)||(this.fragCurrent=i,this.partCurrent=null!=(i=e.part)?i:null,this.clearTimer(),this.timer=self.setInterval(this.onCheck,100))},e.onLevelSwitching=function(t,e){this.clearTimer()},e.getTimeToLoadFrag=function(t,e,r,i){return t+r/e+(i?this.lastLevelLoadSec:0)},e.onLevelLoaded=function(t,e){var r=this.hls.config,i=e.stats,n=i.total,i=i.bwEstimate;y(n)&&y(i)&&(this.lastLevelLoadSec=8*n/i),e.details.live?this.bwEstimator.update(r.abrEwmaSlowLive,r.abrEwmaFastLive):this.bwEstimator.update(r.abrEwmaSlowVoD,r.abrEwmaFastVoD)},e._abandonRulesCheck=function(){var t=this.fragCurrent,e=this.partCurrent,r=this.hls,i=r.autoLevelEnabled,n=r.media;if(t&&n){var a=performance.now(),s=(e||t).stats,o=(e||t).duration,a=a-s.loading.start;if(s.aborted||s.loaded&&s.loaded===s.total||0===t.level)this.clearTimer(),this._nextAutoLevel=-1;else if(i&&!n.paused&&n.playbackRate&&n.readyState){i=r.mainForwardBufferInfo;if(null!==i){var h=this.bwEstimator.getEstimateTTFB(),n=Math.abs(n.playbackRate);if(!(a<=Math.max(h,o/(2*n)*1e3))){var c=i.len/n;if(!(2*o/n<=c)){var i=s.loading.first?s.loading.first-s.loading.start:-1,n=s.loaded&&-1<i,v=this.bwEstimator.getEstimate(),m=r.levels,p=r.minAutoLevel,E=m[t.level],E=s.total||Math.max(s.loaded,Math.round(o*E.maxBitrate/8)),L=a-i,L=(L<1&&n&&(L=Math.min(a,8*s.loaded/v)),n?1e3*s.loaded/L:0),E=L?(E-s.loaded)/L:8*E/v+h/1e3;if(!(E<=c)){for(var D=L?8*L:v,I=Number.POSITIVE_INFINITY,A=t.level-1;p<A;A--){var _=m[A].maxBitrate;if((I=this.getTimeToLoadFrag(h/1e3,D,o*_,!m[A].details))<c)break}E<=I||10*o<I||(r.nextLoadLevel=A,n?this.bwEstimator.sample(a-Math.min(h,i),s.loaded):this.bwEstimator.sampleTTFB(a),this.clearTimer(),b.warn("[abr] Fragment "+t.sn+(e?" part "+e.index:"")+" of level "+t.level+" is loading too slowly;\n      Time to underbuffer: "+c.toFixed(3)+" s\n      Estimated load time for current fragment: "+E.toFixed(3)+" s\n      Estimated load time for down switch fragment: "+I.toFixed(3)+" s\n      TTFB estimate: "+i+"\n      Current BW estimate: "+(y(v)?(v/1024).toFixed(3):"Unknown")+" Kb/s\n      New BW estimate: "+(this.bwEstimator.getEstimate()/1024).toFixed(3)+" Kb/s\n      Aborting and switching to level "+A),t.loader&&(this.fragCurrent=this.partCurrent=null,t.abortRequests()),r.trigger(T.FRAG_LOAD_EMERGENCY_ABORTED,{frag:t,part:e,stats:s}))}}}}}}},e.onFragLoaded=function(t,e){var o,a,s,r=e.frag,e=e.part,n=(e||r).stats;r.type===le&&this.bwEstimator.sampleTTFB(n.loading.first-n.loading.start),this.ignoreFragment(r)||(this.clearTimer(),this.lastLoadedFragLevel=r.level,this._nextAutoLevel=-1,this.hls.config.abrMaxWithRealBitrate&&(a=(e||r).duration,o=((s=this.hls.levels[r.level]).loaded?s.loaded.bytes:0)+n.loaded,a=(s.loaded?s.loaded.duration:0)+a,s.loaded={bytes:o,duration:a},s.realBitrate=Math.round(8*o/a)),r.bitrateTest&&(s={stats:n,frag:r,part:e,id:r.type},this.onFragBuffered(T.FRAG_BUFFERED,s),r.bitrateTest=!1))},e.onFragBuffered=function(t,e){var a,r=e.frag,e=e.part,e=(null!=e&&e.stats.loaded?e:r).stats;e.aborted||this.ignoreFragment(r)||(a=e.parsing.end-e.loading.start-Math.min(e.loading.first-e.loading.start,this.bwEstimator.getEstimateTTFB()),this.bwEstimator.sample(a,e.loaded),e.bwEstimate=this.bwEstimator.getEstimate(),r.bitrateTest?this.bitrateTestDelay=a/1e3:this.bitrateTestDelay=0)},e.ignoreFragment=function(t){return t.type!==le||"initSegment"===t.sn},e.clearTimer=function(){self.clearInterval(this.timer)},e.getNextABRAutoLevel=function(){var t=this.fragCurrent,e=this.partCurrent,r=this.hls,i=r.maxAutoLevel,n=r.config,a=r.minAutoLevel,s=r.media,e=e?e.duration:t?t.duration:0,t=s&&0!==s.playbackRate?Math.abs(s.playbackRate):1,s=this.bwEstimator?this.bwEstimator.getEstimate():n.abrEwmaDefaultEstimate,r=r.mainForwardBufferInfo,r=(r?r.len:0)/t,t=this.findBestLevel(s,a,i,r,n.abrBandWidthFactor,n.abrBandWidthUpFactor);if(0<=t)return t;b.trace("[abr] "+(r?"rebuffering expected":"buffer is empty")+", finding optimal quality level");var m,f=e?Math.min(e,n.maxStarvationDelay):n.maxStarvationDelay,g=n.abrBandWidthFactor,v=n.abrBandWidthUpFactor;return r||(m=this.bitrateTestDelay)&&(f=(e?Math.min(e,n.maxLoadingDelay):n.maxLoadingDelay)-m,b.trace("[abr] bitrate test took "+Math.round(1e3*m)+"ms, set first fragment max fetchDuration to "+Math.round(1e3*f)+" ms"),g=v=1),t=this.findBestLevel(s,a,i,r+f,g,v),Math.max(t,0)},e.findBestLevel=function(t,e,r,i,n,a){for(var s,o=this.fragCurrent,l=this.partCurrent,u=this.lastLoadedFragLevel,h=this.hls.levels,d=h[u],c=!(null==d||null==(s=d.details)||!s.live),f=null==d?void 0:d.codecSet,g=l?l.duration:o?o.duration:0,v=this.bwEstimator.getEstimateTTFB()/1e3,m=e,p=-1,T=r;e<=T;T--){var E=h[T];if(!E||f&&E.codecSet!==f)E&&(m=Math.min(T,m),p=Math.max(T,p));else{-1!==p&&b.trace("[abr] Skipped level(s) "+m+"-"+p+' with CODECS:"'+h[p].attrs.CODECS+'"; not compatible with "'+d.attrs.CODECS+'"');var E=E.details,R=(l?null==E?void 0:E.partTarget:null==E?void 0:E.averagetargetduration)||g,S=T<=u?n*t:a*t,k=h[T].maxBitrate,E=this.getTimeToLoadFrag(v,S,k*R,void 0===E);if(b.trace("[abr] level:"+T+" adjustedbw-bitrate:"+Math.round(S-k)+" avgDuration:"+R.toFixed(1)+" maxFetchDuration:"+i.toFixed(1)+" fetchDuration:"+E.toFixed(1)),k<S&&(0===E||!y(E)||c&&!this.bitrateTestDelay||E<i))return T}}return-1},a(t,[{key:"nextAutoLevel",get:function(){var t=this._nextAutoLevel,e=this.bwEstimator;if(-1!==t&&!e.canEstimate())return t;e=this.getNextABRAutoLevel();if(-1!==t){var i=this.hls.levels;if(i.length>Math.max(t,e)&&i[t].loadError<=i[e].loadError)return t}return e=-1!==t?Math.min(t,e):e},set:function(t){this._nextAutoLevel=t}}]),t}(),Qi=function(){function t(){this.chunks=[],this.dataLength=0}var e=t.prototype;return e.push=function(t){this.chunks.push(t),this.dataLength+=t.length},e.flush=function(){var e=this.chunks,r=this.dataLength;return e.length?(e=1===e.length?e[0]:function(t,e){for(var r=new Uint8Array(e),i=0,n=0;n<t.length;n++){var a=t[n];r.set(a,i),i+=a.length}return r}(e,r),this.reset(),e):new Uint8Array(0)},e.reset=function(){this.chunks.length=0,this.dataLength=0},t}(),li=function(t){function r(e,r,i){return(e=t.call(this,e,r,i,"[audio-stream-controller]",ue)||this).videoBuffer=null,e.videoTrackCC=-1,e.waitingVideoCC=-1,e.bufferedTrack=null,e.switchingTrack=null,e.trackId=-1,e.waitingData=null,e.mainDetails=null,e.bufferFlushed=!1,e.cachedTrackLoadedData=null,e._registerListeners(),e}l(r,t);var i=r.prototype;return i.onHandlerDestroying=function(){this._unregisterListeners(),this.mainDetails=null,this.bufferedTrack=null,this.switchingTrack=null},i._registerListeners=function(){var t=this.hls;t.on(T.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(T.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(T.MANIFEST_LOADING,this.onManifestLoading,this),t.on(T.LEVEL_LOADED,this.onLevelLoaded,this),t.on(T.AUDIO_TRACKS_UPDATED,this.onAudioTracksUpdated,this),t.on(T.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),t.on(T.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),t.on(T.ERROR,this.onError,this),t.on(T.BUFFER_RESET,this.onBufferReset,this),t.on(T.BUFFER_CREATED,this.onBufferCreated,this),t.on(T.BUFFER_FLUSHED,this.onBufferFlushed,this),t.on(T.INIT_PTS_FOUND,this.onInitPtsFound,this),t.on(T.FRAG_BUFFERED,this.onFragBuffered,this)},i._unregisterListeners=function(){var t=this.hls;t.off(T.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(T.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(T.MANIFEST_LOADING,this.onManifestLoading,this),t.off(T.LEVEL_LOADED,this.onLevelLoaded,this),t.off(T.AUDIO_TRACKS_UPDATED,this.onAudioTracksUpdated,this),t.off(T.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),t.off(T.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),t.off(T.ERROR,this.onError,this),t.off(T.BUFFER_RESET,this.onBufferReset,this),t.off(T.BUFFER_CREATED,this.onBufferCreated,this),t.off(T.BUFFER_FLUSHED,this.onBufferFlushed,this),t.off(T.INIT_PTS_FOUND,this.onInitPtsFound,this),t.off(T.FRAG_BUFFERED,this.onFragBuffered,this)},i.onInitPtsFound=function(t,e){var r=e.frag,i=e.id,n=e.initPTS,e=e.timescale;"main"===i&&(i=r.cc,this.initPTS[r.cc]={baseTime:n,timescale:e},this.log("InitPTS for cc: "+i+" found from main: "+n),this.videoTrackCC=i,this.state===Cr)&&this.tick()},i.startLoad=function(t){var e;this.levels?(e=this.lastCurrentTime,this.stopLoad(),this.setInterval(100),0<e&&-1===t?(this.log("Override startPosition with lastCurrentTime @"+e.toFixed(3)),t=e,this.state=Lr):(this.loadedmetadata=!1,this.state=br),this.nextLoadPosition=this.startPosition=this.lastCurrentTime=t,this.tick()):(this.startPosition=t,this.state=Sr)},i.doTick=function(){switch(this.state){case Lr:this.doTickIdle();break;case br:var r=this.levels,i=this.trackId,i=null==r||null==(r=r[i])?void 0:r.details;if(i){if(this.waitForCdnTuneIn(i))break;this.state=Cr}break;case Ar:r=performance.now(),i=this.retryDate;(!i||i<=r||null!=(i=this.media)&&i.seeking)&&(this.log("RetryDate reached, switch back to IDLE state"),this.resetStartWhenNotLoaded(this.trackId),this.state=Lr);break;case Cr:var h,d,r=this.waitingData;r?(i=r.frag,h=r.part,d=r.cache,r=r.complete,void 0!==this.initPTS[i.cc]?(this.waitingData=null,this.waitingVideoCC=-1,this.state=kr,h={frag:i,part:h,payload:d.flush(),networkDetails:null},this._handleFragmentLoadProgress(h),r&&t.prototype._handleFragmentLoadComplete.call(this,h)):this.videoTrackCC!==this.waitingVideoCC?(this.log("Waiting fragment cc ("+i.cc+") cancelled because video is at cc "+this.videoTrackCC),this.clearWaitingFragment()):(d=this.getLoadPosition(),He((r=dr.bufferInfo(this.mediaBuffer,d,this.config.maxBufferHole)).end,this.config.maxFragLookUpTolerance,i)<0&&(this.log("Waiting fragment cc ("+i.cc+") @ "+i.start+" cancelled because another fragment at "+r.end+" is needed"),this.clearWaitingFragment()))):this.state=Lr}this.onTickEnd()},i.clearWaitingFragment=function(){var t=this.waitingData;t&&(this.fragmentTracker.removeFragment(t.frag),this.waitingData=null,this.waitingVideoCC=-1,this.state=Lr)},i.resetLoadingState=function(){this.clearWaitingFragment(),t.prototype.resetLoadingState.call(this)},i.onTickEnd=function(){var t=this.media;null!=t&&t.readyState&&(this.lastCurrentTime=t.currentTime)},i.doTickIdle=function(){var t=this.hls,e=this.levels,r=this.media,i=this.trackId,n=t.config;if(null!=e&&e[i]&&(r||!this.startFragRequested&&n.startFragPrefetch)){n=e[i],e=n.details;if(!e||e.live&&this.levelLastLoaded!==i||this.waitForCdnTuneIn(e))this.state=br;else{i=this.mediaBuffer||this.media,i=(this.bufferFlushed&&i&&(this.bufferFlushed=!1,this.afterBufferFlushed(i,P,ue)),this.getFwdBufferInfo(i,ue));if(null!==i){var u=this.bufferedTrack,h=this.switchingTrack;if(!h&&this._streamEnded(i,e))t.trigger(T.BUFFER_EOS,{type:"audio"}),this.state=_r;else{var t=this.getFwdBufferInfo(this.videoBuffer||this.media,le),c=i.len,f=this.getMaxBufferLength(null==t?void 0:t.len);if(!(f<=c)||h){var c=e.fragments[0].start,v=i.end,h=(h&&r&&(m=this.getLoadPosition(),u&&h.attrs!==u.attrs&&(v=m),e.PTSKnown)&&m<c&&(i.end>c||i.nextStart)&&(this.log("Alt audio track ahead of main track, seek to start of alt audio track"),r.currentTime=c+.05),this.getNextFragment(v,e)),u=!1;if(h&&this.isLoopLoading(h,v)&&(u=!!h.gap,h=this.getNextFragmentLoopLoading(h,e,i,le,f)),h){var m=t&&h.start>t.end+e.targetduration;if(m||(null==t||!t.len)&&i.len){r=this.getAppendedFrag(h.start,le);if(null===r)return;if(u=u||!!r.gap||!!m&&0===t.len,m&&!u||u&&i.nextStart&&i.nextStart<r.end)return}this.loadFragment(h,n,v)}else this.bufferFlushed=!0}}}}}},i.getMaxBufferLength=function(e){var r=t.prototype.getMaxBufferLength.call(this);return e?Math.min(Math.max(r,e),this.config.maxMaxBufferLength):r},i.onMediaDetaching=function(){this.videoBuffer=null,t.prototype.onMediaDetaching.call(this)},i.onAudioTracksUpdated=function(t,e){e=e.audioTracks;this.resetTransmuxer(),this.levels=e.map(function(t){return new De(t)})},i.onAudioTrackSwitching=function(t,e){var r=!!e.url,i=(this.trackId=e.id,this.fragCurrent);i&&(i.abortRequests(),this.removeUnbufferedFrags(i.start)),this.resetLoadingState(),r?this.setInterval(100):this.resetTransmuxer(),r?(this.switchingTrack=e,this.state=Lr):(this.switchingTrack=null,this.bufferedTrack=e,this.state=Sr),this.tick()},i.onManifestLoading=function(){this.fragmentTracker.removeAllFragments(),this.startPosition=this.lastCurrentTime=0,this.bufferFlushed=!1,this.levels=this.mainDetails=this.waitingData=this.bufferedTrack=this.cachedTrackLoadedData=this.switchingTrack=null,this.startFragRequested=!1,this.trackId=this.videoTrackCC=this.waitingVideoCC=-1},i.onLevelLoaded=function(t,e){this.mainDetails=e.details,null!==this.cachedTrackLoadedData&&(this.hls.trigger(T.AUDIO_TRACK_LOADED,this.cachedTrackLoadedData),this.cachedTrackLoadedData=null)},i.onAudioTrackLoaded=function(t,e){if(null!=this.mainDetails){var i=this.levels,n=e.details,a=e.id;if(i){this.log("Track "+a+" loaded ["+n.startSN+","+n.endSN+"]"+(n.lastPartSn?"[part-"+n.lastPartSn+"-"+n.lastPartIndex+"]":"")+",duration:"+n.totalduration);var i=i[a],o=0;if(n.live||null!=(r=i.details)&&r.live){var r=this.mainDetails;if(n.fragments[0]||(n.deltaUpdateFailed=!0),n.deltaUpdateFailed||!r)return;o=!i.details&&n.hasProgramDateTime&&r.hasProgramDateTime?(mr(n,r),n.fragments[0].start):this.alignPlaylists(n,i.details)}i.details=n,this.levelLastLoaded=a,this.startFragRequested||!this.mainDetails&&n.live||this.setStartPosition(i.details,o),this.state!==br||this.waitForCdnTuneIn(n)||(this.state=Lr),this.tick()}else this.warn("Audio tracks were reset while loading level "+a)}else this.cachedTrackLoadedData=e},i._handleFragmentLoadProgress=function(t){var u,c,e,g,r=t.frag,i=t.part,t=t.payload,a=this.config,s=this.trackId,o=this.levels;o?(o=o[s])?(u=o.details)?(a=a.defaultAudioCodec||o.audioCodec||"mp4a.40.2",o=(o=this.transmuxer)||(this.transmuxer=new ji(this.hls,ue,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this))),c=this.initPTS[r.cc],e=null==(e=r.initSegment)?void 0:e.data,void 0!==c?(g=i?i.index:-1,g=new cr(r.level,r.sn,r.stats.chunkCount,t.byteLength,g,-1!==g),o.push(t,e,a,"",r,i,u.totalduration,!1,g,c)):(this.log("Unknown video PTS for cc "+r.cc+", waiting for video PTS before demuxing audio frag "+r.sn+" of ["+u.startSN+" ,"+u.endSN+"],track "+s),(this.waitingData=this.waitingData||{frag:r,part:i,cache:new Qi,complete:!1}).cache.push(new Uint8Array(t)),this.waitingVideoCC=this.videoTrackCC,this.state=Cr)):(this.warn("Audio track details undefined on fragment load progress"),this.removeUnbufferedFrags(r.start)):this.warn("Audio track is undefined on fragment load progress"):this.warn("Audio tracks were reset while fragment load was in progress. Fragment "+r.sn+" of level "+r.level+" will not be buffered")},i._handleFragmentLoadComplete=function(e){this.waitingData?this.waitingData.complete=!0:t.prototype._handleFragmentLoadComplete.call(this,e)},i.onBufferReset=function(){this.mediaBuffer=this.videoBuffer=null,this.loadedmetadata=!1},i.onBufferCreated=function(t,e){var r=e.tracks.audio;r&&(this.mediaBuffer=r.buffer||null),e.tracks.video&&(this.videoBuffer=e.tracks.video.buffer||null)},i.onFragBuffered=function(t,r){var s,n=r.frag,r=r.part;n.type===ue?this.fragContextChanged(n)?this.warn("Fragment "+n.sn+(r?" p: "+r.index:"")+" of level "+n.level+" finished buffering, but was aborted. state: "+this.state+", audioSwitch: "+(this.switchingTrack?this.switchingTrack.name:"false")):("initSegment"!==n.sn&&(this.fragPrevious=n,s=this.switchingTrack)&&(this.bufferedTrack=s,this.switchingTrack=null,this.hls.trigger(T.AUDIO_TRACK_SWITCHED,e({},s))),this.fragBufferedComplete(n,r)):this.loadedmetadata||n.type!==le||null!=(s=this.videoBuffer||this.media)&&s.buffered.length&&(this.loadedmetadata=!0)},i.onError=function(e,r){var i;if(r.fatal)this.state=wr;else switch(r.details){case S.FRAG_GAP:case S.FRAG_PARSING_ERROR:case S.FRAG_DECRYPT_ERROR:case S.FRAG_LOAD_ERROR:case S.FRAG_LOAD_TIMEOUT:case S.KEY_LOAD_ERROR:case S.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError(ue,r);break;case S.AUDIO_TRACK_LOAD_ERROR:case S.AUDIO_TRACK_LOAD_TIMEOUT:case S.LEVEL_PARSING_ERROR:r.levelRetry||this.state!==br||(null==(i=r.context)?void 0:i.type)!==se||(this.state=Lr);break;case S.BUFFER_FULL_ERROR:r.parent&&"audio"===r.parent&&this.reduceLengthAndFlushBuffer(r)&&(this.bufferedTrack=null,t.prototype.flushMainBuffer.call(this,0,Number.POSITIVE_INFINITY,"audio"));break;case S.INTERNAL_EXCEPTION:this.recoverWorkerError(r)}},i.onBufferFlushed=function(t,e){e.type===P&&(this.bufferFlushed=!0,this.state===_r)&&(this.state=Lr)},i._handleTransmuxComplete=function(t){var l,u,d,c,f,v,E,y,r="audio",i=this.hls,n=t.remuxResult,t=t.chunkMeta,s=this.getCurrentContext(t);s?(l=s.frag,u=s.part,s=s.level.details,d=n.audio,c=n.text,f=n.id3,n=n.initSegment,!this.fragContextChanged(l)&&s?(this.state=Dr,this.switchingTrack&&d&&this.completeAudioSwitch(this.switchingTrack),null!=n&&n.tracks&&(v=l.initSegment||l,this._bufferInitSegment(n.tracks,v,t),i.trigger(T.FRAG_PARSING_INIT_SEGMENT,{frag:v,id:r,tracks:n.tracks})),d&&(v=d.startPTS,n=d.endPTS,y=d.startDTS,E=d.endDTS,u&&(u.elementaryStreams[P]={startPTS:v,endPTS:n,startDTS:y,endDTS:E}),l.setElementaryStreamInfo(P,v,n,y,E),this.bufferFragmentData(d,l,u,t)),null!=f&&null!=(v=f.samples)&&v.length&&(n=o({id:r,frag:l,details:s},f),i.trigger(T.FRAG_PARSING_METADATA,n)),c&&(y=o({id:r,frag:l,details:s},c),i.trigger(T.FRAG_PARSING_USERDATA,y))):this.fragmentTracker.removeFragment(l)):this.resetWhenMissingContext(t)},i._bufferInitSegment=function(t,e,r){var i;this.state===Dr&&(t.video&&delete t.video,i=t.audio)&&(i.levelCodec=i.codec,i.id="audio",this.log("Init audio buffer, container:"+i.container+", codecs[parsed]=["+i.codec+"]"),this.hls.trigger(T.BUFFER_CODECS,t),null!=(t=i.initSegment)&&t.byteLength&&(i={type:"audio",frag:e,part:null,chunkMeta:r,parent:e.type,data:t},this.hls.trigger(T.BUFFER_APPENDING,i)),this.tick())},i.loadFragment=function(e,r,i){var a=this.fragmentTracker.getState(e);this.fragCurrent=e,this.switchingTrack||a===Qe||a===$e?"initSegment"===e.sn?this._loadInitSegment(e,r):null!=(a=r.details)&&a.live&&!this.initPTS[e.cc]?(this.log("Waiting for video PTS in continuity counter "+e.cc+" of live stream before loading audio fragment "+e.sn+" of level "+this.trackId),this.state=Cr):(this.startFragRequested=!0,t.prototype.loadFragment.call(this,e,r,i)):this.clearTrackerIfNeeded(e)},i.completeAudioSwitch=function(r){var i=this.hls,n=this.media,a=this.bufferedTrack,a=null==a?void 0:a.attrs,o=r.attrs;n&&a&&(a.CHANNELS!==o.CHANNELS||a.NAME!==o.NAME||a.LANGUAGE!==o.LANGUAGE)&&(this.log("Switching audio track : flushing all audio"),t.prototype.flushMainBuffer.call(this,0,Number.POSITIVE_INFINITY,"audio")),this.bufferedTrack=r,this.switchingTrack=null,i.trigger(T.AUDIO_TRACK_SWITCHED,e({},r))},r}(ur),Wr=function(t){function r(e){return(e=t.call(this,e,"[audio-track-controller]")||this).tracks=[],e.groupId=null,e.tracksInGroup=[],e.trackId=-1,e.currentTrack=null,e.selectDefaultTrack=!0,e.registerListeners(),e}l(r,t);var i=r.prototype;return i.registerListeners=function(){var t=this.hls;t.on(T.MANIFEST_LOADING,this.onManifestLoading,this),t.on(T.MANIFEST_PARSED,this.onManifestParsed,this),t.on(T.LEVEL_LOADING,this.onLevelLoading,this),t.on(T.LEVEL_SWITCHING,this.onLevelSwitching,this),t.on(T.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),t.on(T.ERROR,this.onError,this)},i.unregisterListeners=function(){var t=this.hls;t.off(T.MANIFEST_LOADING,this.onManifestLoading,this),t.off(T.MANIFEST_PARSED,this.onManifestParsed,this),t.off(T.LEVEL_LOADING,this.onLevelLoading,this),t.off(T.LEVEL_SWITCHING,this.onLevelSwitching,this),t.off(T.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),t.off(T.ERROR,this.onError,this)},i.destroy=function(){this.unregisterListeners(),this.tracks.length=0,this.tracksInGroup.length=0,this.currentTrack=null,t.prototype.destroy.call(this)},i.onManifestLoading=function(){this.tracks=[],this.groupId=null,this.tracksInGroup=[],this.trackId=-1,this.currentTrack=null,this.selectDefaultTrack=!0},i.onManifestParsed=function(t,e){this.tracks=e.audioTracks||[]},i.onAudioTrackLoaded=function(t,e){var s,r=e.id,i=e.groupId,n=e.details,a=this.tracksInGroup[r];a&&a.groupId===i?(s=a.details,a.details=e.details,this.log("audio-track "+r+' "'+a.name+'" lang:'+a.lang+" group:"+i+" loaded ["+n.startSN+"-"+n.endSN+"]"),r===this.trackId&&this.playlistLoaded(r,e,s)):this.warn("Track with id:"+r+" and group:"+i+" not found in active group "+a.groupId)},i.onLevelLoading=function(t,e){this.switchLevel(e.level)},i.onLevelSwitching=function(t,e){this.switchLevel(e.level)},i.switchLevel=function(t){var r,n,t=this.hls.levels[t];null!=t&&t.audioGroupIds&&(r=t.audioGroupIds[t.urlId],this.groupId!==r?(this.groupId=r||null,t=this.tracks.filter(function(t){return!r||t.groupId===r}),this.selectDefaultTrack&&!t.some(function(t){return t.default})&&(this.selectDefaultTrack=!1),n={audioTracks:this.tracksInGroup=t},this.log("Updating audio tracks, "+t.length+" track(s) found in group:"+r),this.hls.trigger(T.AUDIO_TRACKS_UPDATED,n),this.selectInitialTrack()):this.shouldReloadPlaylist(this.currentTrack)&&this.setAudioTrack(this.trackId))},i.onError=function(t,e){!e.fatal&&e.context&&e.context.type===se&&e.context.id===this.trackId&&e.context.groupId===this.groupId&&(this.requestScheduled=-1,this.checkRetry(e))},i.setAudioTrack=function(t){var i,a,s,r=this.tracksInGroup;t<0||t>=r.length?this.warn("Invalid id passed to audio-track controller"):(this.clearTimer(),i=this.currentTrack,r[this.trackId],a=(r=r[t]).groupId,s=r.name,this.log("Switching to audio-track "+t+' "'+s+'" lang:'+r.lang+" group:"+a),this.trackId=t,this.currentTrack=r,this.selectDefaultTrack=!1,this.hls.trigger(T.AUDIO_TRACK_SWITCHING,e({},r)),r.details&&!r.details.live||(s=this.switchParams(r.url,null==i?void 0:i.details),this.loadPlaylist(s)))},i.selectInitialTrack=function(){var t=this.tracksInGroup,e=this.findTrackId(this.currentTrack)|this.findTrackId(null);-1!=e?this.setAudioTrack(e):(e=new Error("No track found for running audio group-ID: "+this.groupId+" track count: "+t.length),this.warn(e.message),this.hls.trigger(T.ERROR,{type:E.MEDIA_ERROR,details:S.AUDIO_TRACK_LOAD_ERROR,fatal:!0,error:e}))},i.findTrackId=function(t){for(var e=this.tracksInGroup,r=0;r<e.length;r++){var i=e[r];if(!this.selectDefaultTrack||i.default){if(!t||void 0!==t.attrs["STABLE-RENDITION-ID"]&&t.attrs["STABLE-RENDITION-ID"]===i.attrs["STABLE-RENDITION-ID"])return i.id;if(t.name===i.name&&t.lang===i.lang)return i.id}}return-1},i.loadPlaylist=function(e){t.prototype.loadPlaylist.call(this);var r=this.tracksInGroup[this.trackId];if(this.shouldLoadPlaylist(r)){var i=r.id,n=r.groupId,a=r.url;if(e)try{a=e.addDirectives(a)}catch(t){this.warn("Could not construct new URL with HLS Delivery Directives: "+t)}this.log("loading audio-track playlist "+i+' "'+r.name+'" lang:'+r.lang+" group:"+n),this.clearTimer(),this.hls.trigger(T.AUDIO_TRACK_LOADING,{url:a,id:i,groupId:n,deliveryDirectives:e||null})}},a(r,[{key:"audioTracks",get:function(){return this.tracksInGroup}},{key:"audioTrack",get:function(){return this.trackId},set:function(t){this.selectDefaultTrack=!1,this.setAudioTrack(t)}}]),r}(We);function Ji(t,e){if(t.length===e.length){for(var r=0;r<t.length;r++)if(!function(t,e){var r=t["STABLE-RENDITION-ID"];return r?r===e["STABLE-RENDITION-ID"]:!["LANGUAGE","NAME","CHARACTERISTICS","AUTOSELECT","DEFAULT","FORCED"].some(function(r){return t[r]!==e[r]})}(t[r].attrs,e[r].attrs))return;return 1}}var Fr=function(t){function e(e,r,i){return(e=t.call(this,e,r,i,"[subtitle-stream-controller]",he)||this).levels=[],e.currentTrackId=-1,e.tracksBuffered=[],e.mainDetails=null,e._registerListeners(),e}l(e,t);var r=e.prototype;return r.onHandlerDestroying=function(){this._unregisterListeners(),this.mainDetails=null},r._registerListeners=function(){var t=this.hls;t.on(T.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(T.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(T.MANIFEST_LOADING,this.onManifestLoading,this),t.on(T.LEVEL_LOADED,this.onLevelLoaded,this),t.on(T.ERROR,this.onError,this),t.on(T.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),t.on(T.SUBTITLE_TRACK_SWITCH,this.onSubtitleTrackSwitch,this),t.on(T.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),t.on(T.SUBTITLE_FRAG_PROCESSED,this.onSubtitleFragProcessed,this),t.on(T.BUFFER_FLUSHING,this.onBufferFlushing,this),t.on(T.FRAG_BUFFERED,this.onFragBuffered,this)},r._unregisterListeners=function(){var t=this.hls;t.off(T.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(T.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(T.MANIFEST_LOADING,this.onManifestLoading,this),t.off(T.LEVEL_LOADED,this.onLevelLoaded,this),t.off(T.ERROR,this.onError,this),t.off(T.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),t.off(T.SUBTITLE_TRACK_SWITCH,this.onSubtitleTrackSwitch,this),t.off(T.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),t.off(T.SUBTITLE_FRAG_PROCESSED,this.onSubtitleFragProcessed,this),t.off(T.BUFFER_FLUSHING,this.onBufferFlushing,this),t.off(T.FRAG_BUFFERED,this.onFragBuffered,this)},r.startLoad=function(t){this.stopLoad(),this.state=Lr,this.setInterval(500),this.nextLoadPosition=this.startPosition=this.lastCurrentTime=t,this.tick()},r.onManifestLoading=function(){this.mainDetails=null,this.fragmentTracker.removeAllFragments()},r.onMediaDetaching=function(){this.tracksBuffered=[],t.prototype.onMediaDetaching.call(this)},r.onLevelLoaded=function(t,e){this.mainDetails=e.details},r.onSubtitleFragProcessed=function(t,e){var r=e.frag,e=e.success;if(this.fragPrevious=r,this.state=Lr,e){var n=this.tracksBuffered[this.currentTrackId];if(n){for(var a,s=r.start,o=0;o<n.length;o++)if(s>=n[o].start&&s<=n[o].end){a=n[o];break}e=r.start+r.duration;a?a.end=e:n.push(a={start:s,end:e}),this.fragmentTracker.fragBuffered(r)}}},r.onBufferFlushing=function(t,e){var n,a,s,r=e.startOffset,i=e.endOffset;0===r&&i!==Number.POSITIVE_INFINITY&&(n=this.currentTrackId,(a=this.levels).length)&&a[n]&&a[n].details&&((s=i-a[n].details.targetduration)<=0||(e.endOffsetSubtitles=Math.max(0,s),this.tracksBuffered.forEach(function(t){for(var e=0;e<t.length;)if(t[e].end<=s)t.shift();else{if(!(t[e].start<s))break;t[e].start=s,e++}}),this.fragmentTracker.removeFragmentsInRange(r,s,he)))},r.onFragBuffered=function(t,e){this.loadedmetadata||e.frag.type!==le||null!=(e=this.media)&&e.buffered.length&&(this.loadedmetadata=!0)},r.onError=function(t,e){e=e.frag;(null==e?void 0:e.type)===he&&(this.fragCurrent&&this.fragCurrent.abortRequests(),this.state!==Sr)&&(this.state=Lr)},r.onSubtitleTracksUpdated=function(t,e){var r=this,e=e.subtitleTracks;Ji(this.levels,e)?this.levels=e.map(function(t){return new De(t)}):(this.tracksBuffered=[],this.levels=e.map(function(t){t=new De(t);return r.tracksBuffered[t.id]=[],t}),this.fragmentTracker.removeFragmentsInRange(0,Number.POSITIVE_INFINITY,he),this.fragPrevious=null,this.mediaBuffer=null)},r.onSubtitleTrackSwitch=function(t,e){this.currentTrackId=e.id,this.levels.length&&-1!==this.currentTrackId?(null!=(e=this.levels[this.currentTrackId])&&e.details?this.mediaBuffer=this.mediaBufferTimeRanges:this.mediaBuffer=null,e&&this.setInterval(500)):this.clearInterval()},r.onSubtitleTrackLoaded=function(t,e){var i=e.details,e=e.id,a=this.currentTrackId,s=this.levels;if(s.length){var o=s[a];if(!(e>=s.length||e!==a)&&o){this.mediaBuffer=this.mediaBufferTimeRanges;s=0;if(i.live||null!=(a=o.details)&&a.live){a=this.mainDetails;if(i.deltaUpdateFailed||!a)return;var h=a.fragments[0];o.details?0===(s=this.alignPlaylists(i,o.details))&&h&&xe(i,s=h.start):i.hasProgramDateTime&&a.hasProgramDateTime?(mr(i,a),s=i.fragments[0].start):h&&xe(i,s=h.start)}o.details=i,this.levelLastLoaded=e,this.startFragRequested||!this.mainDetails&&i.live||this.setStartPosition(o.details,s),this.tick(),!i.live||this.fragCurrent||!this.media||this.state!==Lr||Ke(null,i.fragments,this.media.currentTime,0)||(this.warn("Subtitle playlist not aligned with playback"),o.details=void 0)}}},r._handleFragmentLoadComplete=function(t){var s,e=this,r=t.frag,t=t.payload,n=r.decryptdata,a=this.hls;!this.fragContextChanged(r)&&t&&0<t.byteLength&&n&&n.key&&n.iv&&"AES-128"===n.method&&(s=performance.now(),this.decrypter.decrypt(new Uint8Array(t),n.key.buffer,n.iv.buffer).catch(function(t){throw a.trigger(T.ERROR,{type:E.MEDIA_ERROR,details:S.FRAG_DECRYPT_ERROR,fatal:!1,error:t,reason:t.message,frag:r}),t}).then(function(t){var e=performance.now();a.trigger(T.FRAG_DECRYPTED,{frag:r,payload:t,stats:{tstart:s,tdecrypt:e}})}).catch(function(t){e.warn(t.name+": "+t.message),e.state=Lr}))},r.doTick=function(){var t,a,l,s,h,e,v,n;this.media?this.state===Lr&&(t=this.currentTrackId,t=(e=this.levels)[t],e.length)&&t&&t.details&&(n=(e=t.details).targetduration,a=this.config,s=this.getLoadPosition(),l=(s=dr.bufferedInfo(this.tracksBuffered[this.currentTrackId]||[],s-n,a.maxBufferHole)).end,s=s.len,h=this.getFwdBufferInfo(this.media,le),s>this.getMaxBufferLength(null==h?void 0:h.len)+n||(h=(s=e.fragments).length,n=e.edge,e=null,v=this.fragPrevious,l<n?(n=a.maxFragLookUpTolerance,!(e=Ke(v,s,Math.max(s[0].start,l),n))&&v&&v.start<s[0].start&&(e=s[0])):e=s[h-1],e&&(e=this.mapToInitFragWhenRequired(e),this.fragmentTracker.getState(e)===Qe)&&this.loadFragment(e,t,l))):this.state=Lr},r.getMaxBufferLength=function(e){var r=t.prototype.getMaxBufferLength.call(this);return e?Math.max(r,e):r},r.loadFragment=function(e,r,i){"initSegment"===(this.fragCurrent=e).sn?this._loadInitSegment(e,r):(this.startFragRequested=!0,t.prototype.loadFragment.call(this,e,r,i))},a(e,[{key:"mediaBufferTimeRanges",get:function(){return new rn(this.tracksBuffered[this.currentTrackId]||[])}}]),e}(ur),rn=function(t){this.buffered=void 0;function e(e,r,i){if((r>>>=0)>i-1)throw new DOMException("Failed to execute '"+e+"' on 'TimeRanges': The index provided ("+r+") is greater than the maximum bound ("+i+")");return t[r][e]}this.buffered={get length(){return t.length},end:function(r){return e("end",r,t.length)},start:function(r){return e("start",r,t.length)}}},Ri=function(t){function e(e){var r;return(r=t.call(this,e,"[subtitle-track-controller]")||this).media=null,r.tracks=[],r.groupId=null,r.tracksInGroup=[],r.trackId=-1,r.selectDefaultTrack=!0,r.queuedDefaultTrack=-1,r.trackChangeListener=function(){return r.onTextTracksChanged()},r.asyncPollTrackChange=function(){return r.pollTrackChange(0)},r.useTextTrackPolling=!1,r.subtitlePollingInterval=-1,r._subtitleDisplay=!0,r.registerListeners(),r}l(e,t);var r=e.prototype;return r.destroy=function(){this.unregisterListeners(),this.tracks.length=0,this.tracksInGroup.length=0,this.trackChangeListener=this.asyncPollTrackChange=null,t.prototype.destroy.call(this)},r.registerListeners=function(){var t=this.hls;t.on(T.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(T.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(T.MANIFEST_LOADING,this.onManifestLoading,this),t.on(T.MANIFEST_PARSED,this.onManifestParsed,this),t.on(T.LEVEL_LOADING,this.onLevelLoading,this),t.on(T.LEVEL_SWITCHING,this.onLevelSwitching,this),t.on(T.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),t.on(T.ERROR,this.onError,this)},r.unregisterListeners=function(){var t=this.hls;t.off(T.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(T.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(T.MANIFEST_LOADING,this.onManifestLoading,this),t.off(T.MANIFEST_PARSED,this.onManifestParsed,this),t.off(T.LEVEL_LOADING,this.onLevelLoading,this),t.off(T.LEVEL_SWITCHING,this.onLevelSwitching,this),t.off(T.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),t.off(T.ERROR,this.onError,this)},r.onMediaAttached=function(t,e){this.media=e.media,this.media&&(-1<this.queuedDefaultTrack&&(this.subtitleTrack=this.queuedDefaultTrack,this.queuedDefaultTrack=-1),this.useTextTrackPolling=!(this.media.textTracks&&"onchange"in this.media.textTracks),this.useTextTrackPolling?this.pollTrackChange(500):this.media.textTracks.addEventListener("change",this.asyncPollTrackChange))},r.pollTrackChange=function(t){self.clearInterval(this.subtitlePollingInterval),this.subtitlePollingInterval=self.setInterval(this.trackChangeListener,t)},r.onMediaDetaching=function(){this.media&&(self.clearInterval(this.subtitlePollingInterval),this.useTextTrackPolling||this.media.textTracks.removeEventListener("change",this.asyncPollTrackChange),-1<this.trackId&&(this.queuedDefaultTrack=this.trackId),an(this.media.textTracks).forEach(function(t){me(t)}),this.subtitleTrack=-1,this.media=null)},r.onManifestLoading=function(){this.tracks=[],this.groupId=null,this.tracksInGroup=[],this.trackId=-1,this.selectDefaultTrack=!0},r.onManifestParsed=function(t,e){this.tracks=e.subtitleTracks},r.onSubtitleTrackLoaded=function(t,e){var s,r=e.id,i=e.details,n=this.trackId,n=this.tracksInGroup[n];n?(s=n.details,n.details=e.details,this.log("subtitle track "+r+" loaded ["+i.startSN+"-"+i.endSN+"]"),r===this.trackId&&this.playlistLoaded(r,e,s)):this.warn("Invalid subtitle track id "+r)},r.onLevelLoading=function(t,e){this.switchLevel(e.level)},r.onLevelSwitching=function(t,e){this.switchLevel(e.level)},r.switchLevel=function(t){var r,n,a,s,t=this.hls.levels[t];null!=t&&t.textGroupIds&&(r=t.textGroupIds[t.urlId],t=this.tracksInGroup?this.tracksInGroup[this.trackId]:void 0,this.groupId!==r?(n=this.tracks.filter(function(t){return!r||t.groupId===r}),this.tracksInGroup=n,a=this.findTrackId(null==t?void 0:t.name)||this.findTrackId(),this.groupId=r||null,s={subtitleTracks:n},this.log("Updating subtitle tracks, "+n.length+' track(s) found in "'+r+'" group-id'),this.hls.trigger(T.SUBTITLE_TRACKS_UPDATED,s),-1!==a&&this.setSubtitleTrack(a,t)):this.shouldReloadPlaylist(t)&&this.setSubtitleTrack(this.trackId,t))},r.findTrackId=function(t){for(var e=this.tracksInGroup,r=0;r<e.length;r++){var i=e[r];if((!this.selectDefaultTrack||i.default)&&(!t||t===i.name))return i.id}return-1},r.onError=function(t,e){!e.fatal&&e.context&&e.context.type===oe&&e.context.id===this.trackId&&e.context.groupId===this.groupId&&this.checkRetry(e)},r.loadPlaylist=function(e){t.prototype.loadPlaylist.call(this);var r=this.tracksInGroup[this.trackId];if(this.shouldLoadPlaylist(r)){var i=r.id,n=r.groupId,r=r.url;if(e)try{r=e.addDirectives(r)}catch(t){this.warn("Could not construct new URL with HLS Delivery Directives: "+t)}this.log("Loading subtitle playlist for id "+i),this.hls.trigger(T.SUBTITLE_TRACK_LOADING,{url:r,id:i,groupId:n,deliveryDirectives:e||null})}},r.toggleTrackModes=function(t){var a,e=this,r=this.media,i=this.trackId;r&&(a=(r=an(r.textTracks)).filter(function(t){return t.groupId===e.groupId}),-1===t?[].slice.call(r).forEach(function(t){t.mode="disabled"}):(r=a[i])&&(r.mode="disabled"),i=a[t])&&(i.mode=this.subtitleDisplay?"showing":"hidden")},r.setSubtitleTrack=function(t,e){var r,s,l,u,h,i=this.tracksInGroup;this.media?(this.trackId!==t&&this.toggleTrackModes(t),this.trackId===t&&(-1===t||null!=(r=i[t])&&r.details)||t<-1||t>=i.length||(this.clearTimer(),r=i[t],this.log("Switching to subtitle-track "+t+(r?' "'+r.name+'" lang:'+r.lang+" group:"+r.groupId:"")),this.trackId=t,r?(i=r.id,s=r.groupId,l=r.name,u=r.type,h=r.url,this.hls.trigger(T.SUBTITLE_TRACK_SWITCH,{id:i,groupId:void 0===s?"":s,name:l,type:u,url:h}),i=this.switchParams(r.url,null==e?void 0:e.details),this.loadPlaylist(i)):this.hls.trigger(T.SUBTITLE_TRACK_SWITCH,{id:t}))):this.queuedDefaultTrack=t},r.onTextTracksChanged=function(){if(this.useTextTrackPolling||self.clearInterval(this.subtitlePollingInterval),this.media&&this.hls.config.renderTextTracksNatively){for(var t=-1,e=an(this.media.textTracks),r=0;r<e.length;r++)if("hidden"===e[r].mode)t=r;else if("showing"===e[r].mode){t=r;break}this.subtitleTrack!==t&&(this.subtitleTrack=t)}},a(e,[{key:"subtitleDisplay",get:function(){return this._subtitleDisplay},set:function(t){this._subtitleDisplay=t,-1<this.trackId&&this.toggleTrackModes(this.trackId)}},{key:"subtitleTracks",get:function(){return this.tracksInGroup}},{key:"subtitleTrack",get:function(){return this.trackId},set:function(t){this.selectDefaultTrack=!1;var e=this.tracksInGroup?this.tracksInGroup[this.trackId]:void 0;this.setSubtitleTrack(t,e)}}]),e}(We);function an(t){for(var e=[],r=0;r<t.length;r++){var i=t[r];"subtitles"!==i.kind&&"captions"!==i.kind||!i.label||e.push(t[r])}return e}function dn(t){var e=t;return hn.hasOwnProperty(t)&&(e=hn[t]),String.fromCharCode(e)}function En(t){for(var e=[],r=0;r<t.length;r++)e.push(t[r].toString(16));return e}var sn=function(){function t(t){this.buffers=void 0,this.queues={video:[],audio:[],audiovideo:[]},this.buffers=t}var e=t.prototype;return e.append=function(t,e){var r=this.queues[e];r.push(t),1===r.length&&this.buffers[e]&&this.executeNext(e)},e.insertAbort=function(t,e){this.queues[e].unshift(t),this.executeNext(e)},e.appendBlocker=function(t){var e,r=new Promise(function(t){e=t}),i={execute:e,onStart:function(){},onComplete:function(){},onError:function(){}};return this.append(i,t),r},e.executeNext=function(t){var e=this.buffers,r=this.queues,i=e[t],r=r[t];if(r.length){var a=r[0];try{a.execute()}catch(e){b.warn("[buffer-operation-queue]: Unhandled exception executing the current operation"),a.onError(e),null!=i&&i.updating||(r.shift(),this.executeNext(t))}}},e.shiftAndExecuteNext=function(t){this.queues[t].shift(),this.executeNext(t)},e.current=function(t){return this.queues[t][0]},t}(),on=Kt(),ln=/([ha]vc.)(?:\.[^.,]+)+/,Xr=function(){function t(t){var e=this;this.details=null,this._objectUrl=null,this.operationQueue=void 0,this.listeners=void 0,this.hls=void 0,this.bufferCodecEventsExpected=0,this._bufferCodecEventsTotal=0,this.media=null,this.mediaSource=null,this.lastMpegAudioChunk=null,this.appendError=0,this.tracks={},this.pendingTracks={},this.sourceBuffer=void 0,this._onMediaSourceOpen=function(){var t=e.media,r=e.mediaSource;b.log("[buffer-controller]: Media source opened"),t&&(t.removeEventListener("emptied",e._onMediaEmptied),e.updateMediaElementDuration(),e.hls.trigger(T.MEDIA_ATTACHED,{media:t})),r&&r.removeEventListener("sourceopen",e._onMediaSourceOpen),e.checkPendingTracks()},this._onMediaSourceClose=function(){b.log("[buffer-controller]: Media source closed")},this._onMediaSourceEnded=function(){b.log("[buffer-controller]: Media source ended")},this._onMediaEmptied=function(){var t=e.media,r=e._objectUrl;t&&t.src!==r&&b.error("Media element src was set while attaching MediaSource ("+r+" > "+t.src+")")},this.hls=t,this._initSourceBuffer(),this.registerListeners()}var e=t.prototype;return e.hasSourceTypes=function(){return 0<this.getSourceBufferTypes().length||0<Object.keys(this.pendingTracks).length},e.destroy=function(){this.unregisterListeners(),this.details=null,this.lastMpegAudioChunk=null},e.registerListeners=function(){var t=this.hls;t.on(T.MEDIA_ATTACHING,this.onMediaAttaching,this),t.on(T.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(T.MANIFEST_LOADING,this.onManifestLoading,this),t.on(T.MANIFEST_PARSED,this.onManifestParsed,this),t.on(T.BUFFER_RESET,this.onBufferReset,this),t.on(T.BUFFER_APPENDING,this.onBufferAppending,this),t.on(T.BUFFER_CODECS,this.onBufferCodecs,this),t.on(T.BUFFER_EOS,this.onBufferEos,this),t.on(T.BUFFER_FLUSHING,this.onBufferFlushing,this),t.on(T.LEVEL_UPDATED,this.onLevelUpdated,this),t.on(T.FRAG_PARSED,this.onFragParsed,this),t.on(T.FRAG_CHANGED,this.onFragChanged,this)},e.unregisterListeners=function(){var t=this.hls;t.off(T.MEDIA_ATTACHING,this.onMediaAttaching,this),t.off(T.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(T.MANIFEST_LOADING,this.onManifestLoading,this),t.off(T.MANIFEST_PARSED,this.onManifestParsed,this),t.off(T.BUFFER_RESET,this.onBufferReset,this),t.off(T.BUFFER_APPENDING,this.onBufferAppending,this),t.off(T.BUFFER_CODECS,this.onBufferCodecs,this),t.off(T.BUFFER_EOS,this.onBufferEos,this),t.off(T.BUFFER_FLUSHING,this.onBufferFlushing,this),t.off(T.LEVEL_UPDATED,this.onLevelUpdated,this),t.off(T.FRAG_PARSED,this.onFragParsed,this),t.off(T.FRAG_CHANGED,this.onFragChanged,this)},e._initSourceBuffer=function(){this.sourceBuffer={},this.operationQueue=new sn(this.sourceBuffer),this.listeners={audio:[],video:[],audiovideo:[]},this.lastMpegAudioChunk=null},e.onManifestLoading=function(){this.bufferCodecEventsExpected=this._bufferCodecEventsTotal=0,this.details=null},e.onManifestParsed=function(t,e){var r=2;(!e.audio||e.video)&&e.altAudio||(r=1),this.bufferCodecEventsExpected=this._bufferCodecEventsTotal=r,b.log(this.bufferCodecEventsExpected+" bufferCodec event(s) expected")},e.onMediaAttaching=function(t,e){var i,e=this.media=e.media;e&&on&&((i=this.mediaSource=new on).addEventListener("sourceopen",this._onMediaSourceOpen),i.addEventListener("sourceended",this._onMediaSourceEnded),i.addEventListener("sourceclose",this._onMediaSourceClose),e.src=self.URL.createObjectURL(i),this._objectUrl=e.src,e.addEventListener("emptied",this._onMediaEmptied))},e.onMediaDetaching=function(){var t=this.media,e=this.mediaSource,r=this._objectUrl;if(e){if(b.log("[buffer-controller]: media source detaching"),"open"===e.readyState)try{e.endOfStream()}catch(t){b.warn("[buffer-controller]: onMediaDetaching: "+t.message+" while calling endOfStream")}this.onBufferReset(),e.removeEventListener("sourceopen",this._onMediaSourceOpen),e.removeEventListener("sourceended",this._onMediaSourceEnded),e.removeEventListener("sourceclose",this._onMediaSourceClose),t&&(t.removeEventListener("emptied",this._onMediaEmptied),r&&self.URL.revokeObjectURL(r),t.src===r?(t.removeAttribute("src"),t.load()):b.warn("[buffer-controller]: media.src was changed by a third party - skip cleanup")),this.mediaSource=null,this.media=null,this._objectUrl=null,this.bufferCodecEventsExpected=this._bufferCodecEventsTotal,this.pendingTracks={},this.tracks={}}this.hls.trigger(T.MEDIA_DETACHED,void 0)},e.onBufferReset=function(){var t=this;this.getSourceBufferTypes().forEach(function(e){var r=t.sourceBuffer[e];try{r&&(t.removeBufferListeners(e),t.mediaSource&&t.mediaSource.removeSourceBuffer(r),t.sourceBuffer[e]=void 0)}catch(t){b.warn("[buffer-controller]: Failed to reset the "+e+" buffer",t)}}),this._initSourceBuffer()},e.onBufferCodecs=function(t,e){var r=this,i=this.getSourceBufferTypes().length;Object.keys(e).forEach(function(t){var n,s,o,l,u,a,d,c;i?(n=r.tracks[t])&&"function"==typeof n.buffer.changeType&&(s=(a=e[t]).id,o=a.codec,l=a.levelCodec,u=a.container,a=a.metadata,(d=(n.levelCodec||n.codec).replace(ln,"$1"))!==(c=(l||o).replace(ln,"$1")))&&(r.appendChangeType(t,u+";codecs="+(l||o)),b.log("[buffer-controller]: switching codec "+d+" to "+c),r.tracks[t]={buffer:n.buffer,codec:o,container:u,levelCodec:l,metadata:a,id:s}):r.pendingTracks[t]=e[t]}),i||(this.bufferCodecEventsExpected=Math.max(this.bufferCodecEventsExpected-1,0),this.mediaSource&&"open"===this.mediaSource.readyState&&this.checkPendingTracks())},e.appendChangeType=function(t,e){var r=this,i=this.operationQueue;i.append({execute:function(){var n=r.sourceBuffer[t];n&&(b.log("[buffer-controller]: changing "+t+" sourceBuffer type to "+e),n.changeType(e)),i.shiftAndExecuteNext(t)},onStart:function(){},onComplete:function(){},onError:function(e){b.warn("[buffer-controller]: Failed to change "+t+" SourceBuffer type",e)}},t)},e.onBufferAppending=function(t,e){var r=this,i=this.hls,n=this.operationQueue,a=this.tracks,s=e.data,o=e.type,l=e.frag,u=e.part,h=e.chunkMeta,d=h.buffering[o],e=self.performance.now(),f=(d.start=e,l.stats.buffering),g=u?u.stats.buffering:null,e=(0===f.start&&(f.start=e),g&&0===g.start&&(g.start=e),a.audio),m=!1,p=("audio"===o&&"audio/mpeg"===(null==e?void 0:e.container)&&(m=!this.lastMpegAudioChunk||1===h.id||this.lastMpegAudioChunk.sn!==h.sn,this.lastMpegAudioChunk=h),l.start);n.append({execute:function(){var t,e;d.executeStart=self.performance.now(),m&&(t=r.sourceBuffer[o])&&(e=p-t.timestampOffset,.1<=Math.abs(e))&&(b.log("[buffer-controller]: Updating audio SourceBuffer timestampOffset to "+p+" (delta: "+e+") sn: "+l.sn+")"),t.timestampOffset=p),r.appendExecutor(s,o)},onStart:function(){},onComplete:function(){var n,t=self.performance.now(),e=(d.executeEnd=d.end=t,0===f.first&&(f.first=t),g&&0===g.first&&(g.first=t),r.sourceBuffer),i={};for(n in e)i[n]=dr.getBuffered(e[n]);r.appendError=0,r.hls.trigger(T.BUFFER_APPENDED,{type:o,frag:l,part:u,chunkMeta:h,parent:l.type,timeRanges:i})},onError:function(t){b.error("[buffer-controller]: Error encountered while trying to append to the "+o+" SourceBuffer",t);var e={type:E.MEDIA_ERROR,parent:l.type,details:S.BUFFER_APPEND_ERROR,frag:l,part:u,chunkMeta:h,error:t,err:t,fatal:!1};t.code===DOMException.QUOTA_EXCEEDED_ERR?e.details=S.BUFFER_FULL_ERROR:(r.appendError++,e.details=S.BUFFER_APPEND_ERROR,r.appendError>i.config.appendErrorMaxRetry&&(b.error("[buffer-controller]: Failed "+i.config.appendErrorMaxRetry+" times to append segment in sourceBuffer"),e.fatal=!0)),i.trigger(T.ERROR,e)}},o)},e.onBufferFlushing=function(t,e){function n(t){return{execute:r.removeExecutor.bind(r,t,e.startOffset,e.endOffset),onStart:function(){},onComplete:function(){r.hls.trigger(T.BUFFER_FLUSHED,{type:t})},onError:function(e){b.warn("[buffer-controller]: Failed to remove from "+t+" SourceBuffer",e)}}}var r=this,i=this.operationQueue;e.type?i.append(n(e.type),e.type):this.getSourceBufferTypes().forEach(function(t){i.append(n(t),t)})},e.onFragParsed=function(t,e){var r=this,i=e.frag,n=e.part,e=[],s=(n||i).elementaryStreams;s[M]?e.push("audiovideo"):(s[P]&&e.push("audio"),s[O]&&e.push("video")),0===e.length&&b.warn("Fragments must have at least one ElementaryStreamType set. type: "+i.type+" level: "+i.level+" sn: "+i.sn),this.blockBuffers(function(){var t=self.performance.now(),t=(i.stats.buffering.end=t,n&&(n.stats.buffering.end=t),(n||i).stats);r.hls.trigger(T.FRAG_BUFFERED,{frag:i,part:n,stats:t,id:i.type})},e)},e.onFragChanged=function(t,e){this.flushBackBuffer()},e.onBufferEos=function(t,e){var r=this;this.getSourceBufferTypes().reduce(function(t,i){var n=r.sourceBuffer[i];return!n||e.type&&e.type!==i||(n.ending=!0,n.ended)||(n.ended=!0,b.log("[buffer-controller]: "+i+" sourceBuffer now EOS")),t&&!(n&&!n.ended)},!0)&&(b.log("[buffer-controller]: Queueing mediaSource.endOfStream()"),this.blockBuffers(function(){r.getSourceBufferTypes().forEach(function(t){t=r.sourceBuffer[t];t&&(t.ending=!1)});var t=r.mediaSource;t&&"open"===t.readyState?(b.log("[buffer-controller]: Calling mediaSource.endOfStream()"),t.endOfStream()):t&&b.info("[buffer-controller]: Could not call mediaSource.endOfStream(). mediaSource.readyState: "+t.readyState)}))},e.onLevelUpdated=function(t,e){e=e.details;e.fragments.length&&(this.details=e,this.getSourceBufferTypes().length?this.blockBuffers(this.updateMediaElementDuration.bind(this)):this.updateMediaElementDuration())},e.flushBackBuffer=function(){var n,a,s,o,u,t=this.hls,e=this.details,r=this.media,i=this.sourceBuffer;r&&null!==e&&(n=this.getSourceBufferTypes()).length&&(a=e.live&&null!==t.config.liveBackBufferLength?t.config.liveBackBufferLength:t.config.backBufferLength,!y(a)||a<0||(s=r.currentTime,o=e.levelTargetDuration,r=Math.max(a,o),u=Math.floor(s/o)*o-r,n.forEach(function(r){var n=i[r];if(n){var a=dr.getBuffered(n);if(0<a.length&&u>a.start(0)){if(t.trigger(T.BACK_BUFFER_REACHED,{bufferEnd:u}),e.live)t.trigger(T.LIVE_BACK_BUFFER_REACHED,{bufferEnd:u});else if(n.ended&&a.end(a.length-1)-s<2*o)return void b.info("[buffer-controller]: Cannot flush "+r+" back buffer while SourceBuffer is in ended state");t.trigger(T.BUFFER_FLUSHING,{startOffset:0,endOffset:u,type:r})}}})))},e.updateMediaElementDuration=function(){var t,e,i,n,r,s;this.details&&this.media&&this.mediaSource&&"open"===this.mediaSource.readyState&&(t=this.details,e=this.hls,r=this.media,i=this.mediaSource,n=t.fragments[0].start+t.totalduration,r=r.duration,s=y(i.duration)?i.duration:0,t.live&&e.config.liveDurationInfinity?(b.log("[buffer-controller]: Media Source duration is set to Infinity"),i.duration=1/0,this.updateSeekableRange(t)):(s<n&&r<n||!y(r))&&(b.log("[buffer-controller]: Updating Media Source duration to "+n.toFixed(3)),i.duration=n))},e.updateSeekableRange=function(t){var e=this.mediaSource,r=t.fragments;r.length&&t.live&&null!=e&&e.setLiveSeekableRange&&(r=Math.max(0,r[0].start),t=Math.max(r,r+t.totalduration),e.setLiveSeekableRange(r,t))},e.checkPendingTracks=function(){var t=this.bufferCodecEventsExpected,e=this.operationQueue,r=this.pendingTracks,i=Object.keys(r).length;(i&&!t||2===i)&&(this.createSourceBuffers(r),this.pendingTracks={},(t=this.getSourceBufferTypes()).length?(this.hls.trigger(T.BUFFER_CREATED,{tracks:this.tracks}),t.forEach(function(t){e.executeNext(t)})):(i=new Error("could not create source buffer for media codec(s)"),this.hls.trigger(T.ERROR,{type:E.MEDIA_ERROR,details:S.BUFFER_INCOMPATIBLE_CODECS_ERROR,fatal:!0,error:i,reason:i.message})))},e.createSourceBuffers=function(t){var i,e=this.sourceBuffer,r=this.mediaSource;if(!r)throw Error("createSourceBuffers called when mediaSource was null");for(i in t)if(!e[i]){var n=t[i];if(!n)throw Error("source buffer exists for track "+i+", however track does not");var a=n.levelCodec||n.codec,s=n.container+";codecs="+a;b.log("[buffer-controller]: creating sourceBuffer("+s+")");try{var o=e[i]=r.addSourceBuffer(s),l=i;this.addBufferListener(l,"updatestart",this._onSBUpdateStart),this.addBufferListener(l,"updateend",this._onSBUpdateEnd),this.addBufferListener(l,"error",this._onSBUpdateError),this.tracks[i]={buffer:o,codec:a,container:n.container,levelCodec:n.levelCodec,metadata:n.metadata,id:n.id}}catch(t){b.error("[buffer-controller]: error while trying to add sourceBuffer: "+t.message),this.hls.trigger(T.ERROR,{type:E.MEDIA_ERROR,details:S.BUFFER_ADD_CODEC_ERROR,fatal:!1,error:t,mimeType:s})}}},e._onSBUpdateStart=function(t){this.operationQueue.current(t).onStart()},e._onSBUpdateEnd=function(t){var e=this.operationQueue;e.current(t).onComplete(),e.shiftAndExecuteNext(t)},e._onSBUpdateError=function(t,e){var r=new Error(t+" SourceBuffer error"),r=(b.error("[buffer-controller]: "+r,e),this.hls.trigger(T.ERROR,{type:E.MEDIA_ERROR,details:S.BUFFER_APPENDING_ERROR,error:r,fatal:!1}),this.operationQueue.current(t));r&&r.onError(e)},e.removeExecutor=function(t,e,r){var i=this.media,n=this.mediaSource,a=this.operationQueue,s=this.sourceBuffer[t];i&&n&&s?(i=y(i.duration)?i.duration:1/0,n=y(n.duration)?n.duration:1/0,(e=Math.max(0,e))<(r=Math.min(r,i,n))&&!s.ending?(s.ended=!1,b.log("[buffer-controller]: Removing ["+e+","+r+"] from the "+t+" SourceBuffer"),s.remove(e,r)):a.shiftAndExecuteNext(t)):(b.warn("[buffer-controller]: Attempting to remove from the "+t+" SourceBuffer, but it does not exist"),a.shiftAndExecuteNext(t))},e.appendExecutor=function(t,e){var r=this.operationQueue,i=this.sourceBuffer[e];i?(i.ended=!1,i.appendBuffer(t)):(b.warn("[buffer-controller]: Attempting to append to the "+e+" SourceBuffer, but it does not exist"),r.shiftAndExecuteNext(e))},e.blockBuffers=function(t,e){var i,n,r=this;(e=void 0===e?this.getSourceBufferTypes():e).length?(i=this.operationQueue,n=e.map(function(t){return i.appendBlocker(t)}),Promise.all(n).then(function(){t(),e.forEach(function(t){var e=r.sourceBuffer[t];null!=e&&e.updating||i.shiftAndExecuteNext(t)})})):(b.log("[buffer-controller]: Blocking operation requested, but no SourceBuffers exist"),Promise.resolve().then(t))},e.getSourceBufferTypes=function(){return Object.keys(this.sourceBuffer)},e.addBufferListener=function(t,e,r){var i=this.sourceBuffer[t];i&&(r=r.bind(this,t),this.listeners[t].push({event:e,listener:r}),i.addEventListener(e,r))},e.removeBufferListeners=function(t){var e=this.sourceBuffer[t];e&&this.listeners[t].forEach(function(t){e.removeEventListener(t.event,t.listener)})},t}(),hn={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,128:174,129:176,130:189,131:191,132:8482,133:162,134:163,135:9834,136:224,137:32,138:232,139:226,140:234,141:238,142:244,143:251,144:193,145:201,146:211,147:218,148:220,149:252,150:8216,151:161,152:42,153:8217,154:9473,155:169,156:8480,157:8226,158:8220,159:8221,160:192,161:194,162:199,163:200,164:202,165:203,166:235,167:206,168:207,169:239,170:212,171:217,172:249,173:219,174:171,175:187,176:195,177:227,178:205,179:204,180:236,181:210,182:242,183:213,184:245,185:123,186:125,187:92,188:94,189:95,190:124,191:8764,192:196,193:228,194:214,195:246,196:223,197:165,198:164,199:9475,200:197,201:229,202:216,203:248,204:9487,205:9491,206:9495,207:9499},cn=15,fn=100,gn={17:1,18:3,21:5,22:7,23:9,16:11,19:12,20:14},vn={17:2,18:4,21:6,22:8,23:10,19:13,20:15},mn={25:1,26:3,29:5,30:7,31:9,24:11,27:12,28:14},pn={25:2,26:4,29:6,30:8,31:10,27:13,28:15},yn=["white","green","blue","cyan","red","yellow","magenta","black","transparent"],Tn=function(){function t(){this.time=null,this.verboseLevel=0}return t.prototype.log=function(t,e){this.verboseLevel>=t&&(e="function"==typeof e?e():e,b.log(this.time+" ["+t+"] "+e))},t}(),Sn=function(){function t(t,e,r,i,n){this.foreground=void 0,this.underline=void 0,this.italics=void 0,this.background=void 0,this.flash=void 0,this.foreground=t||"white",this.underline=e||!1,this.italics=r||!1,this.background=i||"black",this.flash=n||!1}var e=t.prototype;return e.reset=function(){this.foreground="white",this.underline=!1,this.italics=!1,this.background="black",this.flash=!1},e.setStyles=function(t){for(var e=["foreground","underline","italics","background","flash"],r=0;r<e.length;r++){var i=e[r];t.hasOwnProperty(i)&&(this[i]=t[i])}},e.isDefault=function(){return"white"===this.foreground&&!this.underline&&!this.italics&&"black"===this.background&&!this.flash},e.equals=function(t){return this.foreground===t.foreground&&this.underline===t.underline&&this.italics===t.italics&&this.background===t.background&&this.flash===t.flash},e.copy=function(t){this.foreground=t.foreground,this.underline=t.underline,this.italics=t.italics,this.background=t.background,this.flash=t.flash},e.toString=function(){return"color="+this.foreground+", underline="+this.underline+", italics="+this.italics+", background="+this.background+", flash="+this.flash},t}(),Ln=function(){function t(t,e,r,i,n,a){this.uchar=void 0,this.penState=void 0,this.uchar=t||" ",this.penState=new Sn(e,r,i,n,a)}var e=t.prototype;return e.reset=function(){this.uchar=" ",this.penState.reset()},e.setChar=function(t,e){this.uchar=t,this.penState.copy(e)},e.setPenState=function(t){this.penState.copy(t)},e.equals=function(t){return this.uchar===t.uchar&&this.penState.equals(t.penState)},e.copy=function(t){this.uchar=t.uchar,this.penState.copy(t.penState)},e.isEmpty=function(){return" "===this.uchar&&this.penState.isDefault()},t}(),Rn=function(){function t(t){this.chars=void 0,this.pos=void 0,this.currPenState=void 0,this.cueStartTime=void 0,this.logger=void 0,this.chars=[];for(var e=0;e<fn;e++)this.chars.push(new Ln);this.logger=t,this.pos=0,this.currPenState=new Sn}var e=t.prototype;return e.equals=function(t){for(var e=!0,r=0;r<fn;r++)if(!this.chars[r].equals(t.chars[r])){e=!1;break}return e},e.copy=function(t){for(var e=0;e<fn;e++)this.chars[e].copy(t.chars[e])},e.isEmpty=function(){for(var t=!0,e=0;e<fn;e++)if(!this.chars[e].isEmpty()){t=!1;break}return t},e.setCursor=function(t){this.pos!==t&&(this.pos=t),this.pos<0?(this.logger.log(3,"Negative cursor position "+this.pos),this.pos=0):this.pos>fn&&(this.logger.log(3,"Too large cursor position "+this.pos),this.pos=fn)},e.moveCursor=function(t){var e=this.pos+t;if(1<t)for(var r=this.pos+1;r<e+1;r++)this.chars[r].setPenState(this.currPenState);this.setCursor(e)},e.backSpace=function(){this.moveCursor(-1),this.chars[this.pos].setChar(" ",this.currPenState)},e.insertChar=function(t){var e=this,r=(144<=t&&this.backSpace(),dn(t));this.pos>=fn?this.logger.log(0,function(){return"Cannot insert "+t.toString(16)+" ("+r+") at position "+e.pos+". Skipping it!"}):(this.chars[this.pos].setChar(r,this.currPenState),this.moveCursor(1))},e.clearFromPos=function(t){for(var e=t;e<fn;e++)this.chars[e].reset()},e.clear=function(){this.clearFromPos(0),this.pos=0,this.currPenState.reset()},e.clearToEndOfRow=function(){this.clearFromPos(this.pos)},e.getTextString=function(){for(var t=[],e=!0,r=0;r<fn;r++){var i=this.chars[r].uchar;" "!==i&&(e=!1),t.push(i)}return e?"":t.join("")},e.setPenStyles=function(t){this.currPenState.setStyles(t),this.chars[this.pos].setPenState(this.currPenState)},t}(),kn=function(){function t(t){this.rows=void 0,this.currRow=void 0,this.nrRollUpRows=void 0,this.lastOutputScreen=void 0,this.logger=void 0,this.rows=[];for(var e=0;e<cn;e++)this.rows.push(new Rn(t));this.logger=t,this.currRow=14,this.nrRollUpRows=null,this.lastOutputScreen=null,this.reset()}var e=t.prototype;return e.reset=function(){for(var t=0;t<cn;t++)this.rows[t].clear();this.currRow=14},e.equals=function(t){for(var e=!0,r=0;r<cn;r++)if(!this.rows[r].equals(t.rows[r])){e=!1;break}return e},e.copy=function(t){for(var e=0;e<cn;e++)this.rows[e].copy(t.rows[e])},e.isEmpty=function(){for(var t=!0,e=0;e<cn;e++)if(!this.rows[e].isEmpty()){t=!1;break}return t},e.backSpace=function(){this.rows[this.currRow].backSpace()},e.clearToEndOfRow=function(){this.rows[this.currRow].clearToEndOfRow()},e.insertChar=function(t){this.rows[this.currRow].insertChar(t)},e.setPen=function(t){this.rows[this.currRow].setPenStyles(t)},e.moveCursor=function(t){this.rows[this.currRow].moveCursor(t)},e.setCursor=function(t){this.logger.log(2,"setCursor: "+t),this.rows[this.currRow].setCursor(t)},e.setPAC=function(t){this.logger.log(2,function(){return"pacData = "+JSON.stringify(t)});var e=t.row-1;if(this.nrRollUpRows&&e<this.nrRollUpRows-1&&(e=this.nrRollUpRows-1),this.nrRollUpRows&&this.currRow!==e){for(var r=0;r<cn;r++)this.rows[r].clear();var i=this.currRow+1-this.nrRollUpRows,n=this.lastOutputScreen;if(n){var a=n.rows[i].cueStartTime,s=this.logger.time;if(a&&null!==s&&a<s)for(var o=0;o<this.nrRollUpRows;o++)this.rows[e-this.nrRollUpRows+o+1].copy(n.rows[i+o])}}this.currRow=e;a=this.rows[this.currRow],null!==t.indent&&(s=t.indent,s=Math.max(s-1,0),a.setCursor(t.indent),t.color=a.chars[s].penState.foreground),a={foreground:t.color,underline:t.underline,italics:t.italics,background:"black",flash:!1};this.setPen(a)},e.setBkgData=function(t){this.logger.log(2,function(){return"bkgData = "+JSON.stringify(t)}),this.backSpace(),this.setPen(t),this.insertChar(32)},e.setRollUpRows=function(t){this.nrRollUpRows=t},e.rollUp=function(){var e,t=this;null!==this.nrRollUpRows?(this.logger.log(1,function(){return t.getDisplayText()}),e=this.currRow+1-this.nrRollUpRows,(e=this.rows.splice(e,1)[0]).clear(),this.rows.splice(this.currRow,0,e),this.logger.log(2,"Rolling up")):this.logger.log(3,"roll_up but nrRollUpRows not set yet")},e.getDisplayText=function(t){t=t||!1;for(var e=[],r="",n=0;n<cn;n++){var a=this.rows[n].getTextString();a&&e.push(t?"Row "+(n+1)+": '"+a+"'":a.trim())}return r=0<e.length?t?"["+e.join(" | ")+"]":e.join("\n"):r},e.getTextAndFormat=function(){return this.rows},t}(),An=function(){function t(t,e,r){this.chNr=void 0,this.outputFilter=void 0,this.mode=void 0,this.verbose=void 0,this.displayedMemory=void 0,this.nonDisplayedMemory=void 0,this.lastOutputScreen=void 0,this.currRollUpRow=void 0,this.writeScreen=void 0,this.cueStartTime=void 0,this.logger=void 0,this.chNr=t,this.outputFilter=e,this.mode=null,this.verbose=0,this.displayedMemory=new kn(r),this.nonDisplayedMemory=new kn(r),this.lastOutputScreen=new kn(r),this.currRollUpRow=this.displayedMemory.rows[14],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null,this.logger=r}var e=t.prototype;return e.reset=function(){this.mode=null,this.displayedMemory.reset(),this.nonDisplayedMemory.reset(),this.lastOutputScreen.reset(),this.outputFilter.reset(),this.currRollUpRow=this.displayedMemory.rows[14],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null},e.getHandler=function(){return this.outputFilter},e.setHandler=function(t){this.outputFilter=t},e.setPAC=function(t){this.writeScreen.setPAC(t)},e.setBkgData=function(t){this.writeScreen.setBkgData(t)},e.setMode=function(t){t!==this.mode&&(this.mode=t,this.logger.log(2,function(){return"MODE="+t}),"MODE_POP-ON"===this.mode?this.writeScreen=this.nonDisplayedMemory:(this.writeScreen=this.displayedMemory,this.writeScreen.reset()),"MODE_ROLL-UP"!==this.mode&&(this.displayedMemory.nrRollUpRows=null,this.nonDisplayedMemory.nrRollUpRows=null),this.mode=t)},e.insertChars=function(t){for(var e=this,r=0;r<t.length;r++)this.writeScreen.insertChar(t[r]);var i=this.writeScreen===this.displayedMemory?"DISP":"NON_DISP";this.logger.log(2,function(){return i+": "+e.writeScreen.getDisplayText(!0)}),"MODE_PAINT-ON"!==this.mode&&"MODE_ROLL-UP"!==this.mode||(this.logger.log(1,function(){return"DISPLAYED: "+e.displayedMemory.getDisplayText(!0)}),this.outputDataUpdate())},e.ccRCL=function(){this.logger.log(2,"RCL - Resume Caption Loading"),this.setMode("MODE_POP-ON")},e.ccBS=function(){this.logger.log(2,"BS - BackSpace"),"MODE_TEXT"!==this.mode&&(this.writeScreen.backSpace(),this.writeScreen===this.displayedMemory)&&this.outputDataUpdate()},e.ccAOF=function(){},e.ccAON=function(){},e.ccDER=function(){this.logger.log(2,"DER- Delete to End of Row"),this.writeScreen.clearToEndOfRow(),this.outputDataUpdate()},e.ccRU=function(t){this.logger.log(2,"RU("+t+") - Roll Up"),this.writeScreen=this.displayedMemory,this.setMode("MODE_ROLL-UP"),this.writeScreen.setRollUpRows(t)},e.ccFON=function(){this.logger.log(2,"FON - Flash On"),this.writeScreen.setPen({flash:!0})},e.ccRDC=function(){this.logger.log(2,"RDC - Resume Direct Captioning"),this.setMode("MODE_PAINT-ON")},e.ccTR=function(){this.logger.log(2,"TR"),this.setMode("MODE_TEXT")},e.ccRTD=function(){this.logger.log(2,"RTD"),this.setMode("MODE_TEXT")},e.ccEDM=function(){this.logger.log(2,"EDM - Erase Displayed Memory"),this.displayedMemory.reset(),this.outputDataUpdate(!0)},e.ccCR=function(){this.logger.log(2,"CR - Carriage Return"),this.writeScreen.rollUp(),this.outputDataUpdate(!0)},e.ccENM=function(){this.logger.log(2,"ENM - Erase Non-displayed Memory"),this.nonDisplayedMemory.reset()},e.ccEOC=function(){var e,t=this;this.logger.log(2,"EOC - End Of Caption"),"MODE_POP-ON"===this.mode&&(e=this.displayedMemory,this.displayedMemory=this.nonDisplayedMemory,this.nonDisplayedMemory=e,this.writeScreen=this.nonDisplayedMemory,this.logger.log(1,function(){return"DISP: "+t.displayedMemory.getDisplayText()})),this.outputDataUpdate(!0)},e.ccTO=function(t){this.logger.log(2,"TO("+t+") - Tab Offset"),this.writeScreen.moveCursor(t)},e.ccMIDROW=function(t){var e={flash:!1};e.underline=t%2==1,e.italics=46<=t,e.italics?e.foreground="white":(t=Math.floor(t/2)-16,e.foreground=["white","green","blue","cyan","red","yellow","magenta"][t]),this.logger.log(2,"MIDROW: "+JSON.stringify(e)),this.writeScreen.setPen(e)},e.outputDataUpdate=function(t){void 0===t&&(t=!1);var e=this.logger.time;null!==e&&this.outputFilter&&(null!==this.cueStartTime||this.displayedMemory.isEmpty()?this.displayedMemory.equals(this.lastOutputScreen)||(this.outputFilter.newCue(this.cueStartTime,e,this.lastOutputScreen),t&&this.outputFilter.dispatchCue&&this.outputFilter.dispatchCue(),this.cueStartTime=this.displayedMemory.isEmpty()?null:e):this.cueStartTime=e,this.lastOutputScreen.copy(this.displayedMemory))},e.cueSplitAtTime=function(t){!this.outputFilter||this.displayedMemory.isEmpty()||(this.outputFilter.newCue&&this.outputFilter.newCue(this.cueStartTime,t,this.displayedMemory),this.cueStartTime=t)},t}(),bn=function(){function t(t,e,r){this.channels=void 0,this.currentChannel=0,this.cmdHistory=void 0,this.logger=void 0;var i=new Tn;this.channels=[null,new An(t,e,i),new An(t+1,r,i)],this.cmdHistory={a:null,b:null},this.logger=i}var e=t.prototype;return e.getHandler=function(t){return this.channels[t].getHandler()},e.setHandler=function(t,e){this.channels[t].setHandler(e)},e.addData=function(t,e){var r,a=!1;this.logger.time=t;for(var s=0;s<e.length;s+=2){var o,i=127&e[s],n=127&e[s+1];0==i&&0==n||(this.logger.log(3,"["+En([e[s],e[s+1]])+"] -> ("+En([i,n])+")"),!(r=(r=(r=(r=this.parseCmd(i,n))||this.parseMidrow(i,n))||this.parsePAC(i,n))||this.parseBackgroundAttributes(i,n))&&(a=this.parseChars(i,n))&&((o=this.currentChannel)&&0<o?this.channels[o].insertChars(a):this.logger.log(2,"No channel found yet. TEXT-MODE?")),r)||a||this.logger.log(2,"Couldn't parse cleaned data "+En([i,n])+" orig: "+En([e[s],e[s+1]]))}},e.parseCmd=function(t,e){var i,n,r=this.cmdHistory;return((20===t||28===t||21===t||29===t)&&32<=e&&e<=47||(23===t||31===t)&&33<=e&&e<=35)&&(In(t,e,r)?(Dn(null,null,r),this.logger.log(3,"Repeated command ("+En([t,e])+") is dropped")):(n=this.channels[i=20===t||21===t||23===t?1:2],20===t||21===t||28===t||29===t?32===e?n.ccRCL():33===e?n.ccBS():34===e?n.ccAOF():35===e?n.ccAON():36===e?n.ccDER():37===e?n.ccRU(2):38===e?n.ccRU(3):39===e?n.ccRU(4):40===e?n.ccFON():41===e?n.ccRDC():42===e?n.ccTR():43===e?n.ccRTD():44===e?n.ccEDM():45===e?n.ccCR():46===e?n.ccENM():47===e&&n.ccEOC():n.ccTO(e-32),Dn(t,e,r),this.currentChannel=i),!0)},e.parseMidrow=function(t,e){var r;return(17===t||25===t)&&32<=e&&e<=47&&((r=17===t?1:2)!==this.currentChannel?(this.logger.log(0,"Mismatch channel in midrow parsing"),!1):!!(r=this.channels[r])&&(r.ccMIDROW(e),this.logger.log(3,"MIDROW ("+En([t,e])+")"),!0))},e.parsePAC=function(t,e){var n,a,i=this.cmdHistory;return((17<=t&&t<=23||25<=t&&t<=31)&&64<=e&&e<=127||(16===t||24===t)&&64<=e&&e<=95)&&(In(t,e,i)?(Dn(null,null,i),!0):!!(a=this.channels[n=t<=23?1:2])&&(a.setPAC(this.interpretPAC((64<=e&&e<=95?1==n?gn:mn:1==n?vn:pn)[t],e)),Dn(t,e,i),this.currentChannel=n,!0))},e.interpretPAC=function(t,e){t={color:null,italics:!1,indent:null,underline:!1,row:t},e=95<e?e-96:e-64;return t.underline=1==(1&e),e<=13?t.color=["white","green","blue","cyan","red","yellow","magenta","white"][Math.floor(e/2)]:e<=15?(t.italics=!0,t.color="white"):t.indent=4*Math.floor((e-16)/2),t},e.parseChars=function(t,e){var r,n=null,a=null;return 17<=(a=25<=t?(r=2,t-8):(r=1,t))&&a<=19?(this.logger.log(2,"Special char '"+dn(a=17===a?e+80:18===a?e+112:e+144)+"' in channel "+r),n=[a]):32<=t&&t<=127&&(n=0===e?[t]:[t,e]),n&&(r=En(n),this.logger.log(3,"Char codes =  "+r.join(",")),Dn(t,e,this.cmdHistory)),n},e.parseBackgroundAttributes=function(t,e){var r,i;return((16===t||24===t)&&32<=e&&e<=47||(23===t||31===t)&&45<=e&&e<=47)&&(i={},16===t||24===t?(r=Math.floor((e-32)/2),i.background=yn[r],e%2==1&&(i.background=i.background+"_semi")):45===e?i.background="transparent":(i.foreground="black",47===e&&(i.underline=!0)),this.channels[t<=23?1:2].setBkgData(i),Dn(t,e,this.cmdHistory),!0)},e.reset=function(){for(var t=0;t<Object.keys(this.channels).length;t++){var e=this.channels[t];e&&e.reset()}this.cmdHistory={a:null,b:null}},e.cueSplitAtTime=function(t){for(var e=0;e<this.channels.length;e++){var r=this.channels[e];r&&r.cueSplitAtTime(t)}},t}();function Dn(t,e,r){r.a=t,r.b=e}function In(t,e,r){return r.a===t&&r.b===e}var _n=function(){function t(t,e){this.timelineController=void 0,this.cueRanges=[],this.trackName=void 0,this.startTime=null,this.endTime=null,this.screen=null,this.timelineController=t,this.trackName=e}var e=t.prototype;return e.dispatchCue=function(){null!==this.startTime&&(this.timelineController.addCues(this.trackName,this.startTime,this.endTime,this.screen,this.cueRanges),this.startTime=null)},e.newCue=function(t,e,r){(null===this.startTime||this.startTime>t)&&(this.startTime=t),this.endTime=e,this.screen=r,this.timelineController.createCaptionsTrack(this.trackName)},e.reset=function(){this.cueRanges=[],this.startTime=null},t}(),wn=function(){var t,e;return"undefined"!=typeof self&&self.VTTCue?self.VTTCue:(t=["","lr","rl"],e=["start","middle","end","left","right"],a.prototype.getCueAsHTML=function(){return self.WebVTT.convertCueToDOMTree(self,this.text)},a);function r(t,e){return"string"==typeof e&&!!Array.isArray(t)&&(e=e.toLowerCase(),!!~t.indexOf(e))&&e}function i(t){return r(e,t)}function n(t){for(var e=arguments.length,r=new Array(1<e?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];for(var n=1;n<arguments.length;n++){var s,a=arguments[n];for(s in a)t[s]=a[s]}return t}function a(e,a,s){var o=this,l={enumerable:!0},u=(o.hasBeenReset=!1,""),h=!1,d=e,c=a,f=s,g=null,v="",m=!0,p="auto",y="start",T=50,E="middle",S=50,L="middle";Object.defineProperty(o,"id",n({},l,{get:function(){return u},set:function(t){u=""+t}})),Object.defineProperty(o,"pauseOnExit",n({},l,{get:function(){return h},set:function(t){h=!!t}})),Object.defineProperty(o,"startTime",n({},l,{get:function(){return d},set:function(t){if("number"!=typeof t)throw new TypeError("Start time must be set to a number.");d=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"endTime",n({},l,{get:function(){return c},set:function(t){if("number"!=typeof t)throw new TypeError("End time must be set to a number.");c=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"text",n({},l,{get:function(){return f},set:function(t){f=""+t,this.hasBeenReset=!0}})),Object.defineProperty(o,"region",n({},l,{get:function(){return g},set:function(t){g=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"vertical",n({},l,{get:function(){return v},set:function(e){e=function(e){return r(t,e)}(e);if(!1===e)throw new SyntaxError("An invalid or illegal string was specified.");v=e,this.hasBeenReset=!0}})),Object.defineProperty(o,"snapToLines",n({},l,{get:function(){return m},set:function(t){m=!!t,this.hasBeenReset=!0}})),Object.defineProperty(o,"line",n({},l,{get:function(){return p},set:function(t){if("number"!=typeof t&&"auto"!==t)throw new SyntaxError("An invalid number or illegal string was specified.");p=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"lineAlign",n({},l,{get:function(){return y},set:function(t){t=i(t);if(!t)throw new SyntaxError("An invalid or illegal string was specified.");y=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"position",n({},l,{get:function(){return T},set:function(t){if(t<0||100<t)throw new Error("Position must be between 0 and 100.");T=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"positionAlign",n({},l,{get:function(){return E},set:function(t){t=i(t);if(!t)throw new SyntaxError("An invalid or illegal string was specified.");E=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"size",n({},l,{get:function(){return S},set:function(t){if(t<0||100<t)throw new Error("Size must be between 0 and 100.");S=t,this.hasBeenReset=!0}})),Object.defineProperty(o,"align",n({},l,{get:function(){return L},set:function(t){t=i(t);if(!t)throw new SyntaxError("An invalid or illegal string was specified.");L=t,this.hasBeenReset=!0}})),o.displayState=void 0}}(),Cn=function(){function t(){}return t.prototype.decode=function(t,e){if(!t)return"";if("string"!=typeof t)throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(t))},t}();function xn(t){function e(t,e,r,i){return 3600*(0|t)+60*(0|e)+(0|r)+parseFloat(i||0)}t=t.match(/^(?:(\d+):)?(\d{2}):(\d{2})(\.\d+)?/);return t?59<parseFloat(t[2])?e(t[2],t[3],0,t[4]):e(t[1],t[2],t[3],t[4]):null}var Pn=function(){function t(){this.values=Object.create(null)}var e=t.prototype;return e.set=function(t,e){this.get(t)||""===e||(this.values[t]=e)},e.get=function(t,e,r){return r?this.has(t)?this.values[t]:e[r]:this.has(t)?this.values[t]:e},e.has=function(t){return t in this.values},e.alt=function(t,e,r){for(var i=0;i<r.length;++i)if(e===r[i]){this.set(t,e);break}},e.integer=function(t,e){/^-?\d+$/.test(e)&&this.set(t,parseInt(e,10))},e.percent=function(t,e){if(/^([\d]{1,3})(\.[\d]*)?%$/.test(e)){e=parseFloat(e);if(0<=e&&e<=100)return this.set(t,e),!0}return!1},t}();function On(t,e,r,i){var a,s,n=i?t.split(i):[t];for(a in n)"string"==typeof n[a]&&2===(s=n[a].split(r)).length&&e(s[0],s[1])}var Mn=new wn(0,0,""),Fn="middle"===Mn.align?"middle":"center";function Un(t){return t.replace(/<br(?: \/)?>/gi,"\n")}function Kn(t,e,r){return t.slice(r=void 0===r?0:r,r+e.length)===e}var Bn=function(){function t(){this.state="INITIAL",this.buffer="",this.decoder=new Cn,this.regionList=[],this.cue=null,this.oncue=void 0,this.onparsingerror=void 0,this.onflush=void 0}var e=t.prototype;return e.parse=function(t){var e=this;function r(){for(var r=0,t=Un(t=e.buffer);r<t.length&&"\r"!==t[r]&&"\n"!==t[r];)++r;var i=t.slice(0,r);return"\r"===t[r]&&++r,"\n"===t[r]&&++r,e.buffer=t.slice(r),i}t&&(e.buffer+=e.decoder.decode(t,{stream:!0}));try{var i="";if("INITIAL"===e.state){if(!/\r\n|\n/.test(e.buffer))return this;var n=(i=r()).match(/^(ï»¿)?WEBVTT([ \t].*)?$/);if(null==n||!n[0])throw new Error("Malformed WebVTT signature.");e.state="HEADER"}for(var a=!1;e.buffer;){if(!/\r\n|\n/.test(e.buffer))return this;switch(a?a=!1:i=r(),e.state){case"HEADER":/:/.test(i)?On(i,function(t,e){},/:/):i||(e.state="ID");continue;case"NOTE":i||(e.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(i)){e.state="NOTE";break}if(!i)continue;if(e.cue=new wn(0,0,""),e.state="CUE",-1===i.indexOf("--\x3e")){e.cue.id=i;continue}case"CUE":if(!e.cue){e.state="BADCUE";continue}try{!function(t,e,r){var i=t;function n(){var e=xn(t);if(null===e)throw new Error("Malformed timestamp: "+i);return t=t.replace(/^[^\sa-zA-Z-]+/,""),e}function a(){t=t.replace(/^\s+/,"")}if(a(),e.startTime=n(),a(),"--\x3e"!==t.slice(0,3))throw new Error("Malformed time stamp (time stamps must be separated by '--\x3e'): "+i);t=t.slice(3),a(),e.endTime=n(),a(),function(t,e){var i=new Pn,t=(On(t,function(t,e){var n;switch(t){case"region":for(var a=r.length-1;0<=a;a--)if(r[a].id===e){i.set(t,r[a].region);break}break;case"vertical":i.alt(t,e,["rl","lr"]);break;case"line":n=e.split(","),i.integer(t,n[0]),i.percent(t,n[0])&&i.set("snapToLines",!1),i.alt(t,n[0],["auto"]),2===n.length&&i.alt("lineAlign",n[1],["start",Fn,"end"]);break;case"position":n=e.split(","),i.percent(t,n[0]),2===n.length&&i.alt("positionAlign",n[1],["start",Fn,"end","line-left","line-right","auto"]);break;case"size":i.percent(t,e);break;case"align":i.alt(t,e,["start",Fn,"end","left","right"])}},/:/,/\s/),e.region=i.get("region",null),e.vertical=i.get("vertical",""),i.get("line","auto")),t=("auto"===t&&-1===Mn.line&&(t=-1),e.line=t,e.lineAlign=i.get("lineAlign","start"),e.snapToLines=i.get("snapToLines",!0),e.size=i.get("size",100),e.align=i.get("align",Fn),i.get("position","auto"));"auto"===t&&50===Mn.position&&(t="start"===e.align||"left"===e.align?0:"end"===e.align||"right"===e.align?100:50),e.position=t}(t,e)}(i,e.cue,e.regionList)}catch(t){e.cue=null,e.state="BADCUE";continue}e.state="CUETEXT";continue;case"CUETEXT":var s=-1!==i.indexOf("--\x3e");if(!i||s&&(a=!0)){e.oncue&&e.cue&&e.oncue(e.cue),e.cue=null,e.state="ID";continue}if(null===e.cue)continue;e.cue.text&&(e.cue.text+="\n"),e.cue.text+=i;continue;case"BADCUE":i||(e.state="ID")}}}catch(t){"CUETEXT"===e.state&&e.cue&&e.oncue&&e.oncue(e.cue),e.cue=null,e.state="INITIAL"===e.state?"BADWEBVTT":"BADCUE"}return this},e.flush=function(){var t=this;try{if(!t.cue&&"HEADER"!==t.state||(t.buffer+="\n\n",t.parse()),"INITIAL"===t.state||"BADWEBVTT"===t.state)throw new Error("Malformed WebVTT signature.")}catch(e){t.onparsingerror&&t.onparsingerror(e)}return t.onflush&&t.onflush(),this},t}(),Gn=/\r\n|\n\r|\n|\r/g,Hn=function(t){for(var e=5381,r=t.length;r;)e=33*e^t.charCodeAt(--r);return(e>>>0).toString()};function Vn(t,e,r){return Hn(t.toString())+Hn(e.toString())+Hn(r)}var jn="stpp.ttml.im1t",Yn=/^(\d{2,}):(\d{2}):(\d{2}):(\d{2})\.?(\d+)?$/,Wn=/^(\d*(?:\.\d*)?)(h|m|s|ms|f|t)$/,qn={left:"start",center:"center",right:"end",start:"start",end:"end"};function Xn(t,e,r,i){var n=Rt(new Uint8Array(t),["mdat"]);if(0!==n.length){var l,n=n.map(function(t){return ft(t)}),h=Ti(e.baseTime,1,1/(e=void 0===(e=e.timescale)?1:e),l=void 0===l?!1:l);try{n.forEach(function(t){return r(function(t,e){var i,n,a,s,l,r=(new DOMParser).parseFromString(t,"text/xml").getElementsByTagName("tt")[0];if(r)return i={frameRate:30,subFrameRate:1,frameRateMultiplier:0,tickRate:0},n=Object.keys(i).reduce(function(t,e){return t[e]=r.getAttribute("ttp:"+e)||i[e],t},{}),a="preserve"!==r.getAttribute("xml:space"),s=Qn(zn(r,"styling","style")),l=Qn(zn(r,"layout","region")),t=zn(r,"body","[begin]"),[].map.call(t,function(t){var r=function Zn(t,e){return[].slice.call(t.childNodes).reduce(function(t,r,i){return"br"===r.nodeName&&i?t+"\n":null!=(i=r.childNodes)&&i.length?Zn(r,e):e?t+r.textContent.trim().replace(/\s+/g," "):t+r.textContent},"")}(t,a);if(!r||!t.hasAttribute("begin"))return null;var i=ta(t.getAttribute("begin"),n),u=ta(t.getAttribute("dur"),n),h=ta(t.getAttribute("end"),n);if(null===i)throw Jn(t);if(null===h){if(null===u)throw Jn(t);h=i+u}u=new wn(i-e,h-e,r),u.id=Vn(u.startTime,u.endTime,u.text),i=function(t,e,r){var i="http://www.w3.org/ns/ttml#styling",n=null,a=null!=t&&t.hasAttribute("style")?t.getAttribute("style"):null;return a&&r.hasOwnProperty(a)&&(n=r[a]),["displayAlign","textAlign","color","backgroundColor","fontSize","fontFamily"].reduce(function(r,a){var s=$n(e,i,a)||$n(t,i,a)||$n(n,i,a);return s&&(r[a]=s),r},{})}(l[t.getAttribute("region")],s[t.getAttribute("style")],s),h=i.textAlign;return h&&((r=qn[h])&&(u.lineAlign=r),u.align=h),o(u,i),u}).filter(function(t){return null!==t});throw new Error("Invalid ttml")}(t,h))})}catch(t){i(t)}}else i(new Error("Could not parse IMSC1 mdat"))}function zn(t,e,r){t=t.getElementsByTagName(e)[0];return t?[].slice.call(t.querySelectorAll(r)):[]}function Qn(t){return t.reduce(function(t,e){var r=e.getAttribute("xml:id");return r&&(t[r]=e),t},{})}function $n(t,e,r){return t&&t.hasAttributeNS(e,r)?t.getAttributeNS(e,r):null}function Jn(t){return new Error("Could not parse ttml timestamp "+t)}function ta(t,e){var r;return t?(null===(r=xn(t))&&(Yn.test(t)?r=function(t,e){var t=Yn.exec(t),i=(0|t[4])+(0|t[5])/e.subFrameRate;return 3600*(0|t[1])+60*(0|t[2])+(0|t[3])+i/e.frameRate}(t,e):Wn.test(t)&&(r=function(t,e){var t=Wn.exec(t),i=Number(t[1]);switch(t[2]){case"h":return 3600*i;case"m":return 60*i;case"ms":return 1e3*i;case"f":return i/e.frameRate;case"t":return i/e.tickRate}return i}(t,e))),r):null}ur=function(){function t(t){var e,r,i,n;this.hls=void 0,this.media=null,this.config=void 0,this.enabled=!0,this.Cues=void 0,this.textTracks=[],this.tracks=[],this.initPTS=[],this.unparsedVttFrags=[],this.captionsTracks={},this.nonNativeCaptionsTracks={},this.cea608Parser1=void 0,this.cea608Parser2=void 0,this.lastSn=-1,this.lastPartIndex=-1,this.prevCC=-1,this.vttCCs={ccOffset:0,presentationOffset:0,0:{start:0,prevCC:-1,new:!0}},this.captionsProperties=void 0,this.hls=t,this.config=t.config,this.Cues=t.config.cueHandler,this.captionsProperties={textTrack1:{label:this.config.captionsTextTrack1Label,languageCode:this.config.captionsTextTrack1LanguageCode},textTrack2:{label:this.config.captionsTextTrack2Label,languageCode:this.config.captionsTextTrack2LanguageCode},textTrack3:{label:this.config.captionsTextTrack3Label,languageCode:this.config.captionsTextTrack3LanguageCode},textTrack4:{label:this.config.captionsTextTrack4Label,languageCode:this.config.captionsTextTrack4LanguageCode}},this.config.enableCEA708Captions&&(e=new _n(this,"textTrack1"),r=new _n(this,"textTrack2"),i=new _n(this,"textTrack3"),n=new _n(this,"textTrack4"),this.cea608Parser1=new bn(1,e,r),this.cea608Parser2=new bn(3,i,n)),t.on(T.MEDIA_ATTACHING,this.onMediaAttaching,this),t.on(T.MEDIA_DETACHING,this.onMediaDetaching,this),t.on(T.MANIFEST_LOADING,this.onManifestLoading,this),t.on(T.MANIFEST_LOADED,this.onManifestLoaded,this),t.on(T.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),t.on(T.FRAG_LOADING,this.onFragLoading,this),t.on(T.FRAG_LOADED,this.onFragLoaded,this),t.on(T.FRAG_PARSING_USERDATA,this.onFragParsingUserdata,this),t.on(T.FRAG_DECRYPTED,this.onFragDecrypted,this),t.on(T.INIT_PTS_FOUND,this.onInitPtsFound,this),t.on(T.SUBTITLE_TRACKS_CLEARED,this.onSubtitleTracksCleared,this),t.on(T.BUFFER_FLUSHING,this.onBufferFlushing,this)}var e=t.prototype;return e.destroy=function(){var t=this.hls;t.off(T.MEDIA_ATTACHING,this.onMediaAttaching,this),t.off(T.MEDIA_DETACHING,this.onMediaDetaching,this),t.off(T.MANIFEST_LOADING,this.onManifestLoading,this),t.off(T.MANIFEST_LOADED,this.onManifestLoaded,this),t.off(T.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),t.off(T.FRAG_LOADING,this.onFragLoading,this),t.off(T.FRAG_LOADED,this.onFragLoaded,this),t.off(T.FRAG_PARSING_USERDATA,this.onFragParsingUserdata,this),t.off(T.FRAG_DECRYPTED,this.onFragDecrypted,this),t.off(T.INIT_PTS_FOUND,this.onInitPtsFound,this),t.off(T.SUBTITLE_TRACKS_CLEARED,this.onSubtitleTracksCleared,this),t.off(T.BUFFER_FLUSHING,this.onBufferFlushing,this),this.hls=this.config=this.cea608Parser1=this.cea608Parser2=null},e.addCues=function(t,e,r,i,n){for(var a,o,f,u=!1,h=n.length;h--;){var d=n[h],s=(a=d[0],s=d[1],o=e,Math.min(s,r)-Math.max(a,o));if(0<=s&&(d[0]=Math.min(d[0],e),d[1]=Math.max(d[1],r),u=!0,.5<s/(r-e)))return}u||n.push([e,r]),this.config.renderTextTracksNatively?(f=this.captionsTracks[t],this.Cues.newCue(f,e,r,i)):(f=this.Cues.newCue(null,e,r,i),this.hls.trigger(T.CUES_PARSED,{type:"captions",cues:f,track:t}))},e.onInitPtsFound=function(t,e){var r=this,i=e.frag,n=e.id,a=e.initPTS,e=e.timescale,o=this.unparsedVttFrags;"main"===n&&(this.initPTS[i.cc]={baseTime:a,timescale:e}),o.length&&(this.unparsedVttFrags=[],o.forEach(function(t){r.onFragLoaded(T.FRAG_LOADED,t)}))},e.getExistingTrack=function(t){var e=this.media;if(e)for(var r=0;r<e.textTracks.length;r++){var i=e.textTracks[r];if(i[t])return i}return null},e.createCaptionsTrack=function(t){this.config.renderTextTracksNatively?this.createNativeTrack(t):this.createNonNativeTrack(t)},e.createNativeTrack=function(t){var r,i,a,e,o;this.captionsTracks[t]||(e=this.captionsProperties,r=this.captionsTracks,i=this.media,a=(e=e[t]).label,e=e.languageCode,(o=this.getExistingTrack(t))?(r[t]=o,me(r[t]),ge(r[t],i)):(o=this.createTextTrack("captions",a,e))&&(o[t]=!0,r[t]=o))},e.createNonNativeTrack=function(t){var e;this.nonNativeCaptionsTracks[t]||(e=this.captionsProperties[t])&&(e={_id:t,label:e.label,kind:"captions",default:!!e.media&&!!e.media.default,closedCaptions:e.media},this.nonNativeCaptionsTracks[t]=e,this.hls.trigger(T.NON_NATIVE_TEXT_TRACKS_FOUND,{tracks:[e]}))},e.createTextTrack=function(t,e,r){var i=this.media;if(i)return i.addTextTrack(t,e,r)},e.onMediaAttaching=function(t,e){this.media=e.media,this._cleanTracks()},e.onMediaDetaching=function(){var t=this.captionsTracks;Object.keys(t).forEach(function(e){me(t[e]),delete t[e]}),this.nonNativeCaptionsTracks={}},e.onManifestLoading=function(){this.lastSn=-1,this.lastPartIndex=-1,this.prevCC=-1,this.vttCCs={ccOffset:0,presentationOffset:0,0:{start:0,prevCC:-1,new:!0}},this._cleanTracks(),this.tracks=[],this.captionsTracks={},this.nonNativeCaptionsTracks={},this.textTracks=[],this.unparsedVttFrags=this.unparsedVttFrags||[],this.initPTS=[],this.cea608Parser1&&this.cea608Parser2&&(this.cea608Parser1.reset(),this.cea608Parser2.reset())},e._cleanTracks=function(){var t=this.media;if(t){var e=t.textTracks;if(e)for(var r=0;r<e.length;r++)me(e[r])}},e.onSubtitleTracksUpdated=function(t,e){var a,r=this,e=e.subtitleTracks||[],n=e.some(function(t){return t.textCodec===jn});(this.config.enableWebVTT||n&&this.config.enableIMSC1)&&(Ji(this.tracks,e)?this.tracks=e:(this.textTracks=[],this.tracks=e,this.config.renderTextTracksNatively?(a=this.media?this.media.textTracks:null,this.tracks.forEach(function(t,e){var i;if(a&&e<a.length){for(var n=null,s=0;s<a.length;s++)if(function(t,e){return t&&t.label===e.name&&!t.textTrack1&&!t.textTrack2}(a[s],t)){n=a[s];break}n&&(i=n)}i?me(i):(e=r._captionsOrSubtitlesFromCharacteristics(t),(i=r.createTextTrack(e,t.name,t.lang))&&(i.mode="disabled")),i&&(i.groupId=t.groupId,r.textTracks.push(i))})):this.tracks.length&&(n=this.tracks.map(function(t){return{label:t.name,kind:t.type.toLowerCase(),default:t.default,subtitleTrack:t}}),this.hls.trigger(T.NON_NATIVE_TEXT_TRACKS_FOUND,{tracks:n}))))},e._captionsOrSubtitlesFromCharacteristics=function(t){if(t.attrs.CHARACTERISTICS){var e=/transcribes-spoken-dialog/gi.test(t.attrs.CHARACTERISTICS),t=/describes-music-and-sound/gi.test(t.attrs.CHARACTERISTICS);if(e&&t)return"captions"}return"subtitles"},e.onManifestLoaded=function(t,e){var r=this;this.config.enableCEA708Captions&&e.captions&&e.captions.forEach(function(t){var e=/(?:CC|SERVICE)([1-4])/.exec(t.instreamId);e&&(e="textTrack"+e[1],e=r.captionsProperties[e])&&(e.label=t.name,t.lang&&(e.languageCode=t.lang),e.media=t)})},e.closedCaptionsForLevel=function(t){t=this.hls.levels[t.level];return null==t?void 0:t.attrs["CLOSED-CAPTIONS"]},e.onFragLoading=function(t,e){var l,r=this.cea608Parser1,i=this.cea608Parser2,n=this.lastSn,a=this.lastPartIndex;this.enabled&&r&&i&&e.frag.type===le&&(l=e.frag.sn,e=null!=(e=null==e||null==(e=e.part)?void 0:e.index)?e:-1,l===n+1||l===n&&e===a+1||(r.reset(),i.reset()),this.lastSn=l,this.lastPartIndex=e)},e.onFragLoaded=function(t,e){var s,o,r=e.frag,i=e.payload,n=this.initPTS,a=this.unparsedVttFrags;r.type===he&&(i.byteLength?n[r.cc]?(s=r.decryptdata,o="stats"in e,null!=s&&s.encrypted&&!o||(s=this.tracks[r.level],(o=this.vttCCs)[r.cc]||(o[r.cc]={start:r.start,prevCC:this.prevCC,new:!0},this.prevCC=r.cc),s&&s.textCodec===jn?this._parseIMSC1(r,i):this._parseVTTs(r,i,o))):(a.push(e),n.length&&this.hls.trigger(T.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:r,error:new Error("Missing initial subtitle PTS")})):this.hls.trigger(T.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:r,error:new Error("Empty subtitle payload")}))},e._parseIMSC1=function(t,e){var r=this,i=this.hls;Xn(e,this.initPTS[t.cc],function(e){r._appendCues(e,t.level),i.trigger(T.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:t})},function(e){b.log("Failed to parse IMSC1: "+e),i.trigger(T.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:t,error:e})})},e._parseVTTs=function(t,e,r){var i,n=this,a=this.hls;!function(t,e,r,i,n,a,s){var u,h=new Bn,t=ft(new Uint8Array(t)).trim().replace(Gn,"\n").split("\n"),c=[],f=Ti(e.baseTime,9e4,1/(e=void 0===(e=e.timescale)?1:e)),g="00:00.000",v=0,m=0,p=!0;h.oncue=function(t){var e=r[i],a=r.ccOffset,s=(v-f)/9e4,e=(null!=e&&e.new&&(void 0!==m?a=r.ccOffset=e.start:function(t,e,r){var i=t[e],n=t[i.prevCC];if(!n||!n.new&&i.new)return t.ccOffset=t.presentationOffset=i.start,i.new=!1;for(;null!=n&&n.new;)t.ccOffset+=i.start-n.start,i.new=!1,n=t[(i=n).prevCC];t.presentationOffset=r}(r,i,s)),s&&(a=s-r.presentationOffset),t.endTime-t.startTime),s=ki(9e4*(t.startTime+a-m),9e4*n)/9e4,a=(t.startTime=Math.max(s,0),t.endTime=Math.max(s+e,0),t.text.trim());t.text=decodeURIComponent(encodeURIComponent(a)),t.id||(t.id=Vn(t.startTime,t.endTime,a)),0<t.endTime&&c.push(t)},h.onparsingerror=function(t){u=t},h.onflush=function(){u?s(u):a(c)},t.forEach(function(t){if(p){if(Kn(t,"X-TIMESTAMP-MAP=")){p=!1,t.slice(16).split(",").forEach(function(t){Kn(t,"LOCAL:")?g=t.slice(6):Kn(t,"MPEGTS:")&&(v=parseInt(t.slice(7)))});try{m=function(t){var e=parseInt(t.slice(-3)),r=parseInt(t.slice(-6,-4)),i=parseInt(t.slice(-9,-7)),n=9<t.length?parseInt(t.substring(0,t.indexOf(":"))):0;if(y(e)&&y(r)&&y(i)&&y(n))return e+1e3*r+6e4*i+36e5*n;throw Error("Malformed X-TIMESTAMP-MAP: Local:"+t)}(g)/1e3}catch(t){u=t}return}""===t&&(p=!1)}h.parse(t+"\n")}),h.flush()}(null!=(i=t.initSegment)&&i.data?It(t.initSegment.data,new Uint8Array(e)):e,this.initPTS[t.cc],r,t.cc,t.start,function(e){n._appendCues(e,t.level),a.trigger(T.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:t})},function(r){n._fallbackToIMSC1(t,e),b.log("Failed to parse VTT cue: "+r),a.trigger(T.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:t,error:r})})},e._fallbackToIMSC1=function(t,e){var r=this,i=this.tracks[t.level];i.textCodec||Xn(e,this.initPTS[t.cc],function(){i.textCodec=jn,r._parseIMSC1(t,e)},function(){i.textCodec="wvtt"})},e._appendCues=function(t,e){var i,n,r=this.hls;this.config.renderTextTracksNatively?(i=this.textTracks[e])&&"disabled"!==i.mode&&t.forEach(function(t){return ve(i,t)}):(n=this.tracks[e])&&(n=n.default?"default":"subtitles"+e,r.trigger(T.CUES_PARSED,{type:"subtitles",cues:t,track:n}))},e.onFragDecrypted=function(t,e){var r=e.frag;r.type===he&&(this.initPTS[r.cc]?this.onFragLoaded(T.FRAG_LOADED,e):this.unparsedVttFrags.push(e))},e.onSubtitleTracksCleared=function(){this.tracks=[],this.captionsTracks={}},e.onFragParsingUserdata=function(t,e){var r=this.cea608Parser1,i=this.cea608Parser2;if(this.enabled&&r&&i){var n=e.frag,a=e.samples;if(n.type!==le||"NONE"!==this.closedCaptionsForLevel(n))for(var s=0;s<a.length;s++){var o=a[s].bytes;o&&(o=this.extractCea608Data(o),r.addData(a[s].pts,o[0]),i.addData(a[s].pts,o[1]))}}},e.onBufferFlushing=function(t,e){var o,l,r=e.startOffset,i=e.endOffset,n=e.endOffsetSubtitles,e=e.type,s=this.media;!s||s.currentTime<i||(e&&"video"!==e||(o=this.captionsTracks,Object.keys(o).forEach(function(t){return pe(o[t],r,i)})),this.config.renderTextTracksNatively&&0===r&&void 0!==n&&(l=this.textTracks,Object.keys(l).forEach(function(t){return pe(l[t],r,n)})))},e.extractCea608Data=function(t){for(var e=[[],[]],r=31&t[0],i=2,n=0;n<r;n++){var a=t[i++],s=127&t[i++],o=127&t[i++];0==s&&0==o||0==(4&a)||0!=(a=3&a)&&1!=a||(e[a].push(s),e[a].push(o))}return e},t}();var We=function(){function t(t){this.hls=void 0,this.autoLevelCapping=void 0,this.firstLevel=void 0,this.media=void 0,this.restrictedLevels=void 0,this.timer=void 0,this.clientRect=void 0,this.streamController=void 0,this.hls=t,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.firstLevel=-1,this.media=null,this.restrictedLevels=[],this.timer=void 0,this.clientRect=null,this.registerListeners()}var e=t.prototype;return e.setStreamController=function(t){this.streamController=t},e.destroy=function(){this.unregisterListener(),this.hls.config.capLevelToPlayerSize&&this.stopCapping(),this.media=null,this.clientRect=null,this.hls=this.streamController=null},e.registerListeners=function(){var t=this.hls;t.on(T.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),t.on(T.MEDIA_ATTACHING,this.onMediaAttaching,this),t.on(T.MANIFEST_PARSED,this.onManifestParsed,this),t.on(T.BUFFER_CODECS,this.onBufferCodecs,this),t.on(T.MEDIA_DETACHING,this.onMediaDetaching,this)},e.unregisterListener=function(){var t=this.hls;t.off(T.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),t.off(T.MEDIA_ATTACHING,this.onMediaAttaching,this),t.off(T.MANIFEST_PARSED,this.onManifestParsed,this),t.off(T.BUFFER_CODECS,this.onBufferCodecs,this),t.off(T.MEDIA_DETACHING,this.onMediaDetaching,this)},e.onFpsDropLevelCapping=function(t,e){e=this.hls.levels[e.droppedLevel];this.isLevelAllowed(e)&&this.restrictedLevels.push({bitrate:e.bitrate,height:e.height,width:e.width})},e.onMediaAttaching=function(t,e){this.media=e.media instanceof HTMLVideoElement?e.media:null,this.clientRect=null},e.onManifestParsed=function(t,e){var r=this.hls;this.restrictedLevels=[],this.firstLevel=e.firstLevel,r.config.capLevelToPlayerSize&&e.video&&this.startCapping()},e.onBufferCodecs=function(t,e){this.hls.config.capLevelToPlayerSize&&e.video&&this.startCapping()},e.onMediaDetaching=function(){this.stopCapping()},e.detectPlayerSize=function(){var t,e;this.media&&0<this.mediaHeight&&0<this.mediaWidth&&(t=this.hls.levels).length&&((e=this.hls).autoLevelCapping=this.getMaxLevel(t.length-1),e.autoLevelCapping>this.autoLevelCapping&&this.streamController&&this.streamController.nextLevelSwitch(),this.autoLevelCapping=e.autoLevelCapping)},e.getMaxLevel=function(e){var r=this,i=this.hls.levels;return i.length?(i=i.filter(function(t,i){return r.isLevelAllowed(t)&&i<=e}),this.clientRect=null,t.getMaxLevelByMediaSize(i,this.mediaWidth,this.mediaHeight)):-1},e.startCapping=function(){this.timer||(this.autoLevelCapping=Number.POSITIVE_INFINITY,this.hls.firstLevel=this.getMaxLevel(this.firstLevel),self.clearInterval(this.timer),this.timer=self.setInterval(this.detectPlayerSize.bind(this),1e3),this.detectPlayerSize())},e.stopCapping=function(){this.restrictedLevels=[],this.firstLevel=-1,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.timer&&(self.clearInterval(this.timer),this.timer=void 0)},e.getDimensions=function(){var t,e,r;return this.clientRect||(e={width:0,height:0},(t=this.media)&&(r=t.getBoundingClientRect(),e.width=r.width,e.height=r.height,e.width||e.height||(e.width=r.right-r.left||t.width||0,e.height=r.bottom-r.top||t.height||0)),this.clientRect=e)},e.isLevelAllowed=function(t){return!this.restrictedLevels.some(function(e){return t.bitrate===e.bitrate&&t.width===e.width&&t.height===e.height})},t.getMaxLevelByMediaSize=function(t,e,r){if(null==t||!t.length)return-1;for(var n,a=t.length-1,s=0;s<t.length;s+=1){var o=t[s];if((o.width>=e||o.height>=r)&&(!(n=t[s+1])||o.width!==n.width||o.height!==n.height)){a=s;break}}return a},a(t,[{key:"mediaWidth",get:function(){return this.getDimensions().width*this.contentScaleFactor}},{key:"mediaHeight",get:function(){return this.getDimensions().height*this.contentScaleFactor}},{key:"contentScaleFactor",get:function(){var t=1;if(!this.hls.config.ignoreDevicePixelRatio)try{t=self.devicePixelRatio}catch(t){}return t}}]),t}(),na=function(){function t(t){this.hls=void 0,this.isVideoPlaybackQualityAvailable=!1,this.timer=void 0,this.media=null,this.lastTime=void 0,this.lastDroppedFrames=0,this.lastDecodedFrames=0,this.streamController=void 0,this.hls=t,this.registerListeners()}var e=t.prototype;return e.setStreamController=function(t){this.streamController=t},e.registerListeners=function(){this.hls.on(T.MEDIA_ATTACHING,this.onMediaAttaching,this)},e.unregisterListeners=function(){this.hls.off(T.MEDIA_ATTACHING,this.onMediaAttaching,this)},e.destroy=function(){this.timer&&clearInterval(this.timer),this.unregisterListeners(),this.isVideoPlaybackQualityAvailable=!1,this.media=null},e.onMediaAttaching=function(t,e){var r=this.hls.config;r.capLevelOnFPSDrop&&(e=e.media instanceof self.HTMLVideoElement?e.media:null,(this.media=e)&&"function"==typeof e.getVideoPlaybackQuality&&(this.isVideoPlaybackQualityAvailable=!0),self.clearInterval(this.timer),this.timer=self.setInterval(this.checkFPSInterval.bind(this),r.fpsDroppedMonitoringPeriod))},e.checkFPS=function(t,e,r){var a,s,l,n,i=performance.now();e&&(this.lastTime&&(n=i-this.lastTime,a=r-this.lastDroppedFrames,s=e-this.lastDecodedFrames,n=1e3*a/n,(l=this.hls).trigger(T.FPS_DROP,{currentDropped:a,currentDecoded:s,totalDroppedFrames:r}),0<n)&&a>l.config.fpsDroppedMonitoringThreshold*s&&(n=l.currentLevel,b.warn("drop FPS ratio greater than max allowed value for currentLevel: "+n),0<n)&&(-1===l.autoLevelCapping||l.autoLevelCapping>=n)&&(l.trigger(T.FPS_DROP_LEVEL_CAPPING,{level:--n,droppedLevel:l.currentLevel}),l.autoLevelCapping=n,this.streamController.nextLevelSwitch()),this.lastTime=i,this.lastDroppedFrames=r,this.lastDecodedFrames=e)},e.checkFPSInterval=function(){var e,t=this.media;t&&(this.isVideoPlaybackQualityAvailable?(e=t.getVideoPlaybackQuality(),this.checkFPS(t,e.totalVideoFrames,e.droppedVideoFrames)):this.checkFPS(t,t.webkitDecodedFrameCount,t.webkitDroppedFrameCount))},t}(),aa="[eme]",sa=function(){function t(e){this.hls=void 0,this.config=void 0,this.media=null,this.keyFormatPromise=null,this.keySystemAccessPromises={},this._requestLicenseFailureCount=0,this.mediaKeySessions=[],this.keyIdToKeySessionPromise={},this.setMediaKeysQueue=t.CDMCleanupPromise?[t.CDMCleanupPromise]:[],this.onMediaEncrypted=this._onMediaEncrypted.bind(this),this.onWaitingForKey=this._onWaitingForKey.bind(this),this.debug=b.debug.bind(b,aa),this.log=b.log.bind(b,aa),this.warn=b.warn.bind(b,aa),this.error=b.error.bind(b,aa),this.hls=e,this.config=e.config,this.registerListeners()}var r=t.prototype;return r.destroy=function(){this.unregisterListeners(),this.onMediaDetached();var t=this.config;t.requestMediaKeySystemAccessFunc=null,t.licenseXhrSetup=t.licenseResponseCallback=void 0,t.drmSystems=t.drmSystemOptions={},this.hls=this.onMediaEncrypted=this.onWaitingForKey=this.keyIdToKeySessionPromise=null,this.config=null},r.registerListeners=function(){this.hls.on(T.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.on(T.MEDIA_DETACHED,this.onMediaDetached,this),this.hls.on(T.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.on(T.MANIFEST_LOADED,this.onManifestLoaded,this)},r.unregisterListeners=function(){this.hls.off(T.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.off(T.MEDIA_DETACHED,this.onMediaDetached,this),this.hls.off(T.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.off(T.MANIFEST_LOADED,this.onManifestLoaded,this)},r.getLicenseServerUrl=function(t){var e=this.config,r=e.drmSystems,e=e.widevineLicenseUrl,r=r[t];if(r)return r.licenseUrl;if(t===H.WIDEVINE&&e)return e;throw new Error('no license server URL configured for key-system "'+t+'"')},r.getServerCertificateUrl=function(t){var e=this.config.drmSystems[t];if(e)return e.serverCertificateUrl;this.log('No Server Certificate in config.drmSystems["'+t+'"]')},r.attemptKeySystemAccess=function(t){function i(t,e,r){return!!t&&r.indexOf(t)===e}var e=this,r=this.hls.levels,n=r.map(function(t){return t.audioCodec}).filter(i),a=r.map(function(t){return t.videoCodec}).filter(i);return n.length+a.length===0&&a.push("avc1.42e01e"),new Promise(function(r,i){!function t(s){var o=s.shift();e.getMediaKeysPromise(o,n,a).then(function(t){return r({keySystem:o,mediaKeys:t})}).catch(function(e){s.length?t(s):i(e instanceof oa?e:new oa({type:E.KEY_SYSTEM_ERROR,details:S.KEY_SYSTEM_NO_ACCESS,error:e,fatal:!0},e.message))})}(t)})},r.requestMediaKeySystemAccess=function(t,e){var i,r=this.config.requestMediaKeySystemAccessFunc;return"function"!=typeof r?(i="Configured requestMediaKeySystemAccess is not a function "+r,null===Z&&"http:"===self.location.protocol&&(i="navigator.requestMediaKeySystemAccess is not available over insecure protocol "+location.protocol),Promise.reject(new Error(i))):r(t,e)},r.getMediaKeysPromise=function(t,e,r){var o,i=this,e=function(t,e,r,i){var n;switch(t){case H.FAIRPLAY:n=["cenc","sinf"];break;case H.WIDEVINE:case H.PLAYREADY:n=["cenc"];break;case H.CLEARKEY:n=["cenc","keyids"];break;default:throw new Error("Unknown key-system: "+t)}return function(e,r,i){return[{initDataTypes:n,persistentState:i.persistentState||"not-allowed",distinctiveIdentifier:i.distinctiveIdentifier||"not-allowed",sessionTypes:i.sessionTypes||[i.sessionType||"temporary"],audioCapabilities:e.map(function(t){return{contentType:'audio/mp4; codecs="'+t+'"',robustness:i.audioRobustness||"",encryptionScheme:i.audioEncryptionScheme||null}}),videoCapabilities:r.map(function(t){return{contentType:'video/mp4; codecs="'+t+'"',robustness:i.videoRobustness||"",encryptionScheme:i.videoEncryptionScheme||null}})}]}(e,r,i)}(t,e,r,this.config.drmSystemOptions),a=this.keySystemAccessPromises[t],r=null==a?void 0:a.keySystemAccess;return r?r.then(function(){return a.mediaKeys}):(this.log('Requesting encrypted media "'+t+'" key-system access with config: '+JSON.stringify(e)),r=this.requestMediaKeySystemAccess(t,e),o=this.keySystemAccessPromises[t]={keySystemAccess:r},r.catch(function(e){i.log('Failed to obtain access to key-system "'+t+'": '+e)}),r.then(function(e){i.log('Access for key-system "'+e.keySystem+'" obtained');var r=i.fetchServerCertificate(t);return i.log('Create media-keys for "'+t+'"'),o.mediaKeys=e.createMediaKeys().then(function(e){return i.log('Media-keys created for "'+t+'"'),r.then(function(r){return r?i.setMediaKeysServerCertificate(e,t,r):e})}),o.mediaKeys.catch(function(e){i.error('Failed to create media-keys for "'+t+'"}: '+e)}),o.mediaKeys}))},r.createMediaKeySessionContext=function(t){var e=t.decryptdata,r=t.keySystem,t=t.mediaKeys,n=(this.log('Creating key-system session "'+r+'" keyId: '+gt(e.keyId||[])),t.createSession()),e={decryptdata:e,keySystem:r,mediaKeys:t,mediaKeysSession:n,keyStatus:"status-pending"};return this.mediaKeySessions.push(e),e},r.renewKeySession=function(t){var r,i,e=t.decryptdata;e.pssh?(r=this.createMediaKeySessionContext(t),i=this.getKeyIdString(e),this.keyIdToKeySessionPromise[i]=this.generateRequestWithPreferredKeySession(r,"cenc",e.pssh,"expired")):this.warn("Could not renew expired session. Missing pssh initData."),this.removeSession(t)},r.getKeyIdString=function(t){if(!t)throw new Error("Could not read keyId of undefined decryptdata");if(null===t.keyId)throw new Error("keyId is null");return gt(t.keyId)},r.updateKeySession=function(t,e){var i=t.mediaKeysSession;return this.log('Updating key-session "'+i.sessionId+'" for keyID '+gt((null==(t=t.decryptdata)?void 0:t.keyId)||[])+"\n      } (data length: "+(e&&e.byteLength)+")"),i.update(e)},r.selectKeySystemFormat=function(t){var e=Object.keys(t.levelkeys||{});return this.keyFormatPromise||(this.log("Selecting key-system from fragment (sn: "+t.sn+" "+t.type+": "+t.level+") key formats "+e.join(", ")),this.keyFormatPromise=this.getKeyFormatPromise(e)),this.keyFormatPromise},r.getKeyFormatPromise=function(t){var e=this;return new Promise(function(r,i){var n=Q(e.config),a=t.map(q).filter(function(t){return!!t&&-1!==n.indexOf(t)});return e.getKeySystemSelectionPromise(a).then(function(t){var t=t.keySystem,n=z(t);n?r(n):i(new Error('Unable to find format for key-system "'+t+'"'))}).catch(i)})},r.loadKey=function(t){var e=this,r=t.keyInfo.decryptdata,i=this.getKeyIdString(r),n="(keyId: "+i+' format: "'+r.keyFormat+'" method: '+r.method+" uri: "+r.uri+")",a=(this.log("Starting session for key "+n),this.keyIdToKeySessionPromise[i]);return a||(a=this.keyIdToKeySessionPromise[i]=this.getKeySystemForKeyPromise(r).then(function(i){var a=i.keySystem,s=i.mediaKeys;return e.throwIfDestroyed(),e.log("Handle encrypted media sn: "+t.frag.sn+" "+t.frag.type+": "+t.frag.level+" using key "+n),e.attemptSetMediaKeys(a,s).then(function(){e.throwIfDestroyed();var t=e.createMediaKeySessionContext({keySystem:a,mediaKeys:s,decryptdata:r});return e.generateRequestWithPreferredKeySession(t,"cenc",r.pssh,"playlist-key")})})).catch(function(t){return e.handleError(t)}),a},r.throwIfDestroyed=function(t){if(!this.hls)throw new Error("invalid state")},r.handleError=function(t){this.hls&&(this.error(t.message),t instanceof oa?this.hls.trigger(T.ERROR,t.data):this.hls.trigger(T.ERROR,{type:E.KEY_SYSTEM_ERROR,details:S.KEY_SYSTEM_NO_KEYS,error:t,fatal:!0}))},r.getKeySystemForKeyPromise=function(t){var e=this.getKeyIdString(t),e=this.keyIdToKeySessionPromise[e];return e||(t=(e=q(t.keyFormat))?[e]:Q(this.config),this.attemptKeySystemAccess(t))},r.getKeySystemSelectionPromise=function(t){if(0===(t=t.length?t:Q(this.config)).length)throw new oa({type:E.KEY_SYSTEM_ERROR,details:S.KEY_SYSTEM_NO_CONFIGURED_LICENSE,fatal:!0},"Missing key-system license configuration options "+JSON.stringify({drmSystems:this.config.drmSystems}));return this.attemptKeySystemAccess(t)},r._onMediaEncrypted=function(t){var n,a,e=this,r=t.initDataType,i=t.initData;if(this.debug('"'+t.type+'" event: init data type: "'+r+'"'),null!==i){if("sinf"===r&&this.config.drmSystems[H.FAIRPLAY]){var s=yt(new Uint8Array(i));try{var o=G(JSON.parse(s).sinf),l=bt(new Uint8Array(o));if(!l)return;n=l.subarray(8,24),a=H.FAIRPLAY}catch(t){return void this.warn('Failed to parse sinf "encrypted" event message initData')}}else{var u=function(t){if(!(t instanceof ArrayBuffer)||t.byteLength<32)return null;var e={version:0,systemId:"",kids:null,data:null},r=new DataView(t),i=r.getUint32(0);if(t.byteLength!==i&&44<i)return null;if(**********!==r.getUint32(4))return null;if(e.version=r.getUint32(8)>>>24,1<e.version)return null;e.systemId=gt(new Uint8Array(t,12,16));var n=r.getUint32(28);if(0===e.version){if(i-32<n)return null;e.data=new Uint8Array(t,32,n)}else if(1===e.version){e.kids=[];for(var a=0;a<n;a++)e.kids.push(new Uint8Array(t,32+16*a,16))}return e}(i);if(null===u)return;0===u.version&&u.systemId===X&&u.data&&(n=u.data.subarray(8,24)),a=function(){if(u.systemId===X)return H.WIDEVINE}()}if(a&&n){for(var h=gt(n),d=this.keyIdToKeySessionPromise,c=this.mediaKeySessions,f=d[h],v=0;v<c.length;v++){var m=function(){var s,t=c[v],a=t.decryptdata;return a.pssh||!a.keyId?"continue":(s=gt(a.keyId),h===s||-1!==a.uri.replace(/-/g,"").indexOf(h)?(f=d[s],delete d[s],a.pssh=new Uint8Array(i),a.keyId=n,f=d[h]=f.then(function(){return e.generateRequestWithPreferredKeySession(t,r,i,"encrypted-event-key-match")}),"break"):void 0)}();if("continue"!==m&&"break"===m)break}(f=f||(d[h]=this.getKeySystemSelectionPromise([a]).then(function(t){var s=t.keySystem,o=t.mediaKeys,l=(e.throwIfDestroyed(),new Ot("ISO-23001-7",h,null!=(t=z(s))?t:""));return l.pssh=new Uint8Array(i),l.keyId=n,e.attemptSetMediaKeys(s,o).then(function(){e.throwIfDestroyed();var t=e.createMediaKeySessionContext({decryptdata:l,keySystem:s,mediaKeys:o});return e.generateRequestWithPreferredKeySession(t,r,i,"encrypted-event-no-match")})}))).catch(function(t){return e.handleError(t)})}}},r._onWaitingForKey=function(t){this.log('"'+t.type+'" event')},r.attemptSetMediaKeys=function(t,e){var r=this,i=this.setMediaKeysQueue.slice(),n=(this.log('Setting media-keys for "'+t+'"'),Promise.all(i).then(function(){if(r.media)return r.media.setMediaKeys(e);throw new Error("Attempted to set mediaKeys without media element attached")}));return this.setMediaKeysQueue.push(n),n.then(function(){r.log('Media-keys set for "'+t+'"'),i.push(n),r.setMediaKeysQueue=r.setMediaKeysQueue.filter(function(t){return-1===i.indexOf(t)})})},r.generateRequestWithPreferredKeySession=function(t,e,r,i){var h,d,c,s=this,n=null==(n=this.config.drmSystems)||null==(n=n[t.keySystem])?void 0:n.generateRequest;if(n)try{var l=n.call(this.hls,e,r,t);if(!l)throw new Error("Invalid response from configured generateRequest filter");e=l.initDataType,r=t.decryptdata.pssh=l.initData?new Uint8Array(l.initData):null}catch(t){if(this.warn(t.message),null!=(n=this.hls)&&n.config.debug)throw t}return null===r?(this.log('Skipping key-session request for "'+i+'" (no initData)'),Promise.resolve(t)):(h=this.getKeyIdString(t.decryptdata),this.log('Generating key-session request for "'+i+'": '+h+" (init data type: "+e+" length: "+(r?r.byteLength:null)+")"),d=new Bi,t.mediaKeysSession.onmessage=function(e){var i,r=t.mediaKeysSession;r?(i=e.messageType,e=e.message,s.log('"'+i+'" message event for session "'+r.sessionId+'" message size: '+e.byteLength),"license-request"===i||"license-renewal"===i?s.renewLicense(t,e).catch(function(t){s.handleError(t),d.emit("error",t)}):"license-release"===i?t.keySystem===H.FAIRPLAY&&(s.updateKeySession(t,K("acknowledged")),s.removeSession(t)):s.warn('unhandled media key message type "'+i+'"')):d.emit("error",new Error("invalid state"))},t.mediaKeysSession.onkeystatuseschange=function(e){var r;t.mediaKeysSession?(s.onKeyStatusChange(t),r=t.keyStatus,d.emit("keyStatus",r),"expired"===r&&(s.warn(t.keySystem+" expired for key "+h),s.renewKeySession(t))):d.emit("error",new Error("invalid state"))},c=new Promise(function(t,e){d.on("error",e),d.on("keyStatus",function(r){r.startsWith("usable")?t():"output-restricted"===r?e(new oa({type:E.KEY_SYSTEM_ERROR,details:S.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED,fatal:!1},"HDCP level output restricted")):"internal-error"===r?e(new oa({type:E.KEY_SYSTEM_ERROR,details:S.KEY_SYSTEM_STATUS_INTERNAL_ERROR,fatal:!0},'key status changed to "'+r+'"')):"expired"===r?e(new Error("key expired while generating request")):s.warn('unhandled key status change "'+r+'"')})}),t.mediaKeysSession.generateRequest(e,r).then(function(){var e;s.log('Request generated for key-session "'+(null==(e=t.mediaKeysSession)?void 0:e.sessionId)+'" keyId: '+h)}).catch(function(t){throw new oa({type:E.KEY_SYSTEM_ERROR,details:S.KEY_SYSTEM_NO_SESSION,error:t,fatal:!1},"Error generating key-session request: "+t)}).then(function(){return c}).catch(function(e){throw d.removeAllListeners(),s.removeSession(t),e}).then(function(){return d.removeAllListeners(),t}))},r.onKeyStatusChange=function(t){var e=this;t.mediaKeysSession.keyStatuses.forEach(function(r,i){e.log('key status change "'+r+'" for keyStatuses keyId: '+gt("buffer"in i?new Uint8Array(i.buffer,i.byteOffset,i.byteLength):new Uint8Array(i))+" session keyId: "+gt(new Uint8Array(t.decryptdata.keyId||[]))+" uri: "+t.decryptdata.uri),t.keyStatus=r})},r.fetchServerCertificate=function(t){var r=this.config,i=new r.loader(r),n=this.getServerCertificateUrl(t);return n?(this.log('Fetching serverCertificate for "'+t+'"'),new Promise(function(a,s){var o={responseType:"arraybuffer",url:n},l=r.certLoadPolicy.default,l={loadPolicy:l,timeout:l.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0};i.load(o,l,{onSuccess:function(t,e,r,i){a(t.data)},onError:function(r,i,a,l){s(new oa({type:E.KEY_SYSTEM_ERROR,details:S.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED,fatal:!0,networkDetails:a,response:e({url:o.url,data:void 0},r)},'"'+t+'" certificate request failed ('+n+"). Status: "+r.code+" ("+r.text+")"))},onTimeout:function(e,r,i){s(new oa({type:E.KEY_SYSTEM_ERROR,details:S.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED,fatal:!0,networkDetails:i,response:{url:o.url,data:void 0}},'"'+t+'" certificate request timed out ('+n+")"))},onAbort:function(t,e,r){s(new Error("aborted"))}})})):Promise.resolve()},r.setMediaKeysServerCertificate=function(t,e,r){var i=this;return new Promise(function(n,a){t.setServerCertificate(r).then(function(a){i.log("setServerCertificate "+(a?"success":"not supported by CDM")+" ("+(null==r?void 0:r.byteLength)+') on "'+e+'"'),n(t)}).catch(function(t){a(new oa({type:E.KEY_SYSTEM_ERROR,details:S.KEY_SYSTEM_SERVER_CERTIFICATE_UPDATE_FAILED,error:t,fatal:!0},t.message))})})},r.renewLicense=function(t,e){var r=this;return this.requestLicense(t,new Uint8Array(e)).then(function(e){return r.updateKeySession(t,new Uint8Array(e)).catch(function(t){throw new oa({type:E.KEY_SYSTEM_ERROR,details:S.KEY_SYSTEM_SESSION_UPDATE_FAILED,error:t,fatal:!0},t.message)})})},r.setupLicenseXHR=function(t,e,r,i){var n=this,a=this.config.licenseXhrSetup;return a?Promise.resolve().then(function(){if(r.decryptdata)return a.call(n.hls,t,e,r,i);throw new Error("Key removed")}).catch(function(s){if(r.decryptdata)return t.open("POST",e,!0),a.call(n.hls,t,e,r,i);throw s}).then(function(r){return t.readyState||t.open("POST",e,!0),{xhr:t,licenseChallenge:r||i}}):(t.open("POST",e,!0),Promise.resolve({xhr:t,licenseChallenge:i}))},r.requestLicense=function(t,e){var r=this,i=this.config.keyLoadPolicy.default;return new Promise(function(n,a){var s=r.getLicenseServerUrl(t.keySystem),o=(r.log("Sending license request to URL: "+s),new XMLHttpRequest);o.responseType="arraybuffer",o.onreadystatechange=function(){if(!r.hls||!t.mediaKeysSession)return a(new Error("invalid state"));if(4===o.readyState)if(200===o.status){r._requestLicenseFailureCount=0;var l=o.response,u=(r.log("License received "+(l instanceof ArrayBuffer?l.byteLength:l)),r.config.licenseResponseCallback);if(u)try{l=u.call(r.hls,o,s,t)}catch(t){r.error(t)}n(l)}else{var u=i.errorRetry,l=u?u.maxNumRetry:0;r._requestLicenseFailureCount++,r._requestLicenseFailureCount>l||400<=o.status&&o.status<500?a(new oa({type:E.KEY_SYSTEM_ERROR,details:S.KEY_SYSTEM_LICENSE_REQUEST_FAILED,fatal:!0,networkDetails:o,response:{url:s,data:void 0,code:o.status,text:o.statusText}},"License Request XHR failed ("+s+"). Status: "+o.status+" ("+o.statusText+")")):(u=l-r._requestLicenseFailureCount+1,r.warn("Retrying license request, "+u+" attempts left"),r.requestLicense(t,e).then(n,a))}},t.licenseXhr&&t.licenseXhr.readyState!==XMLHttpRequest.DONE&&t.licenseXhr.abort(),t.licenseXhr=o,r.setupLicenseXHR(o,s,t,e).then(function(t){var e=t.xhr,t=t.licenseChallenge;e.send(t)})})},r.onMediaAttached=function(t,e){this.config.emeEnabled&&(e=e.media,(this.media=e).addEventListener("encrypted",this.onMediaEncrypted),e.addEventListener("waitingforkey",this.onWaitingForKey))},r.onMediaDetached=function(){var e=this,r=this.media,i=this.mediaKeySessions,n=(r&&(r.removeEventListener("encrypted",this.onMediaEncrypted),r.removeEventListener("waitingforkey",this.onWaitingForKey),this.media=null),this._requestLicenseFailureCount=0,this.setMediaKeysQueue=[],this.mediaKeySessions=[],this.keyIdToKeySessionPromise={},Ot.clearKeyUriToKeyIdMap(),i.length);t.CDMCleanupPromise=Promise.all(i.map(function(t){return e.removeSession(t)}).concat(null==r?void 0:r.setMediaKeys(null).catch(function(t){e.log("Could not clear media keys: "+t+". media.src: "+(null==r?void 0:r.src))}))).then(function(){n&&(e.log("finished closing key sessions and clearing media keys"),i.length=0)}).catch(function(t){e.log("Could not close sessions and clear media keys: "+t+". media.src: "+(null==r?void 0:r.src))})},r.onManifestLoading=function(){this.keyFormatPromise=null},r.onManifestLoaded=function(t,e){var e=e.sessionKeys;e&&this.config.emeEnabled&&!this.keyFormatPromise&&(e=e.reduce(function(t,e){return-1===t.indexOf(e.keyFormat)&&t.push(e.keyFormat),t},[]),this.log("Selecting key-system from session-keys "+e.join(", ")),this.keyFormatPromise=this.getKeyFormatPromise(e))},r.removeSession=function(t){var e=this,r=t.mediaKeysSession,i=t.licenseXhr;if(r)return this.log("Remove licenses and keys and close session "+r.sessionId),r.onmessage=null,r.onkeystatuseschange=null,i&&i.readyState!==XMLHttpRequest.DONE&&i.abort(),t.mediaKeysSession=t.decryptdata=t.licenseXhr=void 0,-1<(i=this.mediaKeySessions.indexOf(t))&&this.mediaKeySessions.splice(i,1),r.remove().catch(function(t){e.log("Could not remove session: "+t)}).then(function(){return r.close()}).catch(function(t){e.log("Could not close session: "+t)})},t}(),oa=(sa.CDMCleanupPromise=void 0,function(t){function e(e,r){var i;return(i=t.call(this,r)||this).data=void 0,e.error||(e.error=new Error(r)),(i.data=e).err=e.error,i}return l(e,t),e}(c(Error))),la="a",ua="av",ha=function(){function t(e){var r=this,e=(this.hls=void 0,this.config=void 0,this.media=void 0,this.sid=void 0,this.cid=void 0,this.useHeaders=!1,this.initialized=!1,this.starved=!1,this.buffering=!0,this.audioBuffer=void 0,this.videoBuffer=void 0,this.onWaiting=function(){r.initialized&&(r.starved=!0),r.buffering=!0},this.onPlaying=function(){r.initialized||(r.initialized=!0),r.buffering=!1},this.applyPlaylistData=function(t){try{r.apply(t,{ot:"m",su:!r.initialized})}catch(t){b.warn("Could not generate manifest CMCD data.",t)}},this.applyFragmentData=function(t){try{var e=t.frag,i=r.hls.levels[e.level],n=r.getObjectType(e),a={d:1e3*e.duration,ot:n};"v"!==n&&n!==la&&n!=ua||(a.br=i.bitrate/1e3,a.tb=r.getTopBandwidth(n)/1e3,a.bl=r.getBufferLength(n)),r.apply(t,a)}catch(t){b.warn("Could not generate segment CMCD data.",t)}},this.hls=e,this.config=e.config),n=e.cmcd;null!=n&&(e.pLoader=this.createPlaylistLoader(),e.fLoader=this.createFragmentLoader(),this.sid=n.sessionId||t.uuid(),this.cid=n.contentId,this.useHeaders=!0===n.useHeaders,this.registerListeners())}var e=t.prototype;return e.registerListeners=function(){var t=this.hls;t.on(T.MEDIA_ATTACHED,this.onMediaAttached,this),t.on(T.MEDIA_DETACHED,this.onMediaDetached,this),t.on(T.BUFFER_CREATED,this.onBufferCreated,this)},e.unregisterListeners=function(){var t=this.hls;t.off(T.MEDIA_ATTACHED,this.onMediaAttached,this),t.off(T.MEDIA_DETACHED,this.onMediaDetached,this),t.off(T.BUFFER_CREATED,this.onBufferCreated,this)},e.destroy=function(){this.unregisterListeners(),this.onMediaDetached(),this.hls=this.config=this.audioBuffer=this.videoBuffer=null},e.onMediaAttached=function(t,e){this.media=e.media,this.media.addEventListener("waiting",this.onWaiting),this.media.addEventListener("playing",this.onPlaying)},e.onMediaDetached=function(){this.media&&(this.media.removeEventListener("waiting",this.onWaiting),this.media.removeEventListener("playing",this.onPlaying),this.media=null)},e.onBufferCreated=function(t,e){var r;this.audioBuffer=null==(r=e.tracks.audio)?void 0:r.buffer,this.videoBuffer=null==(r=e.tracks.video)?void 0:r.buffer},e.createData=function(){var t;return{v:1,sf:"h",sid:this.sid,cid:this.cid,pr:null==(t=this.media)?void 0:t.playbackRate,mtp:this.hls.bandwidthEstimate/1e3}},e.apply=function(e,r){o(r=void 0===r?{}:r,this.createData());var i="i"===r.ot||"v"===r.ot||r.ot===ua;this.starved&&i&&(r.bs=!0,r.su=!0,this.starved=!1),null==r.su&&(r.su=this.buffering),this.useHeaders?(i=t.toHeaders(r),Object.keys(i).length&&(e.headers||(e.headers={}),o(e.headers,i))):(i=t.toQuery(r))&&(e.url=t.appendQueryToUri(e.url,i))},e.getObjectType=function(t){var e=t.type;return"subtitle"===e?"tt":"initSegment"===t.sn?"i":"audio"===e?la:"main"===e?this.hls.audioTracks.length?"v":ua:void 0},e.getTopBandwidth=function(t){for(var r=0,i=this.hls,o=g(t===la?i.audioTracks:(t=-1<(t=i.maxAutoLevel)?t+1:i.levels.length,i.levels.slice(0,t)));!(s=o()).done;){var s=s.value;s.bitrate>r&&(r=s.bitrate)}return 0<r?r:NaN},e.getBufferLength=function(t){var e=this.hls.media,t=t===la?this.audioBuffer:this.videoBuffer;return t&&e?1e3*dr.bufferInfo(t,e.currentTime,this.config.maxBufferHole).len:NaN},e.createPlaylistLoader=function(){var t=this.config.pLoader,e=this.applyPlaylistData,r=t||this.config.loader;return function(){function t(t){this.loader=void 0,this.loader=new r(t)}var i=t.prototype;return i.destroy=function(){this.loader.destroy()},i.abort=function(){this.loader.abort()},i.load=function(t,r,i){e(t),this.loader.load(t,r,i)},a(t,[{key:"stats",get:function(){return this.loader.stats}},{key:"context",get:function(){return this.loader.context}}]),t}()},e.createFragmentLoader=function(){var t=this.config.fLoader,e=this.applyFragmentData,r=t||this.config.loader;return function(){function t(t){this.loader=void 0,this.loader=new r(t)}var i=t.prototype;return i.destroy=function(){this.loader.destroy()},i.abort=function(){this.loader.abort()},i.load=function(t,r,i){e(t),this.loader.load(t,r,i)},a(t,[{key:"stats",get:function(){return this.loader.stats}},{key:"context",get:function(){return this.loader.context}}]),t}()},t.uuid=function(){var t=URL.createObjectURL(new Blob),e=t.toString();return URL.revokeObjectURL(t),e.slice(e.lastIndexOf("/")+1)},t.serialize=function(t){for(var r=[],n=function(t){return Math.round(t)},a=function(t){return 100*n(t/100)},s={br:n,d:n,bl:a,dl:a,mtp:a,nor:function(t){return encodeURIComponent(t)},rtp:a,tb:n},o=g(Object.keys(t||{}).sort());!(e=o()).done;){var h,e=e.value,u=t[e];!function(t){return!Number.isNaN(t)&&null!=t&&""!==t&&!1!==t}(u)||"v"===e&&1===u||"pr"==e&&1===u||(h=typeof(u=(h=s[e])?h(u):u),h="ot"===e||"sf"===e||"st"===e?e+"="+u:"boolean"==h?e:"number"==h?e+"="+u:e+"="+JSON.stringify(u),r.push(h))}return r.join(",")},t.toHeaders=function(e){for(var r={},i=["Object","Request","Session","Status"],n=[{},{},{},{}],a={br:0,d:0,ot:0,tb:0,bl:1,dl:1,mtp:1,nor:1,nrr:1,su:1,cid:2,pr:2,sf:2,sid:2,st:2,v:2,bs:3,rtp:3},s=0,o=Object.keys(e);s<o.length;s++){var l=o[s];n[null!=a[l]?a[l]:1][l]=e[l]}for(var u=0;u<n.length;u++){var h=t.serialize(n[u]);h&&(r["CMCD-"+i[u]]=h)}return r},t.toQuery=function(e){return"CMCD="+encodeURIComponent(t.serialize(e))},t.appendQueryToUri=function(t,e){var r;return e?(r=t.includes("?")?"&":"?",t+r+e):t},t}(),da=function(){function t(t){this.hls=void 0,this.log=void 0,this.loader=null,this.uri=null,this.pathwayId=".",this.pathwayPriority=null,this.timeToLoad=300,this.reloadTimer=-1,this.updated=0,this.started=!1,this.enabled=!0,this.levels=null,this.audioTracks=null,this.subtitleTracks=null,this.penalizedPathways={},this.hls=t,this.log=b.log.bind(b,"[content-steering]:"),this.registerListeners()}var e=t.prototype;return e.registerListeners=function(){var t=this.hls;t.on(T.MANIFEST_LOADING,this.onManifestLoading,this),t.on(T.MANIFEST_LOADED,this.onManifestLoaded,this),t.on(T.MANIFEST_PARSED,this.onManifestParsed,this),t.on(T.ERROR,this.onError,this)},e.unregisterListeners=function(){var t=this.hls;t&&(t.off(T.MANIFEST_LOADING,this.onManifestLoading,this),t.off(T.MANIFEST_LOADED,this.onManifestLoaded,this),t.off(T.MANIFEST_PARSED,this.onManifestParsed,this),t.off(T.ERROR,this.onError,this))},e.startLoad=function(){var t;this.started=!0,self.clearTimeout(this.reloadTimer),this.enabled&&this.uri&&(this.updated?(t=Math.max(1e3*this.timeToLoad-(performance.now()-this.updated),0),this.scheduleRefresh(this.uri,t)):this.loadSteeringManifest(this.uri))},e.stopLoad=function(){this.started=!1,this.loader&&(this.loader.destroy(),this.loader=null),self.clearTimeout(this.reloadTimer)},e.destroy=function(){this.unregisterListeners(),this.stopLoad(),this.hls=null,this.levels=this.audioTracks=this.subtitleTracks=null},e.removeLevel=function(t){var e=this.levels;e&&(this.levels=e.filter(function(e){return e!==t}))},e.onManifestLoading=function(){this.stopLoad(),this.enabled=!0,this.timeToLoad=300,this.updated=0,this.uri=null,this.pathwayId=".",this.levels=this.audioTracks=this.subtitleTracks=null},e.onManifestLoaded=function(t,e){e=e.contentSteering;null!==e&&(this.pathwayId=e.pathwayId,this.uri=e.uri,this.started)&&this.startLoad()},e.onManifestParsed=function(t,e){this.audioTracks=e.audioTracks,this.subtitleTracks=e.subtitleTracks},e.onError=function(t,e){var i,n,e=e.errorAction;2===(null==e?void 0:e.action)&&1===e.flags&&(i=this.pathwayPriority,n=this.pathwayId,this.penalizedPathways[n]||(this.penalizedPathways[n]=performance.now()),i=!i&&this.levels?this.levels.reduce(function(t,e){return-1===t.indexOf(e.pathwayId)&&t.push(e.pathwayId),t},[]):i)&&1<i.length&&(this.updatePathwayPriority(i),e.resolved=this.pathwayId!==n)},e.filterParsedLevels=function(t){this.levels=t;var r,e=this.getLevelsForPathway(this.pathwayId);return 0===e.length&&(r=t[0].pathwayId,this.log("No levels found in Pathway "+this.pathwayId+'. Setting initial Pathway to "'+r+'"'),e=this.getLevelsForPathway(r),this.pathwayId=r),e.length!==t.length?(this.log("Found "+e.length+"/"+t.length+' levels in Pathway "'+this.pathwayId+'"'),e):t},e.getLevelsForPathway=function(t){return null===this.levels?[]:this.levels.filter(function(e){return t===e.pathwayId})},e.updatePathwayPriority=function(t){this.pathwayPriority=t;var e,r=this.penalizedPathways,i=performance.now();Object.keys(r).forEach(function(t){3e5<i-r[t]&&delete r[t]});for(var n=0;n<t.length;n++){var a=t[n];if(!r[a]){if(a===this.pathwayId)return;var s=this.hls.nextLoadLevel,o=this.hls.levels[s];if(0<(e=this.getLevelsForPathway(a)).length){this.log('Setting Pathway to "'+a+'"'),this.pathwayId=a,this.hls.trigger(T.LEVELS_UPDATED,{levels:e});a=this.hls.levels[s];o&&a&&this.levels&&(a.attrs["STABLE-VARIANT-ID"]!==o.attrs["STABLE-VARIANT-ID"]&&a.bitrate!==o.bitrate&&this.log("Unstable Pathways change from bitrate "+o.bitrate+" to "+a.bitrate),this.hls.nextLoadLevel=s);break}}}},e.clonePathways=function(t){var i,n,e=this,r=this.levels;r&&(i={},n={},t.forEach(function(t){var a=t.ID,s=t["BASE-ID"],l=t["URI-REPLACEMENT"];r.some(function(t){return t.pathwayId===a})||(t=e.getLevelsForPathway(s).map(function(t){var e=o({},t),t=(e.details=void 0,e.url=fa(t.uri,t.attrs["STABLE-VARIANT-ID"],"PER-VARIANT-URIS",l),new _(t.attrs)),s=(t["PATHWAY-ID"]=a,t.AUDIO&&t.AUDIO+"_clone_"+a),u=t.SUBTITLES&&t.SUBTITLES+"_clone_"+a,t=(s&&(i[t.AUDIO]=s,t.AUDIO=s),u&&(n[t.SUBTITLES]=u,t.SUBTITLES=u),e.attrs=t,new De(e));return Xe(t,"audio",s),Xe(t,"text",u),t}),r.push.apply(r,t),ca(e.audioTracks,i,l,a),ca(e.subtitleTracks,n,l,a))}))},e.loadSteeringManifest=function(t){var e,r=this,i=this.hls.config,n=i.loader;this.loader&&this.loader.destroy(),this.loader=new n(i);try{e=new self.URL(t)}catch(e){return this.enabled=!1,void this.log("Failed to parse Steering Manifest URI: "+t)}"data:"!==e.protocol&&(n=0|(this.hls.bandwidthEstimate||i.abrEwmaDefaultEstimate),e.searchParams.set("_HLS_pathway",this.pathwayId),e.searchParams.set("_HLS_throughput",""+n));t={responseType:"json",url:e.href},n=i.steeringManifestLoadPolicy.default,i=n.errorRetry||n.timeoutRetry||{},n={loadPolicy:n,timeout:n.maxLoadTimeMs,maxRetry:i.maxNumRetry||0,retryDelay:i.retryDelayMs||0,maxRetryDelay:i.maxRetryDelayMs||0},i={onSuccess:function(t,i,n,a){r.log('Loaded steering manifest: "'+e+'"');var s=t.data;if(1===s.VERSION){r.updated=performance.now(),r.timeToLoad=s.TTL;var o=s["RELOAD-URI"],l=s["PATHWAY-CLONES"],u=s["PATHWAY-PRIORITY"];if(o)try{r.uri=new self.URL(o,e).href}catch(t){return r.enabled=!1,void r.log("Failed to parse Steering Manifest RELOAD-URI: "+o)}r.scheduleRefresh(r.uri||n.url),l&&r.clonePathways(l),u&&r.updatePathwayPriority(u)}else r.log("Steering VERSION "+s.VERSION+" not supported!")},onError:function(t,e,i,n){var a;r.log("Error loading steering manifest: "+t.code+" "+t.text+" ("+e.url+")"),r.stopLoad(),410===t.code?(r.enabled=!1,r.log("Steering manifest "+e.url+" no longer available")):(a=1e3*r.timeToLoad,429!==t.code?r.scheduleRefresh(r.uri||e.url,a):("function"==typeof(null==(t=r.loader)?void 0:t.getResponseHeader)&&(t=t.getResponseHeader("Retry-After"))&&(a=1e3*parseFloat(t)),r.log("Steering manifest "+e.url+" rate limited")))},onTimeout:function(t,e,i){r.log("Timeout loading steering manifest ("+e.url+")"),r.scheduleRefresh(r.uri||e.url)}};this.log("Requesting steering manifest: "+e),this.loader.load(t,n,i)},e.scheduleRefresh=function(t,e){var r=this;void 0===e&&(e=1e3*this.timeToLoad),self.clearTimeout(this.reloadTimer),this.reloadTimer=self.setTimeout(function(){r.loadSteeringManifest(t)},e)},t}();function ca(t,e,r,i){t&&Object.keys(e).forEach(function(n){var a=t.filter(function(t){return t.groupId===n}).map(function(t){var a=o({},t);return a.details=void 0,a.attrs=new _(a.attrs),a.url=a.attrs.URI=fa(t.url,t.attrs["STABLE-RENDITION-ID"],"PER-RENDITION-URIS",r),a.groupId=a.attrs["GROUP-ID"]=e[n],a.attrs["PATHWAY-ID"]=i,a});t.push.apply(t,a)})}function fa(t,e,r,i){var n,a=i.HOST,s=i.PARAMS,i=i[r],l=(e&&(n=null==i?void 0:i[e])&&(t=n),new self.URL(t));return a&&!n&&(l.host=a),s&&Object.keys(s).sort().forEach(function(t){t&&l.searchParams.set(t,s[t])}),l.href}var ga=/^age:\s*[\d.]+\s*$/im,va=function(){function t(t){this.xhrSetup=void 0,this.requestTimeout=void 0,this.retryTimeout=void 0,this.retryDelay=void 0,this.config=null,this.callbacks=null,this.context=void 0,this.loader=null,this.stats=void 0,this.xhrSetup=t&&t.xhrSetup||null,this.stats=new x,this.retryDelay=0}var e=t.prototype;return e.destroy=function(){this.callbacks=null,this.abortInternal(),this.loader=null,this.config=null},e.abortInternal=function(){var t=this.loader;self.clearTimeout(this.requestTimeout),self.clearTimeout(this.retryTimeout),t&&(t.onreadystatechange=null,t.onprogress=null,4!==t.readyState)&&(this.stats.aborted=!0,t.abort())},e.abort=function(){var t;this.abortInternal(),null!=(t=this.callbacks)&&t.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.loader)},e.load=function(t,e,r){if(this.stats.loading.start)throw new Error("Loader can only be used once.");this.stats.loading.start=self.performance.now(),this.context=t,this.config=e,this.callbacks=r,this.loadInternal()},e.loadInternal=function(){var i,n,a,t=this,e=this.config,r=this.context;e&&(i=this.loader=new self.XMLHttpRequest,(n=this.stats).loading.first=0,n.loaded=0,(a=this.xhrSetup)?Promise.resolve().then(function(){if(!t.stats.aborted)return a(i,r.url)}).catch(function(t){return i.open("GET",r.url,!0),a(i,r.url)}).then(function(){t.stats.aborted||t.openAndSendXhr(i,r,e)}).catch(function(e){t.callbacks.onError({code:i.status,text:e.message},r,i,n)}):this.openAndSendXhr(i,r,e))},e.openAndSendXhr=function(t,e,r){t.readyState||t.open("GET",e.url,!0);var i=this.context.headers,n=r.loadPolicy,a=n.maxTimeToFirstByteMs,n=n.maxLoadTimeMs;if(i)for(var o in i)t.setRequestHeader(o,i[o]);e.rangeEnd&&t.setRequestHeader("Range","bytes="+e.rangeStart+"-"+(e.rangeEnd-1)),t.onreadystatechange=this.readystatechange.bind(this),t.onprogress=this.loadprogress.bind(this),t.responseType=e.responseType,self.clearTimeout(this.requestTimeout),r.timeout=a&&y(a)?a:n,this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),r.timeout),t.send()},e.readystatechange=function(){var n,i,l,s,t=this.context,e=this.loader,r=this.stats;t&&e&&(i=e.readyState,n=this.config,!r.aborted)&&2<=i&&(0===r.loading.first&&(r.loading.first=Math.max(self.performance.now(),r.loading.start),n.timeout!==n.loadPolicy.maxLoadTimeMs)&&(self.clearTimeout(this.requestTimeout),n.timeout=n.loadPolicy.maxLoadTimeMs,this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),n.loadPolicy.maxLoadTimeMs-(r.loading.first-r.loading.start))),4===i)&&(self.clearTimeout(this.requestTimeout),e.onreadystatechange=null,e.onprogress=null,i=e.status,s="text"!==e.responseType,200<=i&&i<300&&(s&&e.response||null!==e.responseText)?(r.loading.end=Math.max(self.performance.now(),r.loading.first),s=s?e.response:e.responseText,l="arraybuffer"===e.responseType?s.byteLength:s.length,r.loaded=r.total=l,r.bwEstimate=8e3*r.total/(r.loading.end-r.loading.first),this.callbacks&&((l=this.callbacks.onProgress)&&l(r,t,s,e),this.callbacks)&&(l={url:e.responseURL,data:s,code:i},this.callbacks.onSuccess(l,r,t,e))):Be(s=n.loadPolicy.errorRetry,r.retry,!1,i)?this.retry(s):(b.error(i+" while loading "+t.url),this.callbacks.onError({code:i,text:e.statusText},t,e,r)))},e.loadtimeout=function(){var t=null==(t=this.config)?void 0:t.loadPolicy.timeoutRetry;Be(t,this.stats.retry,!0)?this.retry(t):(b.warn("timeout while loading "+this.context.url),(t=this.callbacks)&&(this.abortInternal(),t.onTimeout(this.stats,this.context,this.loader)))},e.retry=function(t){var e=this.context,r=this.stats;this.retryDelay=Ne(t,r.retry),r.retry++,b.warn((status?"HTTP Status "+status:"Timeout")+" while loading "+e.url+", retrying "+r.retry+"/"+t.maxNumRetry+" in "+this.retryDelay+"ms"),this.abortInternal(),this.loader=null,self.clearTimeout(this.retryTimeout),this.retryTimeout=self.setTimeout(this.loadInternal.bind(this),this.retryDelay)},e.loadprogress=function(t){var e=this.stats;e.loaded=t.loaded,t.lengthComputable&&(e.total=t.total)},e.getCacheAge=function(){var e,t=null;return t=this.loader&&ga.test(this.loader.getAllResponseHeaders())?(e=this.loader.getResponseHeader("age"))?parseFloat(e):null:t},e.getResponseHeader=function(t){return this.loader&&new RegExp("^"+t+":\\s*[\\d.]+\\s*$","im").test(this.loader.getAllResponseHeaders())?this.loader.getResponseHeader(t):null},t}(),ma=/(\d+)-(\d+)\/(\d+)/,pa=function(){function t(t){this.fetchSetup=void 0,this.requestTimeout=void 0,this.request=void 0,this.response=void 0,this.controller=void 0,this.context=void 0,this.config=null,this.callbacks=null,this.stats=void 0,this.loader=null,this.fetchSetup=t.fetchSetup||ya,this.controller=new self.AbortController,this.stats=new x}var e=t.prototype;return e.destroy=function(){this.loader=this.callbacks=null,this.abortInternal()},e.abortInternal=function(){var t=this.response;null!=t&&t.ok||(this.stats.aborted=!0,this.controller.abort())},e.abort=function(){var t;this.abortInternal(),null!=(t=this.callbacks)&&t.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.response)},e.load=function(t,e,r){var i=this,n=this.stats;if(n.loading.start)throw new Error("Loader can only be used once.");n.loading.start=self.performance.now();var a=function(t,e){e={method:"GET",mode:"cors",credentials:"same-origin",signal:e,headers:new self.Headers(o({},t.headers))};return t.rangeEnd&&e.headers.set("Range","bytes="+t.rangeStart+"-"+String(t.rangeEnd-1)),e}(t,this.controller.signal),s=r.onProgress,l="arraybuffer"===t.responseType,u=l?"byteLength":"length",h=e.loadPolicy,d=h.maxTimeToFirstByteMs,c=h.maxLoadTimeMs;this.context=t,this.config=e,this.callbacks=r,this.request=this.fetchSetup(t,a),self.clearTimeout(this.requestTimeout),e.timeout=d&&y(d)?d:c,this.requestTimeout=self.setTimeout(function(){i.abortInternal(),r.onTimeout(n,t,i.response)},e.timeout),self.fetch(this.request).then(function(a){i.response=i.loader=a;var h,o=Math.max(self.performance.now(),n.loading.start);if(self.clearTimeout(i.requestTimeout),e.timeout=c,i.requestTimeout=self.setTimeout(function(){i.abortInternal(),r.onTimeout(n,t,i.response)},c-(o-n.loading.start)),a.ok)return n.loading.first=o,n.total=function(t){var e=t.get("Content-Range");if(e){e=function(t){t=ma.exec(t);if(t)return parseInt(t[2])-parseInt(t[1])+1}(e);if(y(e))return e}e=t.get("Content-Length");if(e)return parseInt(e)}(a.headers)||n.total,s&&y(e.highWaterMark)?i.loadProgressively(a,n,t,e.highWaterMark,s):l?a.arrayBuffer():"json"===t.responseType?a.json():a.text();throw o=a.status,h=a.statusText,new Ta(h||"fetch, bad network response",o,a)}).then(function(a){var o=i.response,l=(self.clearTimeout(i.requestTimeout),n.loading.end=Math.max(self.performance.now(),n.loading.first),a[u]),l=(l&&(n.loaded=n.total=l),{url:o.url,data:a,code:o.status});s&&!y(e.highWaterMark)&&s(n,t,a,o),r.onSuccess(l,n,t,o)}).catch(function(e){var a,s;self.clearTimeout(i.requestTimeout),n.aborted||(a=e&&e.code||0,s=e?e.message:null,r.onError({code:a,text:s},t,e?e.details:null,n))})},e.getCacheAge=function(){var e,t=null;return t=this.response?(e=this.response.headers.get("age"))?parseFloat(e):null:t},e.getResponseHeader=function(t){return this.response?this.response.headers.get(t):null},e.loadProgressively=function(t,e,r,i,n){void 0===i&&(i=0);var a=new Qi,s=t.body.getReader();return function o(){return s.read().then(function(s){var u;return s.done?(a.dataLength&&n(e,r,a.flush(),t),Promise.resolve(new ArrayBuffer(0))):(u=(s=s.value).length,e.loaded+=u,u<i||a.dataLength?(a.push(s),a.dataLength>=i&&n(e,r,a.flush(),t)):n(e,r,s,t),o())}).catch(function(){return Promise.reject()})}()},t}();function ya(t,e){return new self.Request(t.url,e)}var Ta=function(t){function e(e,r,i){return(e=t.call(this,e)||this).code=void 0,e.details=void 0,e.code=r,e.details=i,e}return l(e,t),e}(c(Error)),Ea=/\s/,Sa=e(e({autoStartLoad:!0,startPosition:-1,defaultAudioCodec:void 0,debug:!1,capLevelOnFPSDrop:!1,capLevelToPlayerSize:!1,ignoreDevicePixelRatio:!1,initialLiveManifestSize:1,maxBufferLength:30,backBufferLength:1/0,maxBufferSize:6e7,maxBufferHole:.1,highBufferWatchdogPeriod:2,nudgeOffset:.1,nudgeMaxRetry:3,maxFragLookUpTolerance:.25,liveSyncDurationCount:3,liveMaxLatencyDurationCount:1/0,liveSyncDuration:void 0,liveMaxLatencyDuration:void 0,maxLiveSyncPlaybackRate:1,liveDurationInfinity:!1,liveBackBufferLength:null,maxMaxBufferLength:600,enableWorker:!0,workerPath:null,enableSoftwareAES:!0,startLevel:void 0,startFragPrefetch:!1,fpsDroppedMonitoringPeriod:5e3,fpsDroppedMonitoringThreshold:.2,appendErrorMaxRetry:3,loader:va,fLoader:void 0,pLoader:void 0,xhrSetup:void 0,licenseXhrSetup:void 0,licenseResponseCallback:void 0,abrController:wi,bufferController:Xr,capLevelController:We,errorController:F,fpsController:na,stretchShortVideoTrack:!1,maxAudioFramesDrift:1,forceKeyFrameOnDiscontinuity:!0,abrEwmaFastLive:3,abrEwmaSlowLive:9,abrEwmaFastVoD:3,abrEwmaSlowVoD:9,abrEwmaDefaultEstimate:5e5,abrBandWidthFactor:.95,abrBandWidthUpFactor:.7,abrMaxWithRealBitrate:!1,maxStarvationDelay:4,maxLoadingDelay:4,minAutoBitrate:0,emeEnabled:!1,widevineLicenseUrl:void 0,drmSystems:{},drmSystemOptions:{},requestMediaKeySystemAccessFunc:Z,testBandwidth:!0,progressive:!1,lowLatencyMode:!0,cmcd:void 0,enableDateRangeMetadataCues:!0,enableEmsgMetadataCues:!0,enableID3MetadataCues:!0,certLoadPolicy:{default:{maxTimeToFirstByteMs:8e3,maxLoadTimeMs:2e4,timeoutRetry:null,errorRetry:null}},keyLoadPolicy:{default:{maxTimeToFirstByteMs:8e3,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:2e4,backoff:"linear"},errorRetry:{maxNumRetry:8,retryDelayMs:1e3,maxRetryDelayMs:2e4,backoff:"linear"}}},manifestLoadPolicy:{default:{maxTimeToFirstByteMs:1/0,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},playlistLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:2,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},fragLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:12e4,timeoutRetry:{maxNumRetry:4,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:6,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},steeringManifestLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},manifestLoadingTimeOut:1e4,manifestLoadingMaxRetry:1,manifestLoadingRetryDelay:1e3,manifestLoadingMaxRetryTimeout:64e3,levelLoadingTimeOut:1e4,levelLoadingMaxRetry:4,levelLoadingRetryDelay:1e3,levelLoadingMaxRetryTimeout:64e3,fragLoadingTimeOut:2e4,fragLoadingMaxRetry:6,fragLoadingRetryDelay:1e3,fragLoadingMaxRetryTimeout:64e3},{cueHandler:{newCue:function(t,e,r,i){for(var n,s,o,l,u=[],h=self.VTTCue||self.TextTrackCue,d=0;d<i.rows.length;d++)if(s=!0,o=0,l="",!(n=i.rows[d]).isEmpty()){for(var c,f=0;f<n.chars.length;f++)Ea.test(n.chars[f].uchar)&&s?o++:(l+=n.chars[f].uchar,s=!1);(n.cueStartTime=e)===r&&(r+=1e-4),16<=o?o--:o++;var g=Un(l.trim()),v=Vn(e,r,g);null!=t&&null!=(c=t.cues)&&c.getCueById(v)||((c=new h(e,r,g)).id=v,c.line=d+1,c.align="left",c.position=10+Math.min(80,10*Math.floor(8*o/32)),u.push(c))}return t&&u.length&&(u.sort(function(t,e){return"auto"===t.line||"auto"===e.line?0:8<t.line&&8<e.line?e.line-t.line:t.line-e.line}),u.forEach(function(e){return ve(t,e)})),u}},enableWebVTT:!0,enableIMSC1:!0,enableCEA708Captions:!0,captionsTextTrack1Label:"English",captionsTextTrack1LanguageCode:"en",captionsTextTrack2Label:"Spanish",captionsTextTrack2LanguageCode:"es",captionsTextTrack3Label:"Unknown CC",captionsTextTrack3LanguageCode:"",captionsTextTrack4Label:"Unknown CC",captionsTextTrack4LanguageCode:"",renderTextTracksNatively:!0}),{},{subtitleStreamController:Fr,subtitleTrackController:Ri,timelineController:ur,audioStreamController:li,audioTrackController:Wr,emeController:sa,cmcdController:ha,contentSteeringController:da});function La(t){return t&&"object"==typeof t?Array.isArray(t)?t.map(La):Object.keys(t).reduce(function(e,r){return e[r]=La(t[r]),e},{}):t}wi=function(){function t(r){void 0===r&&(r={}),this.config=void 0,this.userConfig=void 0,this.coreComponents=void 0,this.networkControllers=void 0,this._emitter=new Bi,this._autoLevelCapping=void 0,this._maxHdcpLevel=null,this.abrController=void 0,this.bufferController=void 0,this.capLevelController=void 0,this.latencyController=void 0,this.levelController=void 0,this.streamController=void 0,this.audioTrackController=void 0,this.subtitleTrackController=void 0,this.emeController=void 0,this.cmcdController=void 0,this._media=null,this.url=null,A(r.debug||!1,"Hls instance");var i=this.config=function(t,r){if((r.liveSyncDurationCount||r.liveMaxLatencyDurationCount)&&(r.liveSyncDuration||r.liveMaxLatencyDuration))throw new Error("Illegal hls.js config: don't mix up liveSyncDurationCount/liveMaxLatencyDurationCount and liveSyncDuration/liveMaxLatencyDuration");if(void 0!==r.liveMaxLatencyDurationCount&&(void 0===r.liveSyncDurationCount||r.liveMaxLatencyDurationCount<=r.liveSyncDurationCount))throw new Error('Illegal hls.js config: "liveMaxLatencyDurationCount" must be greater than "liveSyncDurationCount"');if(void 0!==r.liveMaxLatencyDuration&&(void 0===r.liveSyncDuration||r.liveMaxLatencyDuration<=r.liveSyncDuration))throw new Error('Illegal hls.js config: "liveMaxLatencyDuration" must be greater than "liveSyncDuration"');var i=La(t),n=["TimeOut","MaxRetry","RetryDelay","MaxRetryTimeout"];return["manifest","level","frag"].forEach(function(t){var e=("level"===t?"playlist":t)+"LoadPolicy",a=void 0===r[e],s=[];n.forEach(function(n){var o=t+"Loading"+n,l=r[o];if(void 0!==l&&a){s.push(o);var u=i[e].default;switch(r[e]={default:u},n){case"TimeOut":u.maxLoadTimeMs=l,u.maxTimeToFirstByteMs=l;break;case"MaxRetry":u.errorRetry.maxNumRetry=l,u.timeoutRetry.maxNumRetry=l;break;case"RetryDelay":u.errorRetry.retryDelayMs=l,u.timeoutRetry.retryDelayMs=l;break;case"MaxRetryTimeout":u.errorRetry.maxRetryDelayMs=l,u.timeoutRetry.maxRetryDelayMs=l}}}),s.length&&b.warn('hls.js config: "'+s.join('", "')+'" setting(s) are deprecated, use "'+e+'": '+JSON.stringify(r[e]))}),e(e({},i),r)}(t.DefaultConfig,r),r=(this.userConfig=r,this._autoLevelCapping=-1,i.progressive&&function(t){var e=t.loader;e!==pa&&e!==va?(b.log("[config]: Custom loader detected, cannot enable progressive streaming"),t.progressive=!1):function(){if(self.fetch&&self.AbortController&&self.ReadableStream&&self.Request)try{return new self.ReadableStream({}),1}catch(t){}}()&&(t.loader=pa,t.progressive=!0,t.enableSoftwareAES=!0,b.log("[config]: Progressive streaming enabled, using FetchLoader"))}(i),i.abrController),a=i.bufferController,s=i.capLevelController,o=i.errorController,l=i.fpsController,o=new o(this),r=this.abrController=new r(this),a=this.bufferController=new a(this),s=this.capLevelController=new s(this),l=new l(this),g=new fe(this),v=new Re(this),m=i.contentSteeringController,m=m?new m(this):null,y=this.levelController=new qe(this,m),E=new tr(this),S=new lr(this.config),L=this.streamController=new Wi(this,E,S),g=(s.setStreamController(L),l.setStreamController(L),[g,y,L]),y=(m&&g.splice(1,0,m),this.networkControllers=g,[r,a,s,l,v,E]),L=(this.audioTrackController=this.createController(i.audioTrackController,g),i.audioStreamController),m=(L&&g.push(new L(this,E,S)),this.subtitleTrackController=this.createController(i.subtitleTrackController,g),i.subtitleStreamController),r=(m&&g.push(new m(this,E,S)),this.createController(i.timelineController,y),S.emeController=this.emeController=this.createController(i.emeController,y),this.cmcdController=this.createController(i.cmcdController,y),this.latencyController=this.createController(ke,y),this.coreComponents=y,g.push(o),o.onErrorOut);"function"==typeof r&&this.on(T.ERROR,r,o)}t.isSupported=function(){return!!(t=Kt())&&(e=Or(),t=t&&"function"==typeof t.isTypeSupported&&t.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"'),e=!e||e.prototype&&"function"==typeof e.prototype.appendBuffer&&"function"==typeof e.prototype.remove,!!t)&&!!e;var e,t};var r=t.prototype;return r.createController=function(t,e){return t?(t=new t(this),e&&e.push(t),t):null},r.on=function(t,e,r){this._emitter.on(t,e,r=void 0===r?this:r)},r.once=function(t,e,r){this._emitter.once(t,e,r=void 0===r?this:r)},r.removeAllListeners=function(t){this._emitter.removeAllListeners(t)},r.off=function(t,e,r,i){this._emitter.off(t,e,r=void 0===r?this:r,i)},r.listeners=function(t){return this._emitter.listeners(t)},r.emit=function(t,e,r){return this._emitter.emit(t,e,r)},r.trigger=function(t,e){if(this.config.debug)return this.emit(t,t,e);try{return this.emit(t,t,e)}catch(e){b.error("An internal error happened while handling event "+t+'. Error message: "'+e.message+'". Here is a stacktrace:',e),this.trigger(T.ERROR,{type:E.OTHER_ERROR,details:S.INTERNAL_EXCEPTION,fatal:!1,event:t,error:e})}return!1},r.listenerCount=function(t){return this._emitter.listenerCount(t)},r.destroy=function(){b.log("destroy"),this.trigger(T.DESTROYING,void 0),this.detachMedia(),this.removeAllListeners(),this._autoLevelCapping=-1,this.url=null,this.networkControllers.forEach(function(t){return t.destroy()}),this.networkControllers.length=0,this.coreComponents.forEach(function(t){return t.destroy()}),this.coreComponents.length=0;var t=this.config;t.xhrSetup=t.fetchSetup=void 0,this.userConfig=null},r.attachMedia=function(t){b.log("attachMedia"),this._media=t,this.trigger(T.MEDIA_ATTACHING,{media:t})},r.detachMedia=function(){b.log("detachMedia"),this.trigger(T.MEDIA_DETACHING,void 0),this._media=null},r.loadSource=function(t){this.stopLoad();var e=this.media,r=this.url,i=this.url=p.buildAbsoluteURL(self.location.href,t,{alwaysNormalize:!0});b.log("loadSource:"+i),e&&r&&(r!==i||this.bufferController.hasSourceTypes())&&(this.detachMedia(),this.attachMedia(e)),this.trigger(T.MANIFEST_LOADING,{url:t})},r.startLoad=function(t){void 0===t&&(t=-1),b.log("startLoad("+t+")"),this.networkControllers.forEach(function(e){e.startLoad(t)})},r.stopLoad=function(){b.log("stopLoad"),this.networkControllers.forEach(function(t){t.stopLoad()})},r.swapAudioCodec=function(){b.log("swapAudioCodec"),this.streamController.swapAudioCodec()},r.recoverMediaError=function(){b.log("recoverMediaError");var t=this._media;this.detachMedia(),t&&this.attachMedia(t)},r.removeLevel=function(t,e){this.levelController.removeLevel(t,e=void 0===e?0:e)},a(t,[{key:"levels",get:function(){return this.levelController.levels||[]}},{key:"currentLevel",get:function(){return this.streamController.currentLevel},set:function(t){b.log("set currentLevel:"+t),this.loadLevel=t,this.abrController.clearTimer(),this.streamController.immediateLevelSwitch()}},{key:"nextLevel",get:function(){return this.streamController.nextLevel},set:function(t){b.log("set nextLevel:"+t),this.levelController.manualLevel=t,this.streamController.nextLevelSwitch()}},{key:"loadLevel",get:function(){return this.levelController.level},set:function(t){b.log("set loadLevel:"+t),this.levelController.manualLevel=t}},{key:"nextLoadLevel",get:function(){return this.levelController.nextLoadLevel},set:function(t){this.levelController.nextLoadLevel=t}},{key:"firstLevel",get:function(){return Math.max(this.levelController.firstLevel,this.minAutoLevel)},set:function(t){b.log("set firstLevel:"+t),this.levelController.firstLevel=t}},{key:"startLevel",get:function(){return this.levelController.startLevel},set:function(t){b.log("set startLevel:"+t),-1!==t&&(t=Math.max(t,this.minAutoLevel)),this.levelController.startLevel=t}},{key:"capLevelToPlayerSize",get:function(){return this.config.capLevelToPlayerSize},set:function(t){t=!!t;t!==this.config.capLevelToPlayerSize&&(t?this.capLevelController.startCapping():(this.capLevelController.stopCapping(),this.autoLevelCapping=-1,this.streamController.nextLevelSwitch()),this.config.capLevelToPlayerSize=t)}},{key:"autoLevelCapping",get:function(){return this._autoLevelCapping},set:function(t){this._autoLevelCapping!==t&&(b.log("set autoLevelCapping:"+t),this._autoLevelCapping=t)}},{key:"bandwidthEstimate",get:function(){var t=this.abrController.bwEstimator;return t?t.getEstimate():NaN}},{key:"ttfbEstimate",get:function(){var t=this.abrController.bwEstimator;return t?t.getEstimateTTFB():NaN}},{key:"maxHdcpLevel",get:function(){return this._maxHdcpLevel},set:function(t){-1<Ae.indexOf(t)&&(this._maxHdcpLevel=t)}},{key:"autoLevelEnabled",get:function(){return-1===this.levelController.manualLevel}},{key:"manualLevel",get:function(){return this.levelController.manualLevel}},{key:"minAutoLevel",get:function(){var t=this.levels,e=this.config.minAutoBitrate;if(t)for(var r=t.length,i=0;i<r;i++)if(t[i].maxBitrate>=e)return i;return 0}},{key:"maxAutoLevel",get:function(){var e=this.levels,r=this.autoLevelCapping,i=this.maxHdcpLevel,r=-1===r&&e&&e.length?e.length-1:r;if(i)for(var n=r;n--;){var a=e[n].attrs["HDCP-LEVEL"];if(a&&a<=i)return n}return r}},{key:"nextAutoLevel",get:function(){return Math.min(Math.max(this.abrController.nextAutoLevel,this.minAutoLevel),this.maxAutoLevel)},set:function(t){this.abrController.nextAutoLevel=Math.max(this.minAutoLevel,t)}},{key:"playingDate",get:function(){return this.streamController.currentProgramDateTime}},{key:"mainForwardBufferInfo",get:function(){return this.streamController.getMainFwdBufferInfo()}},{key:"audioTracks",get:function(){var t=this.audioTrackController;return t?t.audioTracks:[]}},{key:"audioTrack",get:function(){var t=this.audioTrackController;return t?t.audioTrack:-1},set:function(t){var e=this.audioTrackController;e&&(e.audioTrack=t)}},{key:"subtitleTracks",get:function(){var t=this.subtitleTrackController;return t?t.subtitleTracks:[]}},{key:"subtitleTrack",get:function(){var t=this.subtitleTrackController;return t?t.subtitleTrack:-1},set:function(t){var e=this.subtitleTrackController;e&&(e.subtitleTrack=t)}},{key:"media",get:function(){return this._media}},{key:"subtitleDisplay",get:function(){var t=this.subtitleTrackController;return!!t&&t.subtitleDisplay},set:function(t){var e=this.subtitleTrackController;e&&(e.subtitleDisplay=t)}},{key:"lowLatencyMode",get:function(){return this.config.lowLatencyMode},set:function(t){this.config.lowLatencyMode=t}},{key:"liveSyncPosition",get:function(){return this.latencyController.liveSyncPosition}},{key:"latency",get:function(){return this.latencyController.latency}},{key:"maxLatency",get:function(){return this.latencyController.maxLatency}},{key:"targetLatency",get:function(){return this.latencyController.targetLatency}},{key:"drift",get:function(){return this.latencyController.drift}},{key:"forceStartLoad",get:function(){return this.streamController.forceStartLoad}}],[{key:"version",get:function(){return"1.4.3"}},{key:"Events",get:function(){return T}},{key:"ErrorTypes",get:function(){return E}},{key:"ErrorDetails",get:function(){return S}},{key:"DefaultConfig",get:function(){return t.defaultConfig||Sa},set:function(e){t.defaultConfig=e}}]),t}();return wi.defaultConfig=void 0,wi};"object"==typeof e&&void 0!==t?t.exports=a():"function"==typeof define&&define.amd?define(a):(n="undefined"!=typeof globalThis?globalThis:n||self).Hls=a()}(!1)}}]);