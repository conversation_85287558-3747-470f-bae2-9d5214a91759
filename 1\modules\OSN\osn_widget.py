#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OSN Widget - Wrapper for Original OSN Application
"""

import sys
import os
from pathlib import Path

# Import original OSN modules from current directory
from . import *
from .widgets import *
from .osn_api import <PERSON>NA<PERSON>
from .osn_downloader import OSNDownloader
from .osn_drm import OSNDrm
from .osn_ui import OSNUi

from PySide6.QtWidgets import QWidget, QVBoxLayout, QMainWindow
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon

class OSNWidget(QWidget):
    """OSN Original Application Widget"""
    
    # Signals
    search_requested = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.setup_original_osn()
        
    def setup_original_osn(self):
        """Setup original OSN application content only"""
        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Create the original OSN main window
        self.osn_main_window = OSNMainWindow()

        # Extract only the content area (not the full window with sidebar)
        if hasattr(self.osn_main_window.ui, 'contentBox'):
            # Add only the content box (without sidebar)
            main_layout.addWidget(self.osn_main_window.ui.contentBox)
        elif hasattr(self.osn_main_window.ui, 'stackedWidget'):
            # Add only the stacked widget content
            main_layout.addWidget(self.osn_main_window.ui.stackedWidget)
        else:
            # Fallback: add the whole window
            main_layout.addWidget(self.osn_main_window)

class OSNMainWindow(QMainWindow):
    """Original OSN Main Window"""
    
    def __init__(self):
        QMainWindow.__init__(self)

        # SET AS GLOBAL WIDGETS
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        global widgets
        widgets = self.ui

        # USE CUSTOM TITLE BAR
        Settings.ENABLE_CUSTOM_TITLE_BAR = False  # Disable for embedded use

        # APP NAME
        title = "OSN+ Downloader Pro"
        description = "OSN+ Downloader - Modern UI Design with Dark Theme"
        self.setWindowTitle(title)
        if hasattr(widgets, 'titleLeftDescription'):
            widgets.titleLeftDescription.setText(description)

        # Hide sidebar and title bar for embedded use
        if hasattr(widgets, 'leftMenuBg'):
            widgets.leftMenuBg.setVisible(False)
        if hasattr(widgets, 'topLogo'):
            widgets.topLogo.setVisible(False)
        if hasattr(widgets, 'contentTopBg'):
            widgets.contentTopBg.setVisible(False)

        # TOGGLE MENU
        widgets.toggleButton.clicked.connect(lambda: UIFunctions.toggleMenu(self, True))

        # SET UI DEFINITIONS
        UIFunctions.uiDefinitions(self)

        # QTableWidget PARAMETERS
        if hasattr(widgets, 'tableWidget'):
            widgets.tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # BUTTONS CLICK
        # LEFT MENUS
        widgets.btn_home.clicked.connect(self.buttonClick)
        widgets.btn_widgets.clicked.connect(self.buttonClick)
        widgets.btn_new.clicked.connect(self.buttonClick)
        widgets.btn_save.clicked.connect(self.buttonClick)

        # EXTRA LEFT BOX
        def openCloseLeftBox():
            UIFunctions.toggleLeftBox(self, True)
        if hasattr(widgets, 'toggleLeftBox'):
            widgets.toggleLeftBox.clicked.connect(openCloseLeftBox)
        if hasattr(widgets, 'extraCloseColumnBtn'):
            widgets.extraCloseColumnBtn.clicked.connect(openCloseLeftBox)

        # EXTRA RIGHT BOX
        def openCloseRightBox():
            UIFunctions.toggleRightBox(self, True)
        if hasattr(widgets, 'settingsTopBtn'):
            widgets.settingsTopBtn.clicked.connect(openCloseRightBox)

        # SET CUSTOM THEME
        useCustomTheme = False
        themeFile = "themes/py_dracula_dark.qss"

        # SET THEME AND HACKS
        if useCustomTheme:
            UIFunctions.theme(self, themeFile, True)
            AppFunctions.setThemeHack(self)

        # SET HOME PAGE AND SELECT MENU
        widgets.stackedWidget.setCurrentWidget(widgets.home)
        widgets.btn_home.setStyleSheet(UIFunctions.selectMenu(widgets.btn_home.styleSheet()))

        # Initialize OSN modules
        self.osn_api = OSNApi()
        self.osn_downloader = OSNDownloader()
        self.osn_drm = OSNDrm()
        self.osn_ui = OSNUi(self)

        # Connect OSN API signals
        self.osn_api.content_found.connect(self.handle_content_found)
        self.osn_api.episodes_found.connect(self.handle_episodes_found)
        self.osn_api.error_occurred.connect(self.handle_api_error)
        self.osn_api.login_status_changed.connect(self.handle_login_status)

        # Connect downloader signals
        self.osn_downloader.download_progress.connect(self.handle_download_progress)
        self.osn_downloader.download_completed.connect(self.handle_download_completed)
        self.osn_downloader.download_error.connect(self.handle_download_error)
        self.osn_downloader.video_progress.connect(self.handle_video_progress)
        self.osn_downloader.status_update.connect(self.handle_status_update)
        self.osn_downloader.request_next_episode.connect(self.handle_next_episode_request)

        # Connect sequential download signals
        self.osn_downloader.episode_completed.connect(self.handle_episode_completed)
        self.osn_downloader.all_episodes_completed.connect(self.handle_all_episodes_completed)

        # Connect DRM signals
        self.osn_drm.drm_keys_extracted.connect(self.handle_drm_keys)
        self.osn_drm.drm_error.connect(self.handle_drm_error)

    # BUTTONS CLICK
    def buttonClick(self):
        """Handle button clicks"""
        btn = self.sender()
        btnName = btn.objectName()

        # SHOW HOME PAGE
        if btnName == "btn_home":
            if hasattr(self, 'osn_ui') and self.osn_ui:
                self.osn_ui.clear_content_data()
                print("🧹 Cleared content data when switching to Home page")

            widgets.stackedWidget.setCurrentWidget(widgets.home)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # SHOW MOVIES PAGE
        if btnName == "btn_widgets":
            if hasattr(self, 'osn_ui') and self.osn_ui:
                self.osn_ui.clear_content_data()
                print("🧹 Cleared content data when switching to Movies page")

            widgets.stackedWidget.setCurrentWidget(widgets.widgets)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # SHOW SERIES PAGE
        if btnName == "btn_new":
            if hasattr(self, 'osn_ui') and self.osn_ui:
                self.osn_ui.clear_content_data()
                print("🧹 Cleared content data when switching to Series page")

            widgets.stackedWidget.setCurrentWidget(widgets.new_page)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # SHOW SETTINGS PAGE
        if btnName == "btn_save":
            if hasattr(self, 'osn_ui') and self.osn_ui:
                self.osn_ui.clear_content_data()
                print("🧹 Cleared content data when switching to Settings page")

            widgets.stackedWidget.setCurrentWidget(widgets.save_page)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        print(f'Button "{btnName}" pressed!')

    # OSN API HANDLERS
    def handle_content_found(self, content_data):
        """Handle when content is found by OSN API"""
        content_type = content_data.get('type')
        if content_type == 'movie':
            self.osn_ui.display_movie_info(content_data['data'], content_data['movie_id'])
        elif content_type == 'series':
            self.osn_ui.display_series_info(content_data['data'], content_data['series_id'])

    def handle_episodes_found(self, episodes_data):
        """Handle when episodes are found by OSN API"""
        self.osn_ui.display_episodes_list(episodes_data)

    def handle_api_error(self, error_message):
        """Handle API errors"""
        self.osn_ui.show_message("API Error", error_message)

    def handle_login_status(self, is_logged_in, message):
        """Handle login status changes"""
        if is_logged_in:
            self.osn_ui.status_updated.emit(f"✅ {message}")
        else:
            self.osn_ui.status_updated.emit(f"❌ {message}")

    def handle_download_progress(self, percentage, message):
        """Handle download progress updates"""
        print(f"🎯 MAIN: Received download_progress signal: {percentage}% - {message}")
        self.osn_ui.progress_updated.emit(percentage, message)

    def handle_download_completed(self, file_path, success):
        """Handle download completion"""
        print(f"🎯 MAIN: Received download_completed signal: {success} - {file_path}")

        if success:
            self.osn_ui.show_message("Download Complete", f"File saved to: {file_path}")
        else:
            self.osn_ui.show_message("Download Failed", "Download could not be completed")

    def handle_download_error(self, error_message):
        """Handle download errors"""
        print(f"🎯 MAIN: Received download_error signal: {error_message}")
        self.osn_ui.show_message("Download Error", error_message)

    def handle_video_progress(self, percentage):
        """Handle video progress updates"""
        print(f"🎯 MAIN: Received video_progress signal: {percentage}%")
        self.osn_ui.progress_updated.emit(percentage, f"📥 Video: {percentage}%")
        print(f"🎬 Video Progress: {percentage}%")

    def handle_status_update(self, status_message):
        """Handle status updates"""
        print(f"🎯 MAIN: Received status_update signal: {status_message}")
        self.osn_ui.status_updated.emit(status_message)
        print(f"📝 Status: {status_message}")

    def handle_drm_keys(self, drm_info):
        """Handle DRM keys extraction"""
        self.osn_ui.status_updated.emit("DRM keys extracted successfully")

    def handle_drm_error(self, error_message):
        """Handle DRM errors"""
        self.osn_ui.show_message("DRM Error", error_message)

    def handle_next_episode_request(self):
        """Handle request to fetch next episode details and start download"""
        print(f"🔄 MAIN: Received request to fetch next episode details")

        if hasattr(self.osn_ui, 'fetch_and_start_next_episode'):
            self.osn_ui.fetch_and_start_next_episode()
        else:
            print(f"⚠️ UI doesn't have fetch_and_start_next_episode method")

    def handle_episode_completed(self, episode_number, file_path, success):
        """Handle individual episode completion in sequential download"""
        print(f"🎬 MAIN: Episode {episode_number} completed - Success: {success}")
        print(f"🎬 MAIN: Signal received in osn_widget.py - Episode {episode_number}")
        print(f"🎬 MAIN: File path: {file_path}")

        if success:
            print(f"✅ Episode {episode_number} saved to: {file_path}")
            self.osn_ui.status_updated.emit(f"✅ Episode {episode_number} completed successfully")
            self.osn_ui.mark_episode_completed(episode_number)
            print(f"🎬 MAIN: Episode {episode_number} marked as completed in UI")
        else:
            print(f"❌ Episode {episode_number} failed")
            self.osn_ui.status_updated.emit(f"❌ Episode {episode_number} failed")
            self.osn_ui.mark_episode_failed(episode_number)
            print(f"🎬 MAIN: Episode {episode_number} marked as failed in UI")

    def handle_all_episodes_completed(self):
        """Handle completion of all episodes in sequential download"""
        print(f"🎉 MAIN: All episodes completed!")
        self.osn_ui.show_message("Download Complete", "All episodes have been downloaded successfully!")
        self.osn_ui.status_updated.emit("🎉 All episodes completed!")

    # RESIZE EVENTS
    def resizeEvent(self, event):
        """Handle resize events"""
        UIFunctions.resize_grips(self)

    # MOUSE CLICK EVENTS
    def mousePressEvent(self, event):
        """Handle mouse press events"""
        self.dragPos = event.globalPos()

        if event.buttons() == Qt.LeftButton:
            print('Mouse click: LEFT CLICK')
        if event.buttons() == Qt.RightButton:
            print('Mouse click: RIGHT CLICK')
