[2025-06-18T06:58:57.106289 1872 ] === CCACHE 4.10.2 STARTED =========================================
[2025-06-18T06:58:57.106408 1872 ] Configuration file: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache\ccache.conf
[2025-06-18T06:58:57.106432 1872 ] System configuration file: C:\ProgramData\ccache\ccache.conf
[2025-06-18T06:58:57.106432 1872 ] Config: (default) absolute_paths_in_stderr = false
[2025-06-18T06:58:57.106432 1872 ] Config: (default) base_dir = 
[2025-06-18T06:58:57.106432 1872 ] Config: (environment) cache_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache
[2025-06-18T06:58:57.106432 1872 ] Config: (default) compiler = 
[2025-06-18T06:58:57.106432 1872 ] Config: (default) compiler_check = mtime
[2025-06-18T06:58:57.106432 1872 ] Config: (default) compiler_type = auto
[2025-06-18T06:58:57.106432 1872 ] Config: (default) compression = true
[2025-06-18T06:58:57.106432 1872 ] Config: (default) compression_level = 0
[2025-06-18T06:58:57.106432 1872 ] Config: (default) cpp_extension = 
[2025-06-18T06:58:57.106432 1872 ] Config: (default) debug = false
[2025-06-18T06:58:57.106432 1872 ] Config: (default) debug_dir = 
[2025-06-18T06:58:57.106432 1872 ] Config: (default) debug_level = 2
[2025-06-18T06:58:57.106432 1872 ] Config: (default) depend_mode = false
[2025-06-18T06:58:57.106432 1872 ] Config: (default) direct_mode = true
[2025-06-18T06:58:57.106432 1872 ] Config: (default) disable = false
[2025-06-18T06:58:57.106432 1872 ] Config: (default) extra_files_to_hash = 
[2025-06-18T06:58:57.106432 1872 ] Config: (default) file_clone = false
[2025-06-18T06:58:57.106432 1872 ] Config: (default) hard_link = false
[2025-06-18T06:58:57.106432 1872 ] Config: (default) hash_dir = true
[2025-06-18T06:58:57.106432 1872 ] Config: (default) ignore_headers_in_manifest = 
[2025-06-18T06:58:57.106432 1872 ] Config: (default) ignore_options = 
[2025-06-18T06:58:57.106432 1872 ] Config: (default) inode_cache = false
[2025-06-18T06:58:57.106432 1872 ] Config: (default) keep_comments_cpp = false
[2025-06-18T06:58:57.106432 1872 ] Config: (environment) log_file = C:\Users\<USER>\OneDrive\Desktop\YANGOPL\1\FIXED_~1\MAIN~1.ONE\ccache-22652.txt
[2025-06-18T06:58:57.106432 1872 ] Config: (default) max_files = 0
[2025-06-18T06:58:57.106432 1872 ] Config: (default) max_size = 5.0 GiB
[2025-06-18T06:58:57.106432 1872 ] Config: (default) msvc_dep_prefix = Note: including file:
[2025-06-18T06:58:57.106432 1872 ] Config: (default) namespace = 
[2025-06-18T06:58:57.106432 1872 ] Config: (default) path = 
[2025-06-18T06:58:57.106432 1872 ] Config: (default) pch_external_checksum = false
[2025-06-18T06:58:57.106432 1872 ] Config: (default) prefix_command = 
[2025-06-18T06:58:57.106432 1872 ] Config: (default) prefix_command_cpp = 
[2025-06-18T06:58:57.106432 1872 ] Config: (default) read_only = false
[2025-06-18T06:58:57.106432 1872 ] Config: (default) read_only_direct = false
[2025-06-18T06:58:57.106432 1872 ] Config: (default) recache = false
[2025-06-18T06:58:57.106432 1872 ] Config: (default) remote_only = false
[2025-06-18T06:58:57.106432 1872 ] Config: (default) remote_storage = 
[2025-06-18T06:58:57.106432 1872 ] Config: (default) reshare = false
[2025-06-18T06:58:57.106432 1872 ] Config: (default) run_second_cpp = true
[2025-06-18T06:58:57.106432 1872 ] Config: (environment) sloppiness = include_file_ctime, include_file_mtime
[2025-06-18T06:58:57.106432 1872 ] Config: (default) stats = true
[2025-06-18T06:58:57.106432 1872 ] Config: (default) stats_log = 
[2025-06-18T06:58:57.106432 1872 ] Config: (default) temporary_dir = C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp
[2025-06-18T06:58:57.106432 1872 ] Config: (default) umask = 
[2025-06-18T06:58:57.106560 1872 ] Command line: C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\ccache.exe C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -o static_src\\OnefileBootstrap.o -c -std=c11 -flto=16 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -DMS_WIN64 -D_NUITKA_EXE_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zstd static_src\\OnefileBootstrap.c
[2025-06-18T06:58:57.110593 1872 ] Hostname: TEEFA
[2025-06-18T06:58:57.110639 1872 ] Working directory: C:\Users\<USER>\OneDrive\Desktop\YANGOPL\1\FIXED_~1\MAIN~1.ONE
[2025-06-18T06:58:57.110664 1872 ] Compiler: C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\DOWNLO~1\gcc\x86_64\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\mingw64\bin\gcc.exe
[2025-06-18T06:58:57.110671 1872 ] Compiler type: gcc
[2025-06-18T06:58:57.110915 1872 ] Detected input file: static_src\OnefileBootstrap.c
[2025-06-18T06:58:57.111164 1872 ] Source file: static_src\OnefileBootstrap.c
[2025-06-18T06:58:57.111178 1872 ] Object file: static_src\OnefileBootstrap.o
[2025-06-18T06:58:57.111345 1872 ] Trying direct lookup
[2025-06-18T06:58:57.111556 1872 ] Manifest key: 2041uen1bdj7jerenmhlm16u4487jjvlg
[2025-06-18T06:58:57.111643 1872 ] No 2041uen1bdj7jerenmhlm16u4487jjvlg in local storage
[2025-06-18T06:58:57.112416 1872 ] Running preprocessor
[2025-06-18T06:58:57.112682 1872 ] Executing C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1\\gcc\\x86_64\\14.2.0posix-19.1.1-12.0.0-msvcrt-r2\\mingw64\\bin\\gcc.exe -std=c11 -flto=16 -fuse-linker-plugin -fno-fat-lto-objects -fvisibility=hidden -fwrapv -pipe -Wno-unused-but-set-variable -fpartial-inlining -ftrack-macro-expansion=0 -Wno-deprecated-declarations -fno-var-tracking -Wno-misleading-indentation -fcompare-debug-second -O3 -D_NUITKA_STANDALONE_MODE -D_NUITKA_ONEFILE_MODE -D_NUITKA_ONEFILE_TEMP_BOOL -D_NUITKA_DLL_MODE -D_NUITKA_ONEFILE_DLL_MODE -D_WIN32_WINNT=0x0601 -D__NUITKA_NO_ASSERT__ -D_NUITKA_WINMAIN_ENTRY_POINT -DMS_WIN64 -D_NUITKA_EXE_MODE -D_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1 -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zlib -I. -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\include -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\static_src -IC:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\Lib\\SITE-P~1\\nuitka\\build\\inline_copy\\zstd -E -o C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\ccache/tmp/cpp_stdout.tmp.BNwNsS.i static_src\\OnefileBootstrap.c
[2025-06-18T06:58:58.179658 1872 ] Got result key from preprocessor
[2025-06-18T06:58:58.179681 1872 ] Result key: 7c98jl32dn6jk40i09q5do6v1tldac8ba
[2025-06-18T06:58:58.179953 1872 ] Retrieved 7c98jl32dn6jk40i09q5do6v1tldac8ba from local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/7/c/98jl32dn6jk40i09q5do6v1tldac8baR)
[2025-06-18T06:58:58.180533 1872 ] Reading embedded entry #0 .o (610477 bytes)
[2025-06-18T06:58:58.180551 1872 ] Writing to static_src\OnefileBootstrap.o
[2025-06-18T06:58:58.181063 1872 ] Succeeded getting cached result
[2025-06-18T06:58:58.187776 1872 ] Added result key to manifest 2041uen1bdj7jerenmhlm16u4487jjvlg
[2025-06-18T06:58:58.187797 1872 ] Using Zstandard with default compression level 1
[2025-06-18T06:58:58.188512 1872 ] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_20.lock
[2025-06-18T06:58:58.188688 1872 ] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_20.lock
[2025-06-18T06:58:58.189351 1872 ] Stored 2041uen1bdj7jerenmhlm16u4487jjvlg in local storage (C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/2/0/41uen1bdj7jerenmhlm16u4487jjvlgM)
[2025-06-18T06:58:58.189416 1872 ] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/2/stats.lock
[2025-06-18T06:58:58.189540 1872 ] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/2/stats.lock
[2025-06-18T06:58:58.190174 1872 ] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/2/stats.lock
[2025-06-18T06:58:58.190253 1872 ] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/2/stats.lock
[2025-06-18T06:58:58.190262 1872 ] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_20.lock
[2025-06-18T06:58:58.190311 1872 ] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/subdir_20.lock
[2025-06-18T06:58:58.190376 1872 ] Result: direct_cache_miss
[2025-06-18T06:58:58.190382 1872 ] Result: local_storage_hit
[2025-06-18T06:58:58.190387 1872 ] Result: local_storage_read_hit
[2025-06-18T06:58:58.190391 1872 ] Result: local_storage_read_miss
[2025-06-18T06:58:58.190396 1872 ] Result: local_storage_write
[2025-06-18T06:58:58.190401 1872 ] Result: preprocessed_cache_hit
[2025-06-18T06:58:58.190415 1872 ] Acquiring C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/5/0/stats.lock
[2025-06-18T06:58:58.190556 1872 ] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/5/0/stats.lock
[2025-06-18T06:58:58.191183 1872 ] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/5/0/stats.lock
[2025-06-18T06:58:58.191252 1872 ] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/5/0/stats.lock
[2025-06-18T06:58:58.191279 1872 ] Trying to acquire C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-06-18T06:58:58.191388 1872 ] Acquired C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-06-18T06:58:58.196626 1872 ] No automatic cleanup needed (size 276.9 MiB, files 9956, max size 5.0 GiB)
[2025-06-18T06:58:58.196649 1872 ] Releasing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-06-18T06:58:58.196758 1872 ] Released C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/lock/auto_cleanup.lock
[2025-06-18T06:58:58.196920 1872 ] Removing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/stderr.tmp.aPaM2w.tmp
[2025-06-18T06:58:58.197595 1872 ] Removing C:\Users\<USER>\AppData\Local\Nuitka\Nuitka\Cache\ccache/tmp/cpp_stdout.tmp.BNwNsS.i
