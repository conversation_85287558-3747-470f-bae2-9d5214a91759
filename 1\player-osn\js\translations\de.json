{"advertising": {"admessage": "Diese Werbung endet in xx", "cuetext": "Werbung", "displayHeading": "Werbung", "loadingAd": "Werbung lädt", "podmessage": "Werbung __AD_POD_CURRENT__ von __AD_POD_LENGTH__.", "skipmessage": "Überspringe Werbung in xx", "skiptext": "Überspringe"}, "airplay": "AirPlay", "audioTracks": "Audiospur", "auto": "Auto", "buffer": "<PERSON><PERSON><PERSON>", "cast": "Chromecast", "cc": "Untertitel", "close": "Schließen", "errors": {"badConnection": "Dieses Video kann nicht abgespielt werden, weil es ein Problem mit der Internetverbindung gibt.", "cantLoadPlayer": "Entschuldigung, der Videoplayer konnte es nicht Laden.", "cantPlayInBrowser": "Das Video kann in diesem Browser nicht abgespielt werden.", "cantPlayVideo": "Diese Videodatei kann nicht abgespielt werden.", "errorCode": "Fehlercode", "liveStreamDown": "Dieser Livestream ist entweder abgestürzt oder beendet.", "protectedContent": "Es gab ein Problem Zugang zu dem geschützten Inhalt herzustellen.", "technicalError": "Das Video kann durch einen technischen Fehler nicht abgespielt werden."}, "exitFullscreen": "Vollbild Beenden", "fullscreen": "Vollbild", "hd": "Qualität", "liveBroadcast": "Live", "logo": "Logo", "mute": "<PERSON><PERSON>", "next": "<PERSON><PERSON>", "nextUp": "Als Nächstes", "notLive": "<PERSON>cht Live", "off": "Aus", "pause": "Pause", "pipIcon": "Bild-in-Bild", "play": "<PERSON><PERSON><PERSON>", "playback": "<PERSON><PERSON><PERSON>", "playbackRates": "Abspielrate", "player": "Videoplayer", "poweredBy": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> von", "prev": "Zurück", "related": {"autoplaymessage": "Als Nächstes in xx", "heading": "Weitere Videos"}, "replay": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rewind": "10 Sekunden Zurückspulen", "settings": "Einstellungen", "sharing": {"copied": "<PERSON><PERSON><PERSON>", "email": "E-Mail", "embed": "Einbinden", "heading": "Teilen", "link": "Link"}, "slider": "Positionsanzeige", "stop": "Stopp", "unmute": "Stummschaltung Deaktivieren", "videoInfo": "Über Dieses Video", "volume": "Lautstärke", "volumeSlider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shortcuts": {"playPause": "Spielen/Pause", "volumeToggle": "Summ <PERSON>en/Stummschaltung Deaktivieren", "fullscreenToggle": "Vollbild/Vollbild Beenden", "seekPercent": "Gehe zu %", "keyboardShortcuts": "Tastaturkürzel", "increaseVolume": "Lautstärke Erhöhen", "decreaseVolume": "Lautstärke Verringern", "seekForward": "Vorwärts Springen", "seekBackward": "Rückwärts Springen", "spacebar": "<PERSON><PERSON><PERSON>", "captionsToggle": "Untertitel An/Aus"}, "captionsStyles": {"subtitleSettings": "Untertitel-Einstellungen", "color": "Schriftfarbe", "fontOpacity": "Schrifttransparenz", "userFontScale": "Schriftgröße", "fontFamily": "Schriftfamilie", "edgeStyle": "<PERSON><PERSON><PERSON><PERSON>", "backgroundColor": "Hintergrundfarbe", "backgroundOpacity": "Hintergrundtransparenz", "windowColor": "Fensterfarbe", "windowOpacity": "Fenstertransparenz", "white": "<PERSON><PERSON>", "black": "<PERSON><PERSON><PERSON>", "red": "Rot", "green": "<PERSON><PERSON><PERSON><PERSON>", "blue": "Blau", "yellow": "<PERSON><PERSON><PERSON>", "magenta": "Ma<PERSON><PERSON>", "cyan": "<PERSON><PERSON>", "none": "<PERSON><PERSON>", "raised": "<PERSON><PERSON><PERSON><PERSON>", "depressed": "<PERSON><PERSON><PERSON><PERSON>", "uniform": "<PERSON><PERSON><PERSON><PERSON>", "dropShadow": "Schlagschatten"}, "disabled": "Deaktiviert", "enabled": "Aktiviert", "reset": "Z<PERSON>ücksetzen"}