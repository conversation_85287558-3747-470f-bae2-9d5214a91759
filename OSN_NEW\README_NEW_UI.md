# OSN+ Downloader Pro - New UI Design

## Overview
تم تحديث واجهة المستخدم لتطبيق OSN+ Downloader Pro لتصبح مشابهة لتطبيق Shahid Downloader Pro مع التصميم الحديث والوظائف المحسنة.

## New Features / الميزات الجديدة

### 1. Home Page Design / تصميم الصفحة الرئيسية
- **OSN+ Logo**: شعار OSN+ مع العنوان "OSN+ Downloader Pro"
- **Search Section**: قسم البحث مع إدخال الرابط وزر البحث
- **Recent URLs**: قائمة منسدلة للروابط الحديثة مع زر المسح
- **Content Info**: عرض معلومات المحتوى مع الصورة والتفاصيل

### 2. UI Components / مكونات الواجهة

#### Search Section
```
- Enter URL: حقل إدخال الرابط
- Search Button: زر البحث
- Recent URLs: القائمة المنسدلة للروابط الحديثة
- Clear Button: زر مسح البيانات
```

#### Content Info Section
```
- Poster Image: صورة المحتوى
- Title: عنوان المحتوى
- Details: التفاصيل (النوع، السنة، الحلقات، الأنواع، الممثلين، الوصف)
- Action Buttons: أزرار التشغيل والمتابعة
- Tabs: تبويبات للمعلومات والحلقات وخيارات التحميل
```

### 3. Color Scheme / نظام الألوان
- **Primary Color**: #ff79c6 (Pink/Purple)
- **Background**: Dark theme with rgb(33, 37, 43)
- **Text**: White (#ffffff)
- **Buttons**: Modern gradient design

### 4. Functionality / الوظائف

#### Search Functionality
- إدخال رابط OSN+ أو معرف المحتوى
- البحث التلقائي عن المحتوى
- عرض معلومات الأفلام والمسلسلات
- حفظ الروابط الحديثة

#### Content Display
- عرض الصورة والتفاصيل
- دعم الأفلام والمسلسلات
- تبويبات منظمة للمعلومات
- أزرار تفاعلية للتشغيل والتحميل

## File Structure / هيكل الملفات

```
OSN_NEW/
├── modules/
│   ├── ui_main.py          # Main UI with new home page design
│   ├── osn_ui.py           # OSN UI controller with new functions
│   ├── osn_api.py          # OSN API integration
│   ├── osn_drm.py          # DRM handling
│   └── osn_downloader.py   # Download functionality
├── main.py                 # Main application entry point
├── test_ui.py              # UI testing script
└── README_NEW_UI.md        # This file
```

## Usage / الاستخدام

### Running the Application / تشغيل التطبيق
```bash
cd OSN_NEW
python main.py
```

### Testing the UI / اختبار الواجهة
```bash
cd OSN_NEW
python test_ui.py
```

### Example URLs / أمثلة الروابط
```
https://osnplus.com/en-ae/shows/1035538-al-abqari
https://osnplus.com/en-ae/movies/[movie-id]
1035538  # Content ID only
```

## Technical Details / التفاصيل التقنية

### New UI Functions / الوظائف الجديدة في الواجهة
- `handle_search()`: معالجة البحث
- `handle_clear()`: مسح البيانات
- `handle_play()`: التشغيل
- `handle_continue()`: المتابعة
- `handle_recent_selection()`: اختيار من الروابط الحديثة
- `add_to_recent_urls()`: إضافة للروابط الحديثة
- `display_content_info()`: عرض معلومات المحتوى
- `add_episodes_tab()`: إضافة تبويب الحلقات
- `add_download_options_tab()`: إضافة تبويب خيارات التحميل

### UI Layout / تخطيط الواجهة
- **VBoxLayout**: التخطيط الرئيسي العمودي
- **QFrame**: إطارات منظمة للأقسام
- **QTabWidget**: تبويبات للمحتوى
- **QHBoxLayout**: تخطيط أفقي للأزرار
- **Responsive Design**: تصميم متجاوب

## API Improvements / تحسينات الـ API

### Authentication / المصادقة
- **Cookies Support**: دعم كامل للكوكيز من المتصفح
- **Auto Token Refresh**: تحديث تلقائي للتوكن كل 30 دقيقة
- **Error Handling**: معالجة محسنة للأخطاء مع رسائل واضحة
- **Retry Logic**: إعادة المحاولة التلقائية عند انتهاء صلاحية التوكن

### API Endpoints / نقاط الـ API
- **Movies**: `https://api.osnplus.com/osn/media/v1/get-watch-content`
- **Series**: `https://koussa-osn.anghami.com/osn/media/v1/get-series-page`
- **Token Refresh**: `https://koussa-osn.anghami.com/osn/auth/v1/refresh-token`

### Error Messages / رسائل الخطأ
- **401**: Token expired - automatic refresh
- **403**: Access forbidden - region restriction
- **404**: Content not found
- **Timeout**: Connection timeout
- **No Cookies**: Missing cookies file

## Troubleshooting / استكشاف الأخطاء

### "No access token available"
1. تأكد من وجود ملف `cookies.txt`
2. تأكد من تسجيل الدخول إلى OSN+
3. صدر كوكيز جديدة من المتصفح
4. راجع دليل `COOKIES_GUIDE.md`

### "Failed to retrieve series details. Status code: 400"
1. تأكد من صحة رابط المسلسل
2. تأكد من أن المحتوى متاح في منطقتك
3. جرب استخدام معرف المحتوى فقط بدلاً من الرابط الكامل

### "Connection error"
1. تأكد من اتصالك بالإنترنت
2. تأكد من أن OSN+ متاح في منطقتك
3. جرب استخدام VPN إذا لزم الأمر

## Next Steps / الخطوات التالية

1. **Episodes Tab**: تطوير تبويب الحلقات مع عرض المواسم
2. **Download Options**: تطوير خيارات التحميل والجودة
3. **Quality Selection**: اختيار الجودة والدقة
4. **Progress Tracking**: تتبع تقدم التحميل
5. **Settings**: إعدادات التطبيق والتحميل
6. **DRM Integration**: دمج نظام DRM للتحميل

## Notes / ملاحظات
- التصميم مطابق لـ Shahid Downloader Pro
- يدعم الثيم المظلم
- واجهة سهلة الاستخدام
- تصميم حديث ومتجاوب
- معالجة محسنة للأخطاء
- دعم كامل للكوكيز والمصادقة
