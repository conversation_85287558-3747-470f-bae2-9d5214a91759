@echo off
echo ================================================================
echo   YANGO PLAY - Simple EXE Builder
echo ================================================================
echo.

echo 🎵 Building YANGO PLAY executable...
echo.

python -m nuitka --standalone --enable-plugin=pyside6 --windows-console-mode=disable --windows-icon-from-ico=icons/yango.ico --include-data-dir=binaries=binaries --include-data-dir=modules=modules --include-data-dir=widgets=widgets --include-data-dir=themes=themes --include-data-dir=images=images --include-data-dir=images-osn=images-osn --include-data-dir=images-shahid=images-shahid --include-data-dir=player=player --include-data-dir=player-osn=player-osn --include-data-dir=player-shahid=player-shahid --include-data-dir=icons=icons --include-data-file=cookies.txt=cookies.txt --include-data-file=device.wvd=device.wvd --include-data-file=qt_core.py=qt_core.py --include-data-file=cookies-osn.txt=cookies-osn.txt --include-data-file=osn_refresh_token.txt=osn_refresh_token.txt --output-dir=dist --output-filename=YANGO_PLAY.exe main.py

echo.
if exist "dist\YANGO_PLAY.exe" (
    echo ✅ Build completed successfully!
    echo 📁 Executable created: dist\YANGO_PLAY.exe
    echo.
    echo Do you want to run it now? (y/n)
    set /p choice=
    if /i "%choice%"=="y" (
        start "" "dist\YANGO_PLAY.exe"
    )
) else (
    echo ❌ Build failed!
)

pause
