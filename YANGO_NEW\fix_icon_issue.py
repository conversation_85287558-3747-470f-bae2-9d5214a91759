"""
Try different approaches to fix the icon issue
"""

import os
import subprocess

def try_resource_hacker():
    """Try using Resource Hacker to add icon"""
    print("🔧 Trying Resource Hacker approach...")
    
    # Check if we have the exe file
    exe_path = "../dist/YANGO_PLAY.exe"
    if not os.path.exists(exe_path):
        print("❌ EXE file not found")
        return False
    
    # Check if we have the icon
    icon_path = "YANGOTO.ico"
    if not os.path.exists(icon_path):
        print("❌ Icon file not found")
        return False
    
    print(f"✅ Found EXE: {exe_path}")
    print(f"✅ Found Icon: {icon_path}")
    
    # Try to download Resource Hacker (if not available)
    rh_path = "ResourceHacker.exe"
    if not os.path.exists(rh_path):
        print("ℹ️ Resource Hacker not found")
        print("📥 You can download it from: http://www.angusj.com/resourcehacker/")
        return False
    
    # Use Resource Hacker to add icon
    try:
        cmd = [
            rh_path,
            "-open", exe_path,
            "-save", exe_path + "_with_icon.exe",
            "-action", "addoverwrite",
            "-res", icon_path,
            "-mask", "ICONGROUP,MAINICON,"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Resource Hacker succeeded")
            return True
        else:
            print(f"❌ Resource Hacker failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error running Resource Hacker: {e}")
        return False

def check_icon_format():
    """Check if the icon file is properly formatted"""
    print("\n🔍 Checking icon format...")
    
    icon_path = "YANGOTO.ico"
    if not os.path.exists(icon_path):
        print("❌ Icon file not found")
        return False
    
    try:
        from PIL import Image
        
        with Image.open(icon_path) as img:
            print(f"✅ Icon format: {img.format}")
            print(f"✅ Icon mode: {img.mode}")
            print(f"✅ Icon size: {img.size}")
            
            # Try to count frames
            frame_count = 0
            sizes = []
            try:
                while True:
                    img.seek(frame_count)
                    sizes.append(img.size)
                    frame_count += 1
            except EOFError:
                pass
            
            print(f"✅ Total frames: {frame_count}")
            print(f"✅ Available sizes: {sizes}")
            
            if frame_count >= 4:
                print("✅ Icon has multiple sizes - should work")
                return True
            else:
                print("⚠️ Icon has few sizes - might not work properly")
                return False
                
    except Exception as e:
        print(f"❌ Error checking icon: {e}")
        return False

def suggest_solutions():
    """Suggest alternative solutions"""
    print("\n💡 Alternative solutions:")
    print("1. Try a different ICO converter website")
    print("2. Use a professional icon editor like IcoFX")
    print("3. Convert PNG to ICO using ImageMagick")
    print("4. Use Windows built-in icon cache refresh")
    print("5. Try building without icon first, then add it manually")

if __name__ == "__main__":
    print("🔧 Diagnosing icon issue...")
    
    # Check icon format
    icon_ok = check_icon_format()
    
    # Try Resource Hacker
    rh_ok = try_resource_hacker()
    
    if not icon_ok and not rh_ok:
        suggest_solutions()
    
    print("\n📝 Next steps:")
    print("1. Try recreating the ICO file with a different converter")
    print("2. Make sure the ICO has sizes: 16, 32, 48, 64, 128, 256")
    print("3. Clear Windows icon cache manually")
    print("4. Restart Windows Explorer")
