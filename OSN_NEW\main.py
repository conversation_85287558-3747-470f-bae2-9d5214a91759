# ///////////////////////////////////////////////////////////////
#
# BY: WANDERSON M.PIMENTA
# PROJECT MADE WITH: Qt Designer and PySide6
# V: 1.0.0
#
# This project can be used freely for all uses, as long as they maintain the
# respective credits only in the Python scripts, any information in the visual
# interface (GUI) can be modified without any implication.
#
# There are limitations on Qt licenses if you want to use your products
# commercially, I recommend reading them on the official website:
# https://doc.qt.io/qtforpython/licenses.html
#
# ///////////////////////////////////////////////////////////////

import sys
import os
from pathlib import Path

# IMPORT / GUI AND MODULES AND WIDGETS
# ///////////////////////////////////////////////////////////////
from modules import *
from widgets import *

# IMPORT OSN MODULES
# ///////////////////////////////////////////////////////////////
from modules.osn_api import OSNApi
from modules.osn_ui import OSNUi
from modules.osn_downloader import OSNDownloader
from modules.osn_drm import OSNDrm

os.environ["QT_FONT_DPI"] = "96" # FIX Problem for High DPI and Scale above 100%

# SET AS GLOBAL WIDGETS
# ///////////////////////////////////////////////////////////////
widgets = None

class MainWindow(QMainWindow):
    def __init__(self):
        QMainWindow.__init__(self)

        # SET AS GLOBAL WIDGETS
        # ///////////////////////////////////////////////////////////////
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        global widgets
        widgets = self.ui

        # USE CUSTOM TITLE BAR | USE AS "False" FOR MAC OR LINUX
        # ///////////////////////////////////////////////////////////////
        Settings.ENABLE_CUSTOM_TITLE_BAR = True

        # APP NAME
        # ///////////////////////////////////////////////////////////////
        title = "OSN+ Downloader - Modern GUI"
        description = "OSN+ Downloader - Modern GUI for downloading OSN+ content"
        # APPLY TEXTS
        self.setWindowTitle(title)
        if hasattr(widgets, 'titleLeftDescription'):
            widgets.titleLeftDescription.setText(description)

        # TOGGLE MENU
        # ///////////////////////////////////////////////////////////////
        widgets.toggleButton.clicked.connect(lambda: UIFunctions.toggleMenu(self, True))

        # SET UI DEFINITIONS
        # ///////////////////////////////////////////////////////////////
        UIFunctions.uiDefinitions(self)

        # QTableWidget PARAMETERS
        # ///////////////////////////////////////////////////////////////
        if hasattr(widgets, 'tableWidget'):
            widgets.tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # BUTTONS CLICK
        # ///////////////////////////////////////////////////////////////

        # LEFT MENUS
        widgets.btn_home.clicked.connect(self.buttonClick)
        widgets.btn_widgets.clicked.connect(self.buttonClick)
        widgets.btn_new.clicked.connect(self.buttonClick)
        widgets.btn_save.clicked.connect(self.buttonClick)

        # EXTRA LEFT BOX
        def openCloseLeftBox():
            UIFunctions.toggleLeftBox(self, True)
        if hasattr(widgets, 'toggleLeftBox'):
            widgets.toggleLeftBox.clicked.connect(openCloseLeftBox)
        if hasattr(widgets, 'extraCloseColumnBtn'):
            widgets.extraCloseColumnBtn.clicked.connect(openCloseLeftBox)

        # EXTRA RIGHT BOX
        def openCloseRightBox():
            UIFunctions.toggleRightBox(self, True)
        if hasattr(widgets, 'settingsTopBtn'):
            widgets.settingsTopBtn.clicked.connect(openCloseRightBox)

        # SHOW APP
        # ///////////////////////////////////////////////////////////////
        self.show()

        # SET CUSTOM THEME
        # ///////////////////////////////////////////////////////////////
        useCustomTheme = False
        themeFile = "themes/py_dracula_dark.qss"

        # SET THEME AND HACKS
        if useCustomTheme:
            # LOAD AND APPLY STYLE
            UIFunctions.theme(self, themeFile, True)

            # SET HACKS
            AppFunctions.setThemeHack(self)

        # SET HOME PAGE AND SELECT MENU
        # ///////////////////////////////////////////////////////////////
        widgets.stackedWidget.setCurrentWidget(widgets.home)
        widgets.btn_home.setStyleSheet(UIFunctions.selectMenu(widgets.btn_home.styleSheet()))

        # Initialize OSN modules
        self.osn_api = OSNApi()
        self.osn_downloader = OSNDownloader(self)
        self.osn_drm = OSNDrm()
        self.osn_ui = OSNUi(self)

        # Connect OSN API signals
        self.osn_api.content_found.connect(self.handle_content_found)
        self.osn_api.episodes_found.connect(self.handle_episodes_found)
        self.osn_api.error_occurred.connect(self.handle_api_error)
        self.osn_api.login_status_changed.connect(self.handle_login_status)

        # Connect downloader signals
        self.osn_downloader.download_progress.connect(self.handle_download_progress)
        self.osn_downloader.download_completed.connect(self.handle_download_completed)
        self.osn_downloader.download_error.connect(self.handle_download_error)
        self.osn_downloader.video_progress.connect(self.handle_video_progress)
        self.osn_downloader.status_update.connect(self.handle_status_update)

        # Connect DRM signals
        self.osn_drm.drm_keys_extracted.connect(self.handle_drm_keys)
        self.osn_drm.drm_error.connect(self.handle_drm_error)

    # BUTTONS CLICK
    # Post here your functions for clicked buttons
    # ///////////////////////////////////////////////////////////////
    def buttonClick(self):
        # GET BUTTON CLICKED
        btn = self.sender()
        btnName = btn.objectName()

        # SHOW HOME PAGE
        if btnName == "btn_home":
            widgets.stackedWidget.setCurrentWidget(widgets.home)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # SHOW MOVIES PAGE
        if btnName == "btn_widgets":
            widgets.stackedWidget.setCurrentWidget(widgets.widgets)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # SHOW SERIES PAGE
        if btnName == "btn_new":
            widgets.stackedWidget.setCurrentWidget(widgets.new_page)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # SHOW SETTINGS PAGE
        if btnName == "btn_save":
            widgets.stackedWidget.setCurrentWidget(widgets.save_page)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # PRINT BTN NAME
        print(f'Button "{btnName}" pressed!')

    # OSN API HANDLERS
    # ///////////////////////////////////////////////////////////////
    def handle_content_found(self, content_data):
        """Handle when content is found by OSN API"""
        content_type = content_data.get('type')
        if content_type == 'movie':
            # Display movie info like series - show poster and data first
            self.osn_ui.display_movie_info(content_data['data'], content_data['movie_id'])
        elif content_type == 'series':
            self.osn_ui.display_series_info(content_data['data'], content_data['series_id'])

    def handle_episodes_found(self, episodes_data):
        """Handle when episodes are found by OSN API"""
        self.osn_ui.display_episodes_list(episodes_data)

    def handle_api_error(self, error_message):
        """Handle API errors"""
        self.osn_ui.show_message("API Error", error_message)

    def handle_login_status(self, is_logged_in, message):
        """Handle login status changes"""
        if is_logged_in:
            self.osn_ui.status_updated.emit(f"✅ {message}")
        else:
            self.osn_ui.status_updated.emit(f"❌ {message}")

    def handle_download_progress(self, percentage, message):
        """Handle download progress updates"""
        print(f"🎯 MAIN: Received download_progress signal: {percentage}% - {message}")
        self.osn_ui.progress_updated.emit(percentage, message)

    def handle_download_completed(self, file_path, success):
        """Handle download completion"""
        print(f"🎯 MAIN: Received download_completed signal: {success} - {file_path}")
        if success:
            self.osn_ui.show_message("Download Complete", f"File saved to: {file_path}")
        else:
            self.osn_ui.show_message("Download Failed", "Download could not be completed")

    def handle_download_error(self, error_message):
        """Handle download errors"""
        print(f"🎯 MAIN: Received download_error signal: {error_message}")
        self.osn_ui.show_message("Download Error", error_message)

    def handle_video_progress(self, percentage):
        """Handle video progress updates from friend's solution"""
        print(f"🎯 MAIN: Received video_progress signal: {percentage}%")
        self.osn_ui.progress_updated.emit(percentage, f"📥 Video: {percentage}%")
        print(f"🎬 Video Progress: {percentage}%")

    def handle_status_update(self, status_message):
        """Handle status updates from friend's solution"""
        print(f"🎯 MAIN: Received status_update signal: {status_message}")
        self.osn_ui.status_updated.emit(status_message)
        print(f"📝 Status: {status_message}")

    def handle_drm_keys(self, drm_info):
        """Handle DRM keys extraction"""
        self.osn_ui.status_updated.emit("DRM keys extracted successfully")

    def handle_drm_error(self, error_message):
        """Handle DRM errors"""
        self.osn_ui.show_message("DRM Error", error_message)

    # RESIZE EVENTS
    # ///////////////////////////////////////////////////////////////
    def resizeEvent(self, event):
        # Update Size Grips
        UIFunctions.resize_grips(self)

    # MOUSE CLICK EVENTS
    # ///////////////////////////////////////////////////////////////
    def mousePressEvent(self, event):
        # SET DRAG POS WINDOW
        self.dragPos = event.globalPos()

        # PRINT MOUSE EVENTS
        if event.buttons() == Qt.LeftButton:
            print('Mouse click: LEFT CLICK')
        if event.buttons() == Qt.RightButton:
            print('Mouse click: RIGHT CLICK')

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon("icon.ico"))
    window = MainWindow()

    # Save data when application is about to quit
    def save_on_exit():
        try:
            if hasattr(window, 'osn_ui') and window.osn_ui:
                window.osn_ui.save_recent_urls_to_file()
                print("💾 Saved application data before exit")
        except Exception as e:
            print(f"❌ Error saving data on exit: {str(e)}")

    app.aboutToQuit.connect(save_on_exit)

    sys.exit(app.exec_())
