{"advertising": {"admessage": "Реклама закончится через xx", "cuetext": "Реклама", "displayHeading": "Реклама", "loadingAd": "Загрузка рекламы", "podmessage": "Реклама __AD_POD_CURRENT__ из __AD_POD_LENGTH__.", "skipmessage": "Пропустить рекламу через xx", "skiptext": "Пропустить"}, "airplay": "AirPlay", "audioTracks": "Аудиотреки", "auto": "Автонастройка", "buffer": "Загрузка", "cast": "Chromecast", "cc": "Субтитры", "close": "Закрыть", "errors": {"badConnection": "Невозможно воспроизвести видео из-за проблемы с подключением к Интернету.", "cantLoadPlayer": "К сожалению, при загрузке видеоплеера произошла ошибка.", "cantPlayInBrowser": "Невозможно воспроизвести видео в этом браузере.", "cantPlayVideo": "Невозможно воспроизвести данный видеофайл.", "errorCode": "Код ошибки", "liveStreamDown": "Трансляция приостановлена либо завершена.", "protectedContent": "Ошибка доступа к защищенному контенту.", "technicalError": "Невозможно воспроизвести видео в связи с технической ошибкой."}, "exitFullscreen": "Выйти из полноэкранного режима", "fullscreen": "Полноэкранный режим", "hd": "Качество", "liveBroadcast": "Прямой эфир", "logo": "Лого<PERSON>ип", "mute": "Без звука", "next": "Далее", "nextUp": "Следующее", "notLive": "Запись", "off": "Отключить", "pause": "Пауза", "pipIcon": "Режим Картинка в картинке", "play": "Воспроизвести", "playback": "Воспроизвести", "playbackRates": "Скорость воспроизведения", "player": "Видеоплеер", "poweredBy": "На базе", "prev": "Предыдущее", "related": {"autoplaymessage": "Следующее через xx", "heading": "Другие видео"}, "replay": "Повтор", "rewind": "Перемотать на 10 сек назад", "settings": "Настройки", "sharing": {"copied": "Скопировано", "email": "Эл. почта", "embed": "Встроить", "heading": "Поделиться", "link": "Ссылка"}, "slider": "Поиск", "stop": "Стоп", "unmute": "Включить звук", "videoInfo": "Об этом видео", "volume": "Громкость", "volumeSlider": "Громкость", "shortcuts": {"playPause": "Воспроизвести/Пауза", "volumeToggle": "Без звука/Включить звук", "fullscreenToggle": "Полноэкранный режим/Выйти из полноэкранного режима", "seekPercent": "Перемотать %", "keyboardShortcuts": "Горячие Клавиши", "increaseVolume": "Увеличить Громкость", "decreaseVolume": "Уменьшить Громкость", "seekForward": "Перемотать Вперёд", "seekBackward": "Перемотать Назад", "spacebar": "Пробел", "captionsToggle": "Субтитры Вкл/Выкл"}, "captionsStyles": {"subtitleSettings": "Настройки субтитров", "color": "Цвет шрифта", "fontOpacity": "Прозрачность шрифта", "userFontScale": "Размер шрифта", "fontFamily": "<PERSON>ри<PERSON><PERSON>", "edgeStyle": "Контур шрифта", "backgroundColor": "Цвет фона", "backgroundOpacity": "Прозрачность фона", "windowColor": "Цвет окна", "windowOpacity": "Прозрачность окна", "white": "Белый", "black": "Черный", "red": "Красный", "green": "Зеленый", "blue": "Синий", "yellow": "Желт<PERSON>й", "magenta": "Пурпурный", "cyan": "Сине-зелёный", "none": "Нет", "raised": "Рельефный", "depressed": "Грав<PERSON>рованный", "uniform": "Контурный", "dropShadow": "Отбросить тень"}, "disabled": "Выкл.", "enabled": "<PERSON><PERSON><PERSON>.", "reset": "Сбросить"}