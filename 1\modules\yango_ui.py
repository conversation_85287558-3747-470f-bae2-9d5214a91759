#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
YANGO UI Module
Handles the user interface for YANGO PLAY application
"""

from PySide6.QtCore import QObject, Signal, Qt, QThread
from PySide6.QtWidgets import (QMessageBox, QLabel, QVBoxLayout, QHBoxLayout,
                               QPushButton, QComboBox, QListWidget, QTextEdit, QWidget,
                               QListWidgetItem, QTabWidget, QCheckBox, QGroupBox,
                               QFrame, QLineEdit, QSizePolicy)
from PySide6.QtGui import QPixmap, QBrush, QColor
import requests
import json
import os
from .yango_downloader import YangoDownloader
from .movie_handler import MovieHandler

class YangoUi(QObject):
    # Signals for communication with main window
    status_updated = Signal(str)
    progress_updated = Signal(int, str)
    content_displayed = Signal(dict)

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.widgets = main_window.ui
        self.current_content = None
        self.current_qualities = []
        self.current_seasons = []
        self.current_series_info = {}

        # Initialize downloader with correct base directory
        from modules.resource_utils import get_base_directory
        base_dir = get_base_directory()
        self.downloader = YangoDownloader(base_dir=base_dir)
        self.setup_downloader_signals()

        # Initialize movie handler
        self.movie_handler = MovieHandler(self)

        # Initialize variables for movie handler
        self.current_mpd_url = ""
        self.current_decryption_keys = []

        # Initialize YANGO player instance
        self.yango_player_instance = None

        # Initialize settings
        from .yango_settings import YangoSettings
        self.settings_manager = YangoSettings()

        self.setup_ui_connections()
        self.setup_content_tabs()

        # Connect API signals
        if hasattr(main_window, 'yango_api'):
            print("🔗 Connecting API signals...")
            main_window.yango_api.error_occurred.connect(self.handle_api_error)
            print("✅ Connected error_occurred signal")
            main_window.yango_api.movie_found.connect(self.handle_movie_found)
            print("✅ Connected movie_found signal")
            main_window.yango_api.series_found.connect(self.handle_series_found)
            print("✅ Connected series_found signal")
            main_window.yango_api.streams_found.connect(self.handle_streams_found)
            print("✅ Connected streams_found signal")
            print("✅ All API signals connected successfully")
        else:
            print("❌ main_window.yango_api not found - signals not connected")

    def setup_downloader_signals(self):
        """Setup downloader signal connections"""
        if hasattr(self, 'downloader'):
            self.downloader.download_progress.connect(self.update_download_progress)
            self.downloader.download_completed.connect(self.handle_download_completed)
            self.downloader.download_error.connect(self.handle_download_error)
            self.downloader.fetch_episode_streams.connect(self.fetch_episode_streams_for_download)
            # Connect new signals for improved progress tracking
            self.downloader.status_update.connect(self.handle_status_update)
            self.downloader.video_progress.connect(self.handle_video_progress)
            self.downloader.audio_progress.connect(self.handle_audio_progress)
            self.downloader.subtitle_progress.connect(self.handle_subtitle_progress)
            print("✅ Downloader signals connected")

    def handle_status_update(self, status_message):
        """Handle status update from downloader (silent mode)"""
        # This can be used to update a status bar or log window
        # For now, we'll just print it (can be made silent later)
        pass  # Silent mode - no console output

    def handle_video_progress(self, progress):
        """Handle video progress update from downloader"""
        # Video progress signal - now handled by main download_progress signal
        # This is kept for compatibility but doesn't interfere with main progress
        pass

    def handle_audio_progress(self, progress):
        """Handle audio progress update from downloader"""
        # Audio progress signal - now handled by main download_progress signal
        # This is kept for compatibility but doesn't interfere with main progress
        pass

    def handle_subtitle_progress(self, progress):
        """Handle subtitle progress update from downloader"""
        # Subtitle progress signal - now handled by main download_progress signal
        # This is kept for compatibility but doesn't interfere with main progress
        pass

    def setup_ui_connections(self):
        """Setup UI connections and initialize components"""
        # Connect search functionality - use UI elements from main window
        if hasattr(self.widgets, 'search_button'):
            self.search_button = self.widgets.search_button
            self.search_button.clicked.connect(self.handle_search)
            print("✅ Connected search_button")

        if hasattr(self.widgets, 'url_input'):
            self.url_input = self.widgets.url_input
            # Connect Enter key press to search
            self.url_input.returnPressed.connect(self.handle_search)
            # Update placeholder text for YANGO
            self.url_input.setPlaceholderText("Enter YANGO URL or content ID...")
            print("✅ Found and configured url_input")

        if hasattr(self.widgets, 'recent_combo'):
            self.recent_combo = self.widgets.recent_combo
            # Use activated signal only - this is triggered only when user clicks on an item
            self.recent_combo.activated.connect(self.handle_recent_selection_by_index)
            print("✅ Connected recent_combo")

        # Connect quality selection functionality
        if hasattr(self.widgets, 'quality_combo'):
            self.quality_combo = self.widgets.quality_combo
            self.quality_combo.currentTextChanged.connect(self.on_quality_changed)

            # Load default quality from settings
            try:
                from .yango_settings import YangoSettings
                settings = YangoSettings()
                default_quality = settings.current_settings.get("quality", {}).get("default_quality", "HD")
                self.quality_combo.setCurrentText(default_quality)
                print(f"✅ Loaded default quality from settings: {default_quality}")
            except Exception as e:
                print(f"⚠️ Could not load quality from settings: {str(e)}, using HD")
                default_quality = "HD"
                self.quality_combo.setCurrentText(default_quality)

            # Set initial quality in API
            self.main_window.yango_api.set_quality(default_quality)
            print(f"✅ Connected quality_combo and set initial quality to: {default_quality}")

        # Connect clear functionality
        if hasattr(self.widgets, 'clear_button'):
            self.clear_button = self.widgets.clear_button
            self.clear_button.clicked.connect(self.handle_clear)
            print("✅ Connected clear_button")

        # Load recent URLs from file
        self.load_recent_urls_from_file()

        # Connect play and continue buttons if they exist
        if hasattr(self.widgets, 'play_button'):
            self.widgets.play_button.clicked.connect(self.handle_play)
            print("✅ Connected play_button")

        if hasattr(self.widgets, 'continue_button'):
            self.widgets.continue_button.clicked.connect(self.handle_continue)
            print("✅ Connected continue_button")

        # Setup status updates
        self.status_updated.connect(self.update_status_bar)
        self.progress_updated.connect(self.update_progress_bar)

        print("✅ UI connections setup completed")

        # Load recent URLs from file
        self.load_recent_urls_from_file()

    def setup_content_tabs(self):
        """Setup content tabs like Shahid design"""
        # Use existing content_tabs from main window if available
        if hasattr(self.main_window, 'content_tabs'):
            self.content_tabs = self.main_window.content_tabs
            print(f"✅ Using existing content_tabs from main window: {self.content_tabs}")
            print(f"🔍 Content tabs visible: {self.content_tabs.isVisible()}")
            print(f"🔍 Content tabs parent: {self.content_tabs.parent()}")

            # Also get references to existing UI elements from main window
            if hasattr(self.main_window, 'content_title'):
                self.title_label = self.main_window.content_title
                print(f"✅ Found content_title: {self.title_label}")
            if hasattr(self.main_window, 'content_type'):
                self.type_label = self.main_window.content_type
                print(f"✅ Found content_type: {self.type_label}")
            if hasattr(self.main_window, 'content_description'):
                self.description_text = self.main_window.content_description
                print(f"✅ Found content_description: {self.description_text}")
            if hasattr(self.main_window, 'poster_label'):
                self.poster_label = self.main_window.poster_label
                print(f"✅ Found poster_label: {self.poster_label}")

        elif hasattr(self.widgets, 'content_tabs'):
            self.content_tabs = self.widgets.content_tabs
            print("✅ Using existing content_tabs from widgets")

            # Also get references to existing UI elements
            if hasattr(self.widgets, 'content_title'):
                self.title_label = self.widgets.content_title
            if hasattr(self.widgets, 'content_type'):
                self.type_label = self.widgets.content_type
            if hasattr(self.widgets, 'content_description'):
                self.description_text = self.widgets.content_description
            if hasattr(self.widgets, 'poster_label'):
                self.poster_label = self.widgets.poster_label

        else:
            # Create new content tabs widget
            self.content_tabs = QTabWidget()
            print("✅ Created new content_tabs widget")

        # Apply Shahid-like styling
        self.content_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #44475a;
                background-color: #282a36;
            }
            QTabBar::tab {
                background-color: #282a36;
                color: #f8f8f2;
                padding: 10px 20px;
                margin-right: 2px;
                border: 1px solid #44475a;
                border-bottom: none;
            }
            QTabBar::tab:selected {
                background-color: #44475a;
                border-bottom: 3px solid #00bcd4;
            }
            QTabBar::tab:!selected {
                margin-top: 2px;
            }
        """)

        # Clear existing tabs and add new ones
        self.content_tabs.clear()
        print("🔄 Cleared existing tabs")

        self.setup_content_info_tab()
        self.setup_seasons_tab()
        self.setup_available_streams_tab()
        self.setup_downloads_tab()

        # Keep tabs hidden initially - will show after search
        self.content_tabs.setVisible(False)
        self.content_tabs.setCurrentIndex(0)  # Show first tab when visible

        # Add settings button to the main UI
        self.add_settings_button()

        print("✅ All tabs setup completed (hidden until search)")

    def setup_downloads_tab(self):
        """Setup Downloads tab with organized design matching other tabs"""
        self.downloads_tab = QWidget()
        self.downloads_layout = QVBoxLayout(self.downloads_tab)
        self.downloads_layout.setContentsMargins(10, 10, 10, 10)
        self.downloads_layout.setSpacing(10)

        # Downloads header section
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: transparent;
                border: none;
            }
        """)
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(0, 0, 0, 0)

        # Downloads title
        downloads_title = QLabel("📥 Downloads Queue")
        downloads_title.setStyleSheet("""
            QLabel {
                color: #bd93f9;
                font-size: 16pt;
                font-weight: bold;
                padding: 5px 0px;
            }
        """)
        header_layout.addWidget(downloads_title)

        # Downloads stats
        self.downloads_stats = QLabel("0 items in queue")
        self.downloads_stats.setStyleSheet("""
            QLabel {
                color: #8be9fd;
                font-size: 12pt;
                padding: 5px 0px;
            }
        """)
        header_layout.addStretch()
        header_layout.addWidget(self.downloads_stats)

        self.downloads_layout.addWidget(header_frame)

        # Downloads table with organized design
        from PySide6.QtWidgets import QTableWidget, QHeaderView, QSizePolicy
        self.downloads_table = QTableWidget()
        self.downloads_table.setColumnCount(7)
        self.downloads_table.setHorizontalHeaderLabels([
            "Title", "Season", "Episode", "Quality", "Progress", "Status", "Actions"
        ])

        # Make table take full width and height
        self.downloads_table.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
        self.downloads_table.horizontalHeader().setStretchLastSection(True)

        # Apply consistent styling matching other tabs
        self.downloads_table.setStyleSheet("""
            QTableWidget {
                background-color: #282a36;
                border: 1px solid #44475a;
                border-radius: 8px;
                gridline-color: #44475a;
                selection-background-color: #bd93f9;
                color: #f8f8f2;
                font-size: 12px;
                font-weight: 500;
                font-family: 'Segoe UI', Arial, sans-serif;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 10px 8px;
                border-bottom: 1px solid #44475a;
                border-right: 1px solid #44475a;
                text-align: center;
            }
            QTableWidget::item:selected {
                background-color: #bd93f9;
                color: #282a36;
            }
            QHeaderView::section {
                background-color: #6272a4;
                color: #f8f8f2;
                padding: 12px 8px;
                border: none;
                border-right: 1px solid #44475a;
                font-weight: bold;
                font-size: 12px;
                text-align: center;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QHeaderView::section:first {
                border-top-left-radius: 6px;
            }
            QHeaderView::section:last {
                border-top-right-radius: 6px;
                border-right: none;
            }
            QScrollBar:vertical {
                background-color: #282a36;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #6272a4;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #50fa7b;
            }
        """)

        # Configure table properties
        self.downloads_table.verticalHeader().setVisible(False)
        self.downloads_table.setAlternatingRowColors(False)
        self.downloads_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.downloads_table.setShowGrid(True)

        # Set optimized column widths for better display with manual resize capability
        header = self.downloads_table.horizontalHeader()

        # Enable manual column resizing by dragging column borders
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Interactive)  # Title - user can resize
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Interactive)  # Season - user can resize
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Interactive)  # Episode - user can resize
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Interactive)  # Quality - user can resize
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Interactive)  # Progress - user can resize
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Interactive)  # Status - user can resize
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.Stretch)      # Actions - stretch to fill remaining space

        # Note: setStretchLastSection already set above for full width

        # Set column widths to match the correct image exactly
        self.downloads_table.setColumnWidth(0, 300)  # Title - larger for content names (matches correct image)
        self.downloads_table.setColumnWidth(1, 80)   # Season - small and compact
        self.downloads_table.setColumnWidth(2, 80)   # Episode - small and compact
        self.downloads_table.setColumnWidth(3, 80)   # Quality - small and compact
        self.downloads_table.setColumnWidth(4, 180)   # Progress - smaller as in correct image
        self.downloads_table.setColumnWidth(5, 150)   # Status - smaller as in correct image
        self.downloads_table.setColumnWidth(6, 200)   # Actions - smaller as in correct image

        # Set row height for better appearance and hide row numbers
        self.set_downloads_table_row_height(45)  # Customizable row height
        self.downloads_table.verticalHeader().setVisible(False)  # Hide row numbers

        # Enable smooth column resizing
        header.setCascadingSectionResizes(False)  # Disable cascading resize
        header.setHighlightSections(True)  # Highlight sections on hover
        header.setMinimumSectionSize(50)  # Minimum column width (increased)
        header.setDefaultSectionSize(100)  # Default column width

        # Enable section moving (optional - allows reordering columns)
        header.setSectionsMovable(False)  # Disable for now to avoid confusion

        self.downloads_layout.addWidget(self.downloads_table)

        # Control panel at the bottom - larger frame with buttons inside
        control_frame = QFrame(self.downloads_tab)
        control_frame.setStyleSheet("""
            QFrame {
                background-color: #282a36;
                border: 1px solid #44475a;
                border-radius: 8px;
                margin: 8px 0px;
            }
        """)
        control_frame.setFixedHeight(80)  # Increased height
        control_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)

        control_layout = QHBoxLayout(control_frame)
        control_layout.setContentsMargins(25, 10, 25, 10)  # Reduced top/bottom margins to raise buttons
        control_layout.setSpacing(10)  # Reduced spacing between buttons

        # Add stretch to push buttons to the right
        control_layout.addStretch()

        # Control buttons - smaller and aligned to right
        self.start_download_btn = QPushButton("🚀 Start Downloads")
        self.start_download_btn.setStyleSheet("""
            QPushButton {
                background-color: #50fa7b;
                color: #282a36;
                border: 1px solid #50fa7b;
                padding: 6px 12px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 10px;
                min-width: 100px;
                max-width: 110px;
                min-height: 28px;
                max-height: 30px;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton:hover {
                background-color: #8be9fd;
                border: 1px solid #8be9fd;
            }
            QPushButton:pressed {
                background-color: #44475a;
                color: #f8f8f2;
            }
        """)
        self.start_download_btn.clicked.connect(self.start_all_downloads)
        control_layout.addWidget(self.start_download_btn)

        self.pause_download_btn = QPushButton("⏸️ Pause")
        self.pause_download_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffb86c;
                color: #282a36;
                border: 1px solid #ffb86c;
                border-radius: 5px;
                padding: 6px 12px;
                font-size: 10px;
                font-weight: bold;
                min-width: 65px;
                max-width: 70px;
                min-height: 28px;
                max-height: 30px;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton:hover {
                background-color: #f1fa8c;
                border: 1px solid #f1fa8c;
            }
            QPushButton:pressed {
                background-color: #44475a;
                color: #f8f8f2;
            }
        """)
        self.pause_download_btn.clicked.connect(self.pause_downloads)
        control_layout.addWidget(self.pause_download_btn)

        # Stop Downloads Button - Force stop all downloads
        self.stop_download_btn = QPushButton("⏹️ Stop")
        self.stop_download_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff5555;
                color: #f8f8f2;
                border: 1px solid #ff5555;
                border-radius: 5px;
                padding: 6px 12px;
                font-size: 10px;
                font-weight: bold;
                min-width: 65px;
                max-width: 70px;
                min-height: 28px;
                max-height: 30px;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton:hover {
                background-color: #ff7979;
                border: 1px solid #ff7979;
            }
            QPushButton:pressed {
                background-color: #e84393;
            }
        """)
        self.stop_download_btn.clicked.connect(self.stop_downloads)
        control_layout.addWidget(self.stop_download_btn)

        self.clear_completed_btn = QPushButton("🗑️ Clear Done")
        self.clear_completed_btn.setStyleSheet("""
            QPushButton {
                background-color: #6272a4;
                color: #f8f8f2;
                border: 1px solid #6272a4;
                border-radius: 5px;
                padding: 6px 12px;
                font-size: 10px;
                font-weight: bold;
                min-width: 85px;
                max-width: 90px;
                min-height: 28px;
                max-height: 30px;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton:hover {
                background-color: #8be9fd;
                color: #282a36;
                border: 1px solid #8be9fd;
            }
            QPushButton:pressed {
                background-color: #44475a;
                color: #f8f8f2;
            }
        """)
        self.clear_completed_btn.clicked.connect(self.clear_completed_downloads)
        control_layout.addWidget(self.clear_completed_btn)

        self.clear_all_btn = QPushButton("🗑️ Clear All")
        self.clear_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff5555;
                color: #f8f8f2;
                border: 1px solid #ff5555;
                border-radius: 5px;
                padding: 6px 12px;
                font-size: 10px;
                font-weight: bold;
                min-width: 75px;
                max-width: 80px;
                min-height: 28px;
                max-height: 30px;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
            QPushButton:hover {
                background-color: #ff7979;
                border: 1px solid #ff7979;
            }
            QPushButton:pressed {
                background-color: #e84393;
            }
        """)
        self.clear_all_btn.clicked.connect(self.clear_all_downloads)
        control_layout.addWidget(self.clear_all_btn)

        # Add control frame to layout
        self.downloads_layout.addWidget(control_frame)

        # Initialize downloads data
        self.download_items = []

        # Setup notification area
        self.setup_notification_area()

        # Add notification area to downloads layout
        self.downloads_layout.insertWidget(0, self.notification_area)

        # Add tab to content tabs
        self.content_tabs.addTab(self.downloads_tab, "📥 Downloads")

        # Add some demo data for testing (comment out for production)
        # self.add_demo_downloads()

        print("✅ Downloads tab setup completed with organized design")

    def set_downloads_table_row_height(self, height):
        """Set custom row height for downloads table"""
        self.downloads_table.verticalHeader().setDefaultSectionSize(height)
        print(f"📏 Downloads table row height set to: {height}px")

    def update_downloads_table_row_height(self, height):
        """Update row height for all existing rows in downloads table"""
        self.set_downloads_table_row_height(height)
        # Update existing rows
        for row in range(self.downloads_table.rowCount()):
            self.downloads_table.setRowHeight(row, height)
        print(f"📏 Updated all {self.downloads_table.rowCount()} rows to height: {height}px")

    def add_demo_downloads(self):
        """Add demo downloads for testing the design"""
        try:
            demo_downloads = [
                {
                    'title': 'Prestige',
                    'season': 'S01',
                    'episode': 'E01',
                    'quality': '1080p',
                    'audio_track': 'Arabic',
                    'subtitle': 'English'
                },
                {
                    'title': 'The Crown',
                    'season': 'S02',
                    'episode': 'E03',
                    'quality': '720p',
                    'audio_track': 'English',
                    'subtitle': 'Arabic'
                },
                {
                    'title': 'Breaking Bad',
                    'season': 'S01',
                    'episode': 'E05',
                    'quality': '4K',
                    'audio_track': 'English',
                    'subtitle': None
                }
            ]

            for demo in demo_downloads:
                self.add_download_to_table(demo)

            self.update_downloads_stats()
            print("✅ Added demo downloads for testing")

        except Exception as e:
            print(f"❌ Error adding demo downloads: {str(e)}")

    def setup_content_info_tab(self):
        """Setup Content Info tab like Shahid"""
        self.content_info_tab = QWidget()
        self.content_info_layout = QVBoxLayout(self.content_info_tab)
        self.content_info_layout.setContentsMargins(10, 10, 10, 10)

        # Content info frame with horizontal layout (poster + details)
        self.content_info_frame = QFrame()
        self.content_info_frame.setFrameShape(QFrame.NoFrame)
        self.content_info_frame.setFrameShadow(QFrame.Plain)
        self.content_info_frame.setStyleSheet("""
            QFrame {
                background-color: transparent;
                border-radius: 0px;
                border: none;
            }
        """)
        self.content_info_frame_layout = QHBoxLayout(self.content_info_frame)
        self.content_info_frame_layout.setSpacing(15)
        self.content_info_frame_layout.setContentsMargins(10, 10, 10, 10)

        # Left side - Poster
        self.poster_label = QLabel()
        self.poster_label.setMinimumSize(240, 360)
        self.poster_label.setMaximumSize(240, 360)
        self.poster_label.setStyleSheet("""
            QLabel {
                background-color: #44475a;
                border-radius: 4px;
                border: 1px solid #6272a4;
            }
        """)
        self.poster_label.setAlignment(Qt.AlignCenter)
        self.poster_label.setScaledContents(True)
        self.content_info_frame_layout.addWidget(self.poster_label)

        # Right side - Content details
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout(self.details_widget)
        self.details_layout.setAlignment(Qt.AlignTop)
        self.details_layout.setSpacing(5)
        self.details_layout.setContentsMargins(0, 0, 0, 0)

        # Title
        self.title_label = QLabel()
        self.title_label.setStyleSheet("""
            font-size: 20px;
            font-weight: bold;
            color: #ff79c6;
            margin-bottom: 5px;
            padding: 0px;
        """)
        self.title_label.setWordWrap(True)
        self.details_layout.addWidget(self.title_label)

        # Type/Year combined
        self.type_label = QLabel()
        self.type_label.setStyleSheet("""
            font-size: 13px;
            color: #8be9fd;
            margin-bottom: 8px;
            padding: 0px;
        """)
        self.details_layout.addWidget(self.type_label)

        # Year (hidden - combined with type)
        self.year_label = QLabel()
        self.year_label.setVisible(False)

        # Genres field with label and value
        self.genres_field_label = QLabel("🎭 Genres:")
        self.genres_field_label.setStyleSheet("""
            font-size: 12px;
            color: #50fa7b;
            font-weight: bold;
            margin-top: 3px;
            margin-bottom: 2px;
            padding: 0px;
        """)
        self.genres_field_label.setVisible(False)
        self.details_layout.addWidget(self.genres_field_label)

        self.genres_value_label = QLabel("")
        self.genres_value_label.setStyleSheet("""
            font-size: 12px;
            color: #f8f8f2;
            margin-bottom: 5px;
            padding: 0px;
        """)
        self.genres_value_label.setVisible(False)
        self.genres_value_label.setWordWrap(True)
        self.details_layout.addWidget(self.genres_value_label)

        # Cast field with label and value
        self.cast_field_label = QLabel("👥 Cast:")
        self.cast_field_label.setStyleSheet("""
            font-size: 12px;
            color: #50fa7b;
            font-weight: bold;
            margin-top: 3px;
            margin-bottom: 2px;
            padding: 0px;
        """)
        self.cast_field_label.setVisible(False)
        self.details_layout.addWidget(self.cast_field_label)

        self.cast_value_label = QLabel("")
        self.cast_value_label.setStyleSheet("""
            font-size: 12px;
            color: #f8f8f2;
            margin-bottom: 5px;
            padding: 0px;
        """)
        self.cast_value_label.setVisible(False)
        self.cast_value_label.setWordWrap(True)
        self.details_layout.addWidget(self.cast_value_label)

        # Quality features field with label and value
        self.quality_field_label = QLabel("🎥 Quality:")
        self.quality_field_label.setStyleSheet("""
            font-size: 12px;
            color: #50fa7b;
            font-weight: bold;
            margin-top: 3px;
            margin-bottom: 2px;
            padding: 0px;
        """)
        self.quality_field_label.setVisible(False)
        self.details_layout.addWidget(self.quality_field_label)

        self.quality_value_label = QLabel("")
        self.quality_value_label.setStyleSheet("""
            font-size: 12px;
            color: #f8f8f2;
            margin-bottom: 5px;
            padding: 0px;
        """)
        self.quality_value_label.setVisible(False)
        self.quality_value_label.setWordWrap(True)
        self.details_layout.addWidget(self.quality_value_label)

        # Description label
        self.description_field_label = QLabel("📝 Description:")
        self.description_field_label.setStyleSheet("""
            font-size: 12px;
            color: #50fa7b;
            font-weight: bold;
            margin-top: 8px;
            margin-bottom: 3px;
            padding: 0px;
        """)
        self.details_layout.addWidget(self.description_field_label)

        # Description
        self.description_text = QTextEdit()
        self.description_text.setReadOnly(True)
        self.description_text.setStyleSheet("""
            QTextEdit {
                background-color: rgba(68, 71, 90, 0.3);
                border: 1px solid #44475a;
                border-radius: 6px;
                color: #f8f8f2;
                font-size: 12px;
                padding: 8px;
                line-height: 1.4;
            }
        """)
        self.description_text.setMaximumHeight(100)
        self.details_layout.addWidget(self.description_text)

        # Add stretch to push everything to top
        self.details_layout.addStretch()

        self.content_info_frame_layout.addWidget(self.details_widget)
        self.content_info_layout.addWidget(self.content_info_frame)

        # Play and Continue buttons
        self.play_button = QPushButton("Play")
        self.play_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px;
                font-size: 14px;
                min-width: 120px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #a5d6a7;
                color: #e8f5e9;
            }
        """)
        self.play_button.setEnabled(False)
        self.play_button.clicked.connect(self.handle_play)

        self.continue_button = QPushButton("Continue")
        self.continue_button.setStyleSheet("""
            QPushButton {
                background-color: #00bcd4;
                color: white;
                padding: 10px;
                font-size: 14px;
                min-width: 120px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #00acc1;
            }
            QPushButton:pressed {
                background-color: #0097a7;
            }
            QPushButton:disabled {
                background-color: #80deea;
                color: #e0f7fa;
            }
        """)
        self.continue_button.setEnabled(False)
        self.continue_button.clicked.connect(self.handle_continue)

        self.content_info_button_layout = QHBoxLayout()
        self.content_info_button_layout.addStretch()
        self.content_info_button_layout.addWidget(self.play_button)
        self.content_info_button_layout.addWidget(self.continue_button)
        self.content_info_layout.addLayout(self.content_info_button_layout)

        self.content_tabs.addTab(self.content_info_tab, "📋 Content Info")

    def setup_seasons_tab(self):
        """Setup Seasons & Episodes tab with advanced selection options"""
        self.seasons_tab = QWidget()
        self.seasons_layout = QVBoxLayout(self.seasons_tab)
        self.seasons_layout.setContentsMargins(10, 10, 10, 10)
        self.seasons_layout.setSpacing(10)

        # Main content layout (horizontal)
        main_content_layout = QHBoxLayout()

        # Seasons section
        seasons_group = QGroupBox("Seasons")
        seasons_group.setStyleSheet("""
            QGroupBox {
                font-size: 12pt;
                font-weight: bold;
                color: #50fa7b;
                border: 2px solid #44475a;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #282a36;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        seasons_layout = QVBoxLayout(seasons_group)

        self.seasons_list = QListWidget()
        self.seasons_list.itemClicked.connect(self.handle_season_selection)
        self.seasons_list.setStyleSheet("""
            QListWidget {
                background-color: #44475a;
                color: #f8f8f2;
                border: 1px solid #6272a4;
                border-radius: 5px;
                padding: 5px;
                font-size: 11pt;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #6272a4;
            }
            QListWidget::item:selected {
                background-color: #50fa7b;
                color: #282a36;
            }
            QListWidget::item:hover {
                background-color: #6272a4;
            }
        """)
        seasons_layout.addWidget(self.seasons_list)

        # Episodes section with selection controls
        episodes_group = QGroupBox("Episodes")
        episodes_group.setStyleSheet("""
            QGroupBox {
                font-size: 12pt;
                font-weight: bold;
                color: #8be9fd;
                border: 2px solid #44475a;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: #282a36;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        episodes_layout = QVBoxLayout(episodes_group)

        # Selection controls bar
        selection_controls = QHBoxLayout()

        # Select All button
        self.select_all_btn = QPushButton("Select All")
        self.select_all_btn.clicked.connect(self.select_all_episodes)
        self.select_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #50fa7b;
                color: #282a36;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #5af78e;
            }
            QPushButton:pressed {
                background-color: #4cfa7a;
            }
        """)

        # Select None button
        self.select_none_btn = QPushButton("Select None")
        self.select_none_btn.clicked.connect(self.select_none_episodes)
        self.select_none_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff5555;
                color: #f8f8f2;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #ff6b6b;
            }
            QPushButton:pressed {
                background-color: #ff4757;
            }
        """)

        # Range selection with improved design
        range_frame = QFrame()
        range_frame.setStyleSheet("""
            QFrame {
                background-color: #44475a;
                border: 1px solid #6272a4;
                border-radius: 8px;
                padding: 5px;
                margin: 2px;
            }
        """)
        range_layout = QHBoxLayout(range_frame)
        range_layout.setContentsMargins(8, 5, 8, 5)
        range_layout.setSpacing(8)

        range_label = QLabel("Select Range:")
        range_label.setStyleSheet("color: #f8f8f2; font-weight: bold; font-size: 10pt; margin: 0px;")

        self.range_from_combo = QComboBox()
        self.range_from_combo.setStyleSheet("""
            QComboBox {
                background-color: #282a36;
                color: #f8f8f2;
                border: 1px solid #6272a4;
                border-radius: 5px;
                padding: 5px 8px;
                font-size: 10pt;
                min-width: 80px;
                max-width: 100px;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;
                border-top: 4px solid #f8f8f2;
                margin-right: 3px;
            }
            QComboBox QAbstractItemView {
                background-color: #282a36;
                color: #f8f8f2;
                border: 1px solid #6272a4;
                selection-background-color: #8be9fd;
                selection-color: #282a36;
            }
        """)

        range_to_label = QLabel("to")
        range_to_label.setStyleSheet("color: #8be9fd; font-size: 10pt; font-weight: bold; margin: 0px;")

        self.range_to_combo = QComboBox()
        self.range_to_combo.setStyleSheet(self.range_from_combo.styleSheet())

        # Apply range button
        self.apply_range_btn = QPushButton("Apply")
        self.apply_range_btn.clicked.connect(self.apply_range_selection)
        self.apply_range_btn.setStyleSheet("""
            QPushButton {
                background-color: #8be9fd;
                color: #282a36;
                border: none;
                border-radius: 5px;
                padding: 6px 12px;
                font-weight: bold;
                font-size: 10pt;
                min-width: 50px;
            }
            QPushButton:hover {
                background-color: #9aedfe;
            }
            QPushButton:pressed {
                background-color: #7ce0fc;
            }
        """)

        # Add to range layout
        range_layout.addWidget(range_label)
        range_layout.addWidget(self.range_from_combo)
        range_layout.addWidget(range_to_label)
        range_layout.addWidget(self.range_to_combo)
        range_layout.addWidget(self.apply_range_btn)
        range_layout.addStretch()

        # Add controls to main layout
        selection_controls.addWidget(self.select_all_btn)
        selection_controls.addWidget(self.select_none_btn)
        selection_controls.addStretch()
        selection_controls.addWidget(range_frame)

        episodes_layout.addLayout(selection_controls)

        # Episodes list with multi-selection
        self.episodes_list = QListWidget()
        self.episodes_list.setSelectionMode(QListWidget.SelectionMode.MultiSelection)  # Allow multi-selection without Ctrl
        self.episodes_list.itemSelectionChanged.connect(self.handle_episode_selection_changed)  # Handle selection changes
        self.episodes_list.itemSelectionChanged.connect(self.update_add_selected_button)  # Update button state
        self.episodes_list.setStyleSheet("""
            QListWidget {
                background-color: #44475a;
                color: #f8f8f2;
                border: 1px solid #6272a4;
                border-radius: 5px;
                padding: 5px;
                font-size: 11pt;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #6272a4;
            }
            QListWidget::item:selected {
                background-color: #8be9fd;
                color: #282a36;
            }
            QListWidget::item:hover {
                background-color: #6272a4;
            }
        """)
        episodes_layout.addWidget(self.episodes_list)

        # Add to main content layout
        main_content_layout.addWidget(seasons_group)
        main_content_layout.addWidget(episodes_group)

        # Add main content to seasons layout
        self.seasons_layout.addLayout(main_content_layout)

        # Smart Episode Action button
        episode_actions_layout = QHBoxLayout()
        episode_actions_layout.addStretch()

        # Smart button that adapts based on selection
        self.smart_action_btn = QPushButton("📥 Select Episodes First")
        self.smart_action_btn.clicked.connect(self.handle_smart_episode_action)
        self.smart_action_btn.setStyleSheet("""
            QPushButton {
                background-color: #bd93f9;
                color: #f8f8f2;
                border: none;
                border-radius: 8px;
                padding: 12px 25px;
                font-weight: bold;
                font-size: 12pt;
                margin: 10px;
            }
            QPushButton:hover {
                background-color: #caa6fc;
            }
            QPushButton:pressed {
                background-color: #a980f6;
            }
            QPushButton:disabled {
                background-color: #6272a4;
                color: #44475a;
            }
        """)
        self.smart_action_btn.setEnabled(False)  # Disabled until episodes are selected

        episode_actions_layout.addWidget(self.smart_action_btn)
        episode_actions_layout.addStretch()

        self.seasons_layout.addLayout(episode_actions_layout)

        self.content_tabs.addTab(self.seasons_tab, "Seasons & Episodes")

    def setup_available_streams_tab(self):
        """Setup Available Streams tab"""
        self.available_streams_tab = QWidget()
        self.available_streams_layout = QVBoxLayout(self.available_streams_tab)
        self.available_streams_layout.setContentsMargins(10, 10, 10, 10)

        # Streams container with scroll area
        from PySide6.QtWidgets import QScrollArea
        streams_scroll = QScrollArea()
        streams_scroll.setWidgetResizable(True)
        streams_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        streams_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        self.streams_container = QWidget()
        self.streams_container_layout = QVBoxLayout(self.streams_container)
        self.streams_container_layout.setSpacing(10)

        streams_scroll.setWidget(self.streams_container)
        self.available_streams_layout.addWidget(streams_scroll)

        self.content_tabs.addTab(self.available_streams_tab, "Available Streams")





    # Removed duplicate functions - using setup_seasons_tab() and setup_available_streams_tab() instead



    def setup_notification_area(self):
        """Setup modern notification area"""
        self.notification_area = QWidget()
        self.notification_area.setFixedHeight(0)  # Start hidden
        self.notification_area.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                border-radius: 8px;
                margin: 5px;
            }
        """)

        notification_layout = QHBoxLayout(self.notification_area)
        notification_layout.setContentsMargins(15, 10, 15, 10)

        # Notification icon
        self.notification_icon = QLabel("✅")
        self.notification_icon.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: bold;
            }
        """)
        notification_layout.addWidget(self.notification_icon)

        # Notification text
        self.notification_text = QLabel("Download completed successfully!")
        self.notification_text.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: 500;
                font-family: 'Segoe UI', Arial, sans-serif;
            }
        """)
        notification_layout.addWidget(self.notification_text)

        notification_layout.addStretch()

        # Close button
        close_btn = QPushButton("✕")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: #ffffff;
                border: none;
                border-radius: 12px;
                font-size: 12px;
                font-weight: bold;
                min-width: 24px;
                max-width: 24px;
                min-height: 24px;
                max-height: 24px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
        """)
        close_btn.clicked.connect(self.hide_notification)
        notification_layout.addWidget(close_btn)

        # Add to main layout at the top (notification area will be added to main content area)
        # self.downloads_tab.layout().insertWidget(0, self.notification_area)

    def show_notification(self, message, icon="✅", color="#4CAF50"):
        """Show modern notification"""
        try:
            self.notification_icon.setText(icon)
            self.notification_text.setText(message)

            # Update color based on type
            if "error" in message.lower() or "failed" in message.lower():
                color = "#f44336"
                icon = "❌"
            elif "warning" in message.lower():
                color = "#FF9800"
                icon = "⚠️"
            elif "completed" in message.lower() or "success" in message.lower():
                color = "#4CAF50"
                icon = "✅"
            else:
                color = "#2196F3"
                icon = "ℹ️"

            self.notification_icon.setText(icon)

            self.notification_area.setStyleSheet(f"""
                QWidget {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color}, stop:1 {color}dd);
                    border-radius: 8px;
                    margin: 5px;
                }}
            """)

            # Animate show
            self.notification_area.setFixedHeight(50)

            # Auto hide after 5 seconds
            from PySide6.QtCore import QTimer
            QTimer.singleShot(5000, self.hide_notification)

        except Exception as e:
            print(f"❌ Error showing notification: {str(e)}")

    def hide_notification(self):
        """Hide notification with animation"""
        try:
            self.notification_area.setFixedHeight(0)
        except Exception as e:
            print(f"❌ Error hiding notification: {str(e)}")



    def on_quality_changed(self, quality):
        """Handle quality selection change"""
        self.main_window.yango_api.set_quality(quality)
        print(f"✅ Quality changed to: {quality}")

        # Save the new quality to settings
        try:
            from .yango_settings import YangoSettings
            settings = YangoSettings()
            settings.current_settings["quality"]["default_quality"] = quality
            settings.save_settings()
            print(f"✅ Saved new default quality to settings: {quality}")
        except Exception as e:
            print(f"⚠️ Could not save quality to settings: {str(e)}")



    def extract_content_id(self, url_or_id):
        """Extract content ID from URL or return as-is if it's already an ID"""
        try:
            if url_or_id.startswith('http'):
                # Extract ID from URL
                # Example: https://movies.yango.com/movie/12345 -> 12345
                parts = url_or_id.rstrip('/').split('/')
                return parts[-1]
            else:
                # Assume it's already an ID
                return url_or_id
        except Exception as e:
            print(f"Error extracting content ID: {e}")
            return url_or_id

    def search_movie(self, content_id):
        """Search for movie"""
        from .yango_api import YangoAPIWorker
        self.movie_worker = YangoAPIWorker(self.main_window.yango_api, "search_movie", content_id)
        self.movie_worker.start()

    def search_series(self, content_id):
        """Search for series"""
        from .yango_api import YangoAPIWorker
        self.series_worker = YangoAPIWorker(self.main_window.yango_api, "search_series", content_id)
        self.series_worker.start()



    def handle_clear(self):
        """Handle clear button click"""
        try:
            # Clear URL input
            if hasattr(self, 'url_input'):
                self.url_input.clear()

            # Clear content info using the correct UI elements
            if hasattr(self, 'title_label'):
                self.title_label.setText("Content Title")
            if hasattr(self, 'type_label'):
                self.type_label.setText("TYPE • YEAR")
            if hasattr(self, 'description_text'):
                self.description_text.setPlainText("Description will appear here...")
            if hasattr(self, 'poster_label'):
                self.poster_label.clear()
                self.poster_label.setText("Poster")

            # Clear seasons and episodes if they exist
            if hasattr(self, 'seasons_list'):
                self.seasons_list.clear()
            if hasattr(self, 'episodes_list'):
                self.episodes_list.clear()

            # Hide content tabs
            if hasattr(self, 'content_tabs'):
                self.content_tabs.setVisible(False)

            # Reset current content
            self.current_content = None
            self.current_seasons = []
            self.current_series_info = {}

            print("✅ Interface cleared")

        except Exception as e:
            print(f"❌ Error clearing interface: {str(e)}")
            self.show_message("Error", f"Clear failed: {str(e)}")

    def show_message(self, title, message):
        """Show message box"""
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.exec_()

    def load_recent_urls_from_file(self):
        """Load recent URLs from file"""
        try:
            config_file = os.path.join(os.path.dirname(__file__), "..", "config", "recent_urls.json")

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    recent_items = json.load(f)

                if recent_items:
                    self.recent_combo.clear()
                    for item in recent_items:
                        if item.strip():
                            self.recent_combo.addItem(item.strip())

                    print(f"📂 Loaded {len(recent_items)} recent URLs")

        except Exception as e:
            print(f"❌ Error loading recent URLs: {str(e)}")

    def handle_recent_selection_by_index(self, index):
        """Handle recent selection by index - automatically search"""
        try:
            if index >= 0:
                selected_item = self.recent_combo.itemText(index)
                if selected_item and selected_item.strip():
                    # Extract content ID from the selected item
                    content_id = selected_item.strip()

                    # If it contains a title, extract just the ID part
                    if ' (' in content_id and content_id.endswith(')'):
                        # Format: "Title (content_id)"
                        content_id = content_id.split(' (')[-1].rstrip(')')

                    # Set the URL input
                    self.url_input.setText(content_id)

                    # Automatically start search
                    print(f"🔍 Auto-searching from recent selection: {content_id}")
                    self.search_content(content_id)

        except Exception as e:
            print(f"❌ Error in recent selection: {str(e)}")

    # Signal handlers
    def handle_movie_found(self, movie_data):
        """Handle movie found signal"""
        try:
            print("🎬 Movie found signal received - displaying content...")
            print(f"📊 Movie data keys: {list(movie_data.keys()) if movie_data else 'None'}")

            self.current_content = movie_data
            self.display_content_info(movie_data, content_type="movie")
            self.reset_search_button()

            # Show content tabs
            if hasattr(self, 'content_tabs'):
                print(f"🔍 Before setVisible - content_tabs visible: {self.content_tabs.isVisible()}")
                self.content_tabs.setVisible(True)
                print(f"🔍 After setVisible - content_tabs visible: {self.content_tabs.isVisible()}")
                self.content_tabs.setCurrentIndex(0)  # Show Content Info tab
                print(f"🔍 Current tab index: {self.content_tabs.currentIndex()}")
                print(f"🔍 Tab count: {self.content_tabs.count()}")
                print("✅ Content tabs made visible for movie")
            else:
                print("❌ self.content_tabs not found!")

            # Enable continue button for movies (to go directly to streams)
            if hasattr(self, 'continue_button'):
                self.continue_button.setEnabled(True)
                print("✅ Continue button enabled for movie")
            elif hasattr(self.widgets, 'continue_button'):
                self.widgets.continue_button.setEnabled(True)
                print("✅ Continue button enabled for movie (via widgets)")

        except Exception as e:
            print(f"❌ Error handling movie found: {str(e)}")
            import traceback
            traceback.print_exc()
            self.reset_search_button()

    def handle_series_found(self, series_data):
        """Handle series found signal"""
        try:
            print("📺 Series found signal received - displaying content...")
            print(f"📊 Series data keys: {list(series_data.keys()) if series_data else 'None'}")

            self.current_content = series_data
            self.display_content_info(series_data, content_type="series")
            self.reset_search_button()

            # Show content tabs
            if hasattr(self, 'content_tabs'):
                self.content_tabs.setVisible(True)
                self.content_tabs.setCurrentIndex(0)  # Show Content Info tab
                print("✅ Content tabs made visible for series")

            # Enable continue button for series
            if hasattr(self, 'continue_button'):
                self.continue_button.setEnabled(True)
                print("✅ Continue button enabled for series")
            elif hasattr(self.widgets, 'continue_button'):
                self.widgets.continue_button.setEnabled(True)
                print("✅ Continue button enabled for series (via widgets)")

        except Exception as e:
            print(f"❌ Error handling series found: {str(e)}")
            import traceback
            traceback.print_exc()
            self.reset_search_button()

    def handle_episodes_found(self, episodes):
        """Handle episodes found signal"""
        try:
            print(f"📋 Episodes found: {len(episodes)} - displaying episodes...")
            # TODO: Implement episodes display logic
        except Exception as e:
            print(f"❌ Error handling episodes found: {str(e)}")

    def handle_streams_found(self, streams_data):
        """Handle streams found signal - for both display and download processing"""
        try:
            print("🎬 Streams found - processing...")

            # Check if this is for download processing
            if hasattr(self, 'pending_download_item'):
                print("🔄 Processing streams for download")
                self.handle_episode_streams_for_download(streams_data)
                return

            # Otherwise, this is for episode display
            if hasattr(self, 'current_episode_data') and self.current_episode_data:
                episode_number = self.current_episode_data.get('episode_number', 'Unknown')
                episode_title = self.current_episode_data.get('episode_title', f'Episode {episode_number}')

                print(f"🎬 Displaying streams for Episode {episode_number}")
                # Display streams in the UI
                self.display_episode_streams(streams_data, episode_number, episode_title)
            else:
                print("⚠️ No current episode data found")

        except Exception as e:
            print(f"❌ Error handling streams found: {str(e)}")

    def handle_api_error(self, error_message):
        """Handle API error signal"""
        print(f"❌ API Error: {error_message}")
        self.reset_search_button()

        # Show user-friendly error message based on error type
        if "400 Client Error" in error_message or "Bad Request" in error_message:
            self.show_message("Invalid Content ID",
                "The content ID you entered is not valid or the content is not available.\n\n" +
                "Please check:\n" +
                "• Make sure you're using a direct movie/series URL\n" +
                "• Avoid URLs with '?rt=' parameters\n" +
                "• Try a different content\n\n" +
                "Example: https://play.yango.com/en-eg/movie/content-id")
        elif "Network error" in error_message:
            self.show_message("Network Error",
                "Could not connect to YANGO servers.\n\n" +
                "Please check:\n" +
                "• Your internet connection\n" +
                "• YANGO service availability\n" +
                "• Try again in a few moments")
        else:
            self.show_message("Search Failed", f"Could not find content.\n\nError: {error_message}")

        # Update status
        self.status_updated.emit("Search failed")

    def handle_keys_extracted(self, keys):
        """Handle DRM keys extracted signal"""
        print(f"🔑 DRM keys extracted: {len(keys)} keys")

    def handle_drm_error(self, error_message):
        """Handle DRM error signal"""
        print(f"❌ DRM Error: {error_message}")

    def handle_season_selection(self, item):
        """Handle season selection - Load episodes for selected season"""
        try:
            if not item:
                return

            # Get season data from item
            season_data = item.data(32)  # Qt.UserRole = 32
            if not season_data:
                print("❌ No season data found in item")
                return

            season_number = season_data.get('season_number', 'Unknown')
            content_id = season_data.get('content_id', '')

            print(f"📺 Season {season_number} selected - Loading episodes...")

            if not content_id:
                self.show_message("Error", "No content ID found for this season.")
                return

            # Store current season info
            self.current_season_number = season_number

            # Load episodes for this season
            self.load_episodes_for_season(content_id, season_number)

        except Exception as e:
            error_msg = f"Error handling season selection: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def handle_episode_selection_changed(self):
        """Handle episode selection changes - Update UI based on current selection"""
        try:
            selected_items = self.episodes_list.selectedItems()
            selected_count = len(selected_items)

            print(f"📋 Selection changed: {selected_count} episodes selected")

            if selected_count == 0:
                # No episodes selected
                self.current_episode_data = None
                print("📋 No episodes selected")

            elif selected_count == 1:
                # Single episode selected
                item = selected_items[0]
                episode_data = item.data(32)  # Qt.UserRole = 32

                if episode_data:
                    episode_number = episode_data.get('episode_number', 'Unknown')
                    content_id = episode_data.get('content_id', '')
                    availability_status = episode_data.get('availability_status', 'UNKNOWN')
                    episode_title = episode_data.get('title', f"Episode {episode_number}")

                    print(f"📋 Episode {episode_number} selected: {episode_title}")

                    # Store current episode info for later use
                    self.current_episode_data = {
                        'episode_number': episode_number,
                        'content_id': content_id,
                        'availability_status': availability_status,
                        'episode_title': episode_title
                    }

                    # Check if episode is available
                    if availability_status != 'PUBLISHED':
                        print(f"⏳ Episode {episode_number} is not yet available")
                    else:
                        print(f"✅ Episode {episode_number} is available for streaming")

            else:
                # Multiple episodes selected
                print(f"📋 {selected_count} episodes selected")
                self.current_episode_data = None

        except Exception as e:
            error_msg = f"Error handling episode selection change: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def select_all_episodes(self):
        """Select all episodes in the list"""
        try:
            for i in range(self.episodes_list.count()):
                item = self.episodes_list.item(i)
                if item:
                    item.setSelected(True)
            self.update_add_selected_button()  # Update button state
            print("✅ Selected all episodes")
        except Exception as e:
            print(f"❌ Error selecting all episodes: {str(e)}")

    def select_none_episodes(self):
        """Deselect all episodes in the list"""
        try:
            self.episodes_list.clearSelection()
            self.update_add_selected_button()  # Update button state
            print("✅ Deselected all episodes")
        except Exception as e:
            print(f"❌ Error deselecting episodes: {str(e)}")

    def apply_range_selection(self):
        """Apply range selection based on combo box values"""
        try:
            from_episode = self.range_from_combo.currentText()
            to_episode = self.range_to_combo.currentText()

            if not from_episode or not to_episode:
                self.show_message("Warning", "Please select both from and to episodes.")
                return

            # Extract episode numbers
            from_num = int(from_episode.split()[1]) if "Episode" in from_episode else int(from_episode)
            to_num = int(to_episode.split()[1]) if "Episode" in to_episode else int(to_episode)

            if from_num > to_num:
                self.show_message("Warning", "From episode cannot be greater than to episode.")
                return

            # Clear current selection
            self.episodes_list.clearSelection()

            # Select episodes in range
            selected_count = 0
            for i in range(self.episodes_list.count()):
                item = self.episodes_list.item(i)
                if item:
                    episode_data = item.data(32)
                    if episode_data:
                        episode_num = episode_data.get('episode_number', 0)
                        if from_num <= episode_num <= to_num:
                            item.setSelected(True)
                            selected_count += 1

            print(f"✅ Selected episodes {from_num} to {to_num} ({selected_count} episodes)")
            self.update_add_selected_button()  # Update button state

        except Exception as e:
            print(f"❌ Error applying range selection: {str(e)}")
            self.show_message("Error", f"Error applying range selection: {str(e)}")

    def update_range_combos(self):
        """Update the range combo boxes with available episodes"""
        try:
            self.range_from_combo.clear()
            self.range_to_combo.clear()

            # Get all episodes and add to combo boxes
            for i in range(self.episodes_list.count()):
                item = self.episodes_list.item(i)
                if item:
                    episode_data = item.data(32)
                    if episode_data:
                        episode_num = episode_data.get('episode_number', 0)
                        episode_text = f"Episode {episode_num}"
                        self.range_from_combo.addItem(episode_text)
                        self.range_to_combo.addItem(episode_text)

            # Set default selection (first to last)
            if self.range_from_combo.count() > 0:
                self.range_from_combo.setCurrentIndex(0)
                self.range_to_combo.setCurrentIndex(self.range_to_combo.count() - 1)

        except Exception as e:
            print(f"❌ Error updating range combos: {str(e)}")

    def add_selected_episodes_to_downloads(self):
        """Add selected episodes to downloads - but first need to select quality/audio/subtitle"""
        try:
            selected_items = self.episodes_list.selectedItems()
            if not selected_items:
                self.show_message("Warning", "Please select at least one episode.")
                return

            # Check if any episodes are available
            available_episodes = []
            for item in selected_items:
                episode_data = item.data(32)  # Qt.UserRole = 32
                if episode_data and episode_data.get('availability_status') == 'PUBLISHED':
                    available_episodes.append(episode_data)

            if not available_episodes:
                self.show_message("Info", "No available episodes selected. Please select episodes that are published.")
                return

            print(f"📥 Preparing to add {len(available_episodes)} episodes to downloads...")
            print("🎬 First, you need to select quality, audio, and subtitle options.")

            # Store selected episodes for later use
            self.selected_episodes_for_download = available_episodes

            # Show message to user
            episode_numbers = [str(ep.get('episode_number', '?')) for ep in available_episodes]
            episodes_text = ", ".join(episode_numbers)

            self.show_message("Quality Selection Required",
                f"Selected {len(available_episodes)} episodes: {episodes_text}\n\n"
                "To add these episodes to downloads:\n\n"
                "1. Select a single episode and click 'View Episode Streams'\n"
                "2. Choose your preferred quality, audio, and subtitle options\n"
                "3. Click 'Add to Downloads' in the Available Streams tab\n"
                "4. The selected options will be applied to all episodes\n\n"
                "This ensures you get the exact quality and settings you want!")

        except Exception as e:
            error_msg = f"Error preparing episodes for download: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def handle_smart_episode_action(self):
        """Smart action that adapts based on selection count"""
        try:
            selected_items = self.episodes_list.selectedItems()
            selected_count = len(selected_items)

            if selected_count == 0:
                self.show_message("Info", "Please select at least one episode.")
                return
            elif selected_count == 1:
                # Single episode - go to streams for quality selection
                print(f"🎬 Single episode selected - opening streams for quality selection")
                self.view_selected_episode_streams()
            else:
                # Multiple episodes - store them and go to streams for quality selection
                print(f"🎬 {selected_count} episodes selected - opening streams for quality selection")

                # Store selected episodes for later use in Available Streams
                self.selected_episodes_for_download = []
                for item in selected_items:
                    episode_data = item.data(32)
                    if episode_data and episode_data.get('availability_status') == 'PUBLISHED':
                        self.selected_episodes_for_download.append(episode_data)

                if not self.selected_episodes_for_download:
                    self.show_message("Info", "No available episodes selected. Please select episodes that are published.")
                    return

                # Use the first available episode to load streams
                first_episode = self.selected_episodes_for_download[0]
                self.current_episode_data = {
                    'episode_number': first_episode.get('episode_number', 'Unknown'),
                    'content_id': first_episode.get('content_id', ''),
                    'availability_status': first_episode.get('availability_status', 'UNKNOWN'),
                    'episode_title': first_episode.get('title', f"Episode {first_episode.get('episode_number', 'Unknown')}")
                }

                # Go to Available Streams tab for quality selection
                self.view_selected_episode_streams()

        except Exception as e:
            error_msg = f"Error in smart episode action: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def add_multiple_episodes_to_downloads(self):
        """Add multiple selected episodes to downloads with default settings"""
        try:
            selected_items = self.episodes_list.selectedItems()

            # Get series title from current content
            series_title = getattr(self, 'current_series_title', 'Unknown Series')

            # Default quality and settings
            default_quality = "720p"
            default_audio = "Arabic - Stereo"

            added_count = 0
            for item in selected_items:
                episode_data = item.data(32)
                if episode_data:
                    episode_number = episode_data.get('episode_number', 'Unknown')
                    availability_status = episode_data.get('availability_status', 'UNKNOWN')
                    episode_title = episode_data.get('title', f"Episode {episode_number}")

                    # Only add available episodes
                    if availability_status == 'PUBLISHED':
                        # Get season number from current season
                        season_number = getattr(self, 'current_season_number', 1)

                        # Create download item
                        download_item = {
                            'title': series_title,
                            'season': f'S{season_number:02d}',
                            'episode': f'E{episode_number:02d}',
                            'quality': default_quality,
                            'audio_track': default_audio,
                            'subtitle': None,
                            'status': 'Queued',
                            'progress': 0,
                            'mpd_url': '',  # Will be filled when streams are loaded
                            'decryption_keys': [],
                            'episode_data': episode_data
                        }

                        # Add to downloads table
                        self.add_download_to_table(download_item)
                        added_count += 1

                        print(f"✅ Added Episode {episode_number}: {episode_title}")
                    else:
                        print(f"⏳ Skipped Episode {episode_number}: Not available yet")

            if added_count > 0:
                # Update stats
                self.update_downloads_stats()

                # Switch to downloads tab
                if hasattr(self, 'content_tabs'):
                    for i in range(self.content_tabs.count()):
                        if "Downloads" in self.content_tabs.tabText(i):
                            self.content_tabs.setCurrentIndex(i)
                            break

                print(f"✅ Added {added_count} episodes to downloads queue")
                self.show_message("Success", f"Added {added_count} episodes to downloads with default settings:\n\n• Quality: {default_quality}\n• Audio: {default_audio}\n\nYou can modify settings for individual episodes in the Downloads tab.")
            else:
                self.show_message("Info", "No available episodes were selected.")

        except Exception as e:
            error_msg = f"Error adding multiple episodes to downloads: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def update_add_selected_button(self):
        """Update the smart action button based on selection"""
        try:
            if hasattr(self, 'smart_action_btn'):
                selected_count = len(self.episodes_list.selectedItems())
                if selected_count == 0:
                    self.smart_action_btn.setEnabled(False)
                    self.smart_action_btn.setText("📥 Select Episodes First")
                elif selected_count == 1:
                    self.smart_action_btn.setEnabled(True)
                    self.smart_action_btn.setText("🎬 View Episode Streams & Select Quality")
                else:
                    self.smart_action_btn.setEnabled(True)
                    self.smart_action_btn.setText(f"🎬 View Streams & Select Quality for {selected_count} Episodes")
        except Exception as e:
            print(f"❌ Error updating smart action button: {str(e)}")

    def view_selected_episode_streams(self):
        """View streams for the currently selected episode"""
        try:
            if not hasattr(self, 'current_episode_data') or not self.current_episode_data:
                self.show_message("Info", "Please select an episode first.")
                return

            episode_data = self.current_episode_data
            episode_number = episode_data.get('episode_number', 'Unknown')
            content_id = episode_data.get('content_id', '')
            availability_status = episode_data.get('availability_status', 'UNKNOWN')
            episode_title = episode_data.get('episode_title', f"Episode {episode_number}")

            # Check if episode is available
            if availability_status != 'PUBLISHED':
                self.show_message("Info", f"Episode {episode_number} is not yet available.")
                return

            if not content_id:
                self.show_message("Error", "No content ID found for this episode.")
                return

            print(f"🎬 Loading streams for Episode {episode_number}: {episode_title}")

            # Switch to Available Streams tab
            self.content_tabs.setCurrentIndex(2)  # Assuming Available Streams is the 3rd tab (index 2)

            # Load episode streams
            self.load_episode_streams(content_id, episode_number, episode_title)

        except Exception as e:
            error_msg = f"Error viewing episode streams: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def update_status_bar(self, message):
        """Update status bar with message"""
        try:
            if hasattr(self.widgets, 'status_label'):
                self.widgets.status_label.setText(message)
            print(f"📝 Status: {message}")
        except Exception as e:
            print(f"❌ Error updating status bar: {str(e)}")

    def update_progress_bar(self, percentage, message):
        """Update progress bar"""
        try:
            if hasattr(self.widgets, 'progress_bar'):
                self.widgets.progress_bar.setValue(percentage)
            if hasattr(self.widgets, 'progress_label'):
                self.widgets.progress_label.setText(message)
            print(f"📊 Progress: {percentage}% - {message}")
        except Exception as e:
            print(f"❌ Error updating progress bar: {str(e)}")

    def show_message(self, title, message):
        """Show message box"""
        try:
            msg_box = QMessageBox()
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.exec_()
        except Exception as e:
            print(f"❌ Error showing message: {str(e)}")

    def load_recent_urls_from_file(self):
        """Load recent URLs from file with exe compatibility"""
        try:
            # Try multiple locations for config file (exe compatibility)
            config_paths = [
                os.path.join(os.getcwd(), "config", "recent_urls.json"),  # Current working directory
                os.path.join(os.path.dirname(__file__), "..", "config", "recent_urls.json"),  # Script directory
                os.path.join(os.path.expanduser("~"), "YANGO", "config", "recent_urls.json"),  # User home directory
                os.path.join(os.path.expanduser("~"), "Documents", "YANGO", "config", "recent_urls.json")  # Documents folder
            ]

            config_file = None
            for path in config_paths:
                if os.path.exists(path):
                    config_file = path
                    print(f"✅ Found config file: {config_file}")
                    break

            if not config_file:
                print("📂 No recent URLs file found in any location")
                return

            with open(config_file, 'r', encoding='utf-8') as f:
                recent_items = json.load(f)

            if recent_items and hasattr(self, 'recent_combo'):
                self.recent_combo.clear()
                for item in recent_items:
                    if item.strip():
                        self.recent_combo.addItem(item.strip())

                print(f"📂 Loaded {len(recent_items)} recent URLs from {config_file}")

        except Exception as e:
            print(f"❌ Error loading recent URLs: {str(e)}")

    def save_recent_urls_to_file(self):
        """Save recent URLs to file with exe compatibility"""
        try:
            if not hasattr(self, 'recent_combo'):
                return

            # Try multiple locations for config directory (exe compatibility)
            config_paths = [
                os.path.join(os.getcwd(), "config"),  # Current working directory
                os.path.join(os.path.dirname(__file__), "..", "config"),  # Script directory
                os.path.join(os.path.expanduser("~"), "YANGO", "config"),  # User home directory
                os.path.join(os.path.expanduser("~"), "Documents", "YANGO", "config")  # Documents folder
            ]

            config_dir = None
            config_file = None

            for path in config_paths:
                try:
                    os.makedirs(path, exist_ok=True)
                    # Test write permissions
                    test_file = os.path.join(path, "test_write.tmp")
                    with open(test_file, 'w') as f:
                        f.write("test")
                    os.remove(test_file)  # Delete test file
                    config_dir = path
                    config_file = os.path.join(config_dir, "recent_urls.json")
                    print(f"✅ Using config directory: {config_dir}")
                    break
                except Exception as e:
                    print(f"⚠️ Cannot use {path}: {e}")
                    continue

            if not config_dir:
                print("❌ Cannot create config directory in any location")
                return

            recent_items = []
            for i in range(self.recent_combo.count()):
                item = self.recent_combo.itemText(i)
                if item.strip():
                    recent_items.append(item.strip())

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(recent_items, f, ensure_ascii=False, indent=2)

            print(f"💾 Saved {len(recent_items)} recent URLs to {config_file}")

        except Exception as e:
            print(f"❌ Error saving recent URLs: {str(e)}")



    def handle_search(self):
        """Handle search button click"""
        try:
            if not hasattr(self, 'url_input'):
                self.show_message("Error", "URL input not found")
                return

            url_or_id = self.url_input.text().strip()
            if not url_or_id:
                self.show_message("Warning", "Please enter a YANGO URL or content ID")
                return

            # Clear previous search results before new search
            self.clear_previous_search_results()

            # Disable search button during search
            if hasattr(self, 'search_button'):
                self.search_button.setText("Searching...")
                self.search_button.setEnabled(False)

            # Extract content ID from URL if needed
            content_id = self.extract_content_id(url_or_id)

            # Add to recent URLs (without title for now - will be updated after successful search)
            self.add_to_recent_urls(url_or_id)

            # Try to search as movie first, then series
            print(f"🔍 Starting search for: {content_id}")
            self.search_content(content_id)

        except Exception as e:
            self.show_message("Error", f"Search failed: {str(e)}")
            self.reset_search_button()

    def add_to_recent_urls(self, url, title=None):
        """Add URL to recent URLs list with title"""
        try:
            if hasattr(self, 'recent_combo'):
                # Extract content ID
                content_id = self.extract_content_id(url)

                # Create display text: "Title (ID)" or just "ID" if no title
                if title:
                    display_text = f"{title} ({content_id})"
                else:
                    display_text = content_id

                # Check if entry already exists (by content ID)
                for i in range(self.recent_combo.count()):
                    item_text = self.recent_combo.itemText(i)
                    # Extract ID from existing item
                    if "(" in item_text and ")" in item_text:
                        existing_id = item_text.split("(")[-1].replace(")", "")
                    else:
                        existing_id = item_text

                    if existing_id == content_id:
                        # Remove existing entry
                        self.recent_combo.removeItem(i)
                        break

                # Add to top
                self.recent_combo.insertItem(0, display_text)

                # Limit to 10 recent items
                while self.recent_combo.count() > 10:
                    self.recent_combo.removeItem(self.recent_combo.count() - 1)

                # Save to file
                self.save_recent_urls_to_file()

        except Exception as e:
            print(f"❌ Error adding to recent URLs: {str(e)}")

    def update_recent_urls_with_title(self, content_id, title):
        """Update recent URLs entry with title after successful search"""
        try:
            if hasattr(self, 'recent_combo'):
                # Find the entry with this content ID and update it with title
                for i in range(self.recent_combo.count()):
                    item_text = self.recent_combo.itemText(i)

                    # Check if this is the content ID we're looking for
                    if "(" in item_text and ")" in item_text:
                        existing_id = item_text.split("(")[-1].replace(")", "").strip()
                    else:
                        existing_id = item_text.strip()

                    if existing_id == content_id:
                        # Update with title
                        new_display_text = f"{title} ({content_id})"
                        self.recent_combo.setItemText(i, new_display_text)
                        print(f"✅ Updated recent URL with title: {new_display_text}")

                        # Save to file
                        self.save_recent_urls_to_file()
                        break

        except Exception as e:
            print(f"❌ Error updating recent URLs with title: {str(e)}")

    def display_content_info(self, content_data, content_type="movie"):
        """Display content information in the UI"""
        try:
            print(f"🖥️ Displaying content info...")
            print(f"📊 Content data keys: {list(content_data.keys())}")
            print(f"📊 Content type: {content_type}")

            # Store current content info for use in downloads
            self.current_content_info = content_data

            # Extract title
            title_data = content_data.get('title', {})
            if isinstance(title_data, dict):
                title = title_data.get('localized', title_data.get('original', 'Unknown Title'))
            else:
                title = str(title_data) if title_data else 'Unknown Title'

            # Store title based on content type to prevent mixing
            if content_type == "series" or content_data.get('__typename') == 'TvSeries':
                # Only store series title for series content
                self.current_series_title = title
                print(f"📺 Series title stored: {title}")
                # Clear movie data if exists
                if hasattr(self, 'current_movie_data'):
                    self.current_movie_data = None
                    print("🧹 Cleared movie data for series")
            else:
                # For movies, clear series title to prevent mixing
                if hasattr(self, 'current_series_title'):
                    self.current_series_title = None
                    print("🧹 Cleared series title for movie")
                # Clear episode data if exists
                if hasattr(self, 'current_episode_data'):
                    self.current_episode_data = None
                    print("🧹 Cleared episode data for movie")

            print(f"📝 Title extracted: {title}")

            # Extract description - try multiple sources in order of preference
            print(f"🔍 Checking description sources:")
            print(f"   - shortDescription: {content_data.get('shortDescription', 'NOT FOUND')}")
            print(f"   - ottSynopsis: {content_data.get('ottSynopsis', 'NOT FOUND')}")
            print(f"   - editorAnnotation: {content_data.get('editorAnnotation', 'NOT FOUND')}")

            description = (
                content_data.get('shortDescription') or
                content_data.get('ottSynopsis') or
                content_data.get('editorAnnotation') or
                'No description available'
            )
            print(f"📝 Final description: {description[:100]}..." if len(description) > 100 else f"📝 Final description: {description}")
            # Try different year fields for movies vs series
            year = 'N/A'
            if content_data.get('productionYear'):
                year = content_data.get('productionYear')
            elif content_data.get('releaseYears') and len(content_data.get('releaseYears', [])) > 0:
                year = content_data.get('releaseYears')[0].get('start', 'N/A')
            # Extract genres
            genres_list = content_data.get('genres', [])
            genres = ', '.join([genre.get('name', '') for genre in genres_list]) if genres_list else ''

            # Extract cast information
            actors = content_data.get('actors', {}).get('items', [])
            cast_names = [actor.get('person', {}).get('name', 'Unknown') for actor in actors[:5]]
            # Filter out None values and empty strings
            cast_names = [name for name in cast_names if name and name.strip()]
            cast_text = ', '.join(cast_names) if cast_names else ''

            # Determine content type from API response
            api_type = content_data.get('__typename', content_type)
            seasons_count = 0
            episodes_count = 0

            print(f"🔍 Content type (__typename): {api_type}")
            print(f"🔍 Available content keys: {list(content_data.keys())}")

            if api_type == 'TvSeries':
                content_type = 'SERIES'
                # Try different season data sources
                seasons_count = (
                    content_data.get('seasonsAll', {}).get('total', 0) or
                    content_data.get('seasons', {}).get('total', 0) or
                    0
                )

                # Try to get episodes count
                seasons_data = content_data.get('seasons', {}) or content_data.get('seasonsAll', {})
                if seasons_data.get('items'):
                    for season in seasons_data['items']:
                        season_episodes = season.get('episodes', {}).get('total', 0)
                        episodes_count += season_episodes
                        print(f"📺 Season {season.get('number', '?')}: {season_episodes} episodes")

                if episodes_count > 0:
                    type_text = f"SERIES • {year} • {seasons_count} Season(s) • {episodes_count} Episodes"
                else:
                    type_text = f"SERIES • {year} • {seasons_count} Season(s)"
            else:
                content_type = 'MOVIE'
                type_text = f"MOVIE • {year}"

            print(f"📝 Extracted info - Title: {title}, Type: {type_text}")

            # Update Title
            if hasattr(self, 'title_label'):
                self.title_label.setText(title)
                print(f"✅ Updated title_label: {title}")

            # Update Description in the text area
            if hasattr(self, 'description_text'):
                self.description_text.setPlainText(description)
                print(f"✅ Updated description_text: {description[:50]}...")

            # Update Type field (using the correct label name)
            if hasattr(self, 'type_label'):
                self.type_label.setText(type_text)
                self.type_label.setVisible(True)
                print(f"✅ Updated type_label: {type_text}")

            # Year is already included in type_text, so we don't need separate year field
            # But if year_label exists, we can hide it since we're not using it
            if hasattr(self, 'year_label'):
                self.year_label.setVisible(False)
                print(f"✅ Hidden year_label (included in type_label)")

            # Update Episodes field for series only
            if hasattr(self, 'episodes_field_label') and hasattr(self, 'episodes_value_label'):
                if api_type == 'TvSeries':
                    episodes_text = str(episodes_count) if episodes_count > 0 else str(seasons_count)
                    self.episodes_value_label.setText(episodes_text)
                    self.episodes_field_label.setVisible(True)
                    self.episodes_value_label.setVisible(True)
                    print(f"✅ Updated episodes_value_label: {episodes_text}")
                else:
                    # For movies, hide episodes field
                    self.episodes_field_label.setVisible(False)
                    self.episodes_value_label.setVisible(False)
                    print(f"✅ Hidden episodes field for movie")

            # Update Genres field
            if hasattr(self, 'genres_field_label') and hasattr(self, 'genres_value_label'):
                if genres:
                    # Format genres like Shahid: Social | Drama | Egyptian
                    formatted_genres = ' | '.join([genre.strip() for genre in genres.split(',')])
                    self.genres_value_label.setText(formatted_genres)
                    self.genres_field_label.setVisible(True)
                    self.genres_value_label.setVisible(True)
                    print(f"✅ Updated genres_value_label: {formatted_genres}")
                else:
                    self.genres_field_label.setVisible(False)
                    self.genres_value_label.setVisible(False)
                    print(f"✅ Hidden genres field (no data)")
            else:
                print(f"❌ Genres labels not found")

            # Update Cast field
            if hasattr(self, 'cast_field_label') and hasattr(self, 'cast_value_label'):
                if cast_text:
                    self.cast_value_label.setText(cast_text)
                    self.cast_field_label.setVisible(True)
                    self.cast_value_label.setVisible(True)
                    print(f"✅ Updated cast_value_label: {cast_text}")
                else:
                    self.cast_field_label.setVisible(False)
                    self.cast_value_label.setVisible(False)
                    print(f"✅ Hidden cast field (no data)")
            else:
                print(f"❌ Cast labels not found")

            # Extract and update Quality features
            quality_features = []
            ott_data = content_data.get('ott', {})
            preview_data = ott_data.get('preview', {})
            features = preview_data.get('features', [])

            print(f"🔍 Quality features data: {features}")

            # Process features to extract quality information
            for feature in features:
                feature_alias = feature.get('alias', '')
                feature_name = feature.get('displayedName', '')
                feature_group = feature.get('group', '')

                # Map feature aliases to user-friendly names
                if 'video4k' in feature_alias.lower():
                    quality_features.append('4K')
                elif 'videohd' in feature_alias.lower() or 'hd' in feature_alias.lower():
                    quality_features.append('HD')
                elif 'hdr' in feature_alias.lower():
                    quality_features.append('HDR')
                elif 'dolby' in feature_alias.lower() and '51' in feature_alias:
                    quality_features.append('5.1')
                elif 'surround' in feature_alias.lower() and '51' in feature_alias:
                    quality_features.append('5.1')
                elif feature_group == 'AUDIO' and ('stereo' in feature_alias.lower() or 'audio' in feature_alias.lower()):
                    if '5.1' not in quality_features:  # Don't add stereo if we already have 5.1
                        quality_features.append('Stereo')
                elif feature_group == 'VIDEO' and not any(q in quality_features for q in ['4K', 'HD']):
                    # If no specific video quality found, check for general video quality
                    if 'video' in feature_alias.lower():
                        quality_features.append('HD')  # Default to HD for video content

            # Also check streaming details if available
            streaming_details = content_data.get('streaming_details', {})
            if streaming_details:
                streams = streaming_details.get('streams', [])
                for stream in streams:
                    stream_meta = stream.get('streamMeta', {})
                    video_metas = stream_meta.get('videoMetas', [])
                    for video_meta in video_metas:
                        height = video_meta.get('height', 0)
                        if height >= 2160 and '4K' not in quality_features:
                            quality_features.append('4K')
                        elif height >= 1080 and 'HD' not in quality_features and '4K' not in quality_features:
                            quality_features.append('HD')

                    # Check audio features
                    audio_metas = stream_meta.get('audioMetas', [])
                    for audio_meta in audio_metas:
                        quality = audio_meta.get('quality', '').upper()
                        if 'SURROUND_51' in quality or '5.1' in quality:
                            if '5.1' not in quality_features:
                                quality_features.append('5.1')
                        elif 'STEREO' in quality and not any(q in quality_features for q in ['5.1', 'Stereo']):
                            quality_features.append('Stereo')

            # Remove duplicates and sort
            quality_features = list(dict.fromkeys(quality_features))  # Remove duplicates while preserving order

            # Update Quality field
            if hasattr(self, 'quality_field_label') and hasattr(self, 'quality_value_label'):
                if quality_features:
                    # Format quality features like: 4K • HDR • 5.1
                    formatted_quality = ' • '.join(quality_features)
                    self.quality_value_label.setText(formatted_quality)
                    self.quality_field_label.setVisible(True)
                    self.quality_value_label.setVisible(True)
                    print(f"✅ Updated quality_value_label: {formatted_quality}")
                else:
                    self.quality_field_label.setVisible(False)
                    self.quality_value_label.setVisible(False)
                    print(f"✅ Hidden quality field (no data)")
            else:
                print(f"❌ Quality labels not found")

            # Update poster if available - try multiple poster sources
            poster_url = None
            gallery = content_data.get('gallery', {})

            print(f"🔍 Gallery data structure: {gallery}")

            # Try different poster sources in order of preference
            # Based on the JSON structure you provided
            if gallery.get('posters', {}).get('vertical', {}).get('avatarsUrl'):
                poster_url = gallery['posters']['vertical']['avatarsUrl']
                print(f"📷 Found vertical poster: {poster_url}")
            elif gallery.get('posters', {}).get('verticalWithRightholderLogo', {}).get('avatarsUrl'):
                poster_url = gallery['posters']['verticalWithRightholderLogo']['avatarsUrl']
                print(f"📷 Found vertical with logo poster: {poster_url}")
            elif gallery.get('covers', {}).get('horizontal', {}).get('avatarsUrl'):
                poster_url = gallery['covers']['horizontal']['avatarsUrl']
                print(f"📷 Found horizontal cover: {poster_url}")
            elif gallery.get('logos', {}).get('horizontal', {}).get('avatarsUrl'):
                poster_url = gallery['logos']['horizontal']['avatarsUrl']
                print(f"📷 Found horizontal logo: {poster_url}")

            # Debug: Print all available image URLs
            print(f"🔍 Available gallery keys: {list(gallery.keys()) if gallery else 'No gallery'}")
            if gallery:
                for key, value in gallery.items():
                    if isinstance(value, dict) and 'avatarsUrl' in str(value):
                        print(f"🔍 Found image in {key}: {value}")

            if poster_url and hasattr(self, 'poster_label'):
                # Add https: if missing
                if poster_url.startswith('//'):
                    poster_url = 'https:' + poster_url

                # For YANGO/OTT avatars, try different size parameters
                if 'ott-avatars.akamaized.net' in poster_url:
                    # Try with size parameters that work with OTT avatars
                    poster_url_with_size = f"{poster_url}/240x360"
                    print(f"📷 Using OTT avatar with size: {poster_url_with_size}")
                    poster_url = poster_url_with_size
                elif not poster_url.endswith(('.jpg', '.jpeg', '.png', '.webp')):
                    poster_url += '.jpg'  # Default to jpg for other sources

                self.poster_label.setText("Loading Poster...")
                print(f"📷 Final poster URL: {poster_url}")

                # Load poster image asynchronously
                self.load_poster_image(poster_url)
            elif hasattr(self, 'poster_label'):
                self.poster_label.setText("No Poster\nAvailable")
                print(f"📷 No poster URL found in gallery: {gallery}")

            # Show content tabs after updating content
            if hasattr(self, 'content_tabs'):
                self.content_tabs.setVisible(True)
                self.content_tabs.setCurrentIndex(0)  # Show Content Info tab
                print(f"✅ Content tabs made visible")
            else:
                print(f"❌ content_tabs not found")

            print(f"✅ Content info displayed: {title} ({content_type})")

            # Update recent URLs with title after successful content display
            if hasattr(self, 'current_content_info') and self.current_content_info:
                content_id = self.current_content_info.get('contentId')
                if content_id and title:
                    self.update_recent_urls_with_title(content_id, title)

        except Exception as e:
            print(f"❌ Error displaying content info: {str(e)}")
            import traceback
            traceback.print_exc()

    def load_poster_image(self, url):
        """Load poster image from URL"""
        try:
            print(f"📷 Loading poster from: {url}")

            # Try different URL variations if the first one fails
            urls_to_try = []

            if 'ott-avatars.akamaized.net' in url:
                # For OTT avatars, try different size formats
                base_url = url.replace('/240x360', '').replace('.jpg', '').replace('.png', '').replace('.webp', '')
                urls_to_try = [
                    f"{base_url}/240x360",      # Standard poster size
                    f"{base_url}/300x450",      # Larger poster size
                    f"{base_url}/200x300",      # Smaller poster size
                    f"{base_url}/480x720",      # HD poster size
                    f"{base_url}",              # Original without size
                    f"{base_url}.jpg",          # With jpg extension
                    f"{base_url}.png",          # With png extension
                    f"{base_url}.webp",         # With webp extension
                ]
            else:
                # For other image sources
                urls_to_try = [
                    url,
                    url + "?w=300&h=450&q=80",  # Add size and quality parameters
                    url.replace('.jpg', ''),     # Remove extension
                    url + "/300x450",            # Add size path
                    url + ".webp",               # Try webp format
                    url + ".png"                 # Try png format
                ]

            for attempt_url in urls_to_try:
                try:
                    print(f"📷 Trying URL: {attempt_url}")
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Referer': 'https://play.yango.com/'
                    }

                    response = requests.get(attempt_url, timeout=10, headers=headers)
                    response.raise_for_status()

                    print(f"📷 Response status: {response.status_code}, Content-Type: {response.headers.get('content-type', 'unknown')}")
                    print(f"📷 Content length: {len(response.content)} bytes")

                    if len(response.content) == 0:
                        print(f"❌ Empty response from: {attempt_url}")
                        continue

                    pixmap = QPixmap()
                    success = pixmap.loadFromData(response.content)

                    print(f"📷 Pixmap load success: {success}, isNull: {pixmap.isNull()}")

                    if success and not pixmap.isNull() and hasattr(self, 'poster_label'):
                        # Check if poster_label has valid size
                        label_size = self.poster_label.size()
                        print(f"📷 Label size: {label_size.width()}x{label_size.height()}")

                        if label_size.width() > 0 and label_size.height() > 0:
                            # Scale the image to fit the label while maintaining aspect ratio
                            scaled_pixmap = pixmap.scaled(
                                label_size,
                                Qt.KeepAspectRatio,
                                Qt.SmoothTransformation
                            )
                            self.poster_label.setPixmap(scaled_pixmap)
                            print(f"✅ Poster loaded successfully from: {attempt_url}")
                            return
                        else:
                            # Set a default size if label size is invalid
                            scaled_pixmap = pixmap.scaled(
                                240, 360,
                                Qt.KeepAspectRatio,
                                Qt.SmoothTransformation
                            )
                            self.poster_label.setPixmap(scaled_pixmap)
                            print(f"✅ Poster loaded with default size from: {attempt_url}")
                            return
                    else:
                        print(f"❌ Failed to create valid pixmap from: {attempt_url}")

                except requests.exceptions.RequestException as e:
                    print(f"❌ Failed to load from {attempt_url}: {str(e)}")
                    continue

            # If all attempts failed
            if hasattr(self, 'poster_label'):
                self.poster_label.setText("Failed to\nLoad Poster")
            print(f"❌ All poster URL attempts failed")

        except Exception as e:
            print(f"❌ Error loading poster: {str(e)}")
            if hasattr(self, 'poster_label'):
                self.poster_label.setText("Failed to\nLoad Poster")

    def extract_content_id(self, url_or_id):
        """Extract content ID from YANGO URL or return as-is if it's already an ID"""
        try:
            # Use the API's extraction method
            if hasattr(self.main_window, 'yango_api'):
                return self.main_window.yango_api.extract_content_id_from_url(url_or_id)

            # Fallback method if API not available
            if url_or_id.startswith('http'):
                # Handle YANGO URLs
                # Example: https://play.yango.com/en-eg/movie/content-id
                # Example: https://play.yango.com/en-eg/series/content-id
                if '/movie/' in url_or_id:
                    # Extract movie ID
                    parts = url_or_id.split('/movie/')
                    if len(parts) > 1:
                        content_id = parts[1].split('?')[0].split('/')[0]
                        print(f"🎬 Extracted movie ID: {content_id}")
                        return content_id
                elif '/series/' in url_or_id:
                    # Extract series ID
                    parts = url_or_id.split('/series/')
                    if len(parts) > 1:
                        content_id = parts[1].split('?')[0].split('/')[0]
                        print(f"📺 Extracted series ID: {content_id}")
                        return content_id
                else:
                    # Try to extract from the end of URL
                    parts = url_or_id.rstrip('/').split('/')
                    content_id = parts[-1]
                    print(f"🔍 Extracted ID from URL end: {content_id}")
                    return content_id
            else:
                # Assume it's already an ID
                print(f"🆔 Using as direct ID: {url_or_id}")
                return url_or_id
        except Exception as e:
            print(f"❌ Error extracting content ID: {e}")
            return url_or_id

    def search_content(self, content_id):
        """Search for content (movie or series)"""
        try:
            if not content_id:
                self.show_message("Invalid URL",
                    "Please enter a valid YANGO URL or content ID.\n\n" +
                    "Examples:\n" +
                    "• https://play.yango.com/en-eg/movie/content-id\n" +
                    "• https://play.yango.com/en-eg/series/content-id\n" +
                    "• content-id-12345")
                self.reset_search_button()
                return

            # Try movie first
            if hasattr(self.main_window, 'yango_api'):
                print(f"🔍 Starting search for content ID: {content_id}")
                print(f"🔍 API object exists: {self.main_window.yango_api}")
                print(f"🔍 Calling search_movie method...")

                # Call search_movie which will emit signals
                self.main_window.yango_api.search_movie(content_id)
                print(f"🔍 search_movie method called successfully")
            else:
                print(f"❌ YANGO API not available in main_window")
                self.show_message("Error", "YANGO API not available")
                self.reset_search_button()
        except Exception as e:
            print(f"❌ Error searching content: {str(e)}")
            import traceback
            traceback.print_exc()
            self.show_message("Error", f"Search failed: {str(e)}")
            self.reset_search_button()

    def reset_search_button(self):
        """Reset search button state"""
        try:
            if hasattr(self, 'search_button'):
                self.search_button.setText("🔍 Search")
                self.search_button.setEnabled(True)
        except Exception as e:
            print(f"❌ Error resetting search button: {str(e)}")

    def clear_previous_search_results(self):
        """Clear previous search results before new search"""
        try:
            print("🧹 Clearing previous search results...")

            # Hide content tabs instead of removing them
            if hasattr(self, 'content_tabs'):
                self.content_tabs.setVisible(False)
                print("🔍 Content tabs hidden")

            # Clear downloads table to prevent mixing content from different searches
            if hasattr(self, 'downloads_table'):
                self.downloads_table.setRowCount(0)
                print("🧹 Cleared downloads table")

                # Update downloads stats
                self.update_downloads_stats()

                # Clear download items list
                if hasattr(self, 'download_items'):
                    self.download_items.clear()
                    print("🧹 Cleared download items list")

            # Clear downloader queue to prevent mixing downloads
            if hasattr(self, 'downloader') and self.downloader:
                self.downloader.download_queue.clear()
                self.downloader.current_download = None
                print("🧹 Cleared downloader queue")

            # Clear any stored data
            if hasattr(self, 'current_movie_data'):
                self.current_movie_data = None
            if hasattr(self, 'current_series_data'):
                self.current_series_data = None
            if hasattr(self, 'current_episode_data'):
                self.current_episode_data = None
            if hasattr(self, 'current_series_title'):
                self.current_series_title = None
                print("🧹 Cleared current_series_title")
            if hasattr(self, 'current_season_number'):
                self.current_season_number = None
                print("🧹 Cleared current_season_number")
            if hasattr(self, 'current_content_info'):
                self.current_content_info = None
                print("🧹 Cleared current_content_info")
            if hasattr(self, 'current_mpd_url'):
                self.current_mpd_url = None
            if hasattr(self, 'current_decryption_keys'):
                self.current_decryption_keys = []

            print("✅ Previous search results and downloads cleared")

        except Exception as e:
            print(f"❌ Error clearing previous search results: {str(e)}")



    def handle_play(self):
        """Handle play button click"""
        print("▶️ Play button clicked - implementing logic...")

    def handle_continue(self):
        """Handle continue button click - Load seasons/episodes for series or streams for movies"""
        try:
            print("⏭️ Continue button clicked...")

            if not self.current_content:
                self.show_message("Error", "No content loaded. Please search for content first.")
                return

            # Check content type
            content_type = self.current_content.get('__typename', '')

            if content_type == 'TvSeries':
                print("📺 Loading seasons and episodes for TV Series...")
                # Get seasons data from current content
                seasons_data = self.current_content.get('seasons', {})
                if not seasons_data or not seasons_data.get('items'):
                    self.show_message("Error", "No seasons data available for this series.")
                    return
            else:
                print("🎬 Loading streams for Movie...")
                # Get content ID
                content_id = self.current_content.get('content_id')
                if not content_id:
                    self.show_message("Error", "No content ID found for this movie.")
                    return

                # Use MovieHandler to handle movie continue
                self.movie_handler.handle_continue_for_movie(content_id)
                return

            # Load seasons into the seasons list
            self.load_seasons_data(seasons_data.get('items', []))

            # Switch to seasons & episodes tab
            if hasattr(self, 'content_tabs'):
                # Find the seasons tab index
                for i in range(self.content_tabs.count()):
                    if "Seasons" in self.content_tabs.tabText(i):
                        self.content_tabs.setCurrentIndex(i)
                        break
            elif hasattr(self.widgets, 'content_tabs'):
                # Find the seasons tab index
                for i in range(self.widgets.content_tabs.count()):
                    if "Seasons" in self.widgets.content_tabs.tabText(i):
                        self.widgets.content_tabs.setCurrentIndex(i)
                        break

            print("✅ Seasons and episodes loaded successfully")

        except Exception as e:
            error_msg = f"Error loading seasons: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def load_seasons_data(self, seasons_items):
        """Load seasons data into the seasons list widget"""
        try:
            print(f"📺 Loading {len(seasons_items)} seasons...")

            # Clear existing seasons
            self.seasons_list.clear()
            self.current_seasons = []

            # Add seasons to the list
            for season in seasons_items:
                season_number = season.get('number', 'Unknown')
                episodes_total = season.get('episodes', {}).get('total', 0)
                content_id = season.get('contentId', '')

                # Create display text
                season_text = f"Season {season_number} ({episodes_total} episodes)"

                # Create list item
                from PySide6.QtWidgets import QListWidgetItem
                item = QListWidgetItem(season_text)
                item.setData(32, {  # Qt.UserRole = 32
                    'season_number': season_number,
                    'content_id': content_id,
                    'episodes_total': episodes_total,
                    'season_data': season
                })

                # Add to list
                self.seasons_list.addItem(item)
                self.current_seasons.append(season)

                print(f"✅ Added Season {season_number} with {episodes_total} episodes")

            print(f"✅ Loaded {len(seasons_items)} seasons successfully")

        except Exception as e:
            print(f"❌ Error loading seasons data: {str(e)}")

    def load_episodes_for_season(self, season_content_id, season_number):
        """Load episodes for a specific season using the API"""
        try:
            print(f"📋 Loading episodes for Season {season_number}...")

            # Clear existing episodes
            self.episodes_list.clear()

            # Use the API to fetch season details using the original function
            if hasattr(self.main_window, 'yango_api'):
                # Call the API method to fetch season details
                episodes_data = self.main_window.yango_api.fetch_season_details_and_display(season_content_id)

                if episodes_data:
                    print(f"📋 Loading {len(episodes_data)} episodes for Season {season_number}...")

                    for episode_data in episodes_data:
                        episode_number = episode_data.get('episode_number', 0)
                        episode_title = episode_data.get('title', f"Episode {episode_number}")
                        status = episode_data.get('Status/Release Date', 'Unknown')
                        availability_status = episode_data.get('availability_status', 'UNKNOWN')
                        content_id = episode_data.get('content_id', '')

                        # Create episode text
                        episode_text = f"E{episode_number:02d}: {episode_title} ({status})"

                        # Create list item
                        from PySide6.QtWidgets import QListWidgetItem
                        item = QListWidgetItem(episode_text)
                        item.setData(32, {  # Qt.UserRole = 32
                            'episode_number': episode_number,
                            'content_id': content_id,
                            'title': episode_title,
                            'status': status,
                            'availability_status': availability_status,
                            'episode_data': episode_data
                        })

                        # Set color based on availability
                        if availability_status == 'PUBLISHED':
                            item.setForeground(self.get_color('#50fa7b'))  # Green for available
                            print(f"✅ Added Episode {episode_number}: {episode_title} (Available)")
                        else:
                            item.setForeground(self.get_color('#ffb86c'))  # Orange for upcoming
                            print(f"🔄 Added Episode {episode_number}: {episode_title} ({status})")

                        # Add to list
                        self.episodes_list.addItem(item)

                    print(f"✅ Loaded {len(episodes_data)} episodes for Season {season_number}")

                    # Update range combo boxes
                    self.update_range_combos()
                else:
                    self.show_message("Info", f"No episodes found for Season {season_number}")
            else:
                self.show_message("Error", "API not available")

        except Exception as e:
            error_msg = f"Error loading episodes for season {season_number}: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def load_episodes_data(self, episodes_items, season_number):
        """Load episodes data into the episodes list widget"""
        try:
            print(f"📋 Loading {len(episodes_items)} episodes for Season {season_number}...")

            # Clear existing episodes
            self.episodes_list.clear()

            # Add episodes to the list
            for episode in episodes_items:
                episode_number = episode.get('number', 'Unknown')
                episode_title = episode.get('title', {})
                title_text = episode_title.get('localized') or episode_title.get('original') or f"Episode {episode_number}"
                content_id = episode.get('contentId', '')

                # Get episode status
                ott_data = episode.get('ott', {})
                view_option = ott_data.get('viewOption', {})
                availability_status = view_option.get('availabilityStatus', 'Unknown')

                # Format status
                if availability_status == 'PUBLISHED':
                    status = "Available"
                elif availability_status == 'PLANNED':
                    planned_date = ott_data.get('plannedAvailabilityDate', '')
                    if planned_date:
                        # Extract date in MM/DD format
                        try:
                            date_part = planned_date.split('T')[0]
                            date_parts = date_part.split('-')
                            if len(date_parts) >= 3:
                                status = f"{date_parts[1]}/{date_parts[2]}"
                            else:
                                status = "Soon"
                        except:
                            status = "Soon"
                    else:
                        status = "Soon"
                else:
                    status = "Soon"

                # Create display text
                episode_text = f"E{episode_number:02d}: {title_text} ({status})"

                # Create list item
                from PySide6.QtWidgets import QListWidgetItem
                item = QListWidgetItem(episode_text)
                item.setData(32, {  # Qt.UserRole = 32
                    'episode_number': episode_number,
                    'content_id': content_id,
                    'title': title_text,
                    'status': status,
                    'availability_status': availability_status,
                    'episode_data': episode
                })

                # Set color based on availability
                if availability_status == 'PUBLISHED':
                    item.setForeground(self.get_color('#50fa7b'))  # Green for available
                else:
                    item.setForeground(self.get_color('#ffb86c'))  # Orange for upcoming

                # Add to list
                self.episodes_list.addItem(item)

                print(f"✅ Added Episode {episode_number}: {title_text} ({status})")

            print(f"✅ Loaded {len(episodes_items)} episodes for Season {season_number}")

        except Exception as e:
            print(f"❌ Error loading episodes data: {str(e)}")





    def load_episode_streams(self, content_id, episode_number, episode_title):
        """Load streams for a specific episode"""
        try:
            print(f"🎬 Loading streams for Episode {episode_number}: {episode_title}")

            # Clear existing streams display
            self.clear_streams_display()

            # Use the API to fetch episode streams
            if hasattr(self.main_window, 'yango_api'):
                # Call the API method to fetch episode streams
                streams_data = self.main_window.yango_api.get_player_base_info(content_id)

                if streams_data:
                    print(f"🎬 Episode streams loaded successfully")
                    self.display_episode_streams(streams_data, episode_number, episode_title)
                else:
                    self.show_message("Error", f"No streams found for Episode {episode_number}")
            else:
                self.show_message("Error", "API not available")

        except Exception as e:
            error_msg = f"Error loading episode streams: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def clear_streams_display(self):
        """Clear the streams display"""
        try:
            # Clear the streams list if it exists
            if hasattr(self, 'streams_list'):
                self.streams_list.clear()
            print("🧹 Cleared streams display")
        except Exception as e:
            print(f"❌ Error clearing streams display: {str(e)}")





    def display_movie_streams_info(self, qualities, audio_tracks, subtitle_tracks, mpd_url="", decryption_keys=None):
        """Display movie streams information in the Available Streams tab with modern design"""
        try:
            print(f"📊 Displaying streams info for Movie")

            if decryption_keys is None:
                decryption_keys = set()

            # Create or update the streams display
            if not hasattr(self, 'streams_info_widget') or not hasattr(self, 'quality_combo'):
                self.create_streams_info_widget()

            # Check if all required elements exist
            if not hasattr(self, 'quality_combo') or not hasattr(self, 'streams_episode_label'):
                print("❌ Required UI elements not found, recreating...")
                self.create_streams_info_widget()

            # Update movie info (instead of episode info)
            movie_title = self.current_content.get('title', {}).get('localized', 'Unknown Movie')
            movie_info = f"🎬 Movie: {movie_title}"
            self.streams_episode_label.setText(movie_info)

            # Update quality checkboxes using OSN-style
            if qualities:
                # Sort qualities by actual resolution (highest first) - prioritize pixel count over quality name
                def quality_sort_key(quality_data):
                    """Accurate sorting key prioritizing actual resolution over quality name"""
                    # Extract display quality from format "1080p|1920x804" or just "1080p"
                    if '|' in quality_data:
                        quality, resolution = quality_data.split('|', 1)
                        # Extract actual resolution for better sorting
                        if 'x' in resolution:
                            try:
                                width, height = resolution.split('x')
                                width, height = int(width), int(height)
                                # Use total pixels (width * height) for most accurate sorting
                                # This ensures 3840x1608 comes before 1920x804 even if both are labeled "1080p"
                                total_pixels = width * height
                                print(f"   📊 {quality_data} → {width}x{height} = {total_pixels:,} pixels")
                                return total_pixels
                            except:
                                pass

                    # Fallback to quality name if no resolution info
                    quality = quality_data if '|' not in quality_data else quality_data.split('|')[0]

                    if quality == '2160p':
                        return 8294400  # 3840x2160 pixels
                    elif quality == '1080p':
                        return 2073600  # 1920x1080 pixels
                    elif quality == '720p':
                        return 921600   # 1280x720 pixels
                    elif quality == '520p':
                        return 665600   # 1280x520 pixels
                    elif quality == '430p':
                        return 440320   # 1024x430 pixels
                    elif quality == '360p':
                        return 230400   # 640x360 pixels
                    elif quality == '320p':
                        return 204800   # 640x320 pixels
                    elif quality == '260p':
                        return 166400   # 640x260 pixels
                    elif 'p' in quality:
                        try:
                            # Estimate pixels based on quality number
                            quality_num = int(quality.replace('p', ''))
                            return quality_num * 1280  # Rough estimation
                        except:
                            return 0
                    return 0

                sorted_qualities = sorted(qualities, key=quality_sort_key, reverse=True)
                print(f"📊 Sorted qualities: {sorted_qualities}")
                self.create_quality_checkboxes(sorted_qualities)
            else:
                # Clear quality checkboxes if no qualities available
                for checkbox in self.quality_checkboxes:
                    checkbox.deleteLater()
                self.quality_checkboxes.clear()

            # Update audio tracks using OSN-style checkboxes
            if audio_tracks:
                # Convert to list format expected by create_audio_checkboxes with enhanced display
                audio_list = []
                for track in audio_tracks:
                    enhanced_display = self.get_enhanced_audio_display(track)
                    audio_list.append({
                        'language': enhanced_display,
                        'original_track': track  # Store original for reference
                    })
                self.create_audio_checkboxes(audio_list)
            else:
                self.clear_audio_tracks()

            # Update subtitles using OSN-style checkboxes
            if subtitle_tracks and len(subtitle_tracks) > 0:
                # Convert to list format expected by create_subtitle_checkboxes
                subtitle_list = []
                for track in subtitle_tracks:
                    # Handle different subtitle track formats
                    if isinstance(track, str):
                        subtitle_list.append({'language': track})
                    elif isinstance(track, dict) and 'language' in track:
                        subtitle_list.append(track)
                print(f"✅ Creating subtitle checkboxes for: {subtitle_list}")
                self.create_subtitle_checkboxes(subtitle_list)
            else:
                print("❌ No subtitle tracks found, clearing...")
                self.clear_subtitle_tracks()
                # Add placeholder
                from PySide6.QtWidgets import QLabel
                no_subtitles_label = QLabel("No subtitles available")
                no_subtitles_label.setStyleSheet("""
                    QLabel {
                        color: #f8f8f2;
                        font-size: 12px;
                        padding: 3px;
                        font-style: italic;
                    }
                """)
                self.subtitle_layout.addWidget(no_subtitles_label)

            # Log MPD URL and decryption keys internally (not displayed in UI)
            if mpd_url:
                print(f"   🔗 MPD URL found: {mpd_url[:100]}...")
                # Store for download queue
                self.current_mpd_url = mpd_url
            else:
                print("   ❌ No MPD URL found")
                self.current_mpd_url = ""

            if decryption_keys:
                print(f"   🔑 Found {len(decryption_keys)} decryption keys:")
                for key_info in sorted(decryption_keys):
                    print(f"       {key_info}")
                # Store for download queue
                self.current_decryption_keys = list(decryption_keys)
            else:
                print("   ❌ No decryption keys found")
                self.current_decryption_keys = []

            # Store movie data for download queue
            self.current_movie_data = {
                'title': movie_title,
                'qualities': list(qualities),
                'audio_tracks': list(audio_tracks),
                'subtitle_tracks': list(subtitle_tracks)
            }

            # Save decryption keys to KEYS/KEYS.txt file (following original YANGO script)
            if decryption_keys:
                self.save_keys_to_file(decryption_keys, f"Movie: {movie_title}")

            # Enable both buttons
            self.download_button.setEnabled(True)
            if hasattr(self, 'play_button'):
                self.play_button.setEnabled(True)

            print(f"✅ Streams info displayed for Movie: {movie_title}")
            print(f"   📺 Qualities: {len(qualities)}")
            print(f"   🔊 Audio tracks: {len(audio_tracks)}")
            print(f"   📝 Subtitle tracks: {len(subtitle_tracks)}")
            print(f"   🔑 Decryption keys: {len(decryption_keys)}")

        except Exception as e:
            error_msg = f"Error displaying movie streams info: {str(e)}"
            print(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()

    def display_episode_streams(self, streams_data, episode_number, episode_title):
        """Display episode streams information"""
        try:
            print(f"📺 Displaying streams for Episode {episode_number}: {episode_title}")

            # Extract streams information from the response (same as original YANGO script)
            content_data = streams_data.get('data', {}).get('content', {})

            if not content_data:
                self.show_message("Error", "No content data found in streams response")
                return

            # Get episode OTT data (following original script pattern)
            episode_ott = content_data.get('episodeOtt', {})
            online_streams = episode_ott.get('onlineStreams', {})

            if not online_streams:
                self.show_message("Info", f"No online streams available for Episode {episode_number}")
                return

            # Extract streams information (same as original: streams = data.get("data", {}).get("content", {}).get("episodeOtt", {}).get("onlineStreams", {}).get("streams", []))
            streams = online_streams.get('streams', [])

            if not streams:
                self.show_message("Info", f"No streams found for Episode {episode_number}")
                return

            print(f"🎬 Found {len(streams)} streams for Episode {episode_number}")

            # Process and display streams (following original script logic)
            self.process_episode_streams(streams, episode_number, episode_title)

        except Exception as e:
            error_msg = f"Error displaying episode streams: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def process_episode_streams(self, streams, episode_number, episode_title):
        """Process and display episode streams (following original YANGO script logic)"""
        try:
            print(f"🔄 Processing {len(streams)} streams for Episode {episode_number}")

            # Initialize collections (same logic as original script)
            qualities = set()
            audio_tracks = set()
            subtitle_tracks = set()
            decryption_keys = set()
            mpd_url = ""

            # Step 1: Extract DRM keys first (following original YANGO script pattern)
            # Find the stream with DRM config (usually streams[1] in original script)
            drm_stream = None
            for i, stream in enumerate(streams):
                stream_meta = stream.get('streamMeta', {})
                stream_uri = stream.get('uri', '')
                drm_config = stream_meta.get('drmConfig', {})

                if drm_config and stream_uri and 'manifest.mpd' in stream_uri:
                    drm_stream = stream
                    mpd_url = stream_uri
                    print(f"🔍 Found DRM stream {i+1}/{len(streams)}")
                    print(f"   🔗 MPD URL: {mpd_url[:100]}...")
                    break

            # Extract DRM keys if DRM stream found
            if drm_stream:
                stream_meta = drm_stream.get('streamMeta', {})
                drm_config = stream_meta.get('drmConfig', {})
                request_params = drm_config.get('requestParams', {})

                if request_params:
                    print(f"🔑 Extracting real DRM keys for episode {episode_number}")

                    # Use YangoDRM to extract real keys
                    from .yango_drm import YangoDRM
                    drm_handler = YangoDRM()

                    # Extract PSSH from MPD
                    pssh = drm_handler.get_pssh_from_mpd(mpd_url)
                    if pssh:
                        print(f"   ✅ PSSH extracted: {pssh[:50]}...")

                        # Get real decryption keys
                        keys = drm_handler.get_decryption_key(pssh, request_params)
                        if keys:
                            # Format keys for display
                            for key in keys:
                                formatted_key = f"[+] Key: {key}"
                                decryption_keys.add(formatted_key)
                                print(f"   🔑 Real DRM Key: {formatted_key}")
                        else:
                            print("   ❌ No real keys extracted")
                    else:
                        print("   ❌ No PSSH found in MPD")

            # Step 2: Extract video qualities, audio tracks, and subtitles from ALL streams
            # (following original script pattern - process all streams for metadata)
            for i, stream in enumerate(streams):
                print(f"🔍 Processing stream {i+1}/{len(streams)} for metadata")

                stream_meta = stream.get('streamMeta', {})
                stream_uri = stream.get('uri', '')

                # Extract MPD URL if not found yet
                if stream_uri and '.mpd' in stream_uri and not mpd_url:
                    mpd_url = stream_uri
                    print(f"   🔗 Found MPD URL: {mpd_url[:100]}...")

                # Extract video qualities with detailed information
                video_metas = stream_meta.get('videoMetas', [])
                for video_meta in video_metas:
                    height = video_meta.get('height', 0)
                    width = video_meta.get('width', 0)

                    if height:
                        # Use the new quality classification system that properly handles 4K/HD separation
                        quality_str = self.classify_video_quality(width, height)
                        quality_category = self.get_quality_category(width, height)

                        # Store quality with original resolution for proper download mapping
                        quality_with_resolution = f"{quality_str}|{width}x{height}"
                        qualities.add(quality_with_resolution)
                        print(f"   📺 Video: {quality_str} ({width}x{height}) [{quality_category}]")

                # Extract audio tracks with detailed information
                audio_metas = stream_meta.get('audioMetas', [])
                for audio_meta in audio_metas:
                    language = audio_meta.get('languageName', 'Unknown')
                    quality = audio_meta.get('quality', '')
                    channels = audio_meta.get('audioChannelsNumber', 2)

                    # Format audio track display
                    if quality == 'SURROUND_51':
                        audio_str = f'{language} - 5.1 Surround ({channels} channels)'
                    elif quality == 'STEREO':
                        audio_str = f'{language} - Stereo ({channels} channels)'
                    else:
                        audio_str = f'{language} - {quality} ({channels} channels)' if quality else f'{language} ({channels} channels)'

                    audio_tracks.add(audio_str)
                    print(f"   🔊 Audio: {audio_str}")

                # Extract subtitle tracks
                subtitle_metas = stream_meta.get('subtitleMetas', [])
                for subtitle_meta in subtitle_metas:
                    language = subtitle_meta.get('languageName', 'Unknown')
                    subtitle_type = subtitle_meta.get('type', '')

                    # Format subtitle display
                    if subtitle_type:
                        subtitle_str = f'{language} ({subtitle_type})'
                    else:
                        subtitle_str = language

                    subtitle_tracks.add(subtitle_str)
                    print(f"   📝 Subtitle: {subtitle_str}")

            # Store MPD URL and decryption keys for download
            self.current_mpd_url = mpd_url
            self.current_decryption_keys = list(decryption_keys)

            # Display the information
            self.display_streams_info(qualities, audio_tracks, subtitle_tracks, episode_number, episode_title, mpd_url, decryption_keys)

        except Exception as e:
            error_msg = f"Error processing episode streams: {str(e)}"
            print(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()

    def display_streams_info(self, qualities, audio_tracks, subtitle_tracks, episode_number, episode_title, mpd_url="", decryption_keys=None):
        """Display streams information in the Available Streams tab with modern design"""
        try:
            print(f"📊 Displaying streams info for Episode {episode_number}")

            if decryption_keys is None:
                decryption_keys = set()

            # Create or update the streams display
            if not hasattr(self, 'streams_info_widget') or not hasattr(self, 'quality_combo'):
                self.create_streams_info_widget()

            # Check if all required elements exist
            if not hasattr(self, 'quality_combo') or not hasattr(self, 'streams_episode_label'):
                print("❌ Required UI elements not found, recreating...")
                self.create_streams_info_widget()

            # Update episode info
            episode_info = f"📺 Episode {episode_number}: {episode_title}"
            self.streams_episode_label.setText(episode_info)

            # Update quality checkboxes using OSN-style
            if qualities:
                # Sort qualities by actual resolution (highest first) - prioritize pixel count over quality name
                def quality_sort_key(quality_data):
                    """Accurate sorting key prioritizing actual resolution over quality name"""
                    # Extract display quality from format "1080p|1920x804" or just "1080p"
                    if '|' in quality_data:
                        quality, resolution = quality_data.split('|', 1)
                        # Extract actual resolution for better sorting
                        if 'x' in resolution:
                            try:
                                width, height = resolution.split('x')
                                width, height = int(width), int(height)
                                # Use total pixels (width * height) for most accurate sorting
                                # This ensures 3840x1608 comes before 1920x804 even if both are labeled "1080p"
                                total_pixels = width * height
                                print(f"   📊 {quality_data} → {width}x{height} = {total_pixels:,} pixels")
                                return total_pixels
                            except:
                                pass

                    # Fallback to quality name if no resolution info
                    quality = quality_data if '|' not in quality_data else quality_data.split('|')[0]

                    if quality == '2160p':
                        return 8294400  # 3840x2160 pixels
                    elif quality == '1080p':
                        return 2073600  # 1920x1080 pixels
                    elif quality == '720p':
                        return 921600   # 1280x720 pixels
                    elif quality == '520p':
                        return 665600   # 1280x520 pixels
                    elif quality == '430p':
                        return 440320   # 1024x430 pixels
                    elif quality == '360p':
                        return 230400   # 640x360 pixels
                    elif quality == '320p':
                        return 204800   # 640x320 pixels
                    elif quality == '260p':
                        return 166400   # 640x260 pixels
                    elif 'p' in quality:
                        try:
                            # Estimate pixels based on quality number
                            quality_num = int(quality.replace('p', ''))
                            return quality_num * 1280  # Rough estimation
                        except:
                            return 0
                    return 0

                sorted_qualities = sorted(qualities, key=quality_sort_key, reverse=True)
                print(f"📊 Sorted qualities: {sorted_qualities}")
                self.create_quality_checkboxes(sorted_qualities)
            else:
                # Clear quality checkboxes if no qualities available
                for checkbox in self.quality_checkboxes:
                    checkbox.deleteLater()
                self.quality_checkboxes.clear()

            # Update audio tracks using OSN-style checkboxes
            if audio_tracks:
                # Convert to list format expected by create_audio_checkboxes with enhanced display
                audio_list = []
                for track in audio_tracks:
                    enhanced_display = self.get_enhanced_audio_display(track)
                    audio_list.append({
                        'language': enhanced_display,
                        'original_track': track  # Store original for reference
                    })
                self.create_audio_checkboxes(audio_list)
            else:
                self.clear_audio_tracks()

            # Update subtitles using OSN-style checkboxes
            if subtitle_tracks and len(subtitle_tracks) > 0:
                # Convert to list format expected by create_subtitle_checkboxes
                subtitle_list = []
                for track in subtitle_tracks:
                    # Handle different subtitle track formats
                    if isinstance(track, str):
                        subtitle_list.append({'language': track})
                    elif isinstance(track, dict) and 'language' in track:
                        subtitle_list.append(track)
                print(f"✅ Creating subtitle checkboxes for: {subtitle_list}")
                self.create_subtitle_checkboxes(subtitle_list)
            else:
                print("❌ No subtitle tracks found, clearing...")
                self.clear_subtitle_tracks()
                # Add placeholder
                from PySide6.QtWidgets import QLabel
                no_subtitles_label = QLabel("No subtitles available")
                no_subtitles_label.setStyleSheet("""
                    QLabel {
                        color: #f8f8f2;
                        font-size: 12px;
                        padding: 3px;
                        font-style: italic;
                    }
                """)
                self.subtitle_layout.addWidget(no_subtitles_label)

            # Log MPD URL and decryption keys internally (not displayed in UI)
            if mpd_url:
                print(f"   🔗 MPD URL found: {mpd_url[:100]}...")
                # Store for download queue
                self.current_mpd_url = mpd_url
            else:
                print("   ❌ No MPD URL found")
                self.current_mpd_url = ""

            if decryption_keys:
                print(f"   🔑 Found {len(decryption_keys)} decryption keys:")
                for key_info in sorted(decryption_keys):
                    print(f"       {key_info}")
                # Store for download queue
                self.current_decryption_keys = list(decryption_keys)
            else:
                print("   ❌ No decryption keys found")
                self.current_decryption_keys = []

            # Store episode data for download queue
            self.current_episode_data = {
                'episode_number': episode_number,
                'episode_title': episode_title,
                'qualities': list(qualities),
                'audio_tracks': list(audio_tracks),
                'subtitle_tracks': list(subtitle_tracks)
            }

            # Save decryption keys to KEYS/KEYS.txt file (following original YANGO script)
            if decryption_keys:
                self.save_keys_to_file(decryption_keys, f"Episode {episode_number}: {episode_title}")

            # Enable both buttons
            self.download_button.setEnabled(True)
            if hasattr(self, 'play_button'):
                self.play_button.setEnabled(True)

            print(f"✅ Streams info displayed for Episode {episode_number}")
            print(f"   📺 Qualities: {len(qualities)}")
            print(f"   🔊 Audio tracks: {len(audio_tracks)}")
            print(f"   📝 Subtitle tracks: {len(subtitle_tracks)}")
            print(f"   🔑 Decryption keys: {len(decryption_keys)}")

        except Exception as e:
            error_msg = f"Error displaying streams info: {str(e)}"
            print(f"❌ {error_msg}")
            import traceback
            traceback.print_exc()

    def clear_audio_tracks(self):
        """Clear existing audio track checkboxes"""
        try:
            if hasattr(self, 'audio_layout'):
                while self.audio_layout.count():
                    child = self.audio_layout.takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()

            # Clear stored references
            if hasattr(self, 'audio_checkboxes'):
                self.audio_checkboxes.clear()

        except Exception as e:
            print(f"❌ Error clearing audio tracks: {str(e)}")

    def clear_subtitle_tracks(self):
        """Clear existing subtitle track checkboxes"""
        try:
            if hasattr(self, 'subtitle_layout'):
                while self.subtitle_layout.count():
                    child = self.subtitle_layout.takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()

            # Clear stored references
            if hasattr(self, 'subtitle_checkboxes'):
                self.subtitle_checkboxes.clear()

        except Exception as e:
            print(f"❌ Error clearing subtitle tracks: {str(e)}")

    def add_to_download_queue(self):
        """Add selected content to download queue"""
        try:
            # Get selected quality from checkboxes
            selected_quality = "Unknown"
            selected_display_quality = "Unknown"
            selected_original_resolution = None
            if hasattr(self, 'quality_checkboxes'):
                for checkbox in self.quality_checkboxes:
                    if checkbox.isChecked():
                        # Use the enhanced display text from checkbox (e.g., "2160p 4K UW (3840x1608)")
                        selected_quality = getattr(checkbox, 'enhanced_display', checkbox.text())
                        # Get the original display quality for compatibility (e.g., "1080p")
                        selected_display_quality = getattr(checkbox, 'display_quality', checkbox.text())
                        # Get original resolution if available
                        if hasattr(checkbox, 'original_resolution'):
                            selected_original_resolution = checkbox.original_resolution
                        print(f"   📺 Selected quality: {selected_quality} (original: {selected_original_resolution})")
                        print(f"   📊 Display quality: {selected_display_quality}")
                        break

            # Get selected audio tracks with original data
            selected_audio = []
            if hasattr(self, 'audio_checkboxes'):
                for checkbox in self.audio_checkboxes:
                    if checkbox.isChecked():
                        # Use original track data if available, otherwise use display text
                        if hasattr(checkbox, 'audio_data') and 'original_track' in checkbox.audio_data:
                            original_track = checkbox.audio_data['original_track']
                            selected_audio.append(original_track)
                            print(f"   🔊 Selected audio (original): {original_track}")
                        else:
                            selected_audio.append(checkbox.text())
                            print(f"   🔊 Selected audio (display): {checkbox.text()}")

            # Get selected subtitles
            selected_subtitles = []
            if hasattr(self, 'subtitle_checkboxes'):
                for checkbox in self.subtitle_checkboxes:
                    if checkbox.isChecked():
                        selected_subtitles.append(checkbox.text())

            if selected_quality == "Unknown":
                self.show_message("Warning", "Please select a quality option.")
                return

            # Check if this is a movie or series
            if hasattr(self, 'current_movie_data') and self.current_movie_data:
                # This is a movie
                print(f"🎬 Adding movie to download queue")
                movie_data = self.current_movie_data
                movie_title = movie_data.get('title', 'Unknown Movie')

                # For movies, use a simple format without season/episode
                self.add_movie_to_download_queue(
                    title=movie_title,
                    quality=selected_quality,
                    original_resolution=selected_original_resolution,
                    audio_track=selected_audio,  # Pass all selected audio tracks
                    subtitle=selected_subtitles
                )
                return

            # Check if we have multiple episodes selected for batch download
            episodes_to_add = []
            if hasattr(self, 'selected_episodes_for_download') and self.selected_episodes_for_download:
                # Multiple episodes - use the stored list
                episodes_to_add = self.selected_episodes_for_download
                print(f"📥 Adding {len(episodes_to_add)} episodes with selected quality settings")
            else:
                # Single episode - use current episode data
                episode_data = getattr(self, 'current_episode_data', {})
                if episode_data:
                    episodes_to_add = [episode_data]

            if not episodes_to_add:
                self.show_message("Error", "No episode data found.")
                return

            # Get series info
            series_title = getattr(self, 'current_series_title', 'Unknown Series')
            season_number = getattr(self, 'current_season_number', 1)

            added_count = 0
            for episode_data in episodes_to_add:
                episode_number = episode_data.get('episode_number', 'Unknown')
                episode_title = episode_data.get('title', f'Episode {episode_number}')

                title = series_title
                season = f'S{season_number:02d}'
                episode = f'E{episode_number:02d}'

                # Check if this is a single episode with current MPD URL available
                if (len(episodes_to_add) == 1 and
                    hasattr(self, 'current_mpd_url') and self.current_mpd_url):
                    # Single episode with MPD URL - use the regular add_download_to_queue
                    print(f"📥 Adding single episode with current MPD URL")

                    # Convert original resolution to proper quality format for filename
                    if selected_original_resolution and 'x' in selected_original_resolution:
                        try:
                            width, height = map(int, selected_original_resolution.split('x'))
                            classified_quality = self.classify_video_quality(width, height)
                            print(f"   📺 Using quality: {selected_quality} (original: {selected_original_resolution} → {classified_quality})")
                            actual_quality = classified_quality
                        except ValueError:
                            actual_quality = selected_quality
                            print(f"   📺 Using quality: {selected_quality} (fallback)")
                    else:
                        actual_quality = selected_quality
                        print(f"   📺 Using quality: {selected_quality}")

                    self.add_download_to_queue(
                        title=title,
                        season=season,
                        episode=episode,
                        quality=actual_quality,  # Use classified quality for download (e.g., "2160p")
                        display_quality=selected_quality,  # Keep display quality for UI
                        audio_track=selected_audio,  # Pass all selected audio tracks
                        subtitle=selected_subtitles  # Pass all selected subtitles
                    )
                else:
                    # Multiple episodes or no current MPD URL - use the single method
                    # Convert original resolution to proper quality format for filename
                    if selected_original_resolution and 'x' in selected_original_resolution:
                        try:
                            width, height = map(int, selected_original_resolution.split('x'))
                            classified_quality = self.classify_video_quality(width, height)
                            print(f"   📺 Using quality for batch: {selected_quality} (original: {selected_original_resolution} → {classified_quality})")
                            actual_quality = classified_quality
                        except ValueError:
                            actual_quality = selected_quality
                            print(f"   📺 Using quality for batch: {selected_quality} (fallback)")
                    else:
                        actual_quality = selected_quality
                        print(f"   📺 Using quality for batch: {selected_quality}")

                    self.add_download_to_queue_single(
                        title=title,
                        season=season,
                        episode=episode,
                        quality=actual_quality,  # Use classified quality for download (e.g., "2160p")
                        display_quality=selected_quality,  # Keep display quality for UI
                        audio_track=selected_audio,  # Pass all selected audio tracks
                        subtitle=selected_subtitles,  # Pass all selected subtitles
                        episode_data=episode_data
                    )

                added_count += 1
                print(f"✅ Added Episode {episode_number}: {episode_title}")

            # Clear the selected episodes list after adding
            if hasattr(self, 'selected_episodes_for_download'):
                self.selected_episodes_for_download = []

            print(f"✅ Added {added_count} episodes to download queue")
            print(f"   📺 Quality: {selected_quality}")
            print(f"   🔊 Audio: {', '.join(selected_audio) if selected_audio else 'None'}")
            print(f"   📝 Subtitles: {', '.join(selected_subtitles) if selected_subtitles else 'None'}")

            # Show success message
            if added_count > 1:
                self.show_message("Success", f"Added {added_count} episodes to downloads with:\n\n• Quality: {selected_quality}\n• Audio: {', '.join(selected_audio) if selected_audio else 'None'}\n• Subtitles: {', '.join(selected_subtitles) if selected_subtitles else 'None'}")

        except Exception as e:
            error_msg = f"Error adding to download queue: {str(e)}"
            print(f"❌ {error_msg}")
            self.show_message("Error", error_msg)

    def add_download_to_queue_single(self, title, season, episode, quality, display_quality=None, audio_track=None, subtitle=None, episode_data=None):
        """Add a single download to the organized downloads table with proper episode data extraction"""
        try:
            if not episode_data:
                print(f"❌ No episode data provided for {episode}")
                return

            # Extract episode-specific data
            episode_content_id = episode_data.get('content_id', '')
            episode_number = episode_data.get('episode_number', 'Unknown')

            print(f"🔄 Processing episode {episode_number} with content ID: {episode_content_id}")

            # Create download item with placeholder data - will be updated when download starts
            download_item = {
                'title': title,
                'season': season,
                'episode': episode,
                'quality': quality,  # This is the actual resolution for download
                'display_quality': display_quality or quality,  # This is for UI display
                'audio_track': audio_track,
                'subtitle': subtitle,
                'status': 'Queued',
                'progress': 0,
                'episode_data': episode_data,
                'episode_content_id': episode_content_id,
                'episode_number': episode_number,
                # These will be fetched when download starts
                'mpd_url': '',
                'decryption_keys': []
            }

            # Add to downloads table
            self.add_download_to_table(download_item)

            # Update stats
            self.update_downloads_stats()

            # Switch to downloads tab
            if hasattr(self, 'content_tabs'):
                for i in range(self.content_tabs.count()):
                    if "Downloads" in self.content_tabs.tabText(i):
                        self.content_tabs.setCurrentIndex(i)
                        break

            print(f"✅ Added to downloads: {title} {season} {episode} ({quality}) - Episode {episode_number}")

        except Exception as e:
            print(f"❌ Error adding single download: {str(e)}")
            self.show_notification(f"Error adding to downloads: {str(e)}", "❌", "#ff5555")

    def add_movie_to_download_queue(self, title, quality, original_resolution=None, audio_track=None, subtitle=None):
        """Add a movie to the download queue"""
        try:
            print(f"🎬 Adding movie to download queue: {title}")

            # Get content ID from current content
            content_id = self.current_content.get('content_id', '')
            if not content_id:
                self.show_message("Error", "No content ID found for this movie.")
                return

            # Convert original resolution to proper quality format for filename
            if original_resolution and 'x' in original_resolution:
                # Extract width and height from resolution
                try:
                    width, height = map(int, original_resolution.split('x'))
                    # Use the quality classification system to get proper quality
                    classified_quality = self.classify_video_quality(width, height)
                    print(f"   📺 Using quality: {quality} (original: {original_resolution} → {classified_quality})")
                    actual_quality = classified_quality
                except ValueError:
                    # Fallback to original quality if parsing fails
                    actual_quality = quality
                    print(f"   📺 Using quality: {quality} (fallback)")
            else:
                actual_quality = quality
                print(f"   📺 Using quality: {quality}")

            # Create download item for movie
            download_item = {
                'title': title,
                'season': '',  # Movies don't have seasons
                'episode': '',  # Movies don't have episodes
                'quality': actual_quality,  # Use classified quality for download (e.g., "2160p")
                'original_resolution': original_resolution,  # Keep original resolution for N_m3u8DL-RE (e.g., "640x268")
                'display_quality': quality,  # Keep enhanced display quality for UI (e.g., "2160p 4K UW (3840x1608)")
                'audio_track': audio_track,
                'subtitle': subtitle,
                'status': 'Queued',
                'progress': 0,
                'content_id': content_id,
                'content_type': 'movie',
                # Use current MPD URL and keys if available
                'mpd_url': getattr(self, 'current_mpd_url', ''),
                'decryption_keys': getattr(self, 'current_decryption_keys', [])
            }

            # Add to downloads table
            self.add_download_to_table(download_item)

            # Update stats
            self.update_downloads_stats()

            # Switch to downloads tab
            if hasattr(self, 'content_tabs'):
                for i in range(self.content_tabs.count()):
                    if "Downloads" in self.content_tabs.tabText(i):
                        self.content_tabs.setCurrentIndex(i)
                        break

            print(f"✅ Added movie to downloads: {title} ({quality})")

            # Show success message
            self.show_message("Success", f"Added movie to downloads:\n\n• Title: {title}\n• Quality: {quality}\n• Audio: {audio_track if audio_track else 'None'}\n• Subtitles: {', '.join(subtitle) if subtitle else 'None'}")

        except Exception as e:
            print(f"❌ Error adding movie to download queue: {str(e)}")
            self.show_message("Error", f"Error adding movie to downloads: {str(e)}")







    def save_keys_to_file(self, decryption_keys, title="Unknown"):
        """Save decryption keys to KEYS/KEYS.txt file (following original YANGO script)"""
        try:
            import os

            # Define KEYS_PATH following original script pattern
            keys_dir = "KEYS"
            keys_path = os.path.join(keys_dir, "KEYS.txt")

            # Create KEYS directory if it doesn't exist
            os.makedirs(keys_dir, exist_ok=True)

            # Extract actual keys from the formatted strings
            actual_keys = []
            for key_info in decryption_keys:
                if key_info.startswith("[+] Key: "):
                    # Extract the actual key part after "[+] Key: "
                    actual_key = key_info.replace("[+] Key: ", "")
                    actual_keys.append(actual_key)
                else:
                    actual_keys.append(key_info)

            # Append keys to KEYS.txt file
            with open(keys_path, "a", encoding="utf-8") as keys_file:
                keys_file.write(f"\n# {title}\n")
                keys_file.write("\n".join(actual_keys) + "\n")

            print(f"✅ Keys saved to {keys_path}")
            print(f"   📁 Saved {len(actual_keys)} keys for: {title}")

        except Exception as e:
            error_msg = f"Error saving keys to file: {str(e)}"
            print(f"❌ {error_msg}")

    def create_streams_info_widget(self):
        """Create the streams info widget in Available Streams tab with OSN-style design"""
        try:
            # Find the Available Streams tab
            streams_tab = None
            for i in range(self.content_tabs.count()):
                if "Available Streams" in self.content_tabs.tabText(i):
                    streams_tab = self.content_tabs.widget(i)
                    break

            if not streams_tab:
                print("❌ Available Streams tab not found")
                return

            # Set OSN-style dark background
            streams_tab.setStyleSheet("""
                QWidget {
                    background-color: #282a36;
                    color: #f8f8f2;
                }
            """)

            # Import all required widgets
            from PySide6.QtWidgets import QVBoxLayout, QLabel, QFrame, QComboBox, QPushButton, QScrollArea, QWidget, QCheckBox, QGroupBox

            # Clear existing layout
            if streams_tab.layout():
                while streams_tab.layout().count():
                    child = streams_tab.layout().takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()
            else:
                streams_tab.setLayout(QVBoxLayout())

            layout = streams_tab.layout()
            layout.setSpacing(12)
            layout.setContentsMargins(15, 15, 15, 15)

            self.streams_episode_label = QLabel("📺 Select an episode to view streams")
            self.streams_episode_label.setStyleSheet("""
                QLabel {
                    font-size: 13pt;
                    font-weight: bold;
                    color: #50fa7b;
                    padding: 8px 12px;
                    border: 2px solid #5f6368;
                    border-radius: 6px;
                    background-color: #3c4043;
                }
            """)
            layout.addWidget(self.streams_episode_label)

            # Create Video Quality Section (OSN-style matching Audio Tracks)
            self.quality_group = QGroupBox("📺 Select Video Quality")
            self.quality_group.setStyleSheet("""
                QGroupBox {
                    font-size: 12px;
                    font-weight: bold;
                    color: #ffb86c;
                    border: 2px solid #6272a4;
                    border-radius: 5px;
                    margin: 3px 0;
                    padding-top: 8px;
                    background-color: #282a36;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 6px;
                    padding: 0 3px 0 3px;
                }
            """)
            self.quality_layout = QVBoxLayout(self.quality_group)
            self.quality_layout.setContentsMargins(6, 3, 6, 5)
            self.quality_layout.setSpacing(2)

            # Add placeholder for video quality
            placeholder_quality = QLabel("Select an episode to view available qualities")
            placeholder_quality.setStyleSheet("""
                QLabel {
                    color: #f8f8f2;
                    font-size: 12px;
                    padding: 3px;
                    font-style: italic;
                }
            """)
            self.quality_layout.addWidget(placeholder_quality)

            layout.addWidget(self.quality_group)

            # Create Audio Tracks Section (OSN-style)
            self.audio_group = QGroupBox("🔊 Select Audio Tracks")
            self.audio_group.setStyleSheet("""
                QGroupBox {
                    font-size: 12px;
                    font-weight: bold;
                    color: #50fa7b;
                    border: 2px solid #6272a4;
                    border-radius: 5px;
                    margin: 3px 0;
                    padding-top: 8px;
                    background-color: #282a36;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 6px;
                    padding: 0 3px 0 3px;
                }
            """)
            self.audio_layout = QVBoxLayout(self.audio_group)
            self.audio_layout.setContentsMargins(6, 3, 6, 5)
            self.audio_layout.setSpacing(2)

            # Add placeholder for audio tracks
            placeholder_audio = QLabel("Select an episode to view available audio tracks")
            placeholder_audio.setStyleSheet("""
                QLabel {
                    color: #f8f8f2;
                    font-size: 12px;
                    padding: 3px;
                    font-style: italic;
                }
            """)
            self.audio_layout.addWidget(placeholder_audio)

            layout.addWidget(self.audio_group)

            # Create Subtitles Section (OSN-style)
            self.subtitle_group = QGroupBox("📝 Select Subtitles")
            self.subtitle_group.setStyleSheet("""
                QGroupBox {
                    font-size: 12px;
                    font-weight: bold;
                    color: #bd93f9;
                    border: 2px solid #6272a4;
                    border-radius: 5px;
                    margin: 3px 0;
                    padding-top: 8px;
                    background-color: #282a36;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 6px;
                    padding: 0 3px 0 3px;
                }
            """)
            self.subtitle_layout = QVBoxLayout(self.subtitle_group)
            self.subtitle_layout.setContentsMargins(6, 3, 6, 5)
            self.subtitle_layout.setSpacing(2)

            # Add placeholder for subtitles
            placeholder_subtitles = QLabel("Select an episode to view available subtitles")
            placeholder_subtitles.setStyleSheet("""
                QLabel {
                    color: #f8f8f2;
                    font-size: 12px;
                    padding: 3px;
                    font-style: italic;
                }
            """)
            self.subtitle_layout.addWidget(placeholder_subtitles)

            layout.addWidget(self.subtitle_group)

            # Create Play Button (no background)
            self.play_button = QPushButton("▶️ Play")
            self.play_button.setEnabled(False)
            self.play_button.setStyleSheet("""
                QPushButton {
                    background: transparent;
                    color: #f8f8f2;
                    border: 2px solid #6272a4;
                    padding: 10px 20px;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 14px;
                    min-width: 200px;
                    min-height: 35px;
                    text-align: center;
                }
                QPushButton:hover {
                    border: 2px solid #50fa7b;
                    color: #50fa7b;
                }
                QPushButton:pressed {
                    border: 2px solid #44475a;
                    color: #44475a;
                }
                QPushButton:disabled {
                    border: 2px solid #44475a;
                    color: #44475a;
                }
            """)
            self.play_button.clicked.connect(self.handle_play)

            # Create Download Button (no background)
            self.download_button = QPushButton("📥 Start Download")
            self.download_button.setEnabled(False)
            self.download_button.setStyleSheet("""
                QPushButton {
                    background: transparent;
                    color: #f8f8f2;
                    border: 2px solid #6272a4;
                    padding: 10px 20px;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 14px;
                    min-width: 200px;
                    min-height: 35px;
                    text-align: center;
                }
                QPushButton:hover {
                    border: 2px solid #50fa7b;
                    color: #50fa7b;
                }
                QPushButton:pressed {
                    border: 2px solid #44475a;
                    color: #44475a;
                }
                QPushButton:disabled {
                    border: 2px solid #44475a;
                    color: #44475a;
                }
            """)
            self.download_button.clicked.connect(self.add_to_download_queue)

            # Create horizontal layout for buttons (side by side) - no borders
            from PySide6.QtWidgets import QHBoxLayout, QWidget
            buttons_widget = QWidget()
            buttons_widget.setStyleSheet("QWidget { border: none; background: transparent; }")
            buttons_layout = QHBoxLayout(buttons_widget)
            buttons_layout.setContentsMargins(0, 0, 0, 0)
            buttons_layout.setSpacing(10)
            buttons_layout.addWidget(self.play_button)
            buttons_layout.addWidget(self.download_button)

            # Add the buttons widget to the main layout
            layout.addWidget(buttons_widget)

            # Add stretch to push everything to the top
            layout.addStretch()

            # Store references to important elements
            self.streams_info_widget = streams_tab

            # Initialize empty state for all checkboxes
            self.quality_checkboxes = []
            self.audio_checkboxes = []
            self.subtitle_checkboxes = []

            print("✅ Created OSN-style streams info widget")
            print(f"✅ Quality group created: {hasattr(self, 'quality_group')}")
            print(f"✅ Episode label created: {hasattr(self, 'streams_episode_label')}")
            print(f"✅ Download button created: {hasattr(self, 'download_button')}")

        except Exception as e:
            error_msg = f"Error creating streams info widget: {str(e)}"
            print(f"❌ {error_msg}")

    def classify_video_quality(self, width, height):
        """Classify video quality based on resolution with proper 4K/HD separation"""
        try:
            # Special YANGO resolution mappings (exact matches first)
            if height == 960:  # 1920x960
                return '1080p'
            elif height == 812:  # 1920x812 (new format)
                return '1080p'
            elif height == 640:  # 1280x640
                return '720p'
            elif height == 542:  # 1280x542 (new format)
                return '720p'
            elif height == 512:  # 1024x512
                return '520p'
            elif height == 434:  # 1024x434 (new format)
                return '430p'
            elif height == 428:  # 854x428
                return '430p'
            elif height == 362:  # 854x362 (new format)
                return '360p'
            elif height == 320:  # 640x320
                return '320p'
            elif height == 270:  # 640x270 (new format)
                return '260p'

            # 4K Quality Classification (separate from HD) - specific resolutions first
            elif width == 3840 and height == 1608:  # Ultra-Wide 4K
                return "2160p"
            elif width == 2560 and height == 1072:  # Ultra-Wide 1440p (2K)
                return "1440p"
            elif height >= 2160:  # Standard 4K
                return "2160p"
            elif height >= 1608:  # Ultra-Wide 4K range
                return "2160p"
            elif height >= 1440:  # 2K/QHD
                return "1440p"

            # HD Quality Classification (traditional ranges)
            elif height >= 1080:  # Full HD
                return "1080p"
            elif height >= 720:  # HD
                return "720p"
            elif height >= 576:  # PAL
                return "576p"
            elif height >= 480:  # NTSC
                return "480p"
            elif height >= 430:  # Enhanced SD
                return "430p"
            elif height >= 360:  # Low quality
                return "360p"
            elif height >= 268:  # Very low quality
                return "260p"
            else:
                return f"{height}p"

        except Exception as e:
            print(f"⚠️ Error classifying video quality: {str(e)}")
            return f"{height}p"

    def is_4k_quality(self, width, height):
        """Determine if a resolution is 4K quality (separate from HD)"""
        try:
            # 4K resolutions (including Ultra-Wide)
            if width == 3840 and height == 1608:  # Ultra-Wide 4K
                return True
            elif width == 2560 and height == 1072:  # Ultra-Wide 1440p (2K)
                return True
            elif height >= 2160:  # Standard 4K
                return True
            elif height >= 1608:  # Ultra-Wide 4K range
                return True
            elif height >= 1440:  # 2K/QHD
                return True
            else:
                return False
        except Exception as e:
            print(f"⚠️ Error determining 4K quality: {str(e)}")
            return False

    def get_quality_category(self, width, height):
        """Get quality category (4K, HD, SD) for grouping"""
        try:
            if self.is_4k_quality(width, height):
                return "4K"
            elif height >= 720:
                return "HD"
            else:
                return "SD"
        except Exception as e:
            print(f"⚠️ Error getting quality category: {str(e)}")
            return "HD"

    def get_enhanced_quality_display(self, display_quality, original_resolution):
        """Get enhanced display name for quality with resolution and type info"""
        try:
            # Extract resolution if available
            if 'x' in original_resolution:
                try:
                    width, height = map(int, original_resolution.split('x'))

                    # Use the unified quality classification system
                    quality_str = self.classify_video_quality(width, height)
                    quality_category = self.get_quality_category(width, height)

                    # Determine quality type based on resolution and category
                    if width == 3840 and height == 1608:  # Ultra-Wide 4K
                        return f"2160p 4K UW ({original_resolution})"
                    elif width == 2560 and height == 1072:  # Ultra-Wide 1440p
                        return f"1440p 2K UW ({original_resolution})"
                    elif quality_category == "4K":
                        if height >= 2160:  # Standard 4K
                            return f"2160p 4K ({original_resolution})"
                        elif height >= 1608:  # Ultra-wide 4K range
                            return f"2160p 4K UW ({original_resolution})"
                        elif height >= 1440:  # 2K/QHD
                            return f"1440p 2K ({original_resolution})"
                        else:
                            return f"{quality_str} 4K ({original_resolution})"
                    elif height >= 1080:  # Full HD
                        return f"1080p FHD ({original_resolution})"
                    elif height >= 804:  # Cinema 1080p
                        return f"1080p FHD Cinema ({original_resolution})"
                    elif height >= 720:  # HD
                        return f"720p HD ({original_resolution})"
                    elif height >= 536:  # Cinema 720p
                        return f"720p HD Cinema ({original_resolution})"
                    elif height >= 576:  # PAL
                        return f"576p PAL ({original_resolution})"
                    elif height >= 480:  # NTSC
                        return f"480p NTSC ({original_resolution})"
                    elif height >= 430:  # Enhanced SD
                        return f"576p Enhanced SD ({original_resolution})"
                    elif height >= 358:  # NTSC format
                        return f"480p NTSC ({original_resolution})"
                    elif height >= 360:  # Low quality
                        return f"360p Low ({original_resolution})"
                    elif height >= 268:  # Very low quality
                        return f"360p Low ({original_resolution})"
                    else:
                        return f"{display_quality} ({original_resolution})"

                except ValueError:
                    # If resolution parsing fails, return basic format
                    return f"{display_quality} ({original_resolution})"
            else:
                # No resolution info, return basic quality
                return display_quality

        except Exception as e:
            print(f"⚠️ Error enhancing quality display: {str(e)}")
            return display_quality

    def get_enhanced_audio_display(self, audio_track):
        """Get enhanced display name for audio track with type and channel info"""
        try:
            # Parse audio track format: "Arabic - Stereo (2 channels)" or "Arabic - 5.1 Surround (6 channels)"
            if ' - ' in audio_track:
                language, audio_info = audio_track.split(' - ', 1)

                # Determine audio type and add appropriate icons/labels
                if 'Stereo' in audio_info:
                    if '2 channels' in audio_info:
                        return f"🔊 {language} - Stereo 2.0"
                    else:
                        return f"🔊 {language} - Stereo"
                elif '5.1 Surround' in audio_info or '6 channels' in audio_info:
                    return f"🎵 {language} - Dolby 5.1 Surround"
                elif '7.1' in audio_info or '8 channels' in audio_info:
                    return f"🎵 {language} - Dolby 7.1 Surround"
                elif 'Surround' in audio_info:
                    return f"🎵 {language} - Surround Sound"
                elif 'Mono' in audio_info or '1 channel' in audio_info:
                    return f"📻 {language} - Mono"
                else:
                    # Extract channel count if available
                    import re
                    channel_match = re.search(r'\((\d+) channels?\)', audio_info)
                    if channel_match:
                        channel_count = int(channel_match.group(1))
                        if channel_count == 1:
                            return f"📻 {language} - Mono"
                        elif channel_count == 2:
                            return f"🔊 {language} - Stereo 2.0"
                        elif channel_count >= 6:
                            return f"🎵 {language} - Surround {channel_count}.0"
                        else:
                            return f"🔊 {language} - {channel_count}.0"
                    else:
                        return f"🔊 {language} - {audio_info}"
            else:
                # No additional info, just return language with default icon
                return f"🔊 {audio_track}"

        except Exception as e:
            print(f"⚠️ Error enhancing audio display: {str(e)}")
            return f"🔊 {audio_track}"

    def create_quality_checkboxes(self, qualities):
        """Create OSN-style quality checkboxes matching Audio Tracks design"""
        try:
            # Clear existing checkboxes
            for checkbox in self.quality_checkboxes:
                checkbox.deleteLater()
            self.quality_checkboxes.clear()

            # Remove placeholder if exists
            for i in reversed(range(self.quality_layout.count())):
                child = self.quality_layout.itemAt(i).widget()
                if child and isinstance(child, QLabel):
                    child.deleteLater()

            # Create checkboxes for each quality with enhanced display names
            for i, quality_data in enumerate(qualities):
                # Parse quality data (format: "1080p|1920x804" or just "1080p")
                if '|' in quality_data:
                    display_quality, original_resolution = quality_data.split('|', 1)
                else:
                    display_quality = quality_data
                    original_resolution = quality_data

                # Enhanced display name with resolution info
                enhanced_display = self.get_enhanced_quality_display(display_quality, original_resolution)

                # Extract original resolution from enhanced display if not already in correct format
                if not ('x' in original_resolution and original_resolution.count('x') == 1):
                    # Try to extract resolution from enhanced display (e.g., "360p Low (640x268)" → "640x268")
                    import re
                    resolution_match = re.search(r'\((\d+x\d+)\)', enhanced_display)
                    if resolution_match:
                        original_resolution = resolution_match.group(1)
                        print(f"   📐 Extracted resolution from enhanced display: {original_resolution}")
                    else:
                        # Fallback: use display quality
                        original_resolution = display_quality
                        print(f"   📐 Using display quality as resolution: {original_resolution}")

                checkbox = QCheckBox(enhanced_display)
                checkbox.setStyleSheet("""
                    QCheckBox {
                        color: #f8f8f2;
                        font-size: 11px;
                        padding: 2px;
                    }
                    QCheckBox::indicator {
                        width: 14px;
                        height: 14px;
                    }
                    QCheckBox::indicator:unchecked {
                        background-color: #44475a;
                        border: 2px solid #6272a4;
                        border-radius: 3px;
                    }
                    QCheckBox::indicator:checked {
                        background-color: #50fa7b;
                        border: 2px solid #50fa7b;
                        border-radius: 3px;
                    }
                """)
                # Don't auto-select any quality - let user choose
                checkbox.setChecked(False)
                checkbox.quality_data = quality_data  # Store full data
                checkbox.display_quality = display_quality  # Store original display name (e.g., "1080p")
                checkbox.enhanced_display = enhanced_display  # Store enhanced display name (e.g., "2160p 4K UW (3840x1608)")
                checkbox.original_resolution = original_resolution  # Store original resolution

                # Debug: Print checkbox data
                print(f"   📊 Checkbox {i+1}: {enhanced_display}")
                print(f"      📺 Display: {display_quality}")
                print(f"      📐 Resolution: {original_resolution}")
                print(f"      📊 Data: {quality_data}")

                # Connect signal to handle exclusive selection
                checkbox.stateChanged.connect(lambda state, cb=checkbox: self.handle_quality_selection(cb, state))

                self.quality_checkboxes.append(checkbox)
                self.quality_layout.addWidget(checkbox)

            print(f"✅ Created {len(qualities)} quality checkboxes")

        except Exception as e:
            print(f"❌ Error creating quality checkboxes: {str(e)}")

    def create_audio_checkboxes(self, audio_tracks):
        """Create OSN-style audio checkboxes"""
        try:
            # Clear existing checkboxes
            for checkbox in self.audio_checkboxes:
                checkbox.deleteLater()
            self.audio_checkboxes.clear()

            # Remove placeholder if exists
            for i in reversed(range(self.audio_layout.count())):
                child = self.audio_layout.itemAt(i).widget()
                if child and isinstance(child, QLabel):
                    child.deleteLater()

            # Create checkboxes for each audio track
            for i, audio in enumerate(audio_tracks):
                # Use just the language name without "Track X" prefix
                audio_text = audio['language']

                checkbox = QCheckBox(audio_text)
                checkbox.setStyleSheet("""
                    QCheckBox {
                        color: #f8f8f2;
                        font-size: 11px;
                        padding: 2px;
                    }
                    QCheckBox::indicator {
                        width: 14px;
                        height: 14px;
                    }
                    QCheckBox::indicator:unchecked {
                        background-color: #44475a;
                        border: 2px solid #6272a4;
                        border-radius: 3px;
                    }
                    QCheckBox::indicator:checked {
                        background-color: #50fa7b;
                        border: 2px solid #50fa7b;
                        border-radius: 3px;
                    }
                """)
                # Don't auto-select any audio track - let user choose
                checkbox.setChecked(False)
                checkbox.audio_data = audio

                # Connect signal to handle multiple audio selection
                checkbox.stateChanged.connect(lambda state, cb=checkbox: self.handle_audio_selection(cb, state))

                self.audio_checkboxes.append(checkbox)
                self.audio_layout.addWidget(checkbox)

            print(f"✅ Created {len(audio_tracks)} audio checkboxes")

        except Exception as e:
            print(f"❌ Error creating audio checkboxes: {str(e)}")

    def create_subtitle_checkboxes(self, subtitle_tracks):
        """Create OSN-style subtitle checkboxes matching Audio Tracks design"""
        try:
            # Clear existing checkboxes
            for checkbox in self.subtitle_checkboxes:
                checkbox.deleteLater()
            self.subtitle_checkboxes.clear()

            # Remove placeholder if exists
            for i in reversed(range(self.subtitle_layout.count())):
                child = self.subtitle_layout.itemAt(i).widget()
                if child and isinstance(child, QLabel):
                    child.deleteLater()

            # Create checkboxes for each subtitle track with same style as audio
            for subtitle in subtitle_tracks:
                subtitle_text = f"{subtitle['language'].upper()}"

                checkbox = QCheckBox(subtitle_text)
                checkbox.setStyleSheet("""
                    QCheckBox {
                        color: #f8f8f2;
                        font-size: 11px;
                        padding: 2px;
                    }
                    QCheckBox::indicator {
                        width: 14px;
                        height: 14px;
                    }
                    QCheckBox::indicator:unchecked {
                        background-color: #44475a;
                        border: 2px solid #6272a4;
                        border-radius: 3px;
                    }
                    QCheckBox::indicator:checked {
                        background-color: #50fa7b;
                        border: 2px solid #50fa7b;
                        border-radius: 3px;
                    }
                """)
                checkbox.subtitle_data = subtitle
                self.subtitle_checkboxes.append(checkbox)
                self.subtitle_layout.addWidget(checkbox)

            print(f"✅ Created {len(subtitle_tracks)} subtitle checkboxes")

        except Exception as e:
            print(f"❌ Error creating subtitle checkboxes: {str(e)}")

    def handle_quality_selection(self, checkbox, state):
        """Handle exclusive quality selection (only one can be selected)"""
        try:
            if state == 2:  # Checked
                # Uncheck all other quality checkboxes
                for cb in self.quality_checkboxes:
                    if cb != checkbox:
                        cb.setChecked(False)

                # Show enhanced quality info
                display_quality = getattr(checkbox, 'display_quality', checkbox.text())
                enhanced_display = getattr(checkbox, 'enhanced_display', checkbox.text())
                original_resolution = getattr(checkbox, 'original_resolution', 'Unknown')
                quality_data = getattr(checkbox, 'quality_data', checkbox.text())

                print(f"✅ Selected quality: {checkbox.text()}")
                print(f"   📺 Display Quality: {display_quality}")
                print(f"   🎯 Enhanced Display: {enhanced_display}")
                print(f"   📐 Original Resolution: {original_resolution}")
                print(f"   📊 Full Quality Data: {quality_data}")
        except Exception as e:
            print(f"❌ Error handling quality selection: {str(e)}")

    def handle_audio_selection(self, checkbox, state):
        """Handle multiple audio selection (multiple can be selected)"""
        try:
            if state == 2:  # Checked
                print(f"✅ Selected audio: {checkbox.text()}")
                # Show original track info if available
                if hasattr(checkbox, 'audio_data') and 'original_track' in checkbox.audio_data:
                    original_track = checkbox.audio_data['original_track']
                    print(f"   📊 Original track: {original_track}")
            else:  # Unchecked
                print(f"❌ Deselected audio: {checkbox.text()}")

            # Print all currently selected audio tracks with enhanced info
            selected_audio = []
            selected_originals = []
            for cb in self.audio_checkboxes:
                if cb.isChecked():
                    selected_audio.append(cb.text())
                    if hasattr(cb, 'audio_data') and 'original_track' in cb.audio_data:
                        selected_originals.append(cb.audio_data['original_track'])

            print(f"🔊 Currently selected audio tracks: {selected_audio}")
            if selected_originals:
                print(f"   📊 Original formats: {selected_originals}")

        except Exception as e:
            print(f"❌ Error handling audio selection: {str(e)}")

    def handle_play(self):
        """Handle play button click - open YANGO player"""
        try:
            # Get selected quality with enhanced info
            selected_quality = "Unknown"
            selected_display_quality = "Unknown"
            selected_resolution = "Unknown"

            if hasattr(self, 'quality_checkboxes'):
                for checkbox in self.quality_checkboxes:
                    if checkbox.isChecked():
                        selected_quality = checkbox.text()
                        selected_display_quality = getattr(checkbox, 'display_quality', checkbox.text())
                        selected_resolution = getattr(checkbox, 'original_resolution', 'Unknown')
                        print(f"🎬 Playing with quality: {selected_quality}")
                        print(f"   📺 Display Quality: {selected_display_quality}")
                        print(f"   📐 Resolution: {selected_resolution}")
                        break

            if selected_quality == "Unknown":
                self.show_message("Warning", "Please select a quality option first.")
                return

            # Check if we have MPD URL
            if not hasattr(self, 'current_mpd_url') or not self.current_mpd_url:
                self.show_message("Error", "No stream URL available for playback.")
                return

            print(f"🎬 Opening YANGO player with quality: {selected_quality}")
            print(f"🔗 MPD URL: {self.current_mpd_url[:100]}...")

            # Stop any existing player first
            if hasattr(self, 'yango_player_instance') and self.yango_player_instance:
                print("🔄 Stopping existing YANGO player...")
                try:
                    self.yango_player_instance.stop()
                except Exception as e:
                    print(f"⚠️ Error stopping existing player: {e}")
                self.yango_player_instance = None

            # Get decryption keys
            keys = getattr(self, 'current_decryption_keys', [])

            # Prepare content data
            content_data = {}
            content_type = 'movie'  # Default

            # Check if this is a movie or episode
            if hasattr(self, 'current_movie_data') and self.current_movie_data:
                # This is a movie
                content_type = 'movie'
                content_data = {
                    'title': self.current_movie_data.get('title', 'Unknown Movie'),
                    'year': self.current_movie_data.get('year', 'Unknown Year'),
                    'quality': selected_quality
                }
                print(f"🎬 Playing movie: {content_data['title']} ({content_data['year']})")

            elif hasattr(self, 'current_episode_data') and self.current_episode_data:
                # This is an episode
                content_type = 'episode'
                episode_data = self.current_episode_data
                series_title = getattr(self, 'current_series_title', 'Unknown Series')
                season_number = getattr(self, 'current_season_number', 1)

                content_data = {
                    'title': episode_data.get('title', f'Episode {episode_data.get("episode_number", "Unknown")}'),
                    'series_title': series_title,
                    'season': season_number,
                    'number': episode_data.get('episode_number', 'Unknown'),
                    'quality': selected_quality
                }
                print(f"📺 Playing episode: {series_title} S{season_number:02d}E{content_data['number']:02d} - {content_data['title']}")

            # Import and create new YANGO player instance
            try:
                from .yango_player import YangoPlayer

                # Create new player instance
                self.yango_player_instance = YangoPlayer(parent=self)

                # Start YANGO player
                success = self.yango_player_instance.play(
                    mpd_url=self.current_mpd_url,
                    keys=keys,
                    content_data=content_data,
                    content_type=content_type
                )

                if success:
                    print("✅ YANGO player started successfully")
                    # Show success notification
                    if content_type == 'movie':
                        self.show_notification(f"Playing: {content_data['title']}", "🎬", "#50fa7b")
                    else:
                        self.show_notification(f"Playing: {content_data['series_title']} S{content_data['season']:02d}E{content_data['number']:02d}", "📺", "#50fa7b")
                else:
                    print("❌ Failed to start YANGO player")
                    self.show_message("Error", "Failed to start YANGO player")
                    self.yango_player_instance = None

            except Exception as e:
                print(f"❌ Error opening YANGO player: {str(e)}")
                self.show_message("Error", f"Could not open YANGO player: {str(e)}")
                self.yango_player_instance = None

        except Exception as e:
            print(f"❌ Error handling play: {str(e)}")
            self.show_message("Error", f"Error starting playback: {str(e)}")

    def cleanup_player(self):
        """Cleanup YANGO player when closing application"""
        try:
            if hasattr(self, 'yango_player_instance') and self.yango_player_instance:
                print("🔄 Cleaning up YANGO player...")
                self.yango_player_instance.stop()
                self.yango_player_instance = None
                print("✅ YANGO player cleaned up")
        except Exception as e:
            print(f"⚠️ Error cleaning up YANGO player: {e}")

    def get_color(self, hex_color):
        """Helper method to create QColor from hex string"""
        from PySide6.QtGui import QColor
        return QColor(hex_color)

    # Downloads Management Functions
    # ///////////////////////////////////////////////////////////////

    def add_download_to_queue(self, title, season, episode, quality, display_quality=None, audio_track=None, subtitle=None):
        """Add download to the organized downloads table"""
        try:
            # Create download item
            download_item = {
                'title': title,
                'season': season,
                'episode': episode,
                'quality': quality,  # This is the actual resolution for download
                'display_quality': display_quality or quality,  # This is for UI display
                'audio_track': audio_track,
                'subtitle': subtitle,
                'status': 'Queued',
                'progress': 0,
                'mpd_url': getattr(self, 'current_mpd_url', ''),
                'decryption_keys': getattr(self, 'current_decryption_keys', []),
                'episode_data': getattr(self, 'current_episode_data', {})
            }

            # Add to downloads table
            self.add_download_to_table(download_item)

            # Update stats
            self.update_downloads_stats()

            # Switch to downloads tab
            if hasattr(self, 'content_tabs'):
                for i in range(self.content_tabs.count()):
                    if "Downloads" in self.content_tabs.tabText(i):
                        self.content_tabs.setCurrentIndex(i)
                        break

            print(f"✅ Added to downloads: {title} {season} {episode} ({quality})")

        except Exception as e:
            print(f"❌ Error adding to downloads: {str(e)}")
            self.show_notification(f"Error adding to downloads: {str(e)}", "❌", "#ff5555")

    def add_download_to_table(self, download_item):
        """Add download item to the organized table"""
        try:
            from PySide6.QtWidgets import QTableWidgetItem, QProgressBar
            from PySide6.QtCore import Qt

            row_count = self.downloads_table.rowCount()
            self.downloads_table.insertRow(row_count)

            # Title
            title_item = QTableWidgetItem(download_item['title'])
            title_item.setTextAlignment(Qt.AlignCenter)
            self.downloads_table.setItem(row_count, 0, title_item)

            # Season
            season_item = QTableWidgetItem(str(download_item['season']))
            season_item.setTextAlignment(Qt.AlignCenter)
            self.downloads_table.setItem(row_count, 1, season_item)

            # Episode
            episode_item = QTableWidgetItem(str(download_item['episode']))
            episode_item.setTextAlignment(Qt.AlignCenter)
            self.downloads_table.setItem(row_count, 2, episode_item)

            # Quality - use basic quality format (e.g., "2160p") instead of enhanced display
            # Extract basic quality from enhanced display or use quality directly
            basic_quality = download_item['quality']
            if 'display_quality' in download_item:
                enhanced_display = download_item['display_quality']
                # Extract basic quality from enhanced display (e.g., "2160p 4K UW (3840x1608)" → "2160p")
                if enhanced_display and ' ' in enhanced_display:
                    basic_quality = enhanced_display.split(' ')[0]  # Get first part (e.g., "2160p")
                else:
                    basic_quality = enhanced_display

            quality_item = QTableWidgetItem(basic_quality)
            quality_item.setTextAlignment(Qt.AlignCenter)
            self.downloads_table.setItem(row_count, 3, quality_item)

            # Progress bar with organized styling
            progress_bar = QProgressBar()
            progress_bar.setValue(0)
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: 1px solid #44475a;
                    border-radius: 4px;
                    text-align: center;
                    background-color: #282a36;
                    color: #f8f8f2;
                    font-weight: bold;
                    font-size: 11px;
                    height: 24px;
                    min-width: 120px;
                }
                QProgressBar::chunk {
                    background-color: #50fa7b;
                    border-radius: 3px;
                }
            """)
            self.downloads_table.setCellWidget(row_count, 4, progress_bar)

            # Status
            status_item = QTableWidgetItem("Queued")
            status_item.setTextAlignment(Qt.AlignCenter)
            # Set text color using QBrush
            from PySide6.QtGui import QBrush, QColor
            status_item.setForeground(QBrush(QColor("#ffb86c")))
            self.downloads_table.setItem(row_count, 5, status_item)

            # Actions button - centered in cell
            actions_btn = QPushButton("❌")
            actions_btn.setStyleSheet("""
                QPushButton {
                    background-color: #ff5555;
                    color: #f8f8f2;
                    border: 1px solid #ff5555;
                    border-radius: 3px;
                    padding: 2px 4px;
                    font-size: 10px;
                    font-weight: bold;
                    min-width: 25px;
                    max-width: 30px;
                    min-height: 20px;
                    max-height: 25px;
                }
                QPushButton:hover {
                    background-color: #ff7979;
                    border: 1px solid #ff7979;
                }
                QPushButton:pressed {
                    background-color: #e84393;
                }
            """)
            actions_btn.clicked.connect(lambda: self.remove_download_row(row_count))

            # Create a widget to center the button
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.addWidget(actions_btn)
            actions_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
            actions_layout.setContentsMargins(0, 0, 0, 0)

            self.downloads_table.setCellWidget(row_count, 6, actions_widget)

            # Set row height
            self.downloads_table.setRowHeight(row_count, 45)

            # Store download data
            self.download_items.append(download_item)

            print(f"✅ Added to downloads table: {download_item['title']}")

        except Exception as e:
            print(f"❌ Error adding to downloads table: {str(e)}")

    def remove_download_row(self, row):
        """Remove download row from table"""
        try:
            if row < self.downloads_table.rowCount():
                self.downloads_table.removeRow(row)
                if row < len(self.download_items):
                    self.download_items.pop(row)
                self.update_downloads_stats()
                print(f"✅ Removed download row {row}")
        except Exception as e:
            print(f"❌ Error removing download row: {str(e)}")

    def update_downloads_stats(self):
        """Update downloads statistics"""
        try:
            total_count = len(self.download_items)
            queued_count = len([item for item in self.download_items if item['status'] == 'Queued'])
            downloading_count = len([item for item in self.download_items if item['status'] == 'Downloading'])
            completed_count = len([item for item in self.download_items if item['status'] == 'Completed'])

            if total_count == 0:
                self.downloads_stats.setText("0 items in queue")
            else:
                self.downloads_stats.setText(f"{total_count} items • {queued_count} queued • {downloading_count} downloading • {completed_count} completed")

        except Exception as e:
            print(f"❌ Error updating downloads stats: {str(e)}")

    def start_all_downloads(self):
        """Start all queued downloads using real downloader"""
        try:
            if not hasattr(self, 'downloader'):
                self.show_notification("Downloader not initialized", "❌", "#ff5555")
                return

            queued_rows = []
            download_items_to_process = []

            for row in range(self.downloads_table.rowCount()):
                status_item = self.downloads_table.item(row, 5)
                if status_item and status_item.text() == "Queued":
                    queued_rows.append(row)

                    # Get download item data
                    if row < len(self.download_items):
                        download_item = self.download_items[row].copy()
                        download_item['row_index'] = row  # Store row index for progress updates
                        download_items_to_process.append(download_item)

            if not queued_rows:
                self.show_notification("No queued downloads found", "ℹ️", "#ffb86c")
                return

            # Update status to downloading
            for row in queued_rows:
                status_item = self.downloads_table.item(row, 5)
                if status_item:
                    status_item.setText("Downloading")
                    from PySide6.QtGui import QBrush, QColor
                    status_item.setForeground(QBrush(QColor("#50fa7b")))

                # Update download item data
                if row < len(self.download_items):
                    self.download_items[row]['status'] = 'Downloading'

            # Add downloads to downloader queue
            for download_item in download_items_to_process:
                # Prepare download data for downloader
                # Handle subtitle tracks properly - ensure it's a list
                subtitle_tracks = download_item.get('subtitle', [])
                if isinstance(subtitle_tracks, str):
                    # If it's a single string, convert to list
                    subtitle_tracks = [subtitle_tracks] if subtitle_tracks else []
                elif not isinstance(subtitle_tracks, list):
                    # If it's not a list, make it an empty list
                    subtitle_tracks = []

                downloader_item = {
                    'title': download_item.get('title', 'Unknown'),
                    'season': download_item.get('season', 'S1'),
                    'episode': download_item.get('episode', 'E1'),
                    'quality': download_item.get('quality', '720p'),
                    'original_resolution': download_item.get('original_resolution', ''),  # Add original resolution
                    'mpd_url': download_item.get('mpd_url', ''),
                    'audio_tracks': download_item.get('audio_track', []),
                    'subtitle_tracks': subtitle_tracks,  # Use processed subtitle tracks
                    'row_index': download_item.get('row_index', 0),
                    'episode_data': download_item.get('episode_data', {}),
                    'episode_content_id': download_item.get('episode_content_id', ''),
                    'episode_number': download_item.get('episode_number', 'Unknown')
                }

                # Debug: Print download item data
                print(f"🔍 Download item data:")
                print(f"   📺 Title: {downloader_item['title']}")
                print(f"   📺 Season: {downloader_item['season']}")
                print(f"   📺 Episode: {downloader_item['episode']}")
                print(f"   📺 Quality: {downloader_item['quality']}")
                print(f"   📐 Original Resolution: {downloader_item['original_resolution']}")
                print(f"   🔗 MPD URL: {downloader_item['mpd_url'][:100] if downloader_item['mpd_url'] else 'None'}...")
                print(f"   🔊 Audio: {downloader_item['audio_tracks']}")
                print(f"   📝 Subtitles: {downloader_item['subtitle_tracks']}")

                self.downloader.add_download(downloader_item)

            # Reset stopping flag to allow new downloads
            self.downloader.reset_stopping_flag()

            # Start downloads
            self.downloader.start_downloads()

            self.update_downloads_stats()
            print(f"✅ Started {len(queued_rows)} real downloads")

        except Exception as e:
            print(f"❌ Error starting downloads: {str(e)}")
            self.show_notification(f"Error starting downloads: {str(e)}", "❌", "#ff5555")

    def pause_downloads(self):
        """Pause all active downloads"""
        try:
            self.show_notification("Download pause functionality - to be implemented", "⏸️", "#ffb86c")
            print("⏸️ Pause downloads - functionality to be implemented")
        except Exception as e:
            print(f"❌ Error pausing downloads: {str(e)}")

    def stop_downloads(self):
        """Force stop all downloads - prevents automatic restart"""
        try:
            if hasattr(self, 'downloader') and self.downloader:
                # Use the downloader's force stop method
                self.downloader.force_stop_downloads()

                # Update all downloading items to stopped
                for row in range(self.downloads_table.rowCount()):
                    status_item = self.downloads_table.item(row, 5)
                    if status_item and status_item.text() in ["Downloading", "Processing"]:
                        status_item.setText("Stopped")
                        status_item.setBackground(QBrush(QColor("#ff5555")))

                        # Reset progress
                        progress_item = self.downloads_table.item(row, 4)
                        if progress_item:
                            progress_item.setText("0%")

                self.update_downloads_stats()
                self.show_notification("All downloads stopped forcefully", "🛑", "#ff5555")
                print("🛑 All downloads stopped forcefully")
            else:
                self.show_notification("No active downloads to stop", "ℹ️", "#ffb86c")
                print("ℹ️ No active downloads to stop")

        except Exception as e:
            print(f"❌ Error stopping downloads: {str(e)}")
            self.show_notification(f"Error stopping downloads: {str(e)}", "❌", "#ff5555")

    def clear_completed_downloads(self):
        """Clear all completed downloads"""
        try:
            rows_to_remove = []
            for row in range(self.downloads_table.rowCount()):
                status_item = self.downloads_table.item(row, 5)
                if status_item and status_item.text() == "Completed":
                    rows_to_remove.append(row)

            # Remove rows in reverse order
            for row in reversed(rows_to_remove):
                self.downloads_table.removeRow(row)
                if row < len(self.download_items):
                    self.download_items.pop(row)

            self.update_downloads_stats()
            self.show_notification(f"Cleared {len(rows_to_remove)} completed downloads", "🗑️", "#8be9fd")
            print(f"✅ Cleared {len(rows_to_remove)} completed downloads")

        except Exception as e:
            print(f"❌ Error clearing completed downloads: {str(e)}")

    def clear_all_downloads(self):
        """Clear all downloads"""
        try:
            row_count = self.downloads_table.rowCount()
            self.downloads_table.setRowCount(0)
            self.download_items.clear()
            self.update_downloads_stats()
            self.show_notification(f"Cleared all {row_count} downloads", "🗑️", "#ff5555")
            print(f"✅ Cleared all {row_count} downloads")

        except Exception as e:
            print(f"❌ Error clearing all downloads: {str(e)}")

    def update_download_progress(self, progress, status):
        """Update download progress for current download with smooth progression"""
        try:
            # Find the currently downloading item
            for row in range(self.downloads_table.rowCount()):
                status_item = self.downloads_table.item(row, 5)
                if status_item and status_item.text() == "Downloading":
                    # Update progress bar with smooth progression
                    progress_bar = self.downloads_table.cellWidget(row, 4)
                    if progress_bar:
                        current_value = progress_bar.value()
                        # Ensure progress never goes backwards
                        new_progress = max(progress, current_value)
                        # Cap at 100%
                        new_progress = min(new_progress, 100)
                        progress_bar.setValue(new_progress)

                    # Update status text if provided
                    if status:
                        print(f"📊 Download progress: {new_progress}% - {status}")
                    break

        except Exception as e:
            print(f"❌ Error updating download progress: {str(e)}")

    def handle_download_completed(self, file_path, success):
        """Handle download completion"""
        try:
            # Find the currently downloading item
            for row in range(self.downloads_table.rowCount()):
                status_item = self.downloads_table.item(row, 5)
                if status_item and status_item.text() == "Downloading":
                    # Update progress bar to 100%
                    progress_bar = self.downloads_table.cellWidget(row, 4)
                    if progress_bar:
                        progress_bar.setValue(100)

                    # Update status
                    if success:
                        status_item.setText("Completed")
                        from PySide6.QtGui import QBrush, QColor
                        status_item.setForeground(QBrush(QColor("#8be9fd")))

                        # Update download item data
                        if row < len(self.download_items):
                            self.download_items[row]['status'] = 'Completed'
                            self.download_items[row]['file_path'] = file_path

                        print(f"✅ Download completed: {file_path}")
                    else:
                        status_item.setText("Failed")
                        from PySide6.QtGui import QBrush, QColor
                        status_item.setForeground(QBrush(QColor("#ff5555")))

                        # Update download item data
                        if row < len(self.download_items):
                            self.download_items[row]['status'] = 'Failed'

                        print("❌ Download failed")

                    self.update_downloads_stats()
                    break

        except Exception as e:
            print(f"❌ Error handling download completion: {str(e)}")

    def handle_download_error(self, error_message):
        """Handle download error"""
        try:
            # Find the currently downloading item
            for row in range(self.downloads_table.rowCount()):
                status_item = self.downloads_table.item(row, 5)
                if status_item and status_item.text() == "Downloading":
                    # Update status to failed
                    status_item.setText("Failed")
                    from PySide6.QtGui import QBrush, QColor
                    status_item.setForeground(QBrush(QColor("#ff5555")))

                    # Update download item data
                    if row < len(self.download_items):
                        self.download_items[row]['status'] = 'Failed'
                        self.download_items[row]['error'] = error_message

                    self.update_downloads_stats()
                    break

            print(f"❌ Download error: {error_message}")

        except Exception as e:
            print(f"❌ Error handling download error: {str(e)}")

    def fetch_episode_streams_for_download(self, episode_content_id, download_item):
        """Fetch episode streams for download processing"""
        try:
            print(f"🔄 Fetching streams for episode content ID: {episode_content_id}")

            # Store the download item for later use
            self.pending_download_item = download_item

            # Use the existing API to fetch episode streams
            if hasattr(self.main_window, 'yango_api'):
                # Set current episode data for the API call
                self.current_episode_data = {
                    'content_id': episode_content_id,
                    'episode_number': download_item.get('episode_number', 'Unknown'),
                    'availability_status': 'PUBLISHED',
                    'episode_title': f"Episode {download_item.get('episode_number', 'Unknown')}"
                }

                # Fetch episode streams using get_player_base_info
                streams_data = self.main_window.yango_api.get_player_base_info(episode_content_id)

                if streams_data:
                    # Process streams data directly
                    self.handle_episode_streams_for_download(streams_data)
                else:
                    print("❌ No streams data returned")
                    self.downloader.download_error.emit(f"No streams found for episode {episode_content_id}")

                print(f"✅ Requested streams for episode {download_item.get('episode_number', 'Unknown')}")
            else:
                print("❌ YANGO API not available")
                self.downloader.download_error.emit(f"API not available for episode {episode_content_id}")

        except Exception as e:
            print(f"❌ Error fetching episode streams for download: {str(e)}")
            # Reset the processing flag on error
            if hasattr(self, 'downloader'):
                self.downloader.processing_episode_streams = False
            self.downloader.download_error.emit(f"Error fetching streams: {str(e)}")

    def handle_episode_streams_for_download(self, streams_data):
        """Handle episode streams fetched for download processing"""
        try:
            if not hasattr(self, 'pending_download_item'):
                print("❌ No pending download item found")
                return

            download_item = self.pending_download_item
            episode_number = download_item.get('episode_number', 'Unknown')

            print(f"🔄 Processing streams data for episode {episode_number}")

            # Extract streams information from the response (same as display_episode_streams)
            content_data = streams_data.get('data', {}).get('content', {})

            if not content_data:
                print("❌ No content data found in streams response")
                self.downloader.download_error.emit("No content data found for episode")
                return

            # Get episode OTT data
            episode_ott = content_data.get('episodeOtt', {})
            online_streams = episode_ott.get('onlineStreams', {})

            if not online_streams:
                print(f"❌ No online streams available for episode {episode_number}")
                self.downloader.download_error.emit(f"No online streams for episode {episode_number}")
                return

            # Extract streams
            streams = online_streams.get('streams', [])

            if not streams:
                print(f"❌ No streams found for episode {episode_number}")
                self.downloader.download_error.emit(f"No streams found for episode {episode_number}")
                return

            print(f"🎬 Found {len(streams)} streams for episode {episode_number}")

            # Process streams to extract MPD URL and keys based on selected quality
            mpd_url = None
            decryption_keys = []
            selected_quality = download_item.get('quality', '')

            print(f"🎯 Looking for quality: {selected_quality}")

            # First, try to find stream with matching quality
            target_stream = None
            for i, stream in enumerate(streams):
                stream_uri = stream.get('uri', '')
                if 'manifest.mpd' in stream_uri:
                    # Extract quality information from stream
                    stream_meta = stream.get('streamMeta', {})
                    video_metas = stream_meta.get('videoMetas', [])

                    for video_meta in video_metas:
                        height = video_meta.get('height', 0)
                        width = video_meta.get('width', 0)

                        # Use the unified quality classification system
                        stream_quality = self.classify_video_quality(width, height)

                        print(f"🔍 Stream {i+1}: {width}x{height} = {stream_quality}")

                        # Check if this matches the selected quality
                        if stream_quality == selected_quality:
                            target_stream = stream
                            print(f"✅ Found matching quality stream: {stream_quality}")
                            break

                    if target_stream:
                        break

            # If no exact match found, use the first available stream (fallback)
            if not target_stream:
                print(f"⚠️ No exact match for {selected_quality}, using first available stream")
                for stream in streams:
                    if 'manifest.mpd' in stream.get('uri', ''):
                        target_stream = stream
                        break

            # Extract MPD URL and keys from the selected stream
            if target_stream:
                mpd_url = target_stream.get('uri', '')
                print(f"🔍 Using stream with MPD URL: {mpd_url[:100]}...")

                # Extract DRM keys for this episode
                episode_content_id = download_item.get('episode_content_id', '')
                keys = self.extract_drm_keys_for_episode(mpd_url, episode_content_id, episode_number)
                if keys:
                    decryption_keys.extend(keys)

            if not mpd_url:
                print(f"❌ No MPD URL found for episode {episode_number}")
                self.downloader.download_error.emit(f"No MPD URL found for episode {episode_number}")
                return

            if not decryption_keys:
                print(f"❌ No decryption keys found for episode {episode_number}")
                self.downloader.download_error.emit(f"No decryption keys found for episode {episode_number}")
                return

            # Update download item with episode-specific data
            download_item['mpd_url'] = mpd_url
            download_item['decryption_keys'] = decryption_keys

            print(f"✅ Updated download item with episode streams:")
            print(f"   🔗 MPD URL: {mpd_url[:100]}...")
            print(f"   🔑 Keys: {len(decryption_keys)} keys")

            # Reset the processing flag
            self.downloader.processing_episode_streams = False

            # Update the download item in the queue (it should be at the front)
            if self.downloader.download_queue and self.downloader.download_queue[0].get('episode_content_id') == episode_content_id:
                # Update the first item in queue with the new data
                self.downloader.download_queue[0].update({
                    'mpd_url': mpd_url,
                    'decryption_keys': decryption_keys
                })
                print(f"✅ Updated queue item with episode streams data")
            else:
                # Add the updated download item back to the queue
                download_item['mpd_url'] = mpd_url
                download_item['decryption_keys'] = decryption_keys
                self.downloader.download_queue.insert(0, download_item)
                print(f"✅ Added updated item back to queue")

            # Continue processing the download queue
            print("🔄 Continuing download processing...")
            self.downloader.continue_downloads()

            # Clear pending item
            delattr(self, 'pending_download_item')

        except Exception as e:
            print(f"❌ Error handling episode streams for download: {str(e)}")
            # Reset the processing flag on error
            if hasattr(self, 'downloader'):
                self.downloader.processing_episode_streams = False
            self.downloader.download_error.emit(f"Error processing episode streams: {str(e)}")

    def extract_drm_keys_for_episode(self, mpd_url, episode_content_id, episode_number):
        """Extract DRM keys for a specific episode"""
        try:
            print(f"🔑 Extracting real DRM keys for episode {episode_number}")

            # Use YangoDRM for extracting keys
            print(f"🔍 Extracting PSSH from MPD: {mpd_url[:100]}...")

            pssh = self.get_pssh_original_method(mpd_url)
            if not pssh:
                print(f"❌ Failed to extract PSSH for episode {episode_number}")
                return []

            print(f"✅ PSSH extracted: {pssh[:50]}...")

            # Get request parameters from the episode streams data
            request_params = self.get_request_params_for_episode(episode_content_id)
            if not request_params:
                print(f"❌ Failed to get request parameters for episode {episode_number}")
                return []

            # Use YangoDRM to extract keys
            from .yango_drm import YangoDRM
            drm_handler = YangoDRM()

            keys = drm_handler.get_decryption_key(pssh, request_params)
            if keys:
                print(f"✅ Successfully extracted {len(keys)} DRM keys")
                for key in keys:
                    print(f"   🔑 Real DRM Key: [+] Key: {key}")

                # Save keys to file
                self.save_keys_to_file(keys, f"Episode {episode_number}: Episode {episode_number}")

                return keys
            else:
                print(f"❌ Failed to extract DRM keys for episode {episode_number}")
                return []

        except Exception as e:
            print(f"❌ Error extracting DRM keys for episode {episode_number}: {str(e)}")
            return []

    def get_pssh_original_method(self, mpd_url):
        """Extract PSSH from MPD using the original YANGO method"""
        try:
            import xmltodict

            response = requests.get(mpd_url)
            response.raise_for_status()

            # تحليل محتوى MPD
            pssh_loads = xmltodict.parse(response.content)
            mpd = pssh_loads.get('MPD', {})

            # التحقق من وجود Period
            period = mpd.get('Period', {})
            if not period:
                print("❌ No Period found in MPD.")
                return None

            # التعامل مع Period كقائمة
            if isinstance(period, list):
                if not period:  # التحقق من أن القائمة ليست فارغة
                    print("❌ Empty Period list in MPD.")
                    return None
                period = period[0]

            # التحقق من وجود AdaptationSet
            adaptation_set = period.get('AdaptationSet', [])
            if not adaptation_set:
                print("❌ No AdaptationSet found in MPD.")
                return None

            # التعامل مع AdaptationSet كقائمة
            if isinstance(adaptation_set, list):
                if not adaptation_set:  # التحقق من أن القائمة ليست فارغة
                    print("❌ Empty AdaptationSet list in MPD.")
                    return None
                adaptation_set = adaptation_set[0]

            # التحقق من وجود ContentProtection
            content_protection = adaptation_set.get('ContentProtection', [])
            if not content_protection:
                print("❌ No ContentProtection found in MPD.")
                return None

            # التحقق من وجود PSSH
            pssh = None
            if isinstance(content_protection, list) and len(content_protection) > 2:
                pssh = content_protection[2].get('cenc:pssh', None)
                if not pssh:
                    # محاولة البحث في جميع عناصر ContentProtection
                    for cp in content_protection:
                        if cp.get('cenc:pssh'):
                            pssh = cp.get('cenc:pssh')
                            break
            else:
                # إذا لم يكن قائمة، تحقق من وجود PSSH مباشرة
                pssh = content_protection.get('cenc:pssh', None)

            if not pssh:
                print("❌ No PSSH found in ContentProtection.")
                return None

            return pssh

        except requests.exceptions.RequestException as e:
            print(f"❌ Failed to fetch MPD: {e}")
            return None
        except Exception as e:
            print(f"❌ Error parsing MPD: {e}")
            import traceback
            traceback.print_exc()  # طباعة التفاصيل الكاملة للخطأ
            return None

    def get_request_params_for_episode(self, episode_content_id):
        """Get request parameters for DRM key extraction for a specific episode"""
        try:
            print(f"🔍 Getting request parameters for episode content ID: {episode_content_id}")

            # Use the API to get player base info which contains request params
            if hasattr(self.main_window, 'yango_api'):
                streams_data = self.main_window.yango_api.get_player_base_info(episode_content_id)

                if not streams_data:
                    print("❌ No streams data returned for request params")
                    return None

                # Extract request params from streams data
                content_data = streams_data.get('data', {}).get('content', {})
                episode_ott = content_data.get('episodeOtt', {})
                online_streams = episode_ott.get('onlineStreams', {})
                streams = online_streams.get('streams', [])

                if not streams:
                    print("❌ No streams found for request params")
                    return None

                # Look for DRM stream with request params
                for stream in streams:
                    stream_meta = stream.get('streamMeta', {})
                    drm_config = stream_meta.get('drmConfig', {})
                    request_params = drm_config.get('requestParams', {})

                    if request_params:
                        print(f"✅ Found request parameters for episode")
                        return request_params

                print("❌ No request parameters found in any stream")
                return None
            else:
                print("❌ YANGO API not available for request params")
                return None

        except Exception as e:
            print(f"❌ Error getting request parameters: {str(e)}")
            return None

    def add_settings_button(self):
        """Add settings button to the main UI"""
        try:
            # Find the main layout or create a toolbar
            if hasattr(self.widgets, 'main_layout'):
                main_layout = self.widgets.main_layout
            else:
                return

            # Create settings button
            self.settings_btn = QPushButton("⚙️ Settings")
            self.settings_btn.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                stop: 0 #6272a4, stop: 1 #44475a);
                    color: #f8f8f2;
                    border: 2px solid #6272a4;
                    padding: 8px 15px;
                    border-radius: 6px;
                    font-weight: bold;
                    font-size: 12px;
                    min-width: 100px;
                    max-width: 120px;
                    margin: 5px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                stop: 0 #7282b4, stop: 1 #54576a);
                    border: 2px solid #7282b4;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                                stop: 0 #44475a, stop: 1 #34374a);
                    border: 2px solid #44475a;
                }
            """)
            self.settings_btn.clicked.connect(self.open_settings)

            # Add to the top of the layout
            main_layout.insertWidget(0, self.settings_btn)
            print("✅ Settings button added to main UI")

        except Exception as e:
            print(f"❌ Error adding settings button: {e}")

    def open_settings(self):
        """Open settings dialog"""
        try:
            from .yango_settings import YangoSettingsWidget
            from PySide6.QtWidgets import QDialog, QVBoxLayout

            # Create settings dialog without parent first
            settings_dialog = QDialog()
            settings_dialog.setWindowTitle("YANGO Settings")
            settings_dialog.setModal(True)
            settings_dialog.resize(600, 500)
            settings_dialog.setStyleSheet("""
                QDialog {
                    background-color: #282a36;
                    color: #f8f8f2;
                }
            """)

            # Create layout for dialog
            dialog_layout = QVBoxLayout(settings_dialog)
            dialog_layout.setContentsMargins(0, 0, 0, 0)

            # Create settings widget
            settings_widget = YangoSettingsWidget(self.settings_manager)
            dialog_layout.addWidget(settings_widget)

            # Show dialog
            settings_dialog.exec()
            print("✅ Settings dialog opened")

        except Exception as e:
            print(f"❌ Error opening settings: {e}")
            self.show_message("Error", f"Could not open settings: {str(e)}")

    def show_message(self, title, message):
        """Show a message dialog"""
        try:
            from PySide6.QtWidgets import QMessageBox
            msg_box = QMessageBox()
            msg_box.setWindowTitle(title)
            msg_box.setText(message)
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #282a36;
                    color: #f8f8f2;
                }
                QMessageBox QPushButton {
                    background-color: #6272a4;
                    color: #f8f8f2;
                    border: none;
                    padding: 8px 15px;
                    border-radius: 4px;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #50fa7b;
                    color: #282a36;
                }
            """)
            msg_box.exec()
        except Exception as e:
            print(f"❌ Error showing message: {e}")
            print(f"{title}: {message}")


