﻿LOG 2025/06/17
Save Path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\Logs
Task Start: 2025/06/17 01:30:14
Task CommandLine: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\N_m3u8DL-RE.exe https://osn-video.anghcdn.co/public/156059-57919-PR681515-BX-AS045373-284231-0ac11ca51c5702c517c702b8ec20aa68-1749797408/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD01NzkxOSZleHBpcnk9MTc1MDE1NjIxMiZzaWduYXR1cmU9YmMyYjZjNTE0OTZkYjBiM2ZjMmM2ZGM1MzIyNTRkNDUxNmY2NzlhNSZzdHJlYW0taWQ9MTI5MDA3JnVzZXItaWQ9MTUyMTE3ODk2 -mt --select-video id=0f4b6d54-993e-4d5b-952f-b1adf7afb0ec --select-audio lang=ar|tr:for=all --select-subtitle lang=en|ar --tmp-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\cache --save-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\cache --save-name "Al Mushardoon S01E34.720p.OSN+.WEB-DL.H264.AAC" --decryption-binary-path=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\mp4decrypt.exe --key-text-file=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\KEYS\KEYS.txt --log-level OFF

01:30:14.510 EXTRA: ffmpeg => C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\ffmpeg.exe
01:30:14.867 EXTRA: VideoFilter => GroupIdReg: 0f4b6d54-993e-4d5b-952f-b1adf7afb0ec For: best
01:30:14.868 EXTRA: AudioFilter => LanguageReg: ar|tr For: all
01:30:14.868 EXTRA: SubtitleFilter => LanguageReg: en|ar For: best
