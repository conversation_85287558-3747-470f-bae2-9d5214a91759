/* ==========================================================================
   Custom JW Player Skin: TOD Skin
   Contains custom styles for control bar, menus, tooltips, etc.
   Ensure NO global overrides for html, body height/overflow exist here.
   ========================================================================== */

/* General Skin Settings (If needed, e.g., base font color for controls) */
.jwplayer.jw-skin-tod { /* Assuming you add 'jw-skin-tod' via config or JS */
    /* font-family: sans-serif; */ /* Set base font if needed */
}

/* Control Bar Background */
.jw-controlbar { /* Target directly, assuming skin class might not be applied */
    background: linear-gradient(0deg, rgba(10, 2, 96, 0.85) 0%, rgba(10, 2, 96, 0.4) 70%, rgba(10, 2, 96, 0) 100%) !important;
}

/* Control Bar Buttons and Text Color */
.jw-controlbar .jw-button-color,
.jw-controlbar .jw-icon-playback, /* Target specific icons */
.jw-controlbar .jw-icon-volume,
.jw-controlbar .jw-icon-fullscreen,
.jw-controlbar .jw-icon-settings,
.jw-controlbar .jw-icon-cc,
.jw-controlbar .jw-icon-next,
.jw-controlbar .jw-icon-prev,
.jw-controlbar .jw-icon-rewind,
.jw-controlbar .jw-text /* Includes elapsed/duration time */
 {
    color: #ffffff;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.4); /* Subtle text shadow */
}
/* Ensure hover/focus states are also visible if needed */
.jw-controlbar [role="button"]:hover .jw-button-color,
.jw-controlbar [role="button"]:focus .jw-button-color {
     color: #e0e0e0; /* Slightly lighter on hover/focus */
}


/* Time Slider Styles */
.jw-slider-time {
    /* Add any specific styles for the time slider container if needed */
}

/* Progress Bar (Played portion) */
/* Use the simpler selector confirmed to work */
.jw-slider-time .jw-progress {
    background-color: rgba(48, 77, 232, 0.85) !important; /* Blue color */
    /* Add transition for smoother seeking? */
    /* transition: width 0.1s linear; */
}

/* Buffer Bar (Loaded portion) */
.jw-slider-time .jw-buffer {
    background-color: rgba(255, 255, 255, 0.3); /* Default light buffer color */
}

/* Rail (Background of the slider) */
.jw-slider-time .jw-rail {
    background-color: rgba(255, 255, 255, 0.2); /* Default light rail color */
}

/* Knob (Seek handle) */
.jw-slider-time .jw-knob {
    background-color: #ffffff; /* White knob */
    border-radius: 50%;
    width: 12px; /* Adjust size if needed */
    height: 12px; /* Adjust size if needed */
    box-shadow: 0 0 3px rgba(0,0,0,0.5);
}

/* Volume Slider Styles (Optional - Apply similar colors) */
.jw-slider-volume .jw-progress {
     background-color: rgba(48, 77, 232, 0.85) !important; /* Match time progress */
}
.jw-slider-volume .jw-rail {
     background-color: rgba(255, 255, 255, 0.2);
}
.jw-slider-volume .jw-knob {
     background-color: #ffffff;
}

/* Settings Menu */
.jw-settings-menu {
    background-color: rgba(10, 2, 96, 0.9); /* Slightly more opaque */
    color: #ffffff;
    border-radius: 4px; /* Optional rounded corners */
}

.jw-settings-menu .jw-submenu-topbar {
    box-shadow: 0 2px 9px 0 rgba(13, 13, 51, 0.5);
    background-color: rgba(10, 2, 96, 0.75); /* Slightly different bg for header */
    color: #ffffff;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Settings Menu Items */
.jw-settings-menu .jw-menu button,
.jw-settings-menu .jw-menu .jw-settings-item {
     color: #f0f0f0;
     padding: 10px 15px; /* Adjust padding */
     border-bottom: 1px solid rgba(255, 255, 255, 0.1); /* Separator */
}
.jw-settings-menu .jw-menu button:last-child,
.jw-settings-menu .jw-menu .jw-settings-item:last-child {
    border-bottom: none; /* Remove border from last item */
}

.jw-settings-menu .jw-menu button:hover,
.jw-settings-menu .jw-menu .jw-settings-item:hover {
    background-color: rgba(25, 15, 140, 0.7);
    color: #ffffff;
}
.jw-settings-menu .jw-menu button[aria-checked="true"] {
    background-color: rgba(48, 77, 232, 0.6);
    color: #ffffff;
    font-weight: bold; /* Indicate selection */
}


/* Right-Click Menu */
.jwplayer .jw-rightclick .jw-rightclick-list { /* Target the list itself for background */
    background-color: rgba(10, 2, 96, 0.9);
     border-radius: 4px;
     overflow: hidden; /* Clip items to rounded corners */
     padding: 5px 0; /* Padding around items */
}
.jwplayer .jw-rightclick .jw-rightclick-list .jw-rightclick-item {
    /* Remove individual item background/border if list has background */
    /* background-color: rgba(10, 2, 96, 0.87); */
    /* border-bottom: 1px solid #161664; */
    color: #f0f0f0;
    padding: 8px 15px; /* Adjust padding */
    border: none; /* Remove border */
}

.jwplayer .jw-rightclick .jw-rightclick-list .jw-rightclick-item:hover {
    background-color: rgba(17, 6, 131, 0.87);
    color: #ffffff;
}

/* Stats Panel */
.jwplayer .jw-stats-panel {
    background-color: rgba(10, 2, 96, 0.9);
    color: #f0f0f0;
    padding: 10px;
    border-radius: 4px;
    font-size: 0.9em;
}

/* Tooltips (Control bar, Settings, Time) */
.jw-tooltip, /* More generic tooltip selector */
.jw-time-tip {
    background-color: rgba(10, 2, 96, 0.95); /* Darker, more opaque tooltip */
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
    border-radius: 3px;
    border: none;
    color: #ffffff;
    font-size: 0.85em;
    padding: 5px 8px; /* Adjust padding */
}

/* Tooltip Arrow */
.jw-tooltip:after,
.jw-time-tip:after {
     content: "";
     position: absolute;
     border-width: 5px;
     border-style: solid;
     /* Adjust based on arrow direction - Example for bottom arrow: */
     bottom: -5px;
     left: 50%;
     transform: translateX(-50%);
     border-color: rgba(10, 2, 96, 0.95) transparent transparent transparent;
}

/* Shortcuts Tooltip */
.jwplayer .jw-shortcuts-tooltip {
    background-color: rgba(10, 2, 96, 0.92);
    width: 300px;
    border-radius: 4px;
    padding: 15px;
    color: #f0f0f0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}
.jwplayer .jw-shortcuts-tooltip h4 {
    color: #ffffff; margin-top: 0; margin-bottom: 10px; font-size: 1.1em;
    border-bottom: 1px solid rgba(255,255,255,0.2); padding-bottom: 5px;
}
.jwplayer .jw-shortcuts-tooltip p { margin-bottom: 5px; font-size: 0.95em; }
.jwplayer .jw-shortcuts-tooltip span {
     background-color: rgba(255, 255, 255, 0.15); padding: 2px 5px;
     border-radius: 3px; margin-right: 5px; font-family: monospace; color: #ffffff;
}

/* Pointer Events & Cursor */
.jwplayer .jw-logo, /* Target specifically within jwplayer container */
.jwplayer .jw-media {
    pointer-events: none;
}

.jwplayer.jw-flag-user-inactive { /* Target specifically within jwplayer container */
    cursor: none !important;
}

/* Add any other custom styles for the skin */