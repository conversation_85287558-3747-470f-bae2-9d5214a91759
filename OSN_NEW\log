WI0MzU2ZWM2ZjUyMWZhNiZzdHJlYW0taWQ9MTA2MDY1JnVzZXItaWQ9MTAzMDI2MDY5
🔐 License URL: https://lic.drmtoday.com/license-proxy-widevine/cenc/?specConform=true
🎫 DRM Token: eyJ1c2VySWQiOiIxMDMwMjYwNjkiLCJtZXJjaGFudCI6ImFuZ2hhbWkiLCJzZXNzaW9uSWQiOiJ7XCJjb250ZW50X2lkXCI6XCI0NzMzOFwiLFwiand0X3Rva2VuXCI6XCJleUpoYkdjaU9pSklVekkxTmlJc0luUjVjQ0k2SWtwWFZDSjkuZXlKcGMzTWlPaUpoYm1kb1lXMXBJaXdpYzNWaUlqb2lNVEF6TURJMk1EWTVJaXdpWlhod0lqb3hOelE1TVRjMk16UTBMQ0p1WW1ZaU9qRTNORGt3TURNMU5EUXNJbWxoZENJNk1UYzBPVEF3TXpVME5Dd2lhblJwSWpvaU5UVXlOREl6TlRRdE16VXhOQzAwWmpVMkxXRm1OV1F0TTJRMU4yTTJNbUkxTmpGaUlpd2lkWE5sY2w5cFpDSTZJakV3TXpBeU5qQTJPU0lzSW1SbGRtbGpaVjlwWkNJNklqTTFZalJtWm1JNUxUWTFOR1l0TkdaallTMWhNVE15TFRobFpERXdNVEZrTVRreU55SXNJbVJsZG1salpWOXBibVp2SWpvaUlpd2lhWE5mWkc5M2JteHZZV1JmY21WeGRXVnpkQ0k2Wm1Gc2MyVXNJbU52Ym5SbGJuUmZhV1FpT2lJME56TXpPQ0lzSW1OdmJuUmxiblJmY0dGeVpXNTBZV3hmY21GMGFXNW5Jam9pSWl3aWMzUnlaV0Z0WDJsa0lqb2lNVEEyTURZMUlpd2lkSFpmWkdWMmFXTmxYMjF2WkdWc0lqb2lJbjAuVFpaWHVhdVlCVzJ4MkFiaVhIV3NmNU5rQk1teW5Cb2JKMGFEX0tEOFlSOFwifSJ9
🔍 Parsing MPD file and extracting DRM info...
🔍 Downloading MPD file: https://osn-video.anghcdn.co/public/90046-47338-PR670863-BX-AS045201-250990-f6e2b6f5c16340b3ac5dc25a9ac34722-**********/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD00NzMzOCZleHBpcnk9MTc0OTA0Njc0NCZzaWduYXR1cmU9Mjk4NzAwMTczZmRlZDVjMjJmZmFkMjVjZWI0MzU2ZWM2ZjUyMWZhNiZzdHJlYW0taWQ9MTA2MDY1JnVzZXItaWQ9MTAzMDI2MDY5
📄 MPD file downloaded successfully, searching for PSSH...
🔍 Found ContentProtection with scheme: urn:mpeg:dash:mp4protection:2011
🔍 Found ContentProtection with scheme: urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed
✅ Found PSSH via Method 1: AAAAu3Bzc2gAAAAA7e+LqXnWSs6jyCfc1R0h7QAAAJsIARIQR6...
🔑 PSSH: AAAAu3Bzc2gAAAAA7e+LqXnWSs6jyCfc1R0h7QAAAJsIARIQR6VO8X5X7RoYjUhQj+V4KSKEAWV5SmhjM05sZEVsa0lqb2lPVEF3TkRZdE5EY3pNemd0VUZJMk56QTROak10UWxndFFWTXdORFV5TURFdE1qVXdPVGt3TFdZMlpUSmlObVkxWXpFMk16UXdZak5oWXpWa1l6STFZVGxoWXpNME56SXlMVEUzTXpZeE5ETXlNRFlpZlE9PQ==
🆔 KID: 47a54ef17e57ed1a188d48508fe57829
🔐 Extracting DRM keys...
✅ Successfully extracted 4 DRM keys
🔑 Key: fa65b9dfa4bd93adbb3dd653754ab853:91caa31140c87dcb8ecf49eba45e65e6
🔑 Key: bd7711e319669b3996002a1ebcf96fd5:d9d2ec8c311303cccfeba71fea076602
🔑 Key: 03f4bff2747b2a9e231cb100f54b6e09:2b09001ce20df74450fa7cf8c6312814
🔑 Key: 47a54ef17e57ed1a188d48508fe57829:fc341dd0fcf7be16d998fa8a031ac9e2
🔓 Extracted 4 DRM keys
📄 Parsing MPD file: https://osn-video.anghcdn.co/public/90046-47338-PR670863-BX-AS045201-250990-f6e2b6f5c16340b3ac5dc25a9ac34722-**********/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD00NzMzOCZleHBpcnk9MTc0OTA0Njc0NCZzaWduYXR1cmU9Mjk4NzAwMTczZmRlZDVjMjJmZmFkMjVjZWI0MzU2ZWM2ZjUyMWZhNiZzdHJlYW0taWQ9MTA2MDY1JnVzZXItaWQ9MTAzMDI2MDY5
🎵 Keeping audio track: tr - mp4a.40.2 (ID: 3551d9a8-eed0-4bdf-ad83-58e0e31518d0)
🎵 Keeping audio track: ar - mp4a.40.2 (ID: 77679a5f-4267-49c2-97be-42827c2b8818)
📊 Found 6 video qualities, 2 audio tracks, 2 subtitle tracks
✅ Download options displayed successfully!
🎵 Audio selected: ar - mp4a.40.2
F:\TEEFA\MBC\shahid_template\OSN_NEW\main.py:253: DeprecationWarning: Function: 'QMouseEvent.globalPos() const' is marked as deprecated, please check the documentation for more information.
  self.dragPos = event.globalPos()
Mouse click: LEFT CLICK
🎬 Batch download: 2 episodes selected
💾 Saved batch settings: Quality=360p, Audio=1, Subtitles=1
🚀 Starting download with selections...
📺 Quality: 360p
🔊 Audio tracks: 1
📝 Subtitles: 1
📊 Episodes: 2
🎬 Processing 2 episodes for batch download
🎬 Adding 2 episodes to downloads table...
✅ Added download to table: Sharab Al Toot AKA One Love S3E1
✅ Added Episode 1 to downloads table
✅ Added download to table: Sharab Al Toot AKA One Love S3E2
✅ Added Episode 2 to downloads table
✅ All 2 episodes added to downloads table
🎬 Starting sequential download 1/2 (Row 0)
🔄 This download needs stream data fetching...
🔄 Fetching stream details for Episode 1...
📡 Fetching stream details for Episode 1 - Stream ID: 106065
✅ Stream details fetched for Episode 1
🔍 Downloading MPD file: https://osn-video.anghcdn.co/public/90046-47338-PR670863-BX-AS045201-250990-f6e2b6f5c16340b3ac5dc25a9ac34722-**********/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD00NzMzOCZleHBpcnk9MTc0OTA0Njc1NyZzaWduYXR1cmU9NWIzODFlZGNjYWY0MzZkN2I1MzQ1MDRkOWE5YzMzMjA0YTIzMWJmNSZzdHJlYW0taWQ9MTA2MDY1JnVzZXItaWQ9MTAzMDI2MDY5
📄 MPD file downloaded successfully, searching for PSSH...
🔍 Found ContentProtection with scheme: urn:mpeg:dash:mp4protection:2011
🔍 Found ContentProtection with scheme: urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed
✅ Found PSSH via Method 1: AAAAu3Bzc2gAAAAA7e+LqXnWSs6jyCfc1R0h7QAAAJsIARIQR6...
🔐 Extracting DRM keys...
✅ Successfully extracted 4 DRM keys
🔑 Key: fa65b9dfa4bd93adbb3dd653754ab853:91caa31140c87dcb8ecf49eba45e65e6
🔑 Key: bd7711e319669b3996002a1ebcf96fd5:d9d2ec8c311303cccfeba71fea076602
🔑 Key: 03f4bff2747b2a9e231cb100f54b6e09:2b09001ce20df74450fa7cf8c6312814
🔑 Key: 47a54ef17e57ed1a188d48508fe57829:fc341dd0fcf7be16d998fa8a031ac9e2
📄 Parsing MPD file: https://osn-video.anghcdn.co/public/90046-47338-PR670863-BX-AS045201-250990-f6e2b6f5c16340b3ac5dc25a9ac34722-**********/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD00NzMzOCZleHBpcnk9MTc0OTA0Njc1NyZzaWduYXR1cmU9NWIzODFlZGNjYWY0MzZkN2I1MzQ1MDRkOWE5YzMzMjA0YTIzMWJmNSZzdHJlYW0taWQ9MTA2MDY1JnVzZXItaWQ9MTAzMDI2MDY5
🎵 Keeping audio track: tr - mp4a.40.2 (ID: 3551d9a8-eed0-4bdf-ad83-58e0e31518d0)
🎵 Keeping audio track: ar - mp4a.40.2 (ID: 77679a5f-4267-49c2-97be-42827c2b8818)
📊 Found 6 video qualities, 2 audio tracks, 2 subtitle tracks
✅ Updated stream details for Episode 1
🔍 DEBUG Content Data: {'ageRating': 'AGE_RATING_15', 'contentId': '47338', 'credits': {'actors': [{'crewId': '4019', 'firstName': {'ar': 'بارس', 'en': 'Baris'}, 'fullName': {'ar': 'بارس كيلتش', 'en': 'Baris Kilic'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_10817/crew_artwork_2448Gfb3kfs3fGDDpcWm1I3dLeJ.jpg', 'lastName': {'ar': 'كيلتش', 'en': 'Kilic'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'كيلتش,بارس', 'en': 'Kilic,Baris'}}, {'crewId': '4013', 'firstName': {'ar': 'إيفرم', 'en': 'Evrim'}, 'fullName': {'ar': 'إيفرم ألاسيا', 'en': 'Evrim Alasya'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4013/crew_artwork_5b6lRs7gUMy9zyTaX9qkDXAziFg.jpg', 'lastName': {'ar': 'ألاسيا', 'en': 'Alasya'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'ألاسيا,إيفرم', 'en': 'Alasya,Evrim'}}, {'crewId': '4014', 'firstName': {'ar': 'سيلا', 'en': 'Sila'}, 'fullName': {'ar': 'سيلا تركوغلو', 'en': 'Sila Turkoglu'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4014/crew_artwork_qQ3nlmPIRKFuqbqCBiZQ4kxhjME.jpg', 'lastName': {'ar': 'تركوغلو', 'en': 'Turkoglu'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'تركوغلو,سيلا', 'en': 'Turkoglu,Sila'}}, {'crewId': '4015', 'firstName': {'ar': 'سيبال', 'en': 'Sibel'}, 'fullName': {'ar': 'سيبال تاشوغلو', 'en': 'Sibel Tasçioglu'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4015/crew_artwork_6r0UACDWdV7HIXumhooXtrLjuGT.jpg', 'lastName': {'ar': 'تاشوغلو', 'en': 'Tasçioglu'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'تاشوغلو,سيبال', 'en': 'Tasçioglu,Sibel'}}, {'crewId': '4017', 'firstName': {'ar': 'دوغوكان', 'en': 'Dogukan'}, 'fullName': {'ar': 'دوغوكان غونغور', 'en': 'Dogukan Güngör'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4017/crew_artwork_3HwkRwnm7CPocjTvwj6nYoroybQ.jpg', 'lastName': {'ar': 'غونغور', 'en': 'Güngör'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'غونغور,دوغوكان', 'en': 'Güngör,Dogukan'}}, {'crewId': '4010', 'firstName': {'ar': 'مجدي', 'en': 'Müjde'}, 'fullName': {'ar': 'مجدي أوزمان', 'en': 'Müjde Uzman'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4010/crew_artwork_ungQdslfnJZUBWJKnDdjF4f4ySI.jpg', 'lastName': {'ar': 'أوزمان', 'en': 'Uzman'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'أوزمان,مجدي', 'en': 'Uzman,Müjde'}}, {'crewId': '4011', 'firstName': {'ar': 'سيرن', 'en': 'Ceren'}, 'fullName': {'ar': 'سيرن يالازوغلو', 'en': 'Ceren Yalazoglu'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4011/crew_artwork_fSOg6yGlwUipKLm9slAPXh0LlXo.jpg', 'lastName': {'ar': 'يالازوغلو', 'en': 'Yalazoglu'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'يالازوغلو,سيرن', 'en': 'Yalazoglu,Ceren'}}, {'crewId': '4012', 'firstName': {'ar': 'فايزة', 'en': 'Feyza'}, 'fullName': {'ar': 'فايزة سيفلك', 'en': 'Feyza Civelek'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4012/crew_artwork_cIgVH7Qk0PMypnsBl1VyLJfZ5Vm.jpg', 'lastName': {'ar': 'سيفلك', 'en': 'Civelek'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'سيفلك,فايزة', 'en': 'Civelek,Feyza'}}, {'crewId': '4020', 'firstName': {'ar': 'عليا', 'en': 'Aliye'}, 'fullName': {'ar': 'عليا أوزنتاغن', 'en': 'Aliye Uzunatagan'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4020/crew_artwork_rrDB7El5o4KsNEGBEikueZIJbYG.jpg', 'lastName': {'ar': 'أوزنتاغن', 'en': 'Uzunatagan'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'أوزنتاغن,عليا', 'en': 'Uzunatagan,Aliye'}}, {'crewId': '4022', 'firstName': {'ar': 'أوزلم', 'en': 'Özlem'}, 'fullName': {'ar': 'أوزلم شاكار يالشنكايا', 'en': 'Özlem Çakar Yalçinkaya'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4022/crew_artwork_o5M1mCSrfP7J5Ksslv98nOi8GCv.jpg', 'lastName': {'ar': 'شاكار يالشنكايا', 'en': 'Çakar Yalçinkaya'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'شاكار يالشنكايا,أوزلم', 'en': 'Çakar Yalçinkaya,Özlem'}}], 'creators': [], 'directors': []}, 'description': {'ar': 'يُصدم فاتح عند معرفته بحمل جوركام (جيهان) بعد إسعافها للمشفى.', 'en': 'Fatih gets shocked when he discovers that Gorkem (Jihan) is pregnant while ruushing her to the hospital.'}, 'episodeNumber': 1, 'freeToStreamText': '', 'genres': [{'genreId': '1', 'genrePageId': 'drama-1', 'name': {'ar': 'دراما', 'en': 'Drama'}}, {'genreId': '11', 'genrePageId': 'romance', 'name': {'ar': 'رومانسي', 'en': 'Romance'}}], 'isDownloadable': True, 'isExclusive': False, 'isFreeToStream': False, 'maxQuality': 'IMAGE_RESOLUTION_4K', 'runTimeMs': 2640000, 'seasonContentId': '47337', 'seasonNumber': 3, 'seriesContentId': '9283', 'seriesImages': {'logoImageUrl': 'https://osn-artwork.anghcdn.co/logo_tt_SR8403/8403_TT.png', 'longImageWithTitleUrl': 'https://osn-artwork.anghcdn.co/portrait_ttol_SR8403/8403_PPOL123153761.jpg', 'wideImageWithTitleUrl': 'https://osn-artwork.anghcdn.co/landscape_tt_SR8403/8403_LTT054310850.jpg', 'wideImageWithoutTitleResizedUrl': '', 'wideImageWithoutTitleUrl': 'https://osn-artwork.anghcdn.co/landscape_cl_SR8403/8403_LC053738687.jpg'}, 'streams': [{'audioTracks': [{'audioType': 'AUDIO_TYPE_STEREO', 'language': 'tr'}, {'audioType': 'AUDIO_TYPE_STEREO', 'language': 'ar'}], 'highestImageResolution': 'IMAGE_RESOLUTION_HD', 'isDolbyVision': False, 'isHdr': False, 'manifestType': 'MANIFEST_TYPE_DASH', 'runTimeMs': 2640000, 'streamId': '106065', 'subtitlesLanguages': ['en', 'ar']}, {'audioTracks': [{'audioType': 'AUDIO_TYPE_STEREO', 'language': 'tr'}, {'audioType': 'AUDIO_TYPE_STEREO', 'language': 'ar'}], 'highestImageResolution': 'IMAGE_RESOLUTION_HD', 'isDolbyVision': False, 'isHdr': False, 'manifestType': 'MANIFEST_TYPE_HLS', 'runTimeMs': 2640000, 'streamId': '106066', 'subtitlesLanguages': ['en', 'ar']}], 'studio': 'Global Agency', 'title': {'ar': 'الحلقة 1', 'en': 'Episode 1'}, 'uriId': 'PR670863', 'wideImageWithoutTitleUrl': 'https://osn-artwork.anghcdn.co/landscape_cl_PR670863/AS045201_QC.jpg', 'year': '2024'}
🔍 DEBUG Episode Data: {'ageRating': 'AGE_RATING_15', 'contentId': '47338', 'credits': {'actors': [{'crewId': '4019', 'firstName': {'ar': 'بارس', 'en': 'Baris'}, 'fullName': {'ar': 'بارس كيلتش', 'en': 'Baris Kilic'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_10817/crew_artwork_2448Gfb3kfs3fGDDpcWm1I3dLeJ.jpg', 'lastName': {'ar': 'كيلتش', 'en': 'Kilic'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'كيلتش,بارس', 'en': 'Kilic,Baris'}}, {'crewId': '4013', 'firstName': {'ar': 'إيفرم', 'en': 'Evrim'}, 'fullName': {'ar': 'إيفرم ألاسيا', 'en': 'Evrim Alasya'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4013/crew_artwork_5b6lRs7gUMy9zyTaX9qkDXAziFg.jpg', 'lastName': {'ar': 'ألاسيا', 'en': 'Alasya'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'ألاسيا,إيفرم', 'en': 'Alasya,Evrim'}}, {'crewId': '4014', 'firstName': {'ar': 'سيلا', 'en': 'Sila'}, 'fullName': {'ar': 'سيلا تركوغلو', 'en': 'Sila Turkoglu'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4014/crew_artwork_qQ3nlmPIRKFuqbqCBiZQ4kxhjME.jpg', 'lastName': {'ar': 'تركوغلو', 'en': 'Turkoglu'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'تركوغلو,سيلا', 'en': 'Turkoglu,Sila'}}, {'crewId': '4015', 'firstName': {'ar': 'سيبال', 'en': 'Sibel'}, 'fullName': {'ar': 'سيبال تاشوغلو', 'en': 'Sibel Tasçioglu'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4015/crew_artwork_6r0UACDWdV7HIXumhooXtrLjuGT.jpg', 'lastName': {'ar': 'تاشوغلو', 'en': 'Tasçioglu'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'تاشوغلو,سيبال', 'en': 'Tasçioglu,Sibel'}}, {'crewId': '4017', 'firstName': {'ar': 'دوغوكان', 'en': 'Dogukan'}, 'fullName': {'ar': 'دوغوكان غونغور', 'en': 'Dogukan Güngör'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4017/crew_artwork_3HwkRwnm7CPocjTvwj6nYoroybQ.jpg', 'lastName': {'ar': 'غونغور', 'en': 'Güngör'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'غونغور,دوغوكان', 'en': 'Güngör,Dogukan'}}, {'crewId': '4010', 'firstName': {'ar': 'مجدي', 'en': 'Müjde'}, 'fullName': {'ar': 'مجدي أوزمان', 'en': 'Müjde Uzman'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4010/crew_artwork_ungQdslfnJZUBWJKnDdjF4f4ySI.jpg', 'lastName': {'ar': 'أوزمان', 'en': 'Uzman'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'أوزمان,مجدي', 'en': 'Uzman,Müjde'}}, {'crewId': '4011', 'firstName': {'ar': 'سيرن', 'en': 'Ceren'}, 'fullName': {'ar': 'سيرن يالازوغلو', 'en': 'Ceren Yalazoglu'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4011/crew_artwork_fSOg6yGlwUipKLm9slAPXh0LlXo.jpg', 'lastName': {'ar': 'يالازوغلو', 'en': 'Yalazoglu'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'يالازوغلو,سيرن', 'en': 'Yalazoglu,Ceren'}}, {'crewId': '4012', 'firstName': {'ar': 'فايزة', 'en': 'Feyza'}, 'fullName': {'ar': 'فايزة سيفلك', 'en': 'Feyza Civelek'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4012/crew_artwork_cIgVH7Qk0PMypnsBl1VyLJfZ5Vm.jpg', 'lastName': {'ar': 'سيفلك', 'en': 'Civelek'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'سيفلك,فايزة', 'en': 'Civelek,Feyza'}}, {'crewId': '4020', 'firstName': {'ar': 'عليا', 'en': 'Aliye'}, 'fullName': {'ar': 'عليا أوزنتاغن', 'en': 'Aliye Uzunatagan'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4020/crew_artwork_rrDB7El5o4KsNEGBEikueZIJbYG.jpg', 'lastName': {'ar': 'أوزنتاغن', 'en': 'Uzunatagan'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'أوزنتاغن,عليا', 'en': 'Uzunatagan,Aliye'}}, {'crewId': '4022', 'firstName': {'ar': 'أوزلم', 'en': 'Özlem'}, 'fullName': {'ar': 'أوزلم شاكار يالشنكايا', 'en': 'Özlem Çakar Yalçinkaya'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4022/crew_artwork_o5M1mCSrfP7J5Ksslv98nOi8GCv.jpg', 'lastName': {'ar': 'شاكار يالشنكايا', 'en': 'Çakar Yalçinkaya'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'شاكار يالشنكايا,أوزلم', 'en': 'Çakar Yalçinkaya,Özlem'}}], 'creators': [], 'directors': []}, 'description': {'ar': 'يُصدم فاتح عند معرفته بحمل جوركام (جيهان) بعد إسعافها للمشفى.', 'en': 'Fatih gets shocked when he discovers that Gorkem (Jihan) is pregnant while ruushing her to the hospital.'}, 'episodeNumber': 1, 'freeToStreamText': '', 'genres': [{'genreId': '1', 'genrePageId': 'drama-1', 'name': {'ar': 'دراما', 'en': 'Drama'}}, {'genreId': '11', 'genrePageId': 'romance', 'name': {'ar': 'رومانسي', 'en': 'Romance'}}], 'isDownloadable': True, 'isExclusive': False, 'isFreeToStream': False, 'maxQuality': 'IMAGE_RESOLUTION_4K', 'runTimeMs': 2640000, 'seasonContentId': '47337', 'seasonNumber': 3, 'seriesContentId': '9283', 'seriesImages': {'logoImageUrl': 'https://osn-artwork.anghcdn.co/logo_tt_SR8403/8403_TT.png', 'longImageWithTitleUrl': 'https://osn-artwork.anghcdn.co/portrait_ttol_SR8403/8403_PPOL123153761.jpg', 'wideImageWithTitleUrl': 'https://osn-artwork.anghcdn.co/landscape_tt_SR8403/8403_LTT054310850.jpg', 'wideImageWithoutTitleResizedUrl': '', 'wideImageWithoutTitleUrl': 'https://osn-artwork.anghcdn.co/landscape_cl_SR8403/8403_LC053738687.jpg'}, 'streams': [{'audioTracks': [{'audioType': 'AUDIO_TYPE_STEREO', 'language': 'tr'}, {'audioType': 'AUDIO_TYPE_STEREO', 'language': 'ar'}], 'highestImageResolution': 'IMAGE_RESOLUTION_HD', 'isDolbyVision': False, 'isHdr': False, 'manifestType': 'MANIFEST_TYPE_DASH', 'runTimeMs': 2640000, 'streamId': '106065', 'subtitlesLanguages': ['en', 'ar']}, {'audioTracks': [{'audioType': 'AUDIO_TYPE_STEREO', 'language': 'tr'}, {'audioType': 'AUDIO_TYPE_STEREO', 'language': 'ar'}], 'highestImageResolution': 'IMAGE_RESOLUTION_HD', 'isDolbyVision': False, 'isHdr': False, 'manifestType': 'MANIFEST_TYPE_HLS', 'runTimeMs': 2640000, 'streamId': '106066', 'subtitlesLanguages': ['en', 'ar']}], 'studio': 'Global Agency', 'title': {'ar': 'الحلقة 1', 'en': 'Episode 1'}, 'uriId': 'PR670863', 'wideImageWithoutTitleUrl': 'https://osn-artwork.anghcdn.co/landscape_cl_PR670863/AS045201_QC.jpg', 'year': '2024'}
🔊 Selected audio: ar - mp4a.40.2
📝 Selected subtitle: ar
🎵 Final audio tracks list: ['ar-mp4a.40.2']
📄 Final subtitle tracks list: ['ar']
🔗 Connecting download signals for the first time...
✅ Connected download progress signals globally
🧪 Testing progress signal for row 0
🎯 UI: Updating progress for row 0: 5% - Testing progress signal...
✅ Progress bar updated to 5%
✅ Status updated: Downloading 5%
📸 Poster URL set for download session: https://osn-artwork.anghcdn.co/portrait_ttol_SR8403/8403_PPOL123153761.jpg
📸 Set poster URL for download: https://osn-artwork.anghcdn.co/portrait_ttol_SR8403/8403_PPOL123153761.jpg
✅ DRM keys saved to: F:\TEEFA\MBC\shahid_template\OSN_NEW\KEYS\OSNPLUS_KEYS.txt
🔍 DEBUG Episode Data:
   Series Title: Sharab Al Toot AKA One Love
   Season Number: 3 (type: <class 'int'>)
   Episode Number: 1 (type: <class 'int'>)
   Episode Title: Episode 1
   Full episode_data keys: ['ageRating', 'contentId', 'credits', 'description', 'episodeNumber', 'freeToStreamText', 'genres', 'isDownloadable', 'isExclusive', 'isFreeToStream', 'maxQuality', 'runTimeMs', 'seasonContentId', 'seasonNumber', 'seriesContentId', 'seriesImages', 'streams', 'studio', 'title', 'uriId', 'wideImageWithoutTitleUrl', 'year']
📺 Started EPISODE download for: Sharab Al Toot AKA One Love S3E1
   Full episode_data: {'ageRating': 'AGE_RATING_15', 'contentId': '47338', 'credits': {'actors': [{'crewId': '4019', 'firstName': {'ar': 'بارس', 'en': 'Baris'}, 'fullName': {'ar': 'بارس كيلتش', 'en': 'Baris Kilic'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_10817/crew_artwork_2448Gfb3kfs3fGDDpcWm1I3dLeJ.jpg', 'lastName': {'ar': 'كيلتش', 'en': 'Kilic'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'كيلتش,بارس', 'en': 'Kilic,Baris'}}, {'crewId': '4013', 'firstName': {'ar': 'إيفرم', 'en': 'Evrim'}, 'fullName': {'ar': 'إيفرم ألاسيا', 'en': 'Evrim Alasya'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4013/crew_artwork_5b6lRs7gUMy9zyTaX9qkDXAziFg.jpg', 'lastName': {'ar': 'ألاسيا', 'en': 'Alasya'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'ألاسيا,إيفرم', 'en': 'Alasya,Evrim'}}, {'crewId': '4014', 'firstName': {'ar': 'سيلا', 'en': 'Sila'}, 'fullName': {'ar': 'سيلا تركوغلو', 'en': 'Sila Turkoglu'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4014/crew_artwork_qQ3nlmPIRKFuqbqCBiZQ4kxhjME.jpg', 'lastName': {'ar': 'تركوغلو', 'en': 'Turkoglu'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'تركوغلو,سيلا', 'en': 'Turkoglu,Sila'}}, {'crewId': '4015', 'firstName': {'ar': 'سيبال', 'en': 'Sibel'}, 'fullName': {'ar': 'سيبال تاشوغلو', 'en': 'Sibel Tasçioglu'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4015/crew_artwork_6r0UACDWdV7HIXumhooXtrLjuGT.jpg', 'lastName': {'ar': 'تاشوغلو', 'en': 'Tasçioglu'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'تاشوغلو,سيبال', 'en': 'Tasçioglu,Sibel'}}, {'crewId': '4017', 'firstName': {'ar': 'دوغوكان', 'en': 'Dogukan'}, 'fullName': {'ar': 'دوغوكان غونغور', 'en': 'Dogukan Güngör'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4017/crew_artwork_3HwkRwnm7CPocjTvwj6nYoroybQ.jpg', 'lastName': {'ar': 'غونغور', 'en': 'Güngör'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'غونغور,دوغوكان', 'en': 'Güngör,Dogukan'}}, {'crewId': '4010', 'firstName': {'ar': 'مجدي', 'en': 'Müjde'}, 'fullName': {'ar': 'مجدي أوزمان', 'en': 'Müjde Uzman'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4010/crew_artwork_ungQdslfnJZUBWJKnDdjF4f4ySI.jpg', 'lastName': {'ar': 'أوزمان', 'en': 'Uzman'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'أوزمان,مجدي', 'en': 'Uzman,Müjde'}}, {'crewId': '4011', 'firstName': {'ar': 'سيرن', 'en': 'Ceren'}, 'fullName': {'ar': 'سيرن يالازوغلو', 'en': 'Ceren Yalazoglu'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4011/crew_artwork_fSOg6yGlwUipKLm9slAPXh0LlXo.jpg', 'lastName': {'ar': 'يالازوغلو', 'en': 'Yalazoglu'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'يالازوغلو,سيرن', 'en': 'Yalazoglu,Ceren'}}, {'crewId': '4012', 'firstName': {'ar': 'فايزة', 'en': 'Feyza'}, 'fullName': {'ar': 'فايزة سيفلك', 'en': 'Feyza Civelek'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4012/crew_artwork_cIgVH7Qk0PMypnsBl1VyLJfZ5Vm.jpg', 'lastName': {'ar': 'سيفلك', 'en': 'Civelek'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'سيفلك,فايزة', 'en': 'Civelek,Feyza'}}, {'crewId': '4020', 'firstName': {'ar': 'عليا', 'en': 'Aliye'}, 'fullName': {'ar': 'عليا أوزنتاغن', 'en': 'Aliye Uzunatagan'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4020/crew_artwork_rrDB7El5o4KsNEGBEikueZIJbYG.jpg', 'lastName': {'ar': 'أوزنتاغن', 'en': 'Uzunatagan'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'أوزنتاغن,عليا', 'en': 'Uzunatagan,Aliye'}}, {'crewId': '4022', 'firstName': {'ar': 'أوزلم', 'en': 'Özlem'}, 'fullName': {'ar': 'أوزلم شاكار يالشنكايا', 'en': 'Özlem Çakar Yalçinkaya'}, 'imageUrl': 'https://osn-artwork.anghcdn.co/crew_profile_4022/crew_artwork_o5M1mCSrfP7J5Ksslv98nOi8GCv.jpg', 'lastName': {'ar': 'شاكار يالشنكايا', 'en': 'Çakar Yalçinkaya'}, 'role': 'ROLE_ACTOR', 'sortableName': {'ar': 'شاكار يالشنكايا,أوزلم', 'en': 'Çakar Yalçinkaya,Özlem'}}], 'creators': [], 'directors': []}, 'description': {'ar': 'يُصدم فاتح عند معرفته بحمل جوركام (جيهان) بعد إسعافها للمشفى.', 'en': 'Fatih gets shocked when he discovers that Gorkem (Jihan) is pregnant while russhing her to the hospital.'}, 'episodeNumber': 1, 'freeToStreamText': '', 'genres': [{'genreId': '1', 'genrePageId': 'drama-1', 'name': {'ar': 'دراما', 'en': 'Drama'}}, {'genreId': '11', 'genrePageId': 'romance', 'name': {'ar': 'رومانسي', 'en': 'Romance'}}], 'isDownloadable': True, 'isExclusive': False, 'isFreeToStream': False, 'maxQuality': 'IMAGE_RESOLUTION_4K', 'runTimeMs': 2640000, 'seasonContentId': '47337', 'seasonNumber': 3, 'seriesContentId': '9283', 'seriesImages': {'logoImageUrl': 'https://osn-artwork.anghcdn.co/logo_tt_SR8403/8403_TT.png', 'longImageWithTitleUrl': 'https://osn-artwork.anghcdn.co/portrait_ttol_SR8403/8403_PPOL123153761.jpg', 'wideImageWithTitleUrl': 'https://osn-artwork.anghcdn.co/landscape_tt_SR8403/8403_LTT054310850.jpg', 'wideImageWithoutTitleResizedUrl': '', 'wideImageWithoutTitleUrl': 'https://osn-artwork.anghcdn.co/landscape_cl_SR8403/8403_LC053738687.jpg'}, 'streams': [{'audioTracks': [{'audioType': 'AUDIO_TYPE_STEREO', 'language': 'tr'}, {'audioType': 'AUDIO_TYPE_STEREO', 'language': 'ar'}], 'highestImageResolution': 'IMAGE_RESOLUTION_HD', 'isDolbyVision': False, 'isHdr': False, 'manifestType': 'MANIFEST_TYPE_DASH', 'runTimeMs': 2640000, 'streamId': '106065', 'subtitlesLanguages': ['en', 'ar']}, {'audioTracks': [{'audioType': 'AUDIO_TYPE_STEREO', 'language': 'tr'}, {'audioType': 'AUDIO_TYPE_STEREO', 'language': 'ar'}], 'highestImageResolution': 'IMAGE_RESOLUTION_HD', 'isDolbyVision': False, 'isHdr': False, 'manifestType': 'MANIFEST_TYPE_HLS', 'runTimeMs': 2640000, 'streamId': '106066', 'subtitlesLanguages': ['en', 'ar']}], 'studio': 'Global Agency', 'title': {'ar': 'الحلقة 1', 'en': 'Episode 1'}, 'uriId': 'PR670863', 'wideImageWithoutTitleUrl': 'https://osn-artwork.anghcdn.co/landscape_cl_PR670863/AS045201_QC.jpg', 'year': '2024'}
🚀 Started real download for row 0
📸 Found poster URL: https://osn-artwork.anghcdn.co/portrait_ttol_SR8403/8403_PPOL123153761.jpg
📺 Series: Sharab Al Toot AKA One Love
📺 Episode: S3E1
🎬 Quality: 640x360
📸 Downloading poster from: https://osn-artwork.anghcdn.co/portrait_ttol_SR8403/8403_PPOL123153761.jpg
🔊 Audio: [{'language': 'ar', 'codecs': 'mp4a.40.2', 'bandwidth': '128000', 'id': '77679a5f-4267-49c2-97be-42827c2b8818'}]
📝 Subtitles: ['ar']
✅ Poster downloaded and saved as F:\TEEFA\MBC\shahid_template\OSN_NEW\cache\poster.jpg
🚀 Starting episode download: Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC
🎵 Selected audio tracks: [{'language': 'ar', 'codecs': 'mp4a.40.2', 'bandwidth': '128000', 'id': '77679a5f-4267-49c2-97be-42827c2b8818'}]
📝 Selected subtitle tracks: ['ar']
🔧 Building selection options...
🎵 Input audio_tracks: [{'language': 'ar', 'codecs': 'mp4a.40.2', 'bandwidth': '128000', 'id': '77679a5f-4267-49c2-97be-42827c2b8818'}]
📝 Input subtitle_tracks: ['ar']
🎯 Using track ID selector: id=77679a5f-4267-49c2-97be-42827c2b8818
🎵 Created audio selector: id=77679a5f-4267-49c2-97be-42827c2b8818 (from ar, mp4a.40.2)
🎵 User audio selection: --select-audio "id=77679a5f-4267-49c2-97be-42827c2b8818"
📝 User subtitle selection: --select-subtitle "lang=ar:for=best"
✅ Final audio option: --select-audio "id=77679a5f-4267-49c2-97be-42827c2b8818"
✅ Final subtitle option: --select-subtitle "lang=ar:for=best"
🔧 Episode download command: "F:\TEEFA\MBC\shahid_template\OSN_NEW\binaries\N_m3u8DL-RE.exe" "https://osn-video.anghcdn.co/public/90046-47338-PR670863-BX-AS045201-250990-f6e2b6f5c16340b3ac5dc25a9ac34722-**********/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD00NzMzOCZleHBpcnk9MTc0OTA0Njc1NyZzaWduYXR1cmU9NWIzODFlZGNjYWY0MzZkN2I1MzQ1MDRkOWE5YzMzMjA0YTIzMWJmNSZzdHJlYW0taWQ9MTA2MDY1JnVzZXItaWQ9MTAzMDI2MDY5" -mt --select-video "id=b5c96828-50f7-40d6-9faa-516d73de99db" --select-audio "id=77679a5f-4267-49c2-97be-42827c2b8818" --select-subtitle "lang=ar:for=best" --tmp-dir "F:\TEEFA\MBC\shahid_template\OSN_NEW\cache" --save-dir "F:\TEEFA\MBC\shahid_template\OSN_NEW\cache" --save-name "Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC" --decryption-binary-path="F:\TEEFA\MBC\shahid_template\OSN_NEW\binaries\mp4decrypt.exe" --key-text-file="F:\TEEFA\MBC\shahid_template\OSN_NEW\KEYS\OSNPLUS_KEYS.txt" --log-level "OFF"
🎯 MAIN: Received download_progress signal: 0% - Starting episode download: Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC
🎯 Updated downloads table progress: 0%
🎯 UI Progress Updated: 0% - Starting episode download: Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC
🎯 UI: Updating progress for row 0: 0% - Starting episode download: Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC
✅ Progress bar updated to 0%
🔍 Starting friend's progress monitoring for: Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC
✅ Status updated: Starting
📡 Sent initial progress signal: 1%
🎯 MAIN: Received download_progress signal: 1% - Initializing download: Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC
🎯 Updated downloads table progress: 1%
🎯 UI Progress Updated: 1% - Initializing download: Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC
🎯 UI: Updating progress for row 0: 1% - Initializing download: Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC
✅ Progress bar updated to 1%
✅ Status updated: Downloading 1%
🎯 MAIN: Received status_update signal: N_m3u8DL: Sub ar: 0%
📝 Status: N_m3u8DL: Sub ar: 0%
📝 Download Status: N_m3u8DL: Sub ar: 0%
📊 Video Progress Target: 25%
🎯 Setting target progress to: 25%
🔊 Audio Progress Target: 28%
🎯 Smooth Progress: 1%
🎯 MAIN: Received download_progress signal: 1% - Video: 1%
🎯 Updated downloads table progress: 1%
🎯 UI Progress Updated: 1% - Video: 1%
🎯 UI: Updating progress for row 0: 1% - Video: 1%
✅ Progress bar updated to 1%
✅ Status updated: Downloading 1%
🎯 Smooth Progress: 2%
🎯 MAIN: Received download_progress signal: 2% - Video: 2%
🎯 Updated downloads table progress: 2%
🎯 UI Progress Updated: 2% - Video: 2%
🎯 UI: Updating progress for row 0: 2% - Video: 2%
✅ Progress bar updated to 2%
✅ Status updated: Downloading 2%
🎯 Smooth Progress: 3%
🎯 MAIN: Received download_progress signal: 3% - Video: 3%
🎯 Updated downloads table progress: 3%
🎯 UI Progress Updated: 3% - Video: 3%
🎯 UI: Updating progress for row 0: 3% - Video: 3%
✅ Progress bar updated to 3%
✅ Status updated: Downloading 3%
🎯 Smooth Progress: 4%
🎯 MAIN: Received download_progress signal: 4% - Video: 4%
🎯 Updated downloads table progress: 4%
🎯 UI Progress Updated: 4% - Video: 4%
🎯 UI: Updating progress for row 0: 4% - Video: 4%
✅ Progress bar updated to 4%
✅ Status updated: Downloading 4%
🎯 Smooth Progress: 5%
🎯 MAIN: Received download_progress signal: 5% - Video: 5%
🎯 Updated downloads table progress: 5%
🎯 UI Progress Updated: 5% - Video: 5%
🎯 UI: Updating progress for row 0: 5% - Video: 5%
✅ Progress bar updated to 5%
✅ Status updated: Downloading 5%
🎯 Smooth Progress: 6%
🎯 MAIN: Received download_progress signal: 6% - Video: 6%
🎯 Updated downloads table progress: 6%
🎯 UI Progress Updated: 6% - Video: 6%
🎯 UI: Updating progress for row 0: 6% - Video: 6%
✅ Progress bar updated to 6%
✅ Status updated: Downloading 6%
🎯 Smooth Progress: 7%
🎯 MAIN: Received download_progress signal: 7% - Video: 7%
🎯 Updated downloads table progress: 7%
🎯 UI Progress Updated: 7% - Video: 7%
🎯 UI: Updating progress for row 0: 7% - Video: 7%
✅ Progress bar updated to 7%
✅ Status updated: Downloading 7%
🎯 Smooth Progress: 8%
🎯 MAIN: Received download_progress signal: 8% - Video: 8%
🎯 Updated downloads table progress: 8%
🎯 UI Progress Updated: 8% - Video: 8%
🎯 UI: Updating progress for row 0: 8% - Video: 8%
✅ Progress bar updated to 8%
✅ Status updated: Downloading 8%
🎯 Smooth Progress: 9%
🎯 MAIN: Received download_progress signal: 9% - Video: 9%
🎯 Updated downloads table progress: 9%
🎯 UI Progress Updated: 9% - Video: 9%
🎯 UI: Updating progress for row 0: 9% - Video: 9%
✅ Progress bar updated to 9%
✅ Status updated: Downloading 9%
🎯 Smooth Progress: 10%
🎯 MAIN: Received download_progress signal: 10% - Video: 10%
🎯 Updated downloads table progress: 10%
🎯 UI Progress Updated: 10% - Video: 10%
🎯 UI: Updating progress for row 0: 10% - Video: 10%
✅ Progress bar updated to 10%
✅ Status updated: Downloading 10%
🎯 Smooth Progress: 11%
🎯 MAIN: Received download_progress signal: 11% - Video: 11%
🎯 Updated downloads table progress: 11%
🎯 UI Progress Updated: 11% - Video: 11%
🎯 UI: Updating progress for row 0: 11% - Video: 11%
✅ Progress bar updated to 11%
✅ Status updated: Downloading 11%
🎯 Smooth Progress: 12%
🎯 MAIN: Received download_progress signal: 12% - Video: 12%
🎯 Updated downloads table progress: 12%
🎯 UI Progress Updated: 12% - Video: 12%
🎯 UI: Updating progress for row 0: 12% - Video: 12%
✅ Progress bar updated to 12%
✅ Status updated: Downloading 12%
🎯 Smooth Progress: 13%
🎯 MAIN: Received download_progress signal: 13% - Video: 13%
🎯 Updated downloads table progress: 13%
🎯 UI Progress Updated: 13% - Video: 13%
🎯 UI: Updating progress for row 0: 13% - Video: 13%
✅ Progress bar updated to 13%
✅ Status updated: Downloading 13%
🎯 Smooth Progress: 14%
🎯 MAIN: Received download_progress signal: 14% - Video: 14%
🎯 Updated downloads table progress: 14%
🎯 UI Progress Updated: 14% - Video: 14%
🎯 UI: Updating progress for row 0: 14% - Video: 14%
✅ Progress bar updated to 14%
✅ Status updated: Downloading 14%
🎯 Smooth Progress: 15%
🎯 MAIN: Received download_progress signal: 15% - Video: 15%
🎯 Updated downloads table progress: 15%
🎯 UI Progress Updated: 15% - Video: 15%
🎯 UI: Updating progress for row 0: 15% - Video: 15%
✅ Progress bar updated to 15%
✅ Status updated: Downloading 15%
🎯 Smooth Progress: 16%
🎯 MAIN: Received download_progress signal: 16% - Video: 16%
🎯 Updated downloads table progress: 16%
🎯 UI Progress Updated: 16% - Video: 16%
🎯 UI: Updating progress for row 0: 16% - Video: 16%
✅ Progress bar updated to 16%
✅ Status updated: Downloading 16%
🎯 Smooth Progress: 17%
🎯 MAIN: Received download_progress signal: 17% - Video: 17%
🎯 Updated downloads table progress: 17%
🎯 UI Progress Updated: 17% - Video: 17%
🎯 UI: Updating progress for row 0: 17% - Video: 17%
✅ Progress bar updated to 17%
✅ Status updated: Downloading 17%
🎯 Smooth Progress: 18%
🎯 MAIN: Received download_progress signal: 18% - Video: 18%
🎯 Updated downloads table progress: 18%
🎯 UI Progress Updated: 18% - Video: 18%
🎯 UI: Updating progress for row 0: 18% - Video: 18%
✅ Progress bar updated to 18%
✅ Status updated: Downloading 18%
🎯 Smooth Progress: 19%
🎯 MAIN: Received download_progress signal: 19% - Video: 19%
🎯 Updated downloads table progress: 19%
🎯 UI Progress Updated: 19% - Video: 19%
🎯 UI: Updating progress for row 0: 19% - Video: 19%
✅ Progress bar updated to 19%
✅ Status updated: Downloading 19%
🎯 Smooth Progress: 20%
🎯 MAIN: Received download_progress signal: 20% - Video: 20%
🎯 Updated downloads table progress: 20%
🎯 UI Progress Updated: 20% - Video: 20%
🎯 UI: Updating progress for row 0: 20% - Video: 20%
✅ Progress bar updated to 20%
✅ Status updated: Downloading 20%
🎯 Smooth Progress: 21%
🎯 MAIN: Received download_progress signal: 21% - Video: 21%
🎯 Updated downloads table progress: 21%
🎯 UI Progress Updated: 21% - Video: 21%
🎯 UI: Updating progress for row 0: 21% - Video: 21%
✅ Progress bar updated to 21%
✅ Status updated: Downloading 21%
🎯 Smooth Progress: 22%
🎯 MAIN: Received download_progress signal: 22% - Video: 22%
🎯 Updated downloads table progress: 22%
🎯 UI Progress Updated: 22% - Video: 22%
🎯 UI: Updating progress for row 0: 22% - Video: 22%
✅ Progress bar updated to 22%
✅ Status updated: Downloading 22%
🎯 Smooth Progress: 23%
🎯 MAIN: Received download_progress signal: 23% - Video: 23%
🎯 Updated downloads table progress: 23%
🎯 UI Progress Updated: 23% - Video: 23%
🎯 UI: Updating progress for row 0: 23% - Video: 23%
✅ Progress bar updated to 23%
✅ Status updated: Downloading 23%
🎯 Smooth Progress: 24%
🎯 MAIN: Received download_progress signal: 24% - Video: 24%
🎯 Updated downloads table progress: 24%
🎯 UI Progress Updated: 24% - Video: 24%
🎯 UI: Updating progress for row 0: 24% - Video: 24%
✅ Progress bar updated to 24%
✅ Status updated: Downloading 24%
🎯 Smooth Progress: 25%
🎯 MAIN: Received download_progress signal: 25% - Video: 25%
🎯 Updated downloads table progress: 25%
🎯 UI Progress Updated: 25% - Video: 25%
🎯 UI: Updating progress for row 0: 25% - Video: 25%
✅ Progress bar updated to 25%
✅ Status updated: Downloading 25%
🎯 Smooth Progress: 26%
🎯 MAIN: Received download_progress signal: 26% - Video: 26%
🎯 Updated downloads table progress: 26%
🎯 UI Progress Updated: 26% - Video: 26%
🎯 UI: Updating progress for row 0: 26% - Video: 26%
✅ Progress bar updated to 26%
✅ Status updated: Downloading 26%
🎯 Smooth Progress: 27%
🎯 MAIN: Received download_progress signal: 27% - Video: 27%
🎯 Updated downloads table progress: 27%
🎯 UI Progress Updated: 27% - Video: 27%
🎯 UI: Updating progress for row 0: 27% - Video: 27%
✅ Progress bar updated to 27%
✅ Status updated: Downloading 27%
🎯 Smooth Progress: 28%
🎯 MAIN: Received download_progress signal: 28% - Video: 28%
🎯 Updated downloads table progress: 28%
🎯 UI Progress Updated: 28% - Video: 28%
🎯 UI: Updating progress for row 0: 28% - Video: 28%
✅ Progress bar updated to 28%
✅ Status updated: Downloading 28%
🔊 Audio Progress Target: 50%
🎯 Smooth Progress: 29%
🎯 MAIN: Received download_progress signal: 29% - Video: 29%
🎯 Updated downloads table progress: 29%
🎯 UI Progress Updated: 29% - Video: 29%
🎯 UI: Updating progress for row 0: 29% - Video: 29%
✅ Progress bar updated to 29%
✅ Status updated: Downloading 29%
🎯 Smooth Progress: 30%
🎯 MAIN: Received download_progress signal: 30% - Video: 30%
🎯 Updated downloads table progress: 30%
🎯 UI Progress Updated: 30% - Video: 30%
🎯 UI: Updating progress for row 0: 30% - Video: 30%
✅ Progress bar updated to 30%
✅ Status updated: Downloading 30%
🎯 Smooth Progress: 31%
🎯 MAIN: Received download_progress signal: 31% - Video: 31%
🎯 Updated downloads table progress: 31%
🎯 UI Progress Updated: 31% - Video: 31%
🎯 UI: Updating progress for row 0: 31% - Video: 31%
✅ Progress bar updated to 31%
✅ Status updated: Downloading 31%
🎯 Smooth Progress: 32%
🎯 MAIN: Received download_progress signal: 32% - Video: 32%
🎯 Updated downloads table progress: 32%
🎯 UI Progress Updated: 32% - Video: 32%
🎯 UI: Updating progress for row 0: 32% - Video: 32%
✅ Progress bar updated to 32%
✅ Status updated: Downloading 32%
🎯 MAIN: Received download_progress signal: 33% - Video: 33%
🎯 Smooth Progress: 33%
🎯 Updated downloads table progress: 33%
🎯 UI Progress Updated: 33% - Video: 33%
🎯 UI: Updating progress for row 0: 33% - Video: 33%
✅ Progress bar updated to 33%
✅ Status updated: Downloading 33%
🎯 MAIN: Received download_progress signal: 34% - Video: 34%
🎯 Smooth Progress: 34%
🎯 Updated downloads table progress: 34%
🎯 UI Progress Updated: 34% - Video: 34%
🎯 UI: Updating progress for row 0: 34% - Video: 34%
✅ Progress bar updated to 34%
✅ Status updated: Downloading 34%
🎯 Smooth Progress: 35%
🎯 MAIN: Received download_progress signal: 35% - Video: 35%
🎯 Updated downloads table progress: 35%
🎯 UI Progress Updated: 35% - Video: 35%
🎯 UI: Updating progress for row 0: 35% - Video: 35%
✅ Progress bar updated to 35%
✅ Status updated: Downloading 35%
🎯 Smooth Progress: 36%
🎯 MAIN: Received download_progress signal: 36% - Video: 36%
🎯 Updated downloads table progress: 36%
🎯 UI Progress Updated: 36% - Video: 36%
🎯 UI: Updating progress for row 0: 36% - Video: 36%
✅ Progress bar updated to 36%
✅ Status updated: Downloading 36%
🎯 Smooth Progress: 37%
🎯 MAIN: Received download_progress signal: 37% - Video: 37%
🎯 Updated downloads table progress: 37%
🎯 UI Progress Updated: 37% - Video: 37%
🎯 UI: Updating progress for row 0: 37% - Video: 37%
✅ Progress bar updated to 37%
✅ Status updated: Downloading 37%
🎯 Smooth Progress: 38%
🎯 MAIN: Received download_progress signal: 38% - Video: 38%
🎯 Updated downloads table progress: 38%
🎯 UI Progress Updated: 38% - Video: 38%
🎯 UI: Updating progress for row 0: 38% - Video: 38%
✅ Progress bar updated to 38%
✅ Status updated: Downloading 38%
🎯 Smooth Progress: 39%
🎯 MAIN: Received download_progress signal: 39% - Video: 39%
🎯 Updated downloads table progress: 39%
🎯 UI Progress Updated: 39% - Video: 39%
🎯 UI: Updating progress for row 0: 39% - Video: 39%
✅ Progress bar updated to 39%
✅ Status updated: Downloading 39%
🎯 Smooth Progress: 40%
🎯 MAIN: Received download_progress signal: 40% - Video: 40%
🎯 Updated downloads table progress: 40%
🎯 UI Progress Updated: 40% - Video: 40%
🎯 UI: Updating progress for row 0: 40% - Video: 40%
✅ Progress bar updated to 40%
✅ Status updated: Downloading 40%
🎯 Smooth Progress: 41%
🎯 MAIN: Received download_progress signal: 41% - Video: 41%
🎯 Updated downloads table progress: 41%
🎯 UI Progress Updated: 41% - Video: 41%
🎯 UI: Updating progress for row 0: 41% - Video: 41%
✅ Progress bar updated to 41%
✅ Status updated: Downloading 41%
🎯 MAIN: Received download_progress signal: 42% - Video: 42%
🎯 Smooth Progress: 42%
🎯 Updated downloads table progress: 42%
🎯 UI Progress Updated: 42% - Video: 42%
🎯 UI: Updating progress for row 0: 42% - Video: 42%
✅ Progress bar updated to 42%
✅ Status updated: Downloading 42%
🎯 Smooth Progress: 43%
🎯 MAIN: Received download_progress signal: 43% - Video: 43%
🎯 Updated downloads table progress: 43%
🎯 UI Progress Updated: 43% - Video: 43%
🎯 UI: Updating progress for row 0: 43% - Video: 43%
✅ Progress bar updated to 43%
✅ Status updated: Downloading 43%
🎯 Smooth Progress: 44%
🎯 MAIN: Received download_progress signal: 44% - Video: 44%
🎯 Updated downloads table progress: 44%
🎯 UI Progress Updated: 44% - Video: 44%
🎯 UI: Updating progress for row 0: 44% - Video: 44%
✅ Progress bar updated to 44%
✅ Status updated: Downloading 44%
🎯 MAIN: Received download_progress signal: 45% - Video: 45%
🎯 Updated downloads table progress: 45%
🎯 UI Progress Updated: 45% - Video: 45%
🎯 Smooth Progress: 45%
🎯 UI: Updating progress for row 0: 45% - Video: 45%
✅ Progress bar updated to 45%
✅ Status updated: Downloading 45%
📊 Video Progress Target: 50%
🎯 Setting target progress to: 50%
🎯 MAIN: Received download_progress signal: 46% - Video: 46%
🎯 Updated downloads table progress: 46%
🎯 Smooth Progress: 46%
🎯 UI Progress Updated: 46% - Video: 46%
🎯 UI: Updating progress for row 0: 46% - Video: 46%
✅ Progress bar updated to 46%
✅ Status updated: Downloading 46%
🎯 Smooth Progress: 47%
🎯 MAIN: Received download_progress signal: 47% - Video: 47%
🎯 Updated downloads table progress: 47%
🎯 UI Progress Updated: 47% - Video: 47%
🎯 UI: Updating progress for row 0: 47% - Video: 47%
✅ Progress bar updated to 47%
✅ Status updated: Downloading 47%
🎯 Smooth Progress: 48%
🎯 MAIN: Received download_progress signal: 48% - Video: 48%
🎯 Updated downloads table progress: 48%
🎯 UI Progress Updated: 48% - Video: 48%
🎯 UI: Updating progress for row 0: 48% - Video: 48%
✅ Progress bar updated to 48%
✅ Status updated: Downloading 48%
🎯 Smooth Progress: 49%
🎯 MAIN: Received download_progress signal: 49% - Video: 49%
🎯 Updated downloads table progress: 49%
🎯 UI Progress Updated: 49% - Video: 49%
🎯 UI: Updating progress for row 0: 49% - Video: 49%
✅ Progress bar updated to 49%
✅ Status updated: Downloading 49%
🎯 Smooth Progress: 50%
🎯 MAIN: Received download_progress signal: 50% - Video: 50%
🎯 Updated downloads table progress: 50%
🎯 UI Progress Updated: 50% - Video: 50%
🎯 UI: Updating progress for row 0: 50% - Video: 50%
✅ Progress bar updated to 50%
✅ Status updated: Downloading 50%
🔊 Audio Progress Target: 76%
🎯 Smooth Progress: 51%
🎯 MAIN: Received download_progress signal: 51% - Video: 51%
🎯 Updated downloads table progress: 51%
🎯 UI Progress Updated: 51% - Video: 51%
🎯 UI: Updating progress for row 0: 51% - Video: 51%
✅ Progress bar updated to 51%
✅ Status updated: Downloading 51%
🎯 MAIN: Received download_progress signal: 52% - Video: 52%
🎯 Updated downloads table progress: 52%
🎯 Smooth Progress: 52%
🎯 UI Progress Updated: 52% - Video: 52%
🎯 UI: Updating progress for row 0: 52% - Video: 52%
✅ Progress bar updated to 52%
✅ Status updated: Downloading 52%
🎯 MAIN: Received download_progress signal: 53% - Video: 53%
🎯 Smooth Progress: 53%
🎯 Updated downloads table progress: 53%
🎯 UI Progress Updated: 53% - Video: 53%
🎯 UI: Updating progress for row 0: 53% - Video: 53%
✅ Progress bar updated to 53%
✅ Status updated: Downloading 53%
🎯 MAIN: Received download_progress signal: 54% - Video: 54%
🎯 Smooth Progress: 54%
🎯 Updated downloads table progress: 54%
🎯 UI Progress Updated: 54% - Video: 54%
🎯 UI: Updating progress for row 0: 54% - Video: 54%
✅ Progress bar updated to 54%
✅ Status updated: Downloading 54%
🎯 Smooth Progress: 55%
🎯 MAIN: Received download_progress signal: 55% - Video: 55%
🎯 Updated downloads table progress: 55%
🎯 UI Progress Updated: 55% - Video: 55%
🎯 UI: Updating progress for row 0: 55% - Video: 55%
✅ Progress bar updated to 55%
✅ Status updated: Downloading 55%
🎯 Smooth Progress: 56%
🎯 MAIN: Received download_progress signal: 56% - Video: 56%
🎯 Updated downloads table progress: 56%
🎯 UI Progress Updated: 56% - Video: 56%
🎯 UI: Updating progress for row 0: 56% - Video: 56%
✅ Progress bar updated to 56%
✅ Status updated: Downloading 56%
🎯 Smooth Progress: 57%
🎯 MAIN: Received download_progress signal: 57% - Video: 57%
🎯 Updated downloads table progress: 57%
🎯 UI Progress Updated: 57% - Video: 57%
🎯 UI: Updating progress for row 0: 57% - Video: 57%
✅ Progress bar updated to 57%
✅ Status updated: Downloading 57%
🎯 Smooth Progress: 58%
🎯 MAIN: Received download_progress signal: 58% - Video: 58%
🎯 Updated downloads table progress: 58%
🎯 UI Progress Updated: 58% - Video: 58%
🎯 UI: Updating progress for row 0: 58% - Video: 58%
✅ Progress bar updated to 58%
✅ Status updated: Downloading 58%
🎯 Smooth Progress: 59%
🎯 MAIN: Received download_progress signal: 59% - Video: 59%
🎯 Updated downloads table progress: 59%
🎯 UI Progress Updated: 59% - Video: 59%
🎯 UI: Updating progress for row 0: 59% - Video: 59%
✅ Progress bar updated to 59%
✅ Status updated: Downloading 59%
🎯 Smooth Progress: 60%
🎯 MAIN: Received download_progress signal: 60% - Video: 60%
🎯 Updated downloads table progress: 60%
🎯 UI Progress Updated: 60% - Video: 60%
🎯 UI: Updating progress for row 0: 60% - Video: 60%
✅ Progress bar updated to 60%
✅ Status updated: Downloading 60%
🎯 Smooth Progress: 61%
🎯 MAIN: Received download_progress signal: 61% - Video: 61%
🎯 Updated downloads table progress: 61%
🎯 UI Progress Updated: 61% - Video: 61%
🎯 UI: Updating progress for row 0: 61% - Video: 61%
✅ Progress bar updated to 61%
✅ Status updated: Downloading 61%
🎯 Smooth Progress: 62%
🎯 MAIN: Received download_progress signal: 62% - Video: 62%
🎯 Updated downloads table progress: 62%
🎯 UI Progress Updated: 62% - Video: 62%
🎯 UI: Updating progress for row 0: 62% - Video: 62%
✅ Progress bar updated to 62%
✅ Status updated: Downloading 62%
🎯 MAIN: Received download_progress signal: 63% - Video: 63%
🎯 Smooth Progress: 63%
🎯 Updated downloads table progress: 63%
🎯 UI Progress Updated: 63% - Video: 63%
🎯 UI: Updating progress for row 0: 63% - Video: 63%
✅ Progress bar updated to 63%
✅ Status updated: Downloading 63%
🎯 MAIN: Received download_progress signal: 64% - Video: 64%
🎯 Smooth Progress: 64%
🎯 Updated downloads table progress: 64%
🎯 UI Progress Updated: 64% - Video: 64%
🎯 UI: Updating progress for row 0: 64% - Video: 64%
✅ Progress bar updated to 64%
✅ Status updated: Downloading 64%
🎯 Smooth Progress: 65%
🎯 MAIN: Received download_progress signal: 65% - Video: 65%
🎯 Updated downloads table progress: 65%
🎯 UI Progress Updated: 65% - Video: 65%
🎯 UI: Updating progress for row 0: 65% - Video: 65%
✅ Progress bar updated to 65%
✅ Status updated: Downloading 65%
🎯 MAIN: Received download_progress signal: 66% - Video: 66%
🎯 Smooth Progress: 66%
🎯 Updated downloads table progress: 66%
🎯 UI Progress Updated: 66% - Video: 66%
🎯 UI: Updating progress for row 0: 66% - Video: 66%
✅ Progress bar updated to 66%
✅ Status updated: Downloading 66%
🎯 Smooth Progress: 67%
🎯 MAIN: Received download_progress signal: 67% - Video: 67%
🎯 Updated downloads table progress: 67%
🎯 UI Progress Updated: 67% - Video: 67%
🎯 UI: Updating progress for row 0: 67% - Video: 67%
✅ Progress bar updated to 67%
✅ Status updated: Downloading 67%
🎯 Smooth Progress: 68%
🎯 MAIN: Received download_progress signal: 68% - Video: 68%
🎯 Updated downloads table progress: 68%
🎯 UI Progress Updated: 68% - Video: 68%
🎯 UI: Updating progress for row 0: 68% - Video: 68%
✅ Progress bar updated to 68%
✅ Status updated: Downloading 68%
🎯 MAIN: Received download_progress signal: 69% - Video: 69%
🎯 Updated downloads table progress: 69%
🎯 UI Progress Updated: 69% - Video: 69%
🎯 Smooth Progress: 69%
🎯 UI: Updating progress for row 0: 69% - Video: 69%
✅ Progress bar updated to 69%
✅ Status updated: Downloading 69%
🎯 Smooth Progress: 70%
🎯 MAIN: Received download_progress signal: 70% - Video: 70%
🎯 Updated downloads table progress: 70%
🎯 UI Progress Updated: 70% - Video: 70%
🎯 UI: Updating progress for row 0: 70% - Video: 70%
✅ Progress bar updated to 70%
✅ Status updated: Downloading 70%
🎯 Smooth Progress: 71%
🎯 MAIN: Received download_progress signal: 71% - Video: 71%
🎯 Updated downloads table progress: 71%
🎯 UI Progress Updated: 71% - Video: 71%
🎯 UI: Updating progress for row 0: 71% - Video: 71%
✅ Progress bar updated to 71%
✅ Status updated: Downloading 71%
🎯 MAIN: Received download_progress signal: 72% - Video: 72%
🎯 Updated downloads table progress: 72%
🎯 UI Progress Updated: 72% - Video: 72%
🎯 Smooth Progress: 72%
🎯 UI: Updating progress for row 0: 72% - Video: 72%
✅ Progress bar updated to 72%
✅ Status updated: Downloading 72%
🎯 Smooth Progress: 73%
🎯 MAIN: Received download_progress signal: 73% - Video: 73%
🎯 Updated downloads table progress: 73%
🎯 UI Progress Updated: 73% - Video: 73%
🎯 UI: Updating progress for row 0: 73% - Video: 73%
✅ Progress bar updated to 73%
✅ Status updated: Downloading 73%
🎯 Smooth Progress: 74%
🎯 MAIN: Received download_progress signal: 74% - Video: 74%
🎯 Updated downloads table progress: 74%
🎯 UI Progress Updated: 74% - Video: 74%
🎯 UI: Updating progress for row 0: 74% - Video: 74%
✅ Progress bar updated to 74%
✅ Status updated: Downloading 74%
🎯 MAIN: Received download_progress signal: 75% - Video: 75%
🎯 Updated downloads table progress: 75%
🎯 Smooth Progress: 75%
🎯 UI Progress Updated: 75% - Video: 75%
🎯 UI: Updating progress for row 0: 75% - Video: 75%
✅ Progress bar updated to 75%
✅ Status updated: Downloading 75%
🎯 Smooth Progress: 76%
🎯 MAIN: Received download_progress signal: 76% - Video: 76%
🎯 Updated downloads table progress: 76%
🎯 UI Progress Updated: 76% - Video: 76%
🎯 UI: Updating progress for row 0: 76% - Video: 76%
✅ Progress bar updated to 76%
✅ Status updated: Downloading 76%
📊 Video Progress Target: 77%
🎯 Setting target progress to: 77%
🎯 Smooth Progress: 77%
🎯 MAIN: Received download_progress signal: 77% - Video: 77%
🎯 Updated downloads table progress: 77%
🎯 UI Progress Updated: 77% - Video: 77%
🎯 UI: Updating progress for row 0: 77% - Video: 77%
✅ Progress bar updated to 77%
✅ Status updated: Downloading 77%
🔊 Audio Progress Target: 96%
🎯 MAIN: Received download_progress signal: 78% - Video: 78%
🎯 Smooth Progress: 78%
🎯 Updated downloads table progress: 78%
🎯 UI Progress Updated: 78% - Video: 78%
🎯 UI: Updating progress for row 0: 78% - Video: 78%
✅ Progress bar updated to 78%
✅ Status updated: Downloading 78%
🎯 MAIN: Received download_progress signal: 79% - Video: 79%
🎯 Smooth Progress: 79%
🎯 Updated downloads table progress: 79%
🎯 UI Progress Updated: 79% - Video: 79%
🎯 UI: Updating progress for row 0: 79% - Video: 79%
✅ Progress bar updated to 79%
✅ Status updated: Downloading 79%
🎯 Smooth Progress: 80%
🎯 MAIN: Received download_progress signal: 80% - Video: 80%
🎯 Updated downloads table progress: 80%
🎯 UI Progress Updated: 80% - Video: 80%
🎯 UI: Updating progress for row 0: 80% - Video: 80%
✅ Progress bar updated to 80%
✅ Status updated: Downloading 80%
🎯 MAIN: Received download_progress signal: 81% - Video: 81%
🎯 Smooth Progress: 81%
🎯 Updated downloads table progress: 81%
🎯 UI Progress Updated: 81% - Video: 81%
🎯 UI: Updating progress for row 0: 81% - Video: 81%
✅ Progress bar updated to 81%
✅ Status updated: Downloading 81%
🎯 Smooth Progress: 82%
🎯 MAIN: Received download_progress signal: 82% - Video: 82%
🎯 Updated downloads table progress: 82%
🎯 UI Progress Updated: 82% - Video: 82%
🎯 UI: Updating progress for row 0: 82% - Video: 82%
✅ Progress bar updated to 82%
✅ Status updated: Downloading 82%
🎯 Smooth Progress: 83%
🎯 MAIN: Received download_progress signal: 83% - Video: 83%
🎯 Updated downloads table progress: 83%
🎯 UI Progress Updated: 83% - Video: 83%
🎯 UI: Updating progress for row 0: 83% - Video: 83%
✅ Progress bar updated to 83%
✅ Status updated: Downloading 83%
🎯 Smooth Progress: 84%
🎯 MAIN: Received download_progress signal: 84% - Video: 84%
🎯 Updated downloads table progress: 84%
🎯 UI Progress Updated: 84% - Video: 84%
🎯 UI: Updating progress for row 0: 84% - Video: 84%
✅ Progress bar updated to 84%
✅ Status updated: Downloading 84%
🎯 Smooth Progress: 85%
🎯 MAIN: Received download_progress signal: 85% - Video: 85%
🎯 Updated downloads table progress: 85%
🎯 UI Progress Updated: 85% - Video: 85%
🎯 UI: Updating progress for row 0: 85% - Video: 85%
✅ Progress bar updated to 85%
✅ Status updated: Downloading 85%
🎯 MAIN: Received download_progress signal: 86% - Video: 86%
🎯 Smooth Progress: 86%
🎯 Updated downloads table progress: 86%
🎯 UI Progress Updated: 86% - Video: 86%
🎯 UI: Updating progress for row 0: 86% - Video: 86%
✅ Progress bar updated to 86%
✅ Status updated: Downloading 86%
🎯 Smooth Progress: 87%
🎯 MAIN: Received download_progress signal: 87% - Video: 87%
🎯 Updated downloads table progress: 87%
🎯 UI Progress Updated: 87% - Video: 87%
🎯 UI: Updating progress for row 0: 87% - Video: 87%
✅ Progress bar updated to 87%
✅ Status updated: Downloading 87%
🎯 Smooth Progress: 88%
🎯 MAIN: Received download_progress signal: 88% - Video: 88%
🎯 Updated downloads table progress: 88%
🎯 UI Progress Updated: 88% - Video: 88%
🎯 UI: Updating progress for row 0: 88% - Video: 88%
✅ Progress bar updated to 88%
✅ Status updated: Downloading 88%
🎯 Smooth Progress: 89%
🎯 MAIN: Received download_progress signal: 89% - Video: 89%
🎯 Updated downloads table progress: 89%
🎯 UI Progress Updated: 89% - Video: 89%
🎯 UI: Updating progress for row 0: 89% - Video: 89%
✅ Progress bar updated to 89%
✅ Status updated: Downloading 89%
🎯 Smooth Progress: 90%
🎯 MAIN: Received download_progress signal: 90% - Video: 90%
🎯 Updated downloads table progress: 90%
🎯 UI Progress Updated: 90% - Video: 90%
🎯 UI: Updating progress for row 0: 90% - Video: 90%
✅ Progress bar updated to 90%
✅ Status updated: Downloading 90%
🎯 Smooth Progress: 91%
🎯 MAIN: Received download_progress signal: 91% - Video: 91%
🎯 Updated downloads table progress: 91%
🎯 UI Progress Updated: 91% - Video: 91%
🎯 UI: Updating progress for row 0: 91% - Video: 91%
✅ Progress bar updated to 91%
✅ Status updated: Downloading 91%
🎯 Smooth Progress: 92%
🎯 MAIN: Received download_progress signal: 92% - Video: 92%
🎯 Updated downloads table progress: 92%
🎯 UI Progress Updated: 92% - Video: 92%
🎯 UI: Updating progress for row 0: 92% - Video: 92%
✅ Progress bar updated to 92%
✅ Status updated: Downloading 92%
🎯 Smooth Progress: 93%
🎯 MAIN: Received download_progress signal: 93% - Video: 93%
🎯 Updated downloads table progress: 93%
🎯 UI Progress Updated: 93% - Video: 93%
🎯 UI: Updating progress for row 0: 93% - Video: 93%
✅ Progress bar updated to 93%
✅ Status updated: Downloading 93%
🎯 Smooth Progress: 94%
🎯 MAIN: Received download_progress signal: 94% - Video: 94%
🎯 Updated downloads table progress: 94%
🎯 UI Progress Updated: 94% - Video: 94%
🎯 UI: Updating progress for row 0: 94% - Video: 94%
✅ Progress bar updated to 94%
✅ Status updated: Downloading 94%
🎯 Smooth Progress: 95%
🎯 MAIN: Received download_progress signal: 95% - Video: 95%
🎯 Updated downloads table progress: 95%
🎯 UI Progress Updated: 95% - Video: 95%
🎯 UI: Updating progress for row 0: 95% - Video: 95%
✅ Progress bar updated to 95%
✅ Status updated: Downloading 95%
🎯 MAIN: Received download_progress signal: 96% - Video: 96%
🎯 Updated downloads table progress: 96%
🎯 Smooth Progress: 96%
🎯 UI Progress Updated: 96% - Video: 96%
🎯 UI: Updating progress for row 0: 96% - Video: 96%
✅ Progress bar updated to 96%
✅ Status updated: Downloading 96%
📊 Video Progress Target: 98%
🎯 Setting target progress to: 98%
🎯 Smooth Progress: 97%
🎯 MAIN: Received download_progress signal: 97% - Video: 97%
🎯 Updated downloads table progress: 97%
🎯 UI Progress Updated: 97% - Video: 97%
🎯 UI: Updating progress for row 0: 97% - Video: 97%
✅ Progress bar updated to 97%
✅ Status updated: Downloading 97%
🎯 MAIN: Received download_progress signal: 98% - Video: 98%
🎯 Smooth Progress: 98%
🎯 Updated downloads table progress: 98%
🎯 UI Progress Updated: 98% - Video: 98%
🎯 UI: Updating progress for row 0: 98% - Video: 98%
✅ Progress bar updated to 98%
✅ Status updated: Downloading 98%
✅ Episode download completed successfully
🎯 MAIN: Received download_progress signal: 95% - Download completed, starting merge...
🎯 Updated downloads table progress: 95%
🎯 UI Progress Updated: 95% - Download completed, starting merge...
🎯 UI: Updating progress for row 0: 95% - Download completed, starting merge...
Downloaded files found, proceeding to merge...
✅ Progress bar updated to 95%
✅ Status updated: Downloading 95%
🔧 Starting episode merge with mkvmerge...
🔊 Adding Arabic audio to the episode MKV file
📝 Adding Arabic subtitle to the episode MKV file
Running mkvmerge command: F:\TEEFA\MBC\shahid_template\OSN_NEW\binaries\mkvmerge.exe -o F:\TEEFA\MBC\shahid_template\OSN_NEW\downloads\Sharab Al Toot AKA One Love\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.mkv F:\TEEFA\MBC\shahid_template\OSN_NEW\cache\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.mp4 --language 0:ara --track-name 0:Arabic F:\TEEFA\MBC\shahid_template\OSN_NEW\cache\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.ar.m4a --language 0:ara --track-name 0:Arabic F:\TEEFA\MBC\shahid_template\OSN_NEW\cache\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.ar.srt
🎯 MAIN: Received download_progress signal: 90% - Merging episode files...
🎯 Updated downloads table progress: 90%
🎯 UI Progress Updated: 90% - Merging episode files...
🎯 UI: Updating progress for row 0: 90% - Merging episode files...
mkvmerge v57.0.0 ('Till The End') 64-bit
'F:\TEEFA\MBC\shahid_template\OSN_NEW\cache\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.mp4': Using the demultiplexer for the format 'QuickTime/MP4'.
✅ Progress bar updated to 90%
✅ Status updated: Downloading 90%
'F:\TEEFA\MBC\shahid_template\OSN_NEW\cache\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.ar.m4a': Using the demultiplexer for the format 'QuickTime/MP4'.
'F:\TEEFA\MBC\shahid_template\OSN_NEW\cache\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.ar.srt': Using the demultiplexer for the format 'SRT subtitles'.
'F:\TEEFA\MBC\shahid_template\OSN_NEW\cache\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.mp4' track 0: Using the output module for the format 'AVC/H.264'.
'F:\TEEFA\MBC\shahid_template\OSN_NEW\cache\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.ar.m4a' track 0: Using the output module for the format 'AAC'.
'F:\TEEFA\MBC\shahid_template\OSN_NEW\cache\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.ar.srt' track 0: Using the output module for the format 'text subtitles'.
The file 'F:\TEEFA\MBC\shahid_template\OSN_NEW\downloads\Sharab Al Toot AKA One Love\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.mkv' has been opened for writing.
'F:\TEEFA\MBC\shahid_template\OSN_NEW\cache\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.mp4' track 0: Extracted the aspect ratio information from the MPEG-4 layer 10 (AVC) video data and set the display dimensions to 640/360.
Progress: 100%
The cue entries (the index) are being written...
Multiplexing took 0 seconds.
✅ Episode merge completed successfully: F:\TEEFA\MBC\shahid_template\OSN_NEW\downloads\Sharab Al Toot AKA One Love\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.mkv
🎯 MAIN: Received download_progress signal: 95% - Episode merge completed!
🎯 Updated downloads table progress: 95%
🎯 UI Progress Updated: 95% - Episode merge completed!
🎯 UI: Updating progress for row 0: 95% - Episode merge completed!
✅ Progress bar updated to 95%
✅ Status updated: Downloading 95%
📸 Poster moved to F:\TEEFA\MBC\shahid_template\OSN_NEW\downloads\Sharab Al Toot AKA One Love\poster.jpg
Cache directory F:\TEEFA\MBC\shahid_template\OSN_NEW\cache has been deleted.
🎯 MAIN: Received download_progress signal: 99% - Video: 99%
🎯 Smooth Progress: 99%
🎯 Updated downloads table progress: 99%
🎯 UI Progress Updated: 99% - Video: 99%
🎯 UI: Updating progress for row 0: 99% - Video: 99%
✅ Progress bar updated to 99%
✅ Status updated: Downloading 99%
🎯 MAIN: Received download_progress signal: 100% - Video: 100%
🎯 Smooth Progress: 100%
🎯 Updated downloads table progress: 100%
🎯 UI Progress Updated: 100% - Video: 100%
🎯 UI: Updating progress for row 0: 100% - Video: 100%
✅ Progress bar updated to 100%
✅ Status updated: Completed
🎯 MAIN: Received download_completed signal: True - F:\TEEFA\MBC\shahid_template\OSN_NEW\downloads\Sharab Al Toot AKA One Love\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.mkv
✅ Download completed for row 0: F:\TEEFA\MBC\shahid_template\OSN_NEW\downloads\Sharab Al Toot AKA One Love\Sharab Al Toot AKA One Love S03E01.360p.OSN+.WEB-DL.H264.AAC.mkv
🔄 Moving to next sequential download (1/2)
🎯 MAIN: Received download_progress signal: 100% - ✅ Episode download completed successfully!
🎯 Updated downloads table progress: 100%
🎯 UI Progress Updated: 100% - ✅ Episode download completed successfully!
🎯 UI: Updating progress for row 0: 100% - ✅ Episode download completed successfully!
✅ Progress bar updated to 100%
✅ Status updated: Completed
🔄 Moving to next sequential download (2/2)
🎬 Starting sequential download 2/2 (Row 1)
🔄 This download needs stream data fetching...
🔄 Fetching stream details for Episode 2...
📡 Fetching stream details for Episode 2 - Stream ID: 106065
❌ Failed to fetch stream details for Episode 2
❌ Failed to fetch stream details for Episode 2