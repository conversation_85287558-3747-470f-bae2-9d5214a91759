<!DOCTYPE html>
<html lang="en">
<head>
    <title>Shahid Player</title>
    <link rel="icon" type="image/png" href="play-on.png">
    <link rel="stylesheet" href="tod_skin.css">
    <style>
        html,body,#player{height:100% !important;overflow:hidden !important}
        body{margin:0 auto;background-color:#000}
        .jw-aspect.jw-reset[style*=padding-top]{padding-top:unset !important}
    </style>
</head>
<body>
    <div id="player"></div>
    <button id="playStreamButton" style="display: none;"></button>
    <script src="js/jwplayer.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/shaka-player/dist/shaka-player.ui.js"></script>
    <script src="js/provider.shaka.js"></script>
    <script src="player.js"></script>
    <script>
        // Get URL parameters
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                mpd: params.get('mpd') || '',
                keyId: params.get('keyId') || '',
                key: params.get('key') || ''
            };
        }

        // Initialize player when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            const params = getUrlParams();
            const mpdUrl = params.mpd;
            const keyId = params.keyId;
            const key = params.key;

            console.log('Initializing player with:', { mpdUrl, keyId, key });

            if (mpdUrl) {
                // Set up the button with stream data
                const playButton = document.getElementById('playStreamButton');
                if (playButton) {
                    playButton.dataset.mpdUrl = mpdUrl;
                    playButton.dataset.keyData = keyId && key ? `${keyId}:${key}` : '';

                    // Auto-start playback
                    setTimeout(() => {
                        console.log('Auto-clicking play button');
                        playButton.click();

                        // Unmute audio after a delay
                        setTimeout(() => {
                            try {
                                const player = jwplayer('player');
                                if (player) {
                                    player.setMute(false);
                                    player.setVolume(100);
                                    console.log('Audio unmuted');
                                }

                                const video = document.querySelector('video');
                                if (video) {
                                    video.muted = false;
                                    video.volume = 1.0;
                                }
                            } catch (e) {
                                console.log('Error unmuting:', e);
                            }
                        }, 2000);
                    }, 500);
                }
            } else {
                console.error('No MPD URL provided');
                document.getElementById('player').innerHTML =
                    '<div style="color: white; text-align: center; padding: 20px; font-size: 18px;">No stream URL provided</div>';
            }
        });
    </script>
</body>
</html>
