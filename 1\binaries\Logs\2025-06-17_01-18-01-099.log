﻿LOG 2025/06/17
Save Path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\Logs
Task Start: 2025/06/17 01:18:01
Task CommandLine: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\N_m3u8DL-RE.exe https://osn-video.anghcdn.co/public/156145-57962-PR681514-BX-AS045372-284272-689b81d9fa0142f5cac7fe145e1b432c-1749804607/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD01Nzk2MiZleHBpcnk9MTc1MDE1NTQ3OCZzaWduYXR1cmU9MjM2YjZjYWJmYjIyNGFiNTg0NjczMmIzZjNhZTg0NTcwOWQ2YjU1NSZzdHJlYW0taWQ9MTI5MDY5JnVzZXItaWQ9MTUyMTE3ODk2 -mt --select-video id=cbb9b9d8-0d45-43ac-a360-2e57c9d146a9 --select-audio lang=ar|tr:for=all --select-subtitle lang=en|ar --tmp-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\cache --save-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\cache --save-name "Al Mushardoon S01E33.720p.OSN+.WEB-DL.H264.AAC" --decryption-binary-path=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\mp4decrypt.exe --key-text-file=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\KEYS\KEYS.txt --log-level OFF

01:18:01.102 EXTRA: ffmpeg => C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\ffmpeg.exe
01:18:01.450 EXTRA: VideoFilter => GroupIdReg: cbb9b9d8-0d45-43ac-a360-2e57c9d146a9 For: best
01:18:01.450 EXTRA: AudioFilter => LanguageReg: ar|tr For: all
01:18:01.451 EXTRA: SubtitleFilter => LanguageReg: en|ar For: best
