﻿LOG 2025/06/17
Save Path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\Logs
Task Start: 2025/06/17 01:01:33
Task CommandLine: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\N_m3u8DL-RE.exe https://osn-video.anghcdn.co/public/156057-57918-PR681513-BX-AS045371-284230-6fe32a4158cc18693243919eb6fd6970-1749797408/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD01NzkxOCZleHBpcnk9MTc1MDE1NDQ4OSZzaWduYXR1cmU9MzM1ZTM5MDIyZjRlZmNhMjQ4YjQwMmFjM2FlZmJjMTc0NTA4NTQzYSZzdHJlYW0taWQ9MTI5MDAzJnVzZXItaWQ9MTUyMTE3ODk2 -mt --select-video id=6017de55-b4c1-4f9f-ad84-c162405b8b81 --select-audio lang=ar --drop-subtitle .* --tmp-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\cache --save-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\cache --save-name "Al Mushardoon S01E32.720p.OSN+.WEB-DL.H264.AAC" --decryption-binary-path=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\mp4decrypt.exe --key-text-file=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\KEYS\KEYS.txt --log-level OFF

01:01:33.983 EXTRA: ffmpeg => C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\ffmpeg.exe
01:01:34.425 EXTRA: DropSubtitleFilter => For: best
01:01:34.425 EXTRA: VideoFilter => GroupIdReg: 6017de55-b4c1-4f9f-ad84-c162405b8b81 For: best
01:01:34.425 EXTRA: AudioFilter => LanguageReg: ar For: best
