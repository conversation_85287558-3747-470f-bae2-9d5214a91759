# ///////////////////////////////////////////////////////////////
#
# BY: WANDERSON M.PIMENTA
# PROJECT MADE WITH: Qt Designer and PySide6
# V: 1.0.0
#
# This project can be used freely for all uses, as long as they maintain the
# respective credits only in the Python scripts, any information in the visual
# interface (GUI) can be modified without any implication.
#
# There are limitations on Qt licenses if you want to use your products
# commercially, I recommend reading them on the official website:
# https://doc.qt.io/qtforpython/licenses.html
#
# ///////////////////////////////////////////////////////////////

import sys
import os
from pathlib import Path

# IMPORT / GUI AND MODULES AND WIDGETS
# ///////////////////////////////////////////////////////////////
from modules import *
from widgets import *

# IMPORT YANGO MODULES
# ///////////////////////////////////////////////////////////////
from modules.yango_api import YangoAPI
from modules.yango_ui import YangoUi
from modules.yango_drm import YangoDRM

os.environ["QT_FONT_DPI"] = "96" # FIX Problem for High DPI and Scale above 100%

# SET AS GLOBAL WIDGETS
# ///////////////////////////////////////////////////////////////
widgets = None

class MainWindow(QMainWindow):
    def __init__(self):
        QMainWindow.__init__(self)

        # SET AS GLOBAL WIDGETS
        # ///////////////////////////////////////////////////////////////
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        global widgets
        widgets = self.ui

        # USE CUSTOM TITLE BAR | USE AS "False" FOR MAC OR LINUX
        # ///////////////////////////////////////////////////////////////
        Settings.ENABLE_CUSTOM_TITLE_BAR = True

        # APP NAME
        # ///////////////////////////////////////////////////////////////
        title = "YANGO PLAY"
        description = "YANGO PLAY"
        # APPLY TEXTS
        self.setWindowTitle(title)
        if hasattr(widgets, 'titleLeftDescription'):
            widgets.titleLeftDescription.setText(description)

        # TOGGLE MENU
        # ///////////////////////////////////////////////////////////////
        widgets.toggleButton.clicked.connect(lambda: UIFunctions.toggleMenu(self, True))

        # SET UI DEFINITIONS
        # ///////////////////////////////////////////////////////////////
        UIFunctions.uiDefinitions(self)

        # QTableWidget PARAMETERS
        # ///////////////////////////////////////////////////////////////
        if hasattr(widgets, 'tableWidget'):
            widgets.tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # BUTTONS CLICK
        # ///////////////////////////////////////////////////////////////

        # LEFT MENUS
        widgets.btn_home.clicked.connect(self.buttonClick)
        widgets.btn_widgets.clicked.connect(self.buttonClick)
        widgets.btn_new.clicked.connect(self.buttonClick)
        widgets.btn_save.clicked.connect(self.buttonClick)

        # EXTRA LEFT BOX
        def openCloseLeftBox():
            UIFunctions.toggleLeftBox(self, True)
        if hasattr(widgets, 'toggleLeftBox'):
            widgets.toggleLeftBox.clicked.connect(openCloseLeftBox)
        if hasattr(widgets, 'extraCloseColumnBtn'):
            widgets.extraCloseColumnBtn.clicked.connect(openCloseLeftBox)

        # EXTRA RIGHT BOX
        def openCloseRightBox():
            UIFunctions.toggleRightBox(self, True)

        # SETTINGS BUTTON - Open Settings Dialog
        def openSettings():
            try:
                if hasattr(window, 'yango_ui') and window.yango_ui:
                    window.yango_ui.open_settings()
                else:
                    print("❌ YANGO UI not available")
            except Exception as e:
                print(f"❌ Error opening settings: {e}")

        if hasattr(widgets, 'settingsTopBtn'):
            widgets.settingsTopBtn.clicked.connect(openSettings)

        # SHOW APP
        # ///////////////////////////////////////////////////////////////
        self.show()

        # SET CUSTOM THEME
        # ///////////////////////////////////////////////////////////////
        useCustomTheme = False
        themeFile = "themes/py_dracula_dark.qss"

        # SET THEME AND HACKS
        if useCustomTheme:
            # LOAD AND APPLY STYLE
            UIFunctions.theme(self, themeFile, True)

            # SET HACKS
            AppFunctions.setThemeHack(self)

        # SET HOME PAGE AND SELECT MENU
        # ///////////////////////////////////////////////////////////////
        widgets.stackedWidget.setCurrentWidget(widgets.home)
        widgets.btn_home.setStyleSheet(UIFunctions.selectMenu(widgets.btn_home.styleSheet()))

        # Initialize YANGO modules
        self.yango_api = YangoAPI()
        self.yango_drm = YangoDRM()
        self.yango_ui = YangoUi(self)

        # Connect YANGO API signals
        self.yango_api.movie_found.connect(self.handle_movie_found)
        self.yango_api.series_found.connect(self.handle_series_found)
        self.yango_api.episodes_found.connect(self.handle_episodes_found)
        self.yango_api.streams_found.connect(self.handle_streams_found)
        self.yango_api.error_occurred.connect(self.handle_api_error)

        # Connect DRM signals
        self.yango_drm.keys_extracted.connect(self.handle_drm_keys)
        self.yango_drm.drm_error.connect(self.handle_drm_error)

    # BUTTONS CLICK
    # Post here your functions for clicked buttons
    # ///////////////////////////////////////////////////////////////
    def buttonClick(self):
        # GET BUTTON CLICKED
        btn = self.sender()
        btnName = btn.objectName()

        # SHOW HOME PAGE
        if btnName == "btn_home":
            widgets.stackedWidget.setCurrentWidget(widgets.home)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # SHOW MOVIES PAGE
        if btnName == "btn_widgets":
            widgets.stackedWidget.setCurrentWidget(widgets.widgets)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # SHOW SERIES PAGE
        if btnName == "btn_new":
            widgets.stackedWidget.setCurrentWidget(widgets.new_page)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # SHOW SETTINGS PAGE
        if btnName == "btn_save":
            widgets.stackedWidget.setCurrentWidget(widgets.save_page)
            UIFunctions.resetStyle(self, btnName)
            btn.setStyleSheet(UIFunctions.selectMenu(btn.styleSheet()))

        # PRINT BTN NAME
        print(f'Button "{btnName}" pressed!')

    # YANGO API HANDLERS
    # ///////////////////////////////////////////////////////////////
    def handle_movie_found(self, movie_data):
        """Handle when movie is found by YANGO API"""
        print(f"🎬 Movie found: {movie_data.get('title', 'Unknown')}")
        # Forward to UI handler
        if hasattr(self, 'yango_ui') and self.yango_ui:
            self.yango_ui.handle_movie_found(movie_data)

    def handle_series_found(self, series_data):
        """Handle when series is found by YANGO API"""
        print(f"📺 Series found: {series_data.get('title', 'Unknown')}")
        # Forward to UI handler
        if hasattr(self, 'yango_ui') and self.yango_ui:
            self.yango_ui.handle_series_found(series_data)

    def handle_episodes_found(self, episodes_data):
        """Handle when episodes are found by YANGO API"""
        print(f"📋 Episodes found: {len(episodes_data)} episodes")
        # Forward to UI handler
        if hasattr(self, 'yango_ui') and self.yango_ui:
            self.yango_ui.handle_episodes_found(episodes_data)

    def handle_streams_found(self, streams_data):
        """Handle when streams are found by YANGO API"""
        print(f"🎬 Streams found for content")
        # Forward to UI handler
        if hasattr(self, 'yango_ui') and self.yango_ui:
            self.yango_ui.handle_streams_found(streams_data)

    def handle_api_error(self, error_message):
        """Handle API errors"""
        print(f"❌ API Error: {error_message}")
        # Forward to UI handler
        if hasattr(self, 'yango_ui') and self.yango_ui:
            self.yango_ui.handle_api_error(error_message)

    def handle_drm_keys(self, keys):
        """Handle DRM keys extraction"""
        print(f"🔑 DRM keys extracted: {len(keys)} keys")
        if hasattr(self, 'yango_ui'):
            self.yango_ui.status_updated.emit("DRM keys extracted successfully")

    def handle_drm_error(self, error_message):
        """Handle DRM errors"""
        print(f"❌ DRM Error: {error_message}")
        if hasattr(self, 'yango_ui'):
            self.yango_ui.show_message("DRM Error", error_message)

    # RESIZE EVENTS
    # ///////////////////////////////////////////////////////////////
    def resizeEvent(self, event):
        # Update Size Grips
        UIFunctions.resize_grips(self)

    # MOUSE CLICK EVENTS
    # ///////////////////////////////////////////////////////////////
    def mousePressEvent(self, event):
        # SET DRAG POS WINDOW
        self.dragPos = event.globalPos()

        # PRINT MOUSE EVENTS
        if event.buttons() == Qt.LeftButton:
            print('Mouse click: LEFT CLICK')
        if event.buttons() == Qt.RightButton:
            print('Mouse click: RIGHT CLICK')

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setWindowIcon(QIcon("YANGOTO.ico"))
    window = MainWindow()

    # Save data when application is about to quit
    def save_on_exit():
        try:
            if hasattr(window, 'yango_ui') and window.yango_ui:
                # Cleanup YANGO player
                window.yango_ui.cleanup_player()
                # Save recent URLs
                window.yango_ui.save_recent_urls_to_file()
                print("💾 Saved application data before exit")
        except Exception as e:
            print(f"❌ Error saving data on exit: {str(e)}")

    app.aboutToQuit.connect(save_on_exit)

    sys.exit(app.exec_())
