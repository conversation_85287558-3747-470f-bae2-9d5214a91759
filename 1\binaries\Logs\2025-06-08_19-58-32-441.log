﻿LOG 2025/06/08
Save Path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\Logs
Task Start: 2025/06/08 19:58:32
Task CommandLine: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\N_m3u8DL-RE.exe https://osn-video.anghcdn.co/public/154185-57504-PR681511-BX-AS045369-283438-05d4503d996d2807eb8151af48a9c795-1749048008/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD01NzUwNCZleHBpcnk9MTc0OTQ0NTAyNyZzaWduYXR1cmU9NGUzNTcwNGVlZDRmZWE3YmUyOTNkZDc4ODNmODViMWY1MjkzNWIyMCZzdHJlYW0taWQ9MTI4MDExJnVzZXItaWQ9MTAzMDI2MDY5 -mt --select-video id=d668b826-8fb5-4e84-b712-c17bb40a2287 --select-audio lang=ar --drop-subtitle .* --tmp-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache --save-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\cache --save-name "Al Mushardoon S01E30.360p.OSN+.WEB-DL.H264.AAC" --decryption-binary-path=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\mp4decrypt.exe --key-text-file=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\KEYS\OSNPLUS_KEYS.txt --log-level OFF

19:58:32.444 EXTRA: ffmpeg => C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\binaries\ffmpeg.exe
19:58:32.798 EXTRA: DropSubtitleFilter => For: best
19:58:32.798 EXTRA: VideoFilter => GroupIdReg: d668b826-8fb5-4e84-b712-c17bb40a2287 For: best
19:58:32.798 EXTRA: AudioFilter => LanguageReg: ar For: best
