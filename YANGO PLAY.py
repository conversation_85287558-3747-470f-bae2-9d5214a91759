import json
import os
import re
import shutil
import subprocess
import requests
from tabulate import tabulate
from colorama import Fore, Style, init
import base64
import xmltodict
from rich.console import Console
from pywidevine.cdm import Cdm, Device, PSSH
import sqlite3
import os
from tqdm import tqdm
from datetime import datetime
import xml.etree.ElementTree as ET
import xml.etree.ElementTree as ET
from xml.etree import ElementTree
import inspect
import threading
import time
import sys
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime, timedelta
# وظائف تحميل البوسترات والتحقق من توفر المسلسلات مدمجة مباشرة في هذا الملف

# وظيفة تحميل صورة من URL وحفظها في المسار المحدد
def download_image(url, file_path, verbose=True):
    """
    تحميل صورة من URL وحفظها في المسار المحدد.

    المعاملات:
        url: رابط الصورة
        file_path: مسار الملف للحفظ
        verbose: عرض رسائل تفصيلية (افتراضي: True)

    الإرجاع:
        True إذا نجح التحميل، False إذا فشل
    """
    try:
        # التحقق من صحة URL
        if not url or not isinstance(url, str):
            if verbose:
                print(f"{Fore.RED}[ERROR] Invalid URL: {url}{Style.RESET_ALL}")
            return False

        # استخراج اسم الملف من المسار للعرض
        file_name = os.path.basename(file_path)

        # إضافة مقاس الصورة إلى URL إذا لم يكن موجودًا بالفعل
        if "ott-avatars.akamaized.net" in url and not re.search(r'/\d+x\d+$', url):
            # إضافة مقاس افتراضي للصورة بناءً على نوع الصورة
            if "vertical" in file_path.lower():
                size = "1000x1500"  # مقاس مناسب للبوسترات العمودية
            elif "logo" in file_path.lower():
                if "horizontal_logo" in file_path.lower():
                    size = "960x540"  # مقاس مناسب للشعارات الأفقية
                else:
                    size = "640x360"  # مقاس مناسب للشعارات العادية
            else:
                size = "1920x1080"  # مقاس مناسب للصور الأفقية

            url = f"{url}/{size}"
            if verbose:
                print(f"{Fore.CYAN}[DEBUG] Added size to URL: {url}{Style.RESET_ALL}")

        if verbose:
            print(f"{Fore.CYAN}[DEBUG] Downloading from: {url}{Style.RESET_ALL}")

        # التأكد من وجود المجلد
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        response = requests.get(url, stream=True, timeout=30)  # إضافة timeout لتجنب التعليق
        response.raise_for_status()

        with open(file_path, 'wb') as file:
            for chunk in response.iter_content(chunk_size=8192):
                file.write(chunk)

        # التحقق من أن الملف تم إنشاؤه بنجاح وله حجم
        if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
            if verbose:
                print(f"{Fore.GREEN}[DEBUG] Successfully downloaded to: {file_path} ({os.path.getsize(file_path)} bytes){Style.RESET_ALL}")
            return True
        else:
            if verbose:
                print(f"{Fore.RED}[ERROR] File was created but is empty: {file_path}{Style.RESET_ALL}")
            return False

    except requests.exceptions.Timeout as timeout_error:
        if verbose:
            print(f"{Fore.RED}[ERROR] Download timed out: {str(timeout_error)}{Style.RESET_ALL}")
        return False
    except requests.exceptions.RequestException as req_error:
        if verbose:
            print(f"{Fore.RED}[ERROR] Request error: {str(req_error)}{Style.RESET_ALL}")

        # محاولة مرة أخرى بمقاس مختلف إذا كان الخطأ 400 أو 404
        if ("400 Client Error" in str(req_error) or "404 Client Error" in str(req_error)) and "ott-avatars.akamaized.net" in url:
            if verbose:
                print(f"{Fore.YELLOW}[DEBUG] Trying alternative sizes...{Style.RESET_ALL}")

            try:
                # تجربة مقاسات مختلفة
                for size in ["960x540", "640x360", "292x440", "1000x1500", "500x750", "1920x1080", "3840x1677"]:
                    # إزالة أي مقاس موجود بالفعل
                    base_url = re.sub(r'/\d+x\d+$', '', url)
                    retry_url = f"{base_url}/{size}"

                    if verbose:
                        print(f"{Fore.YELLOW}[DEBUG] Trying size: {size}, URL: {retry_url}{Style.RESET_ALL}")

                    try:
                        retry_response = requests.get(retry_url, stream=True, timeout=30)
                        retry_response.raise_for_status()

                        with open(file_path, 'wb') as file:
                            for chunk in retry_response.iter_content(chunk_size=8192):
                                file.write(chunk)

                        # التحقق من أن الملف تم إنشاؤه بنجاح وله حجم
                        if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                            if verbose:
                                print(f"{Fore.GREEN}[DEBUG] Successfully downloaded with alternative size: {size} ({os.path.getsize(file_path)} bytes){Style.RESET_ALL}")
                            return True
                        else:
                            if verbose:
                                print(f"{Fore.RED}[DEBUG] File was created but is empty with size {size}{Style.RESET_ALL}")
                    except Exception as retry_error:
                        if verbose:
                            print(f"{Fore.RED}[DEBUG] Failed with size {size}: {str(retry_error)}{Style.RESET_ALL}")
                        # استمر في الحلقة لتجربة المقاس التالي
                        continue
            except Exception as alt_error:
                if verbose:
                    print(f"{Fore.RED}[ERROR] All alternative sizes failed: {str(alt_error)}{Style.RESET_ALL}")
                pass
        return False
    except Exception as e:
        if verbose:
            print(f"{Fore.RED}[ERROR] Download failed with unexpected error: {str(e)}{Style.RESET_ALL}")
        return False

# وظيفة تحميل بوسترات الفيلم أو المسلسل
def download_movie_posters(content_id, output_dir=None, movie_data=None, verbose=True):
    """
    تحميل بوسترات الفيلم أو المسلسل وحفظها في مجلد الفيلم.

    المعاملات:
        content_id: معرف المحتوى
        output_dir: مسار المجلد الذي سيتم حفظ البوسترات فيه (اختياري)
        movie_data: بيانات الفيلم أو المسلسل من API (اختياري)
        verbose: عرض رسائل تفصيلية (افتراضي: True)

    الإرجاع:
        قائمة بمسارات الملفات التي تم تحميلها
    """
    if verbose:
        print(f"{Fore.YELLOW}[INFO] Downloading posters for content ID: {content_id}{Style.RESET_ALL}")

    try:
        # إذا لم يتم تمرير بيانات الفيلم، قم بجلبها من API
        if movie_data is None:
            # استخدام وظيفة fetch_and_print_movie_card لجلب بيانات الفيلم
            movie_card_response = fetch_and_print_movie_card(content_id)
            if movie_card_response and "data" in movie_card_response and "movieByContentUuid" in movie_card_response["data"]:
                # استخدام البيانات من استجابة API مباشرة
                return download_posters_from_api_response(movie_card_response["data"]["movieByContentUuid"], output_dir, verbose)
            else:
                if verbose:
                    print(f"{Fore.RED}[ERROR] Failed to fetch movie data using MovieCard API{Style.RESET_ALL}")

                # إعداد رأس الطلب
                headers = {
                    "accept": "*/*",
                    "accept-language": "ru,en;q=0.9",
                    "content-type": "application/json",
                    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36"
                }

                # إعداد عنوان URL للطلب
                url = "https://kp-graphql-api.movies.yango.com/graphql/?operationName=MovieCard"

                # إعداد بيانات الطلب
                payload = {
                    "operationName": "MovieCard",
                    "variables": {
                        "contentUuid": content_id,
                        "isAuthorized": True,
                        "withUserData": True,
                    },
                    "query": "query MovieCard($contentUuid: String!, $isAuthorized: Boolean!, $withUserData: Boolean!) { movieByContentUuid(contentUuid: $contentUuid) { id contentId title { localized original __typename } gallery { covers { horizontal { avatarsUrl __typename } __typename } logos { horizontal { avatarsUrl __typename } rightholderForCover { image { avatarsUrl __typename } __typename } __typename } posters { vertical { avatarsUrl __typename } verticalWithRightholderLogo { avatarsUrl __typename } __typename } __typename } __typename } }"
                }

                # إرسال الطلب
                response = requests.post(url, json=payload, headers=headers)
                response.raise_for_status()

                # تحليل البيانات
                data = response.json()
                movie_data = data.get("data", {}).get("movieByContentUuid", {})

                if not movie_data:
                    if verbose:
                        print(f"{Fore.RED}[ERROR] No data found for content ID: {content_id}{Style.RESET_ALL}")
                    return []

        # استخدام وظيفة download_posters_from_api_response لتحميل البوسترات
        return download_posters_from_api_response(movie_data, output_dir, verbose)

    except requests.exceptions.RequestException as e:
        if verbose:
            print(f"{Fore.RED}[ERROR] Failed to fetch movie data: {e}{Style.RESET_ALL}")
        return []
    except Exception as e:
        if verbose:
            print(f"{Fore.RED}[ERROR] An unexpected error occurred: {e}{Style.RESET_ALL}")
        return []

# وظيفة تحميل البوسترات من استجابة API مباشرة
def download_posters_from_api_response(movie_data, output_dir, verbose=True):
    """
    تحميل البوسترات من استجابة API مباشرة.

    المعاملات:
        movie_data: بيانات الفيلم أو المسلسل من API
        output_dir: مسار المجلد الذي سيتم حفظ البوسترات فيه
        verbose: عرض رسائل تفصيلية (افتراضي: True)

    الإرجاع:
        قائمة بمسارات الملفات التي تم تحميلها
    """
    if not movie_data:
        if verbose:
            print(f"{Fore.RED}[ERROR] No movie data provided{Style.RESET_ALL}")
        return []

    try:
        # استخراج معرف المحتوى
        content_id = movie_data.get("contentId")
        if not content_id:
            if verbose:
                print(f"{Fore.RED}[ERROR] No content ID found in movie data{Style.RESET_ALL}")
            # محاولة استخراج معرف المحتوى من مكان آخر في البيانات
            content_id = movie_data.get("id", "unknown")
            if verbose:
                print(f"{Fore.YELLOW}[INFO] Using alternative content ID: {content_id}{Style.RESET_ALL}")

        # استخراج عنوان الفيلم أو المسلسل
        title = movie_data.get("title", {}).get("localized", "unknown_title")
        title = title.replace(" ", "_").replace("/", "_").replace("\\", "_")

        if verbose:
            print(f"{Fore.YELLOW}[INFO] Processing posters for: {title} (ID: {content_id}){Style.RESET_ALL}")

        # إعداد مجلد الإخراج
        if output_dir is None:
            output_dir = os.path.join("downloads", title, "posters")

        # إنشاء المجلد إذا لم يكن موجودًا
        os.makedirs(output_dir, exist_ok=True)

        # قائمة لتخزين مسارات الملفات التي تم تحميلها
        downloaded_files = []
        total_images = 0
        successful_downloads = 0

        # استخراج روابط الصور مباشرة من استجابة API
        gallery = movie_data.get("gallery", {})
        if not gallery:
            if verbose:
                print(f"{Fore.RED}[ERROR] No gallery found in movie data{Style.RESET_ALL}")
            return []

        # حساب عدد الصور المتاحة للتحميل
        poster_urls = []

        # 1. الغلاف الأفقي
        covers = gallery.get("covers", {})
        if not covers and verbose:
            print(f"{Fore.YELLOW}[INFO] No covers found in gallery{Style.RESET_ALL}")

        horizontal_cover = covers.get("horizontal", {})
        if horizontal_cover and horizontal_cover.get("avatarsUrl"):
            total_images += 1
            # استخراج معلومات الحجم الأصلي إذا كانت متوفرة
            orig_size = horizontal_cover.get("origSize", {})
            width = orig_size.get("width", "1920") if orig_size else "1920"
            height = orig_size.get("height", "1080") if orig_size else "1080"

            poster_urls.append({
                "url": horizontal_cover.get("avatarsUrl"),
                "type": "horizontal_cover",
                "size": {"width": width, "height": height}
            })

        # 2. الشعار الأفقي
        horizontal_logo = gallery.get("logos", {}).get("horizontal", {})
        if horizontal_logo and horizontal_logo.get("avatarsUrl"):
            total_images += 1
            # استخراج معلومات الحجم الأصلي إذا كانت متوفرة
            orig_size = horizontal_logo.get("origSize", {})
            width = orig_size.get("width", "1920") if orig_size else "1920"
            height = orig_size.get("height", "1080") if orig_size else "1080"

            poster_urls.append({
                "url": horizontal_logo.get("avatarsUrl"),
                "type": "horizontal_logo",
                "size": {"width": width, "height": height}
            })

        # 3. شعارات الناشر
        rightholder_logos = gallery.get("logos", {}).get("rightholderForCover", [])
        # التعامل مع الحالة التي يكون فيها rightholderForCover قائمة
        if isinstance(rightholder_logos, list):
            for logo in rightholder_logos:
                logo_image = logo.get("image", {})
                if logo_image and logo_image.get("avatarsUrl"):
                    total_images += 1
                    poster_urls.append({
                        "url": logo_image.get("avatarsUrl"),
                        "type": "rightholder_logo",
                        "theme": logo.get("theme", "unknown")
                    })
        # التعامل مع الحالة التي يكون فيها rightholderForCover قاموس
        elif isinstance(rightholder_logos, dict) and "image" in rightholder_logos:
            logo_url = rightholder_logos["image"].get("avatarsUrl", "")
            theme = rightholder_logos.get("theme", "light")
            if logo_url:
                total_images += 1
                poster_urls.append({
                    "url": logo_url,
                    "type": "rightholder_logo",
                    "theme": theme
                })

        # 4. البوستر العمودي
        posters = gallery.get("posters", {})
        if not posters and verbose:
            print(f"{Fore.YELLOW}[INFO] No posters found in gallery{Style.RESET_ALL}")

        vertical_poster = posters.get("vertical", {})
        if vertical_poster and vertical_poster.get("avatarsUrl"):
            total_images += 1
            poster_urls.append({
                "url": vertical_poster.get("avatarsUrl"),
                "type": "vertical_poster"
            })

        # 5. البوستر العمودي مع الشعار
        vertical_with_logo = posters.get("verticalWithRightholderLogo", {})
        if vertical_with_logo and vertical_with_logo.get("avatarsUrl"):
            total_images += 1
            poster_urls.append({
                "url": vertical_with_logo.get("avatarsUrl"),
                "type": "vertical_with_logo"
            })

        # عرض رسائل التصحيح فقط في وضع التفصيل
        if verbose and total_images > 0:
            print(f"{Fore.CYAN}[INFO] Found {total_images} images to download{Style.RESET_ALL}")
            # عرض روابط البوسترات للتصحيح
            print(f"{Fore.YELLOW}[DEBUG] Poster URLs:{Style.RESET_ALL}")
            for idx, poster in enumerate(poster_urls):
                print(f"{Fore.YELLOW}[DEBUG] {idx+1}. Type: {poster['type']}, URL: {poster['url']}{Style.RESET_ALL}")
        elif verbose and total_images == 0:
            print(f"{Fore.RED}[WARNING] No poster URLs found in API response{Style.RESET_ALL}")

        # تحميل الصور
        for poster_info in poster_urls:
            avatars_url = poster_info["url"]
            poster_type = poster_info["type"]

            if not avatars_url.startswith("http"):
                avatars_url = "https:" + avatars_url

            # تنظيف اسم الملف من الرموز غير المسموح بها
            safe_title = sanitize_filename(title)

            # تحديد اسم الملف بناءً على نوع البوستر
            if poster_type == "horizontal_cover":
                size = poster_info.get("size", {})
                width = size.get("width", "1920")
                height = size.get("height", "1080")
                file_path = os.path.join(output_dir, f"{safe_title}_horizontal_cover_{width}x{height}.jpg")
            elif poster_type == "horizontal_logo":
                size = poster_info.get("size", {})
                width = size.get("width", "1920")
                height = size.get("height", "1080")
                file_path = os.path.join(output_dir, f"{safe_title}_horizontal_logo_{width}x{height}.png")
            elif poster_type == "rightholder_logo":
                theme = poster_info.get("theme", "unknown")
                file_path = os.path.join(output_dir, f"{safe_title}_rightholder_logo_{theme.lower()}.png")
            elif poster_type == "vertical_poster":
                file_path = os.path.join(output_dir, f"{safe_title}_vertical_poster.jpg")
            elif poster_type == "vertical_with_logo":
                file_path = os.path.join(output_dir, f"{safe_title}_vertical_with_logo.jpg")
            else:
                file_path = os.path.join(output_dir, f"{safe_title}_{poster_type}.jpg")

            # عرض الرابط الكامل للتصحيح
            if verbose:
                print(f"{Fore.CYAN}[DEBUG] Trying to download: {avatars_url} to {file_path}{Style.RESET_ALL}")

            # تحميل الصورة
            try:
                if verbose:
                    success = download_image(avatars_url, file_path, verbose)
                else:
                    # عرض تقدم التحميل بدون تفاصيل كثيرة
                    progress = int((successful_downloads / total_images) * 100) if total_images > 0 else 0
                    print(f"\rDownloading posters: {progress}%", end="", flush=True)
                    success = download_image(avatars_url, file_path, verbose=False)  # تعطيل الرسائل التفصيلية للتحميل الفردي

                if success:
                    downloaded_files.append(file_path)
                    successful_downloads += 1
                    if verbose:
                        print(f"{Fore.GREEN}[INFO] Progress: {successful_downloads}/{total_images} ({int(successful_downloads/total_images*100)}%){Style.RESET_ALL}")
                else:
                    if verbose:
                        print(f"{Fore.RED}[ERROR] Failed to download: {avatars_url}{Style.RESET_ALL}")
            except Exception as download_error:
                if verbose:
                    print(f"{Fore.RED}[ERROR] Exception during download: {download_error}{Style.RESET_ALL}")

        # طباعة ملخص فقط في حالة الوضع غير التفصيلي
        if not verbose:
            print()  # إضافة سطر جديد بعد شريط التقدم

        # نعيد قائمة الملفات التي تم تحميلها بدون طباعة رسائل إضافية
        return downloaded_files

    except Exception as e:
        if verbose:
            print(f"{Fore.RED}[ERROR] An unexpected error occurred in download_posters_from_api_response: {e}{Style.RESET_ALL}")
            import traceback
            traceback.print_exc()
        else:
            print(f"{Fore.RED}[ERROR] Failed to download posters: {str(e)}{Style.RESET_ALL}")
        return []

# وظيفة التحقق من توفر المسلسلات
def check_series_availability(series_id, check_content_availability_func):
    """
    Verifica la disponibilidad de una serie y devuelve información detallada.

    Args:
        series_id: ID de la serie a verificar
        check_content_availability_func: Función para verificar la disponibilidad del contenido

    Returns:
        Tupla con (is_available, availability_data)
        is_available: Boolean que indica si la serie está disponible
        availability_data: Diccionario con información detallada sobre la disponibilidad
    """
    print(f"{Fore.YELLOW}[DEBUG] Checking availability for series ID: {series_id}{Style.RESET_ALL}")

    try:
        # Obtener datos de disponibilidad usando la función proporcionada
        availability_data = check_content_availability_func(series_id)

        print(f"{Fore.YELLOW}[DEBUG] Availability data: {availability_data}{Style.RESET_ALL}")

        # Verificar si el contenido está disponible
        if not availability_data["is_available"]:
            # Verificar si hay información de disponibilidad futura
            if availability_data["availability_status"] == "FUTURE" or availability_data["availability_date"]:
                # La serie estará disponible en el futuro
                print(f"{Fore.YELLOW}[INFO] This series will be available on {availability_data['formatted_date']}{Style.RESET_ALL}")
                return False, availability_data
            else:
                # La serie no está disponible por otra razón
                print(f"{Fore.RED}[ERROR] This series is not available. Status: {availability_data['availability_status']}{Style.RESET_ALL}")
                if availability_data.get('error'):
                    print(f"{Fore.RED}[ERROR] Error: {availability_data['error']}{Style.RESET_ALL}")
                return False, availability_data

        # Si llegamos aquí, el contenido está disponible
        return True, availability_data

    except Exception as e:
        # Manejar cualquier error inesperado
        print(f"{Fore.RED}[ERROR] Unexpected error in check_series_availability: {str(e)}{Style.RESET_ALL}")
        import traceback
        traceback.print_exc()

        # Devolver un resultado de error
        error_data = {
            "is_available": False,
            "availability_status": "ERROR",
            "availability_date": None,
            "formatted_date": None,
            "error": str(e)
        }
        return False, error_data

# نظام التخزين المؤقت لنتائج التوفر
availability_cache = {}

# مدة صلاحية التخزين المؤقت (بالثواني)
CACHE_EXPIRY = 3600  # ساعة واحدة

# قفل للتزامن بين الخيوط
cache_lock = threading.Lock()

init(autoreset=True)  # تهيئة colorama لإعادة ضبط الألوان تلقائيًا

console = Console()
dirPath = os.getcwd()
GREEN = Fore.GREEN
MAGENTA = Fore.MAGENTA
YELLOW = Fore.YELLOW
RED = Fore.RED
CYAN = Fore.CYAN
RESET = Fore.RESET
BLUE = Fore.BLUE + Style.BRIGHT
RESET = Style.RESET_ALL

# تحديد المسارات بناءً على `__file__`
currentFile = __file__
realPath = os.path.realpath(currentFile)
dirPath = os.path.dirname(realPath)
dirName = os.path.basename(dirPath)

################# BINARIES ################
cache_dir = os.path.join(dirPath, "cache")
downloads_dir = os.path.join(dirPath, "downloads")
mkvmerge_path = os.path.join(dirPath, "bin", "mkvmerge.exe")
n_m3u8dl_path = os.path.join(dirPath, "bin", "N_m3u8DL-RE.exe")
mp4decrypt_path = os.path.join(dirPath, "bin", "mp4decrypt.exe")
mediainfo_path = os.path.join(dirPath, "bin", "mediainfo.exe")
ffmpeg_path = os.path.join(dirPath, "bin", "ffmpeg.exe")
KEYS_PATH = os.path.join(dirPath, 'KEYS', 'KEYS.txt')

################################# cookies ################################
console = Console()
# قراءة ملف الكوكيز وتحويله إلى شكل ترويسة `Cookie`
def parse_netscape_cookies(file_path):
    cookies = []
    try:
        with open(file_path, "r") as file:
            for line in file:
                # تخطي التعليقات أو السطور الفارغة
                if line.startswith("#") or not line.strip():
                    continue
                parts = line.strip().split("\t")
                if len(parts) >= 7:
                    # اسم الكوكيز وقيمته
                    name = parts[5]
                    value = parts[6]
                    cookies.append(f"{name}={value}")
        return "; ".join(cookies)
    except FileNotFoundError:
        print("Cookies file not found.")
        return None

# إعداد ملف الكوكيز
cookies_file = "cookies.txt"  # اسم ملف الكوكيز
cookies = parse_netscape_cookies(cookies_file)
if not cookies:
    print("Unable to proceed without cookies. Exiting.")
    exit(1)

##########################################################################

headers_HD = {
        'accept': '*/*',
        'accept-language': 'ru,en;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'origin': 'https://play.yango.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://play.yango.com/',
        'sec-ch-ua': '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'sec-gpc': '1',
        'service-id': '100025',
        'traceparent': '00-8757c04476dcdaf12cfd5e87a2f749ba-d9459f44549c048c-00',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-device-supported-stream-formats': '[{"HLS":["fairplay","clear"]},{"DASH":["playready","widevine","clear"]}]',
        'x-preferred-language': 'en',
        'x-real-host': '*.yango.com',
        'x-request-id': '1731909965309447-11841338694081714137:7',
        "Cookie": cookies,  # استخدام الكوكيز

    }

headers_4K = {
        'accept': '*/*',
        'accept-language': 'ru,en;q=0.9',
        'cache-control': 'no-cache',
        'content-type': 'application/json',
        'origin': 'https://play.yango.com',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://play.yango.com/',
        'sec-ch-ua': '"Brave";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-site',
        'x-device-os': 'IOS',
        'x-device-os-version': '18',
        'x-device-vendor': 'Apple',
        'x-device-model': 'iPhone16,2',
        'x-device-app-version': '1.33.1',
        'x-device-video-formats': 'SD,UHD,HD',
        'X-Device-Audio-Codecs': 'AAC,DD,EC3',
        'X-Device-Dynamic-Ranges': 'HDR10,DV,HLG',
        'sec-gpc': '1',
        'service-id': '100025',
        'traceparent': '00-8757c04476dcdaf12cfd5e87a2f749ba-d9459f44549c048c-00',
        'user-agent': 'iOS client iPhone (iOS 18.0) Saft/1.34.0 (5389) CFNetwork/1.0 Darwin/24.0.0',
        'x-device-supported-stream-formats': '[{"HLS":["fairplay","clear"]},{"DASH":["playready","widevine","clear"]}]',
        'x-preferred-language': 'en',
        'x-real-host': '*.yango.com',
        'x-request-id': '1731909965309447-11841338694081714137:7',
        "Cookie": cookies,  # استخدام الكوكيز

    }

##########################################  الترهيص  ##########################################
# جلب PSSH من MDP URL
def get_pssh(MDP_URL):
    try:
        response = requests.get(MDP_URL)
        response.raise_for_status()

        # تحليل محتوى MPD
        pssh_loads = xmltodict.parse(response.content)
        mpd = pssh_loads.get('MPD', {})

        # التحقق من وجود Period
        period = mpd.get('Period', {})
        if not period:
            print(f"{Fore.RED}[ERROR] No Period found in MPD.{Style.RESET_ALL}")
            return None

        # التعامل مع Period كقائمة
        if isinstance(period, list):
            if not period:  # التحقق من أن القائمة ليست فارغة
                print(f"{Fore.RED}[ERROR] Empty Period list in MPD.{Style.RESET_ALL}")
                return None
            period = period[0]

        # التحقق من وجود AdaptationSet
        adaptation_set = period.get('AdaptationSet', [])
        if not adaptation_set:
            print(f"{Fore.RED}[ERROR] No AdaptationSet found in MPD.{Style.RESET_ALL}")
            return None

        # التعامل مع AdaptationSet كقائمة
        if isinstance(adaptation_set, list):
            if not adaptation_set:  # التحقق من أن القائمة ليست فارغة
                print(f"{Fore.RED}[ERROR] Empty AdaptationSet list in MPD.{Style.RESET_ALL}")
                return None
            adaptation_set = adaptation_set[0]

        # التحقق من وجود ContentProtection
        content_protection = adaptation_set.get('ContentProtection', [])
        if not content_protection:
            print(f"{Fore.RED}[ERROR] No ContentProtection found in MPD.{Style.RESET_ALL}")
            return None

        # التحقق من وجود PSSH
        if isinstance(content_protection, list) and len(content_protection) > 2:
            pssh = content_protection[2].get('cenc:pssh', None)
            if not pssh:
                # محاولة البحث في جميع عناصر ContentProtection
                for cp in content_protection:
                    if cp.get('cenc:pssh'):
                        pssh = cp.get('cenc:pssh')
                        break
        else:
            # إذا لم يكن قائمة، تحقق من وجود PSSH مباشرة
            pssh = content_protection.get('cenc:pssh', None)

        if not pssh:
            print(f"{Fore.RED}[ERROR] No PSSH found in ContentProtection.{Style.RESET_ALL}")
            return None

        return pssh

    except requests.exceptions.RequestException as e:
        print(f"{Fore.RED}[ERROR] Failed to fetch MPD: {e}{Style.RESET_ALL}")
        return None
    except Exception as e:
        print(f"{Fore.RED}[ERROR] Error parsing MPD: {e}{Style.RESET_ALL}")
        import traceback
        traceback.print_exc()  # طباعة التفاصيل الكاملة للخطأ
        return None

# جلب مفتاح فك التشفير
def get_decryption_key(pssh, requestsParams):
    pssh = PSSH(pssh)
    device = Device.load("device.wvd")

    # تحميل CDM
    cdm = Cdm.from_device(device)

    # فتح جلسة CDM
    session_id = cdm.open()

    # إنشاء تحدي الترخيص
    challenge = cdm.get_license_challenge(session_id, pssh)

    headers = {
        'accept': '*/*',
        'content-type': 'application/x-www-form-urlencoded',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    }
    puid = requestsParams['puid']
    watchSessionId = requestsParams['watchSessionId']
    contentId = requestsParams['contentId']
    contentTypeId = requestsParams['contentTypeId']
    serviceName = requestsParams['serviceName']
    productId = requestsParams['productId']
    monetizationModel = requestsParams['monetizationModel']
    verificationRequired = requestsParams['verificationRequired']
    expirationTimestamp = requestsParams['expirationTimestamp']
    signature = requestsParams['signature']
    version = requestsParams['version']
    data = (
    '{"puid":' + str(puid) +
    ',"watchSessionId":"' + str(watchSessionId) +
    '","contentId":"' + str(contentId) +
    '","contentTypeId":' + str(contentTypeId) +
    ',"serviceName":"' + str(serviceName) +
    '","productId":' + str(productId) +
    ',"monetizationModel":"' + str(monetizationModel) +
    '","expirationTimestamp":' + str(expirationTimestamp) +
    ',"verificationRequired":' + 'true' +
    ',"signature":"' + str(signature) +
    '","version":"' + str(version) +
    '","rawLicenseRequestBase64":"' + base64.b64encode(challenge).decode() + '"}'
    )

    # إرسال طلب الترخيص
    licence = requests.post("https://widevine-proxy.movies.funtechservices.com/proxy", data=data)
    licence.raise_for_status()

    # معالجة الترخيص
    cdm.parse_license(session_id, licence.content)

    # استخراج المفاتيح
    keys = []
    for key in cdm.get_keys(session_id):
        if "CONTENT" in key.type:
            keys.append(f"{key.kid.hex}:{key.key.hex()}")

    # إغلاق الجلسة
    cdm.close(session_id)


    console.print("\n[bold green]Decryption Keys:[/bold green]")

    return keys

######################################### Database Constants #########################################
# اسماء ملفات قاعدة البيانات
MOVIES_DB_FILE = "movies.db"
SERIES_DB_FILE = "series.db"

######################################### Database-Movies #########################################
def initialize_movies_database():
    """إنشاء قاعدة بيانات الأفلام والجداول إذا لم تكن موجودة."""
    conn = sqlite3.connect(MOVIES_DB_FILE)
    cursor = conn.cursor()
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS movies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            selection_id TEXT,
            content_id TEXT UNIQUE,
            title TEXT
        )
    """)
    conn.commit()
    conn.close()

def save_movies_to_database(selection_id, movies):
    """حفظ الأفلام في قاعدة البيانات."""
    conn = sqlite3.connect(MOVIES_DB_FILE)
    cursor = conn.cursor()

    for movie in movies:
        try:
            cursor.execute("""
                INSERT OR IGNORE INTO movies (selection_id, content_id, title)
                VALUES (?, ?, ?)
            """, (selection_id, movie['ID'], movie['Title']))
        except sqlite3.IntegrityError as e:
            print(f"Error saving movie {movie['Title']}: {e}")

    conn.commit()
    conn.close()

def load_movies_from_database():
    """تحميل الأفلام من قاعدة البيانات."""
    conn = sqlite3.connect(MOVIES_DB_FILE)
    cursor = conn.cursor()

    cursor.execute("SELECT content_id, title FROM movies ORDER BY title ASC")
    movies = cursor.fetchall()

    conn.close()
    return [{"ID": row[0], "Title": row[1]} for row in movies]


def update_database():
    """تحديث قواعد بيانات الأفلام والمسلسلات بجلب البيانات الجديدة من API."""
    clear_screen()
    print_banner()
    print(f"{Fore.YELLOW}[INFO] Updating databases...{Style.RESET_ALL}")

    # تحديث قاعدة بيانات الأفلام
    print(f"{Fore.CYAN}[INFO] Updating movies database...{Style.RESET_ALL}")
    conn = sqlite3.connect(MOVIES_DB_FILE)
    cursor = conn.cursor()
    cursor.execute("DELETE FROM movies")
    conn.commit()
    conn.close()
    print(f"{Fore.GREEN}[INFO] Movies database cleared. Fetching new movies data...{Style.RESET_ALL}")
    fetch_movies()

    # تحديث قاعدة بيانات المسلسلات
    print(f"{Fore.CYAN}[INFO] Updating series database...{Style.RESET_ALL}")
    conn = sqlite3.connect(SERIES_DB_FILE)
    cursor = conn.cursor()
    cursor.execute("DELETE FROM series")
    conn.commit()
    conn.close()
    print(f"{Fore.GREEN}[INFO] Series database cleared. Fetching new series data...{Style.RESET_ALL}")
    fetch_series()

    print(f"{Fore.GREEN}[INFO] All databases updated successfully!{Style.RESET_ALL}")
    input(f"\n{Fore.YELLOW}Press Enter to return to the main menu...{Style.RESET_ALL}")

######################################### Database-Series #########################################

def initialize_series_database():
    """إنشاء قاعدة بيانات المسلسلات والجداول إذا لم تكن موجودة.
    تم تحديث الهيكل ليشمل معلومات التوفر."""
    conn = sqlite3.connect(SERIES_DB_FILE)
    cursor = conn.cursor()

    # التحقق من وجود الجدول
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='series'")
    table_exists = cursor.fetchone()

    if not table_exists:
        # إنشاء جدول جديد بالهيكل المحدث
        cursor.execute("""
            CREATE TABLE series (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                selection_id TEXT,
                content_id TEXT UNIQUE,
                title TEXT,
                is_available INTEGER DEFAULT 1,
                availability_status TEXT DEFAULT 'UNKNOWN',
                availability_date TEXT,
                formatted_date TEXT,
                last_checked TEXT
            )
        """)
    else:
        # التحقق من وجود الأعمدة الجديدة وإضافتها إذا لم تكن موجودة
        cursor.execute("PRAGMA table_info(series)")
        columns = [column[1] for column in cursor.fetchall()]

        # إضافة الأعمدة الجديدة إذا لم تكن موجودة
        if 'is_available' not in columns:
            cursor.execute("ALTER TABLE series ADD COLUMN is_available INTEGER DEFAULT 1")
        if 'availability_status' not in columns:
            cursor.execute("ALTER TABLE series ADD COLUMN availability_status TEXT DEFAULT 'UNKNOWN'")
        if 'availability_date' not in columns:
            cursor.execute("ALTER TABLE series ADD COLUMN availability_date TEXT")
        if 'formatted_date' not in columns:
            cursor.execute("ALTER TABLE series ADD COLUMN formatted_date TEXT")
        if 'last_checked' not in columns:
            cursor.execute("ALTER TABLE series ADD COLUMN last_checked TEXT")

    conn.commit()
    conn.close()

def save_series_to_database(selection_id, series):
    """حفظ المسلسلات في قاعدة البيانات مع افتراض أن جميع المسلسلات متاحة."""
    conn = sqlite3.connect(SERIES_DB_FILE)
    cursor = conn.cursor()

    print(f"{Fore.CYAN}[INFO] Saving {len(series)} series to database...{Style.RESET_ALL}")

    try:
        # إظهار شريط تقدم
        with tqdm(total=len(series), desc="Saving series", unit="series") as pbar:
            for serie in series:
                # حفظ المسلسل مع افتراض أنه متاح
                try:
                    cursor.execute("""
                        INSERT OR REPLACE INTO series
                        (selection_id, content_id, title, is_available, availability_status, availability_date, formatted_date, last_checked)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        selection_id,
                        serie['ID'],
                        serie['Title'],
                        1,  # افتراض أن المسلسل متاح
                        "WATCHABLE",  # حالة المشاهدة
                        None,  # لا يوجد تاريخ توفر مستقبلي
                        None,  # لا يوجد تاريخ منسق
                        datetime.now().isoformat()
                    ))
                except sqlite3.IntegrityError as e:
                    print(f"Error saving series {serie['Title']}: {e}")

                # تحديث شريط التقدم
                pbar.update(1)

        conn.commit()
        conn.close()
        print(f"{Fore.GREEN}[INFO] All series saved to database.{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}[ERROR] Error saving series to database: {e}{Style.RESET_ALL}")

        # التأكد من إغلاق الاتصال بقاعدة البيانات
        try:
            conn.commit()
            conn.close()
        except:
            pass

def load_series_from_database():
    """تحميل المسلسلات من قاعدة البيانات مع معلومات التوفر."""
    conn = sqlite3.connect(SERIES_DB_FILE)
    cursor = conn.cursor()

    # جلب جميع البيانات بما في ذلك معلومات التوفر
    cursor.execute("""
        SELECT content_id, title, is_available, availability_status, availability_date, formatted_date, last_checked
        FROM series ORDER BY title ASC
    """)
    series = cursor.fetchall()

    conn.close()

    # تحويل النتائج إلى قاموس مع معلومات التوفر
    return [{
        "ID": row[0],
        "Title": row[1],
        "availability": {
            "is_available": bool(row[2]),
            "availability_status": row[3],
            "availability_date": row[4],
            "formatted_date": row[5],
            "last_checked": row[6]
        }
    } for row in series]

######################################### API-Movies #########################################
# # استخراج الأفلام من API
# استخراج الأفلام فقط من API
# جلب قائمة الأفلام
# متغير عالمي لتخزين عناوين الأفلام
global_movies_titles = {}

# متغير عالمي لتخزين الجودة المختارة للحلقات المتتالية
selected_episode_quality = None

def fetch_movies():
    headers = headers_HD if selected_quality == "HD" else headers_4K

    """جلب الأفلام من قاعدة البيانات أو API مع شريط تحميل للأفلام فقط."""
    global global_movies_titles  # استخدام المتغير العالمي لتخزين العناوين

    # التحقق إذا كانت الأفلام موجودة بالفعل في قاعدة البيانات
    movies_from_db = load_movies_from_database()

    if movies_from_db:
        # تحديث المتغير العالمي إذا كانت البيانات موجودة بالفعل في قاعدة البيانات
        global_movies_titles = {movie["ID"]: movie["Title"] for movie in movies_from_db}
        print(f"\n{Fore.GREEN}[INFO] Movies loaded from database.{Style.RESET_ALL}\n")
        return movies_from_db

    # إذا لم تكن قاعدة البيانات تحتوي على بيانات، يتم جلبها من API
    url = "https://kp-graphql-api.movies.yango.com/graphql/?operationName=Selection"


    selection_ids = [
        "2507", "2710", "2513", "2510", "2511", "2512", "2518", "2509", "2514", "2517", "2515",
        "2525", "2519", "2604", "2516", "2528", "2527", "2524", "2530", "2531", "2639", "2635",
        "2649", "2651", "2655", "2657", "2654", "2653", "2652", "2630", "2592", "2728", "2602",
        "2590", "2591", "yango_ara_subs", "yango_genre__6__22", "yango_genre__8__22",
        "editorial_selections__2428__6", "yango_genre__3__22", "editorial_selections__2658__6",
        "editorial_selections__2600__6", "editorial_selections__2601__6", "editorial_selections__2630__6",
        "editorial_selections__2594__6", "editorial_selections__2646__6", "editorial_selections__2602__6",
        "editorial_selections__2705__6", "editorial_selections__2647__6", "editorial_selections__2520__6",
        "editorial_selections__2521__6", "editorial_selections__2637__6", "yango_genre__1750__22",
        "editorial_selections__2747__6", "editorial_selections__2598__6", "editorial_selections__2643__6",
        "editorial_selections__2728__6", "yango_genre__5__22", "editorial_selections__2754__6",
        "editorial_selections__2712__6", "editorial_selections__2508__6", "editorial_selections__2752__6",
        "editorial_selections__2687__6", "yango_genre__17__22", "editorial_selections__2591__6",
        "editorial_selections__2587__6", "editorial_selections__2588__6", "yango_genre__23__22",
        "personal_films_yango", "editorial_selections_all_mm__2668__6", "3092"
    ]

    all_movies = []

    # استخدام tqdm لإنشاء شريط التحميل
    with tqdm(total=len(selection_ids), desc="Fetching movies", unit="selection") as pbar:
        for selection_id in selection_ids:
            payload = {
                "operationName": "Selection",
                "variables": {
                    "selectionId": selection_id,
                    "showcaseId": "home",
                    "limit": 40,
                    "offset": 0,
                    "ignoreCache": False,
                    "withUserData": True,
                    "withCatchupSelection": True,
                },
            "query": "query Selection($showcaseId: String, $selectionId: String!, $offset: Int!, $limit: Int!, $ignoreCache: Boolean!, $withUserData: Boolean!, $withCatchupSelection: Boolean!) { selection(id: $selectionId, showcaseId: $showcaseId, ignoreCache: $ignoreCache) { ...SelectionData __typename } } fragment AbstractSelectionData on AbstractSelection { id showTitle title comment __typename } fragment SelectionPagingListData on SessionPagingList_SelectionItem { offset limit hasMore sessionId __typename } fragment Title on Title { localized original __typename } fragment AbstractSelectionMovieData on Movie { id contentId title { ...Title __typename } __typename } fragment Image on Image { avatarsUrl fallbackUrl __typename } fragment MovieHorizontalPoster on MoviePosters { horizontalWithRightholderLogo { ...Image __typename } horizontal { ...Image __typename } __typename } fragment MovieVerticalPoster on MoviePosters { vertical(override: OTT_WHEN_EXISTS) { ...Image __typename } verticalWithRightholderLogo { ...Image __typename } __typename } fragment SelectionMovie on Movie { ...AbstractSelectionMovieData gallery { covers { horizontal { avatarsUrl __typename } __typename } posters { ...MovieHorizontalPoster ...MovieVerticalPoster __typename } logos { horizontal { avatarsUrl __typename } __typename } __typename } genres { id name __typename } restriction { age __typename } viewOption { buttonText originalButtonText type purchasabilityStatus watchabilityStatus promotionActionType contentPackageToBuy { billingFeatureName __typename } availabilityAnnounce { type announcePromise availabilityDate __typename } __typename } top10 userData @include(if: $withUserData) { isPlannedToWatch __typename } ... on Film { productionYear(override: OTT_WHEN_EXISTS) __typename } ... on TvSeries { releaseYears { start end __typename } __typename } __typename } fragment AbstractMovieSelectionItem on AbstractMovieSelectionItem { movie { ...SelectionMovie __typename } __typename } fragment AbstractAnnounceMovieSelectionItem on AbstractMovieSelectionItem { movie { ...SelectionMovie gallery { posters { horizontalIntroWithRightholderLogo { avatarsUrl fallbackUrl __typename } horizontalIntro { avatarsUrl fallbackUrl __typename } __typename } __typename } __typename } __typename } fragment SeasonAnnounceSelectionItem on SeasonAnnounceSelectionItem { ...AbstractAnnounceMovieSelectionItem season { viewOption { buttonText originalButtonText type purchasabilityStatus watchabilityStatus promotionActionType contentPackageToBuy { billingFeatureName __typename } availabilityAnnounce { type announcePromise availabilityDate __typename } __typename } __typename } __typename } fragment CatchupReferenceContent on Movie { contentId __typename } fragment Catchup on Catchup { contentId catchupTitle: title duration gallery { covers { horizontal { avatarsUrl __typename } __typename } __typename } tvChannel { contentId logo { avatarsUrl __typename } title __typename } referenceContent { ...CatchupReferenceContent __typename } viewOption { watchabilityExpirationTime __typename } __typename } fragment CatchupSelectionItem on CatchupSelectionItem { catchup { ...Catchup __typename } __typename } fragment SelectionData on AbstractSelection { ...AbstractSelectionData content(offset: $offset, limit: $limit) { ...SelectionPagingListData items { ...AbstractMovieSelectionItem ... on AnnounceSelectionItem { ...AbstractAnnounceMovieSelectionItem __typename } ... on SeasonAnnounceSelectionItem { ...SeasonAnnounceSelectionItem __typename } ... on OriginalAnnounceMovieSelectionItem { ...AbstractAnnounceMovieSelectionItem __typename } ... on PromoAnnounceSelectionItem { ...AbstractAnnounceMovieSelectionItem __typename } ... on CatchupSelectionItem { ...CatchupSelectionItem @include(if: $withCatchupSelection) __typename } __typename } __typename } __typename } "

            }

            try:
                # إرسال الطلب وجلب البيانات
                response = requests.post(url, json=payload, headers=headers)
                response.raise_for_status()
                data = response.json()

                # استخراج العناصر وتصفيتها للأفلام فقط
                movies = data.get("data", {}).get("selection", {}).get("content", {}).get("items", [])
                filtered_movies = [
                    {
                        "ID": movie["movie"]["contentId"],
                        "Title": movie["movie"]["title"]["localized"] or "Unknown Title"
                    }
                    for movie in movies if movie.get("movie") and movie["movie"].get("__typename") == "Film"
                ]

                # حفظ الأفلام في قاعدة البيانات
                save_movies_to_database(selection_id, filtered_movies)
                all_movies.extend(filtered_movies)

                # تحديث المتغير العالمي
                for movie in filtered_movies:
                    global_movies_titles[movie["ID"]] = movie["Title"]

            except requests.exceptions.RequestException as e:
                print(f"\n{Fore.RED}[ERROR] Error fetching data for selection ID {selection_id}: {e}{Style.RESET_ALL}")

            # تحديث شريط التحميل
            pbar.update(1)

    print(f"\n{Fore.GREEN}[INFO] Movies fetched and saved to database.{Style.RESET_ALL}\n")
    return all_movies


def display_movies(movies):
    """عرض الأفلام في جدول."""
    if movies:
        print(tabulate(movies, headers="keys", tablefmt="fancy_grid"))
    else:
        print(f"{Fore.RED}No movies found.{Style.RESET_ALL}")

########################################## استخراج روابط الفيلم ##########################################
selected_quality = "HD"

def fetch_movie_details(content_id):
    """جلب تفاصيل الفيلم باستخدام content_id وتمرير العنوان لدالة التحميل."""
    headers = headers_HD if selected_quality == "HD" else headers_4K
    global global_movies_titles  # استخدم القاموس العالمي للعناوين
    # الحصول على العنوان من القاموس
    movie_title = global_movies_titles.get(content_id, "Unknown Title")

    # عرض معلومات الفيلم في جدول منفصل (العنوان وسنة الإصدار)
    try:
        # الحصول على بيانات الفيلم من API مباشرة
        movie_card_response = fetch_and_print_movie_card(content_id, silent=True)
        if movie_card_response and "data" in movie_card_response and "movieByContentUuid" in movie_card_response["data"]:
            movie_data = movie_card_response["data"]["movieByContentUuid"]

            # استخراج سنة الإصدار
            production_year = movie_data.get("productionYear", "N/A")

            # عرض جدول بمعلومات الفيلم
            movie_info = [
                {"Title": movie_title, "Year": production_year}
            ]

            # إعداد العناوين مع الألوان
            headers_table = {
                "Title": f"{Fore.YELLOW}Title{Style.RESET_ALL}",
                "Year": f"{Fore.MAGENTA}Year{Style.RESET_ALL}"
            }

            # عرض الجدول
            print("\n" + tabulate(movie_info, headers=headers_table, tablefmt="fancy_grid", stralign="center"))
            print()  # سطر فارغ للفصل

            # تحميل البوسترات للفيلم
            print(f"{Fore.YELLOW}[INFO] Downloading movie posters...{Style.RESET_ALL}")
            # إنشاء مجلد للبوسترات
            download_dir = os.path.join("downloads", sanitize_filename(movie_title))
            posters_folder = os.path.join(download_dir, "posters")
            os.makedirs(download_dir, exist_ok=True)
            os.makedirs(posters_folder, exist_ok=True)

            # استخدام البيانات من استجابة API مباشرة
            download_posters_from_api_response(movie_data, posters_folder, verbose=True)
        else:
            # إذا فشل الحصول على البيانات من API، نستخدم الطريقة القديمة
            print(f"{Fore.YELLOW}[INFO] Downloading movie posters...{Style.RESET_ALL}")
            download_dir = os.path.join("downloads", sanitize_filename(movie_title))
            posters_folder = os.path.join(download_dir, "posters")
            os.makedirs(download_dir, exist_ok=True)
            os.makedirs(posters_folder, exist_ok=True)
            download_movie_posters(content_id, posters_folder, verbose=True)
    except Exception as e:
        print(f"{Fore.RED}[ERROR] Failed to download posters: {e}{Style.RESET_ALL}")

    # متابعة جلب تفاصيل الفيلم للتحميل
    try:
        url = "https://kp-graphql-api.movies.yango.com/graphql/?operationName=PlayerBaseInfo"

        payload = {
            "operationName": "PlayerBaseInfo",
            "variables": {
                "prerollsSupported": True,
                "isKidSubProfile": False,
                "contentId": content_id
            },
            'query': 'query PlayerBaseInfo($contentId: String!, $prerollsSupported: Boolean = false, $isKidSubProfile: Boolean = false) { content(contentId: $contentId) { ... on Catchup { ...CatchupPlayerBaseInfo __typename } ... on VideoInterface { ...FilmPlayerBaseInfo __typename } ... on Episode { ...EpisodePlayerBaseInfo __typename } ... on TvChannel { ...ChannelPlayerBaseInfo __typename } ... on Clip { ...ClipPlayerBaseInfo __typename } __typename } userProfile @include(if: $isKidSubProfile) { ...ChildLockPlayerKidSubProfile __typename } } fragment CatchupTvChannel on Catchup { tvChannel { contentId ageRestriction title gallery { logos { main { avatarsUrl __typename } __typename } __typename } __typename } __typename } fragment MovieStreamAudioMeta on AudioMeta { audioChannelsNumber audioGroupId forAdult language languageName quality studioName title visibleByDefault __typename } fragment MovieStreamDrmConfig on DrmConfig { drmServers { certificateUrl name processSPCPath provisioningUrl url __typename } headers requestParams __typename } fragment MovieStreamSubtitleMeta on SubtitleMeta { forAdult languageName studio type language title url visibleByDefault __typename } fragment MovieStreamTile on Tile { spriteDuration tileDuration uriTemplate tilePixelResolution { height width __typename } spriteMatrixSize { columns rows __typename } spritePixelResolution { height width __typename } __typename } fragment MovieStreamTiles on TilesContainer { highResolutionTiles { ...MovieStreamTile __typename } lowResolutionTiles { ...MovieStreamTile __typename } offset __typename } fragment MovieStreamPrerolls on PrerollMeta { durationMs promotedEntityId __typename } fragment MovieStreamSkippableFragment on SkippableFragment { startTime endTime final type __typename } fragment MovieStreamMeta on StreamMeta { audioMetas { ...MovieStreamAudioMeta __typename } drmConfig { ...MovieStreamDrmConfig __typename } subtitleMetas { ...MovieStreamSubtitleMeta __typename } tilesContainer { ...MovieStreamTiles __typename } trackings videoMetas { bitrate height width __typename } prerollsDuration prerolls { ...MovieStreamPrerolls __typename } skippableFragments { ...MovieStreamSkippableFragment __typename } videoToken cuesUrl __typename } fragment MovieStream on Stream { streamMeta { ...MovieStreamMeta __typename } uri __typename } fragment MovieOnlineStreams on OnlineStreams { licenseStatus drmRequirement streams { ...MovieStream __typename } sessionId concurrencyArbiterConfig { concurrencyArbiterRequestParams server __typename } playerRestrictionConfig { subtitlesButtonEnable __typename } monetizationModel __typename } fragment CatchupStreams on Catchup { onlineStreams { ...MovieOnlineStreams __typename } __typename } fragment CatchupMeta on Catchup { catchupTitle: title duration hasSmokingScenes ageRestriction viewOption { watchabilityExpirationTime __typename } __typename } fragment FilmPlayerOnlineStreams on Ott { ... on Ott_AbstractVideo { onlineStreams(prerollsSupported: $prerollsSupported) { ...MovieOnlineStreams __typename } __typename } __typename } fragment SmokingFilmFlag on Ott { ... on Ott_AbstractVideo { hasSmokingScenes __typename } __typename } fragment MovieTiming on OttTiming { current __typename } fragment FilmOttPreview on Ott_AbstractVideo { preview { ... on OttPreview_AbstractVideo { timing { ...MovieTiming __typename } duration __typename } __typename } __typename } fragment MovieWatchParams on OttUserData { watchParams { audioLanguage subtitleLanguage __typename } __typename } fragment FilmAgeRestriction on VideoInterface { restriction { age __typename } __typename } fragment EpisodeDetails on Episode { episodeContentId: contentId number tvSeries { contentId id __typename } season { contentId number __typename } episodeOtt: ott { viewOption { watchabilityStatus __typename } __typename } __typename } fragment EpisodeAgeRestriction on Episode { tvSeries { contentId restriction { age __typename } __typename } __typename } fragment EpisodePlayerOnlineStreams on OttEpisode { onlineStreams(prerollsSupported: $prerollsSupported) { ...MovieOnlineStreams __typename } __typename } fragment EpisodeSmokingFlag on OttEpisode { hasSmokingScenes __typename } fragment EpisodePlayerNextEpisode on Episode { neighbourhoodInSeries(limit: 1, filter: {onlyOnline: true}) { items { ...EpisodeDetails __typename } __typename } __typename } fragment ChannelMeta on TvChannel { ageRestriction title gallery { logos { main { avatarsUrl fallbackUrl __typename } __typename } covers { horizontal { avatarsUrl fallbackUrl __typename } __typename } __typename } isNeedToHidePrograms isMultiplex __typename } fragment ChannelStreams on TvChannel { onlineStreams { ...MovieOnlineStreams __typename } __typename } fragment TvChannelAdvertisement on TvAdvertisement { position ... on TvSdkAdvertisement { data { params type vendorClientId __typename } __typename } ... on TvUrlAdvertisement { urls __typename } __typename } fragment ChannelAds on TvChannel { advertisements { ...TvChannelAdvertisement __typename } __typename } fragment TvChannelProgram on TvProgram { id title startTime endTime episodeTitle ageRestriction typeName catchupContentId image { avatarsUrl fallbackUrl __typename } __typename } fragment TvChannelProgramAdsTvis on TvProgram { tvis { url __typename } advertisements { ...TvChannelAdvertisement __typename } __typename } fragment TvChannelProgramWithAds on TvProgram { ...TvChannelProgram ...TvChannelProgramAdsTvis __typename } fragment ChannelPrograms on TvChannel { programsWithAds: tvPrograms(duration: "PT4H") { ...TvChannelProgramWithAds __typename } __typename } fragment ClipStreamMeta on StreamMeta { audioMetas { ...MovieStreamAudioMeta __typename } drmConfig { ...MovieStreamDrmConfig __typename } subtitleMetas { ...MovieStreamSubtitleMeta __typename } tilesContainer { ...MovieStreamTiles __typename } trackings videoMetas { bitrate height width __typename } skippableFragments { ...MovieStreamSkippableFragment __typename } videoToken cuesUrl __typename } fragment ClipStream on Stream { streamMeta { ...ClipStreamMeta __typename } uri __typename } fragment ClipOnlineStreams on OnlineStreams { licenseStatus drmRequirement streams { ...ClipStream __typename } sessionId concurrencyArbiterConfig { concurrencyArbiterRequestParams server __typename } playerRestrictionConfig { subtitlesButtonEnable __typename } monetizationModel __typename } fragment ClipStreams on Clip { onlineStreams { ...ClipOnlineStreams __typename } __typename } fragment CatchupPlayerBaseInfo on Catchup { catchupContentId: contentId ...CatchupTvChannel ...CatchupStreams ...CatchupMeta __typename } fragment FilmPlayerBaseInfo on VideoInterface { filmContentId: contentId id filmOtt: ott { ...FilmPlayerOnlineStreams ...SmokingFilmFlag ...FilmOttPreview userData { ...MovieWatchParams __typename } __typename } ...FilmAgeRestriction __typename } fragment EpisodePlayerBaseInfo on Episode { id episodeContentId: contentId ...EpisodeDetails ...EpisodeAgeRestriction episodeOtt: ott { ...EpisodePlayerOnlineStreams ...EpisodeSmokingFlag timing { ...MovieTiming __typename } duration __typename } ...EpisodePlayerNextEpisode tvSeries { contentId ott { userData { ...MovieWatchParams __typename } __typename } __typename } __typename } fragment ChannelPlayerBaseInfo on TvChannel { channelContentId: contentId ...ChannelMeta ...ChannelStreams ...ChannelAds ...ChannelPrograms __typename } fragment ClipPlayerBaseInfo on Clip { clipContentId: contentId ...ClipStreams __typename } fragment ChildLockPlayerKidSubProfile on UserKidSubProfile { id { ottId puid __typename } restrictions { parentalControlVideo { streamUrl id __typename } parentalControlEnabled __typename } __typename } ',
        }

        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()

        data = response.json()
        streams = data["data"]["content"]["filmOtt"]["onlineStreams"]["streams"]
        requests_params = streams[1]["streamMeta"]["drmConfig"]["requestParams"]
        mpd_url = streams[1]["uri"]
        print()
        print(f"{YELLOW}[+] MPD:{RESET}{CYAN} {mpd_url}{RESET}")
        extract_keys_from_mpd(mpd_url, requests_params)
        # جلب URI الخاص بـ MPD
        mpd_url = data.get("data", {}).get("content", {}).get("filmOtt", {}).get("onlineStreams", {}).get("streams", [{}])[1].get("uri", "")
        if not mpd_url:
            print(f"{Fore.RED}MPD URL not found.{Style.RESET_ALL}")
            return

        # تمرير العنوان إلى parse_mpd
        parse_mpd_movie(mpd_url, movie_title)

    except requests.exceptions.RequestException as e:
        print(f"{Fore.RED}Error fetching movie details: {e}{Style.RESET_ALL}")
    except KeyError as e:
        print(f"{Fore.RED}Missing key in response: {e}{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}An unexpected error occurred: {e}{Style.RESET_ALL}")

def sanitize_filename(filename):
    """
    تعديل اسم الملف لجعله آمنًا للاستخدام في المسارات.
    """
    return re.sub(r'[^\w\-_\. ]', '_', filename).strip()

def parse_mpd_movie(mpd_url, title, grouptag="YANGO"):
    """
    تحليل ملف MPD وتنزيل الفيديو والصوت والترجمات ودمجها في ملف نهائي.
    """
    try:
        title = sanitize_filename(title)  # Ensure the title is safe for file and folder names

        # إعداد الملفات
        cache_dir = "cache"
        download_dir = os.path.join("downloads", title)
        base_filename = f"{title}.{grouptag}.WEB-DL.H264.AAC"  # الاسم الأساسي بدون الجودة

        # طلب MPD
        response = requests.get(mpd_url)
        response.raise_for_status()

        # تحليل XML
        mpd_content = ET.fromstring(response.content)
        namespaces = {"": "urn:mpeg:dash:schema:mpd:2011"}

        # البحث عن AdaptationSet
        adaptation_sets = mpd_content.findall(".//AdaptationSet", namespaces)

        video_tracks = []
        audio_languages = set()
        subtitle_languages = set()

        # تعديل الجودات لتحويل القيم فقط للعرض
        def display_resolution(resolution):
            if resolution == "816":
                return "720"
            elif resolution == "1088":
                return "1080"
            elif resolution == "1632":
                return "2160"
            return resolution

        for adaptation in adaptation_sets:
            mime_type = adaptation.get("mimeType", "")
            content_type = adaptation.get("contentType", "")
            lang = adaptation.get("lang", "unknown")

            if "video" in mime_type:
                for rep in adaptation.findall("Representation", namespaces):
                    quality = rep.get('height')
                    bandwidth = rep.get('bandwidth')
                    if quality and bandwidth:
                        video_tracks.append({
                            "Quality": f"{quality}p",
                            "Bandwidth": f"{bandwidth} bps",
                            "Resolution": quality,
                        })

            elif "audio" in mime_type:
                audio_languages.add(lang)

            elif content_type == "text" or "text" in mime_type:
                subtitle_languages.add(lang)

        if not video_tracks:
            print(f"{Fore.RED}No video tracks found in MPD.{Style.RESET_ALL}")
            return

        # ترتيب الفيديو حسب الجودة
        sorted_video_tracks = sorted(video_tracks, key=lambda x: int(x["Resolution"]))

        # عرض قائمة الجودات
        numbered_tracks = [
            {
                "No.": i + 1,
                "Resolution": f'{display_resolution(track["Resolution"])}P',
                "Bandwidth": track["Bandwidth"],
                "Audios": " | ".join(sorted(audio_languages)),
                "Subtitles": " | ".join(sorted(subtitle_languages)),
                "_OriginalResolution": track["Resolution"],  # مخفي ولكنه يظل جزءًا من البيانات
            }
            for i, track in enumerate(sorted_video_tracks)
        ]

        # إعداد رأس الجدول بألوان مميزة
        headers = {
            "No.": f"{Fore.YELLOW}No.{Style.RESET_ALL}",
            "Resolution": f"{Fore.CYAN}Resolution{Style.RESET_ALL}",
            "Bandwidth": f"{Fore.BLUE}Bandwidth{Style.RESET_ALL}",
            "Audios": f"{Fore.GREEN}Audios{Style.RESET_ALL}",
            "Subtitles": f"{Fore.MAGENTA}Subtitles{Style.RESET_ALL}",
        }

        # عرض الجدول بدون OriginalResolution
        try:
            print(tabulate(
                [
                    {key: row[key] for key in headers.keys()}  # إزالة العمود المخفي عند العرض
                    for row in numbered_tracks
                ],
                headers=headers,
                tablefmt="fancy_grid",
                stralign="center",
                colalign=["center"] * len(headers),
            ))
        except Exception as e:
            print(f"{Fore.RED}[ERROR] Failed to display table: {e}{Style.RESET_ALL}")

        # اختيار الجودة
        choice = int(input(f"\n{Fore.YELLOW}Enter the number of the quality to download (or 0 to cancel): {Style.RESET_ALL}"))
        if choice == 0:
            print(f"{Fore.YELLOW}Download cancelled.{Style.RESET_ALL}")
            return

        selected_track = numbered_tracks[choice - 1]
        original_resolution = selected_track["_OriginalResolution"]  # استخدام القيمة الأصلية
        displayed_resolution = display_resolution(original_resolution)

        print(f"{Fore.GREEN}[INFO] Quality selected: {displayed_resolution}P{Style.RESET_ALL}")

        # تحديث اسم الملف الأساسي ليشمل الجودة
        base_filename = f"{title}.{displayed_resolution}P.{grouptag}.WEB-DL.H264.AAC"
        final_filename = f"{base_filename}.mkv"
        final_path = os.path.join(download_dir, final_filename)

        # التحقق إذا تم التحميل بالفعل
        if os.path.exists(final_path):
            print(f"{Fore.GREEN}[INFO] File already exists: {final_path}{Style.RESET_ALL}")
            return  # إنهاء العملية إذا كان الملف موجودًا

        video_file = os.path.join(cache_dir, f"{base_filename}.mp4")
        audio_files = [
            os.path.join(cache_dir, f"{base_filename}.ara.m4a"),
            os.path.join(cache_dir, f"{base_filename}.ara-x-51.m4a"),
            os.path.join(cache_dir, f"{base_filename}.eng.m4a"),
            os.path.join(cache_dir, f"{base_filename}.eng-atmos.m4a"),
            os.path.join(cache_dir, f"{base_filename}.eng-ddp.m4a")
        ]
        subtitle_files = [
            os.path.join(cache_dir, f"{base_filename}.eng.srt"),
            os.path.join(cache_dir, f"{base_filename}.ara.srt")
        ]

        # التحقق من وجود كل الملفات المطلوبة في الكاش
        all_files_exist = os.path.exists(video_file) and any(os.path.exists(f) for f in audio_files)

        if all_files_exist:
            print(f"{Fore.GREEN}[INFO] All files found in cache. Proceeding to merge.{Style.RESET_ALL}")
            merge_movies_with_mkvmerge(
                video_file=video_file,
                audio_files=[f for f in audio_files if os.path.exists(f)],
                output_file=final_path
            )
            return

        # التأكد من إنشاء المجلدات
        os.makedirs(cache_dir, exist_ok=True)
        os.makedirs(download_dir, exist_ok=True)

        # إعداد ملفات التنزيل
        audio_option = f'--select-audio "lang=ar|en|tr:for=best3"'
        subtitle_option = f'--select-subtitle "lang=ar|en:for=best2"'

        print(f"{Fore.YELLOW}[INFO] Preparing to download: {final_filename}{Style.RESET_ALL}")

        # تنفيذ عملية التنزيل
        download_command = (
            f'"{n_m3u8dl_path}" "{mpd_url}" -mt '
            f'--select-video "res={original_resolution}" '
            f'{audio_option} '
            f'{subtitle_option} '
            f'--tmp-dir "{cache_dir}" '
            f'--save-dir "{cache_dir}" '
            f'--save-name "{base_filename}" '
            f'--decryption-binary-path="{mp4decrypt_path}" '
            f'--key-text-file="{KEYS_PATH}" '
            f'--log-level "OFF"'
        )

        print(f"{Fore.YELLOW}[INFO] Running download command...{Style.RESET_ALL}")
        subprocess.call(download_command, shell=True)

        # التحقق من وجود الملفات
        if not os.path.exists(video_file):
            print(f"{Fore.RED}[ERROR] Video file not found after download.{Style.RESET_ALL}")
            return

        # دمج الملفات باستخدام mkvmerge
        merge_movies_with_mkvmerge(
            video_file=video_file,
            audio_files=[f for f in audio_files if os.path.exists(f)],
            output_file=final_path
        )

        # البوسترات تم تحميلها بالفعل في بداية عملية التحميل في وظيفة fetch_movie_details
        # لذلك لا نحتاج لتحميلها مرة أخرى هنا
        print(f"{Fore.GREEN}[INFO] Download and merge completed successfully!{Style.RESET_ALL}")

    except Exception as e:
        print(f"{Fore.RED}[ERROR] An unexpected error occurred: {e}{Style.RESET_ALL}")

def merge_movies_with_mkvmerge(video_file, audio_files, output_file):
    """دمج ملفات الفيديو والصوت في تنسيق MKV باستخدام mkvmerge."""
    mkvmerge_command = [mkvmerge_path, "-o", output_file]

    # إضافة ملف الفيديو
    mkvmerge_command.append(video_file)

    # خريطة التسمية لملفات الصوت
    audio_map = {
        ".ara.m4a": ("ara", "Arabic Stereo"),
        ".ara-x-51.m4a": ("ara", "Arabic 5.1"),
        ".tr.m4a": ("tur", "Turkish"),
        ".en-atmos.m4a": ("eng", "English Atmos"),
        ".en-ddp.m4a": ("eng", "English Dolby 5.1"),
        ".en-2CH.m4a": ("eng", "English Stereo"),
        ".en.m4a": ("eng", "English")  # Default if no specific label is found
    }

    # إضافة ملفات الصوت مع تعيين اللغة والتسمية
    for audio_file in audio_files:
        track_language = "und"  # افتراضيًا لغة غير معروفة
        track_label = "Unknown"  # تسمية افتراضية

        for key, (language_code, label) in audio_map.items():
            if key in audio_file:
                track_language = language_code
                track_label = label
                break

        # إضافة ملف الصوت إلى الأمر
        mkvmerge_command.extend([
            "--language", f"0:{track_language}",
            "--track-name", f"0:{track_label}",
            audio_file
        ])

    # طباعة وتشغيل الأمر
    print("Running mkvmerge command:", " ".join(mkvmerge_command))
    subprocess.run(mkvmerge_command, check=True)
    print("[INFO] MKV merge completed successfully!")


def extract_keys_from_mpd(mpd_url, requests_params, title="default_title"):
    """
    This function extracts keys from an MPD URL using provided request parameters.
    Automatically appends the decryption keys into the KEYS.txt file.
    """
    # استخراج PSSH
    pssh = get_pssh(mpd_url)
    if not pssh:
        print(f"{Fore.RED}[ERROR] Failed to extract PSSH.{Style.RESET_ALL}")
        return None

    print(f"\n{Fore.YELLOW}[+] PSSH:{Style.RESET_ALL} {Fore.CYAN}{pssh}{Style.RESET_ALL}\n")
    print(f"{Fore.YELLOW}[+] Extracting Decryption Keys...{Style.RESET_ALL}")

    # استخراج مفاتيح فك التشفير
    keys = get_decryption_key(pssh, requests_params)
    if keys:
        # طباعة المفاتيح على الشاشة
        for key in keys:
            print(f"{Fore.YELLOW}[+] Key:{Style.RESET_ALL} {key}")

        # كتابة المفاتيح في الملف النصي KEYS.txt داخل المسار المحدد في KEYS_PATH
        keys_dir = os.path.dirname(KEYS_PATH)
        os.makedirs(keys_dir, exist_ok=True)  # إنشاء المجلد إذا لم يكن موجودًا

        with open(KEYS_PATH, "a") as keys_file:
            keys_file.write(f"\n# Movie: {title}\n")  # إضافة عنوان الفيلم كملاحظة
            keys_file.write("\n".join(keys) + "\n")  # كتابة المفاتيح

        print(f"\n{Fore.GREEN}[INFO] Keys saved successfully in {KEYS_PATH}.{Style.RESET_ALL}")
        return keys
    else:
        print(f"{Fore.RED}[ERROR] Failed to extract decryption keys.{Style.RESET_ALL}")
        return None


################################# display_movies_with_numbers ##############################

def display_movies_with_numbers(movies):
    """عرض الأفلام في جدول مع ترتيب أبجدي وإخفاء عمود ID مع إزالة التكرارات.
    يعرض معلومات التوفر من قاعدة البيانات للمسلسلات."""
    if movies:
        # إزالة التكرارات بناءً على العنوان
        unique_movies = {movie["Title"]: movie for movie in movies}.values()

        # ترتيب الأفلام أبجديًا حسب العنوان
        sorted_movies = sorted(unique_movies, key=lambda x: x["Title"])

        # التحقق من نوع القائمة (مسلسلات أو أفلام)
        is_series_menu = "series_menu" in str(inspect.stack())

        # إضافة معلومات التوفر لكل مسلسل
        numbered_movies = []
        for i, movie in enumerate(sorted_movies):
            movie_data = {"No.": i + 1, "Title": movie["Title"]}

            # إضافة أيقونات الميزات للمسلسلات
            if is_series_menu:
                content_id = movie.get("ID", "")
                features_icons = get_content_features_from_db(content_id)
                movie_data["Features"] = features_icons if features_icons else "—"

            # التحقق من توفر المسلسل (فقط للمسلسلات، وليس للأفلام)
            if is_series_menu and "availability" in movie:
                try:
                    # استخدام معلومات التوفر من قاعدة البيانات
                    availability = movie["availability"]
                    is_available = availability["is_available"]

                    # إذا كان المسلسل غير متاح وله تاريخ توفر مستقبلي
                    if not is_available and availability["availability_date"]:
                        # استخدام تاريخ التوفر المنسق
                        movie_data["Release Date"] = f"{Fore.YELLOW}{availability['formatted_date']}{Style.RESET_ALL}"
                    else:
                        movie_data["Release Date"] = f"{Fore.GREEN}Available{Style.RESET_ALL}"
                except Exception as e:
                    # في حالة حدوث خطأ، لا نعرض تاريخ التوفر
                    print(f"{Fore.RED}[ERROR] Error processing availability for {movie['Title']}: {e}{Style.RESET_ALL}")
                    movie_data["Release Date"] = f"{Fore.RED}Unknown{Style.RESET_ALL}"
            elif is_series_menu:
                # إذا لم تكن هناك معلومات توفر في قاعدة البيانات، نفترض أنه متاح
                movie_data["Release Date"] = f"{Fore.GREEN}Available{Style.RESET_ALL}"

            numbered_movies.append(movie_data)

        # إعداد العناوين مع الألوان
        headers = {
            "No.": f"{Fore.CYAN}No.{Style.RESET_ALL}",
            "Title": f"{Fore.YELLOW}Title{Style.RESET_ALL}",
        }

        # إضافة أعمدة إضافية للمسلسلات فقط
        if is_series_menu:
            headers["Features"] = f"{Fore.GREEN}Features{Style.RESET_ALL}"
            headers["Release Date"] = f"{Fore.MAGENTA}Release Date{Style.RESET_ALL}"

        # عرض الجدول مع العناوين الملونة
        print(tabulate(numbered_movies, headers=headers, tablefmt="fancy_grid", stralign="center"))
        return list(sorted_movies)  # إرجاع القائمة المرتبة
    else:
        print(f"{Fore.RED}No movies found.{Style.RESET_ALL}")
        return []


def check_series_availability_silent(_, _check_func=None):
    """نسخة صامتة من وظيفة التحقق من توفر المسلسل.
    تم تعديلها لإرجاع أن جميع المسلسلات متاحة دائمًا."""

    # إرجاع أن المسلسل متاح دائمًا
    availability_data = {
        "is_available": True,
        "availability_status": "WATCHABLE",
        "availability_date": None,
        "formatted_date": None
    }

    return True, availability_data


def check_availability_batch(series_list, max_workers=10):
    """التحقق من توفر مجموعة من المسلسلات - تم تعديلها لإرجاع أن جميع المسلسلات متاحة."""
    results = {}

    # تعيين جميع المسلسلات كمتاحة
    for series in series_list:
        series_id = series["ID"]
        availability_data = {
            "is_available": True,
            "availability_status": "WATCHABLE",
            "availability_date": None,
            "formatted_date": None
        }
        results[series_id] = (True, availability_data)

    print(f"{Fore.GREEN}[INFO] All series marked as available.{Style.RESET_ALL}")
    return results

######################################### API-Series #########################################
def fetch_series():
    headers = headers_HD if selected_quality == "HD" else headers_4K

    """جلب المسلسلات من قاعدة البيانات أو API."""
    global global_movies_titles  # استخدام نفس المتغير لتخزين العناوين

    # التحقق إذا كانت المسلسلات موجودة بالفعل في قاعدة البيانات
    series_from_db = load_series_from_database()

    if series_from_db:
        global_movies_titles = {serie["ID"]: serie["Title"] for serie in series_from_db}
        print(f"\n{Fore.GREEN}[INFO] Series loaded from database.{Style.RESET_ALL}\n")
        return series_from_db

    # إذا لم تكن قاعدة البيانات تحتوي على بيانات، يتم جلبها من API
    url = "https://kp-graphql-api.movies.yango.com/graphql/?operationName=Selection"

    selection_ids = [
        # قائمة معرّفات المسلسلات
    "2507", "2710", "2513", "2510", "2511", "2512", "2518", "2509", "2514", "2517", "2515",
    "2525", "2519", "2604", "2516", "2528", "2527", "2524", "2530", "2531", "2639", "2635",
    "2649", "2651", "2655", "2657", "2654", "2653", "2652", "2630", "2592", "2728", "2602",
    "2590", "2591", "yango_ara_subs", "yango_genre__6__22", "yango_genre__8__22",
    "editorial_selections__2428__6", "yango_genre__3__22", "editorial_selections__2658__6",
    "editorial_selections__2600__6", "editorial_selections__2601__6", "editorial_selections__2630__6",
    "editorial_selections__2594__6", "editorial_selections__2646__6", "editorial_selections__2602__6",
    "editorial_selections__2705__6", "editorial_selections__2647__6", "editorial_selections__2520__6",
    "editorial_selections__2521__6", "editorial_selections__2637__6", "yango_genre__1750__22",
    "editorial_selections__2747__6", "editorial_selections__2598__6", "editorial_selections__2643__6",
    "editorial_selections__2728__6", "yango_genre__5__22", "editorial_selections__2754__6",
    "editorial_selections__2712__6", "editorial_selections__2508__6", "editorial_selections__2752__6",
    "editorial_selections__2687__6", "yango_genre__17__22", "editorial_selections__2591__6",
    "editorial_selections__2587__6", "editorial_selections__2588__6", "yango_genre__23__22",
    "personal_series_yango", "editorial_selections_all_mm__2668__6", "3092"
    ]

    all_series = []

    with tqdm(total=len(selection_ids), desc="Fetching series", unit="selection") as pbar:
        for selection_id in selection_ids:
            payload = {
                "operationName": "Selection",
                "variables": {
                    "selectionId": selection_id,
                    "showcaseId": "home",
                    "limit": 40,
                    "offset": 0,
                    "ignoreCache": False,
                    "withUserData": True,
                    "withCatchupSelection": True,
                },
  "query": "query Selection($showcaseId: String, $selectionId: String!, $offset: Int!, $limit: Int!, $ignoreCache: Boolean!, $withUserData: Boolean!, $withCatchupSelection: Boolean!) { selection(id: $selectionId, showcaseId: $showcaseId, ignoreCache: $ignoreCache) { ...SelectionData __typename } } fragment AbstractSelectionData on AbstractSelection { id showTitle title comment __typename } fragment SelectionPagingListData on SessionPagingList_SelectionItem { offset limit hasMore sessionId __typename } fragment Title on Title { localized original __typename } fragment AbstractSelectionMovieData on Movie { id contentId title { ...Title __typename } __typename } fragment Image on Image { avatarsUrl fallbackUrl __typename } fragment MovieHorizontalPoster on MoviePosters { horizontalWithRightholderLogo { ...Image __typename } horizontal { ...Image __typename } __typename } fragment MovieVerticalPoster on MoviePosters { vertical(override: OTT_WHEN_EXISTS) { ...Image __typename } verticalWithRightholderLogo { ...Image __typename } __typename } fragment SelectionMovie on Movie { ...AbstractSelectionMovieData gallery { covers { horizontal { avatarsUrl __typename } __typename } posters { ...MovieHorizontalPoster ...MovieVerticalPoster __typename } logos { horizontal { avatarsUrl __typename } __typename } __typename } genres { id name __typename } restriction { age __typename } viewOption { buttonText originalButtonText type purchasabilityStatus watchabilityStatus promotionActionType contentPackageToBuy { billingFeatureName __typename } availabilityAnnounce { type announcePromise availabilityDate __typename } __typename } top10 userData @include(if: $withUserData) { isPlannedToWatch __typename } ... on Film { productionYear(override: OTT_WHEN_EXISTS) __typename } ... on TvSeries { releaseYears { start end __typename } __typename } __typename } fragment AbstractMovieSelectionItem on AbstractMovieSelectionItem { movie { ...SelectionMovie __typename } __typename } fragment AbstractAnnounceMovieSelectionItem on AbstractMovieSelectionItem { movie { ...SelectionMovie gallery { posters { horizontalIntroWithRightholderLogo { avatarsUrl fallbackUrl __typename } horizontalIntro { avatarsUrl fallbackUrl __typename } __typename } __typename } __typename } __typename } fragment SeasonAnnounceSelectionItem on SeasonAnnounceSelectionItem { ...AbstractAnnounceMovieSelectionItem season { viewOption { buttonText originalButtonText type purchasabilityStatus watchabilityStatus promotionActionType contentPackageToBuy { billingFeatureName __typename } availabilityAnnounce { type announcePromise availabilityDate __typename } __typename } __typename } __typename } fragment CatchupReferenceContent on Movie { contentId __typename } fragment Catchup on Catchup { contentId catchupTitle: title duration gallery { covers { horizontal { avatarsUrl __typename } __typename } __typename } tvChannel { contentId logo { avatarsUrl __typename } title __typename } referenceContent { ...CatchupReferenceContent __typename } viewOption { watchabilityExpirationTime __typename } __typename } fragment CatchupSelectionItem on CatchupSelectionItem { catchup { ...Catchup __typename } __typename } fragment SelectionData on AbstractSelection { ...AbstractSelectionData content(offset: $offset, limit: $limit) { ...SelectionPagingListData items { ...AbstractMovieSelectionItem ... on AnnounceSelectionItem { ...AbstractAnnounceMovieSelectionItem __typename } ... on SeasonAnnounceSelectionItem { ...SeasonAnnounceSelectionItem __typename } ... on OriginalAnnounceMovieSelectionItem { ...AbstractAnnounceMovieSelectionItem __typename } ... on PromoAnnounceSelectionItem { ...AbstractAnnounceMovieSelectionItem __typename } ... on CatchupSelectionItem { ...CatchupSelectionItem @include(if: $withCatchupSelection) __typename } __typename } __typename } __typename } "
}


            try:
                response = requests.post(url, json=payload, headers=headers)
                response.raise_for_status()
                data = response.json()

                series = data.get("data", {}).get("selection", {}).get("content", {}).get("items", [])
                filtered_series = [
                    {
                        "ID": serie["movie"]["contentId"],
                        "Title": serie["movie"]["title"]["localized"] or "Unknown Title"
                    }
                    for serie in series if serie.get("movie") and serie["movie"].get("__typename") == "TvSeries"
                ]

                save_series_to_database(selection_id, filtered_series)
                all_series.extend(filtered_series)

                for serie in filtered_series:
                    global_movies_titles[serie["ID"]] = serie["Title"]

                    # جلب ميزات المحتوى وحفظها في قاعدة البيانات
                    try:
                        content_id = serie["ID"]
                        movie_card_response = fetch_and_print_movie_card(content_id, silent=True)
                        if movie_card_response and "data" in movie_card_response and "movieByContentUuid" in movie_card_response["data"]:
                            movie_data = movie_card_response["data"]["movieByContentUuid"]
                            features = extract_content_features(movie_data)
                            save_content_features_to_db(content_id, features)
                    except Exception as e:
                        print(f"{Fore.YELLOW}[WARNING] Failed to fetch features for {serie['Title']}: {e}{Style.RESET_ALL}")

            except requests.exceptions.RequestException as e:
                print(f"\n{Fore.RED}[ERROR] Error fetching data for selection ID {selection_id}: {e}{Style.RESET_ALL}")

            pbar.update(1)

    print(f"\n{Fore.GREEN}[INFO] Series fetched and saved to database.{Style.RESET_ALL}\n")
    return all_series

######################################### Fetch and Print MovieCard API Response #########################################
def extract_content_features(movie_data):
    """
    استخراج ميزات المحتوى من بيانات الفيلم/المسلسل.

    Parameters:
        movie_data: بيانات الفيلم/المسلسل من API

    Returns:
        dict: قاموس يحتوي على ميزات المحتوى المختلفة
    """
    features = {
        'video': [],
        'audio': [],
        'subtitles': [],
        'dynamic_range': []
    }

    try:
        # استخراج الميزات من ott.preview.features
        ott_features = movie_data.get("ott", {}).get("preview", {}).get("features", [])

        for feature in ott_features:
            alias = feature.get("alias", "")
            group = feature.get("group", "")
            displayed_name = feature.get("displayedName", "")

            if group == "VIDEO":
                features['video'].append({
                    'alias': alias,
                    'name': displayed_name
                })
            elif group == "AUDIO":
                features['audio'].append({
                    'alias': alias,
                    'name': displayed_name
                })
            elif group == "SUBTITLES":
                features['subtitles'].append({
                    'alias': alias,
                    'name': displayed_name
                })
            elif group == "DYNAMIC_RANGE":
                features['dynamic_range'].append({
                    'alias': alias,
                    'name': displayed_name
                })

        # استخراج معلومات الصوت من availableMetadata
        available_metadata = movie_data.get("ott", {}).get("preview", {}).get("availableMetadata", {})
        audio_meta = available_metadata.get("audioMeta", [])

        for audio in audio_meta:
            quality = audio.get("quality", "")
            quality_name = audio.get("qualityName", "")
            language = audio.get("language", "")
            language_name = audio.get("languageName", "")

            features['audio'].append({
                'quality': quality,
                'quality_name': quality_name,
                'language': language,
                'language_name': language_name
            })

    except Exception as e:
        print(f"{Fore.RED}[ERROR] Failed to extract content features: {e}{Style.RESET_ALL}")

    return features

def display_content_features(features):
    """
    عرض ميزات المحتوى بالأيقونات.

    Parameters:
        features: قاموس ميزات المحتوى
    """
    try:
        feature_icons = []

        # أيقونات الفيديو - نص بسيط
        has_4k = False
        for video_feature in features.get('video', []):
            alias = video_feature.get('alias', '')
            if 'video4k' in alias.lower():
                feature_icons.append("4K")
                has_4k = True
            elif 'videohd' in alias.lower():
                feature_icons.append("HD")

        # إذا لم يكن هناك 4K أو HD محدد، أضف HD كافتراضي
        if not has_4k and not any('HD' in icon for icon in feature_icons):
            feature_icons.append("HD")

        # أيقونات الصوت - نص بسيط
        audio_features_set = set()
        for audio_feature in features.get('audio', []):
            alias = audio_feature.get('alias', '')
            quality = audio_feature.get('quality', '')

            if 'dolbydigitalplus51' in alias.lower() or 'surround_51' in quality.lower():
                audio_features_set.add("5.1")
            elif 'dolbyatmos' in alias.lower():
                audio_features_set.add("ATMOS")
            elif 'stereo' in quality.lower():
                audio_features_set.add("STEREO")

        feature_icons.extend(list(audio_features_set))

        # أيقونات HDR - نص بسيط
        for hdr_feature in features.get('dynamic_range', []):
            alias = hdr_feature.get('alias', '')
            if 'hdr10' in alias.lower():
                feature_icons.append("HDR")
            elif 'dolbyvision' in alias.lower():
                feature_icons.append("DV")

        # عرض الأيقونات
        if feature_icons:
            print(f"\n{Fore.CYAN}[FEATURES] {' | '.join(feature_icons)}{Style.RESET_ALL}")

    except Exception as e:
        print(f"{Fore.RED}[ERROR] Failed to display content features: {e}{Style.RESET_ALL}")

def save_content_info_to_json(content_id, movie_data):
    """
    حفظ معلومات المحتوى في ملف JSON.

    Parameters:
        content_id: معرف المحتوى
        movie_data: بيانات الفيلم/المسلسل من API
    """
    try:
        # إنشاء مجلد content_info إذا لم يكن موجوداً
        content_info_dir = "content_info"
        os.makedirs(content_info_dir, exist_ok=True)

        # استخراج المعلومات الأساسية
        title_data = movie_data.get("title", {})
        title = title_data.get("localized", title_data.get("original", "Unknown"))

        content_type = movie_data.get("__typename", "Unknown")

        # استخراج ميزات المحتوى
        features = extract_content_features(movie_data)

        # إعداد البيانات للحفظ
        content_info = {
            "content_id": content_id,
            "title": title,
            "content_type": content_type,
            "features": features,
            "last_updated": datetime.now().isoformat(),
            "raw_data": {
                "ott_features": movie_data.get("ott", {}).get("preview", {}).get("features", []),
                "available_metadata": movie_data.get("ott", {}).get("preview", {}).get("availableMetadata", {})
            }
        }

        # حفظ في ملف JSON
        filename = f"{content_id}.json"
        filepath = os.path.join(content_info_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(content_info, f, ensure_ascii=False, indent=2)

        # حفظ معلومات المحتوى في قاعدة البيانات أيضاً
        save_content_features_to_db(content_id, features)

    except Exception as e:
        print(f"{Fore.RED}[ERROR] Failed to save content info: {e}{Style.RESET_ALL}")

def save_content_features_to_db(content_id, features):
    """
    حفظ ميزات المحتوى في قاعدة البيانات.

    Parameters:
        content_id: معرف المحتوى
        features: ميزات المحتوى
    """
    try:
        conn = sqlite3.connect("series.db")
        cursor = conn.cursor()

        # إنشاء جدول ميزات المحتوى إذا لم يكن موجوداً
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS content_features (
                content_id TEXT PRIMARY KEY,
                video_features TEXT,
                audio_features TEXT,
                subtitle_features TEXT,
                hdr_features TEXT,
                last_updated TEXT
            )
        ''')

        # تحويل الميزات إلى JSON strings
        video_features = json.dumps(features.get('video', []))
        audio_features = json.dumps(features.get('audio', []))
        subtitle_features = json.dumps(features.get('subtitles', []))
        hdr_features = json.dumps(features.get('dynamic_range', []))
        last_updated = datetime.now().isoformat()

        # حفظ أو تحديث البيانات
        cursor.execute('''
            INSERT OR REPLACE INTO content_features
            (content_id, video_features, audio_features, subtitle_features, hdr_features, last_updated)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (content_id, video_features, audio_features, subtitle_features, hdr_features, last_updated))

        conn.commit()
        conn.close()

    except Exception as e:
        print(f"{Fore.RED}[ERROR] Failed to save content features to database: {e}{Style.RESET_ALL}")

def get_content_features_from_db(content_id):
    """
    استخراج ميزات المحتوى من قاعدة البيانات.

    Parameters:
        content_id: معرف المحتوى

    Returns:
        str: نص يحتوي على أيقونات الميزات
    """
    try:
        conn = sqlite3.connect("series.db")
        cursor = conn.cursor()

        cursor.execute('''
            SELECT video_features, audio_features, subtitle_features, hdr_features
            FROM content_features
            WHERE content_id = ?
        ''', (content_id,))

        result = cursor.fetchone()
        conn.close()

        if not result:
            return ""

        video_features, audio_features, subtitle_features, hdr_features = result

        # تحويل JSON strings إلى قوائم
        video_list = json.loads(video_features) if video_features else []
        audio_list = json.loads(audio_features) if audio_features else []
        subtitle_list = json.loads(subtitle_features) if subtitle_features else []
        hdr_list = json.loads(hdr_features) if hdr_features else []

        # إنشاء قاموس الميزات
        features = {
            'video': video_list,
            'audio': audio_list,
            'subtitles': subtitle_list,
            'dynamic_range': hdr_list
        }

        # إنشاء أيقونات الميزات
        feature_icons = []

        # أيقونات الفيديو - نص بسيط للجدول
        has_4k = False
        for video_feature in features.get('video', []):
            alias = video_feature.get('alias', '')
            if 'video4k' in alias.lower():
                feature_icons.append("4K")
                has_4k = True
            elif 'videohd' in alias.lower():
                feature_icons.append("HD")

        # إذا لم يكن هناك 4K أو HD محدد، أضف HD كافتراضي
        if not has_4k and not any('HD' in icon for icon in feature_icons):
            feature_icons.append("HD")

        # أيقونات الصوت - نص بسيط للجدول
        audio_features_set = set()
        for audio_feature in features.get('audio', []):
            alias = audio_feature.get('alias', '')
            quality = audio_feature.get('quality', '')

            if 'dolbydigitalplus51' in alias.lower() or 'surround_51' in quality.lower():
                audio_features_set.add("5.1")
            elif 'dolbyatmos' in alias.lower():
                audio_features_set.add("ATMOS")
            elif 'stereo' in quality.lower():
                audio_features_set.add("STEREO")

        feature_icons.extend(list(audio_features_set))

        # أيقونات HDR - نص بسيط للجدول
        for hdr_feature in features.get('dynamic_range', []):
            alias = hdr_feature.get('alias', '')
            if 'hdr10' in alias.lower():
                feature_icons.append("HDR")
            elif 'dolbyvision' in alias.lower():
                feature_icons.append("DV")

        return ' | '.join(feature_icons) if feature_icons else ""

    except Exception as e:
        print(f"{Fore.RED}[ERROR] Failed to get content features from database: {e}{Style.RESET_ALL}")
        return ""

def fetch_and_print_movie_card(content_id, silent=False):
    """
    Fetch movie details, print key API response fields, and set global series title.

    Parameters:
        content_id: The content ID to fetch details for
        silent: If True, suppresses all print statements (default: False)
    """
    headers = headers_HD if selected_quality == "HD" else headers_4K
    global global_series_title
    url = "https://kp-graphql-api.movies.yango.com/graphql/?operationName=MovieCard"

    payload = {
        "operationName": "MovieCard",
        "variables": {
            "contentUuid": content_id,
            "isAuthorized": True,
            "withUserData": True,
        },
          "query": "query MovieCard($contentUuid: String!, $isAuthorized: Boolean!, $withUserData: Boolean!) { movieByContentUuid(contentUuid: $contentUuid) { id contentId ...AgeRestriction ...OverviewMeta ...ContentOverview ...ContentDetails ...ContentCard ...SerialStructure ...MainTrailer ...Promo ...OttNextEpisode @include(if: $isAuthorized) ...OttMovieTiming @include(if: $isAuthorized) ...MovieType __typename } } fragment OttPreviewFeatures on OttPreview { features(filter: {layout: OTT_TITLE_CARD, onlyClientSupported: true}) { alias displayedName: displayName group __typename } __typename } fragment AvailabilityViewOption on ViewOption { watchabilityStatus availabilityAnnounce { __typename } __typename } fragment Years on Movie { __typename ... on VideoInterface { productionYear(override: OTT_WHEN_EXISTS) __typename } ... on Series { releaseYears { start end __typename } __typename } } fragment AudioMeta on AudioMeta { forAdult language languageName quality qualityName studio type __typename } fragment SubtitleMeta on SubtitleMeta { forAdult language languageName studio type __typename } fragment Title on Title { localized original __typename } fragment MovieTitle on Movie { title { ...Title __typename } __typename } fragment ImageSize on ImageSize { width height __typename } fragment MovieHorizontalLogo on MovieLogos { horizontal { avatarsUrl origSize { ...ImageSize __typename } __typename } __typename } fragment Image on Image { avatarsUrl fallbackUrl __typename } fragment MovieVerticalPoster on MoviePosters { vertical(override: OTT_WHEN_EXISTS) { ...Image __typename } verticalWithRightholderLogo { ...Image __typename } __typename } fragment SkippableFragment on SkippableFragment { type startTime endTime final __typename } fragment EpisodeBase on Episode { id contentId number __typename } fragment SeasonBase on Season { id contentId number __typename } fragment SerialStructureEpisode on Episode { ...EpisodeBase synopsis(override: OTT_WHEN_EXISTS) title { ...Title __typename } season { ...SeasonBase __typename } cover { avatarsUrl fallbackUrl __typename } ott { duration editorAnnotation plannedAvailabilityDate viewOption { availabilityStatus availabilityEndDate watchabilityStatus __typename } timing @include(if: $withUserData) { current __typename } __typename } __typename } fragment OttEpisodeTiming on Episode { ott { timing { current __typename } __typename } __typename } fragment NextEpisode on Episode { ...EpisodeBase title { ...Title __typename } cover { avatarsUrl fallbackUrl __typename } ott { duration viewOption { availabilityEndDate availabilityStatus __typename } __typename } season { ...SeasonBase __typename } ...OttEpisodeTiming __typename } fragment AgeRestriction on Movie { restriction { age __typename } __typename } fragment OverviewMeta on Movie { contentId __typename ott { preview { ...OttPreviewFeatures ... on OttPreview_AbstractVideo { duration __typename } __typename } __typename } top10 genres { id name __typename } restriction { age __typename } countries { id name __typename } viewOption { ...AvailabilityViewOption __typename } ...Years ... on Series { seasonsAll: seasons(limit: 0) { total __typename } __typename } } fragment ContentOverview on Movie { shortDescription editorAnnotation userData @include(if: $isAuthorized) { watchStatuses { watched { value __typename } __typename } __typename } ott { ... on Ott_AbstractVideo { preview { ... on OttPreview_AbstractVideo { duration __typename } __typename } __typename } __typename } __typename } fragment ContentDetails on Movie { id contentId title { original __typename } ottSynopsis: synopsis(override: OTT_WHEN_EXISTS) actors: members(limit: 10, role: [ACTOR, CAMEO, UNCREDITED]) { items { person { id name originalName __typename } __typename } __typename } directors: members(role: DIRECTOR, limit: 5) { items { person { id name originalName __typename } __typename } __typename } ott { preview { ...OttPreviewFeatures availableMetadata { audioMeta { ...AudioMeta __typename } subtitleMeta { ...SubtitleMeta __typename } __typename } __typename } __typename } viewOption { ...AvailabilityViewOption __typename } __typename } fragment ContentCard on Movie { ...MovieTitle gallery { covers { horizontal { avatarsUrl __typename } __typename } logos { rightholderForCover(filter: {formFactor: M}) { image { avatarsUrl __typename } theme __typename } ...MovieHorizontalLogo rightholderForCoverRecommendedTheme __typename } posters { ...MovieVerticalPoster __typename } __typename } ott { ... on Ott_AbstractVideo { skippableFragments { ...SkippableFragment __typename } __typename } __typename } __typename } fragment SerialStructure on Series { id seasons(limit: 10000, isOnlyOnline: true) { items { id contentId number episodes(limit: 1, isOnlyOnline: true) { items { ...SerialStructureEpisode __typename } total __typename } episodeGroupings(filter: {onlyOnline: true}, capacity: 20) { from to offset __typename } __typename } __typename } __typename } fragment MainTrailer on Movie { contentId ott { mainTrailers: trailers(limit: 2) { items { contentGroupUuid main __typename } __typename } __typename } __typename } fragment Promo on Movie { contentId ott { promos: trailers(limit: 1, onlyPromo: true) { items { contentGroupUuid __typename } __typename } __typename } __typename } fragment OttNextEpisode on Movie { id contentId ott { ... on Ott_AbstractSeries { nextEpisode(fallbackToFirstEpisode: true) { contentId fallback episode { offsetInSeason(filter: {onlyOnline: true}) ...NextEpisode __typename } __typename } __typename } __typename } __typename } fragment OttMovieTiming on Movie { id ott { ... on Ott_AbstractVideo { preview { ... on OttPreview_AbstractVideo { timing { current __typename } __typename } __typename } __typename } __typename } __typename } fragment MovieType on Movie { __typename } "
}


    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()

        data = response.json()
        movie_data = data.get("data", {}).get("movieByContentUuid", {})
        if not movie_data:
            if not silent:
                print(f"{Fore.RED}[ERROR] No data found for the provided content ID.{Style.RESET_ALL}")
            return None

        title_data = movie_data.get("title", {})
        global_series_title = title_data.get("localized", "default_title")
        if global_series_title:
            global_series_title = global_series_title.replace(" ", ".")

        # استخراج سنة الإصدار
        if "__typename" in movie_data and movie_data["__typename"] == "Film":
            production_year = movie_data.get("productionYear", "N/A")
            if not silent:
                print(f"{Fore.GREEN}[INFO] Movie Title: {global_series_title}, Year: {production_year}{Style.RESET_ALL}")
        elif "__typename" in movie_data and movie_data["__typename"] == "TvSeries":
            release_years = movie_data.get("releaseYears", [{}])
            if release_years and len(release_years) > 0:
                release_year = release_years[0].get("start", "N/A")
                if not silent:
                    print(f"{Fore.GREEN}[INFO] Series Title: {global_series_title}, Year: {release_year}{Style.RESET_ALL}")

        # استخراج ميزات المحتوى وعرضها بالأيقونات
        if not silent:
            content_features = extract_content_features(movie_data)
            if content_features:
                display_content_features(content_features)

            # حفظ معلومات المحتوى في ملف JSON
            save_content_info_to_json(content_id, movie_data)

        # استخراج تفاصيل الحلقات (اختياري)
        if 'seasons' in movie_data and not silent:
            print(f"\n{Fore.YELLOW}[INFO] Seasons:{Style.RESET_ALL}")
            for season in movie_data['seasons'].get('items', []):
                print(f"  - Season {season['number']}: {season['episodes']['total']} Episodes")

        return data

    except requests.exceptions.RequestException as e:
        if not silent:
            print(f"{Fore.RED}[ERROR] Failed to fetch movie details: {e}{Style.RESET_ALL}")
        return None
    except Exception as e:
        if not silent:
            print(f"{Fore.RED}[ERROR] An unexpected error occurred: {e}{Style.RESET_ALL}")
        return None

def fetch_season_details(content_id, default_release_year="N/A"):
    headers = headers_HD if selected_quality == "HD" else headers_4K

    """جلب تفاصيل الموسم باستخدام contentId مع استخدام سنة إصدار المسلسل عند الحاجة."""
    url = "https://kp-graphql-api.movies.yango.com/graphql/?operationName=SerialStructureSeason"

    payload = {
        "operationName": "SerialStructureSeason",
        "variables": {
            "contentId": content_id,
            "limit": 30,
            "offset": 0,
            "withUserData": True,
          },
  "query": "query SerialStructureSeason($contentId: String!, $limit: Int!, $offset: Int!, $withUserData: Boolean!) { seasonByContentId(contentId: $contentId) { id contentId number episodes(limit: $limit, offset: $offset, isOnlyOnline: true) { total items { ...SerialStructureEpisode __typename } __typename } __typename } } fragment EpisodeBase on Episode { id contentId number __typename } fragment Title on Title { localized original __typename } fragment SeasonBase on Season { id contentId number __typename } fragment SerialStructureEpisode on Episode { ...EpisodeBase synopsis(override: OTT_WHEN_EXISTS) title { ...Title __typename } season { ...SeasonBase __typename } cover { avatarsUrl fallbackUrl __typename } ott { duration editorAnnotation plannedAvailabilityDate viewOption { availabilityStatus availabilityEndDate watchabilityStatus __typename } timing @include(if: $withUserData) { current __typename } __typename } __typename } "
}

    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()

        data = response.json()
        seasons = data.get("data", {}).get("seasonByContentId", [])

        if not seasons:
            print(f"[ERROR] No seasons found for content ID: {content_id}.")
            return []

        # استخراج سنة الإصدار وتنسيقها مع الموسم
        formatted_seasons = []
        for season in seasons:
            # استخدام سنة إصدار المسلسل كقيمة افتراضية
            release_year = default_release_year
            if "releaseYears" in season and len(season["releaseYears"]) > 0:
                release_year = season["releaseYears"][0].get("start", default_release_year)

            # إضافة البيانات مع سنة الإصدار
            formatted_seasons.append({
                "Season Number": season.get("number", "N/A"),
                "Content ID": season.get("contentId", "N/A"),
                "Total Episodes": season.get("episodes", {}).get("total", 0),
                "Release Year": release_year  # استخدام سنة إصدار المسلسل
            })

        # عرض الجدول
        display_seasons_table(formatted_seasons)
        return formatted_seasons

    except requests.exceptions.RequestException as e:
        print(f"[ERROR] Failed to fetch season details: {e}")
        return []
    except Exception as e:
        print(f"[ERROR] An unexpected error occurred: {e}")
        return []

def display_seasons_table(seasons):
    """عرض جدول المواسم مع تنسيق النصوص والأرقام في المنتصف تلقائيًا."""
    if not seasons:
        print(f"No seasons to display.")
        return

    # إعداد بيانات الجدول
    table_data = []
    for season in seasons:
        season_number = season.get("Season Number", "N/A")
        total_episodes = season.get("Total Episodes", 0)
        release_year = season.get("Release Year", "N/A")

        table_data.append([season_number, total_episodes, release_year])

    # إعداد العناوين مع ألوان
    headers = [
        Fore.YELLOW + "Season Number" + Style.RESET_ALL,
        Fore.CYAN + "Total Episodes" + Style.RESET_ALL,
        Fore.MAGENTA + "Release Year" + Style.RESET_ALL  # إضافة عمود سنة الإصدار
    ]

    # طباعة الجدول مع تنسيق تلقائي في المنتصف
    table = tabulate(
        table_data,
        headers=headers,
        tablefmt="fancy_grid",
        stralign="center",  # يجعل النصوص والأرقام في المنتصف تلقائيًا
        numalign="center"   # يجعل الأرقام في المنتصف تلقائيًا
    )
    print(table)


global_series_title = sanitize_filename("default_title")  # تنظيف اسم المسلسل
global_season_number = "01"
global_episode_number = "01"

def fetch_season_details_and_display(content_id, auto_download=False, skip_episodes_table=False):
    headers = headers_HD if selected_quality == "HD" else headers_4K
    global global_season_number, global_episode_number, global_series_title
    """
    Fetch episodes of a season using its contentId and provide options to download or process episodes.

    Parameters:
        content_id: The content ID of the season
        auto_download: If True, automatically download all episodes without showing the options menu
        skip_episodes_table: If True, skip displaying the episodes table and go directly to download
    """
    url = "https://kp-graphql-api.movies.yango.com/graphql/?operationName=SerialStructureSeason"

    payload = {
        "operationName": "SerialStructureSeason",
        "variables": {
            "contentId": content_id,
            "limit": 30,
            "offset": 0,
            "withUserData": True,
  },
  "query": "query SerialStructureSeason($contentId: String!, $limit: Int!, $offset: Int!, $withUserData: Boolean!) { seasonByContentId(contentId: $contentId) { id contentId number episodes(limit: $limit, offset: $offset, isOnlyOnline: true) { total items { ...SerialStructureEpisode __typename } __typename } __typename } } fragment EpisodeBase on Episode { id contentId number __typename } fragment Title on Title { localized original __typename } fragment SeasonBase on Season { id contentId number __typename } fragment SerialStructureEpisode on Episode { ...EpisodeBase synopsis(override: OTT_WHEN_EXISTS) title { ...Title __typename } season { ...SeasonBase __typename } cover { avatarsUrl fallbackUrl __typename } ott { duration editorAnnotation plannedAvailabilityDate viewOption { availabilityStatus availabilityEndDate watchabilityStatus __typename } timing @include(if: $withUserData) { current __typename } __typename } __typename } "
}

    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()

        data = response.json()
        season_data = data.get("data", {}).get("seasonByContentId", None)

        if not season_data or not season_data.get("episodes"):
            print(f"{Fore.RED}[ERROR] No episodes found for season with content ID: {content_id}.{Style.RESET_ALL}")
            return []

        # تحديد رقم الموسم
        season_number = season_data.get('number', 1)
        global_season_number = f"S{str(season_number).zfill(2)}"  # حفظ الموسم بصيغة S01
        global_series_title = sanitize_filename(global_series_title)  # تنظيف اسم المسلسل
        #print(f"{Fore.GREEN}[INFO] Season Number Set: {global_season_number}{Style.RESET_ALL}")

        # تجهيز الحلقات
        episodes = season_data.get("episodes", {}).get("items", [])
        formatted_episodes = []
        for episode in episodes:
            # تحديد حالة الحلقة
            availability_status = episode.get("ott", {}).get("viewOption", {}).get("availabilityStatus", None)
            planned_date = episode.get("ott", {}).get("plannedAvailabilityDate", None)

            if availability_status == "PUBLISHED":
                status = "Available"
            elif planned_date:
                # استخراج التاريخ بالتنسيق MM/DD
                formatted_date = "/".join(planned_date.split("T")[0].split("-")[1:])
                status = formatted_date
            else:
                status = "Soon"  # عرض قريباً إذا لم يكن هناك تاريخ إصدار

            # تنسيق البيانات
            formatted_episodes.append({
                "Episode": f"E{str(episode.get('number')).zfill(2)}",
                "Status/Release Date": status,
                "Content ID": episode.get("contentId")  # حفظ Content ID للمعالجة
            })

        # إذا كان auto_download=True و skip_episodes_table=True، قم بتنزيل جميع الحلقات تلقائيًا بدون عرض جدول الحلقات
        if auto_download and skip_episodes_table:
            print(f"{Fore.MAGENTA}[AUTO] Downloading all episodes sequentially with the same quality...{Style.RESET_ALL}")
            print(f"{Fore.GREEN}[INFO] Found {len(formatted_episodes)} available episodes for {global_series_title} {global_season_number}{Style.RESET_ALL}")
            # إعادة تعيين متغير الجودة العالمي للتأكد من أنه فارغ
            global selected_episode_quality
            selected_episode_quality = None
            batch_download_episodes(formatted_episodes)
            return formatted_episodes

        # عرض الحلقات في جدول إذا لم يتم تخطيه
        print(f"\n{Fore.GREEN}Episodes for {global_season_number}:{Style.RESET_ALL}\n")
        table = tabulate(
            [{"Episode": ep["Episode"], "Status/Release Date": ep["Status/Release Date"]} for ep in formatted_episodes],
            headers={
                "Episode": f"{Fore.YELLOW}Episode{Style.RESET_ALL}",
                "Status/Release Date": f"{Fore.CYAN}Status/Release Date{Style.RESET_ALL}",
            },
            tablefmt="fancy_grid",
            stralign="center"
        )
        print(table)

        # إذا كان auto_download=True ولكن skip_episodes_table=False، قم بتنزيل جميع الحلقات تلقائيًا بعد عرض الجدول
        if auto_download:
            print(f"{Fore.MAGENTA}[AUTO] Downloading all episodes sequentially with the same quality...{Style.RESET_ALL}")
            # إعادة تعيين متغير الجودة العالمي للتأكد من أنه فارغ
            selected_episode_quality = None
            batch_download_episodes(formatted_episodes)
            return formatted_episodes

        # إذا لم يكن auto_download=True، عرض قائمة الخيارات
        print(f"\n{Fore.YELLOW}{'-' * 60}")
        print(f"{'OPTIONS MENU':^60}")
        print(f"{'-' * 60}{Style.RESET_ALL}")

        print(f"{Fore.MAGENTA}1. {Style.RESET_ALL}Download {Fore.MAGENTA}all episodes sequentially{Style.RESET_ALL} (same quality for all episodes)")
        print(f"{Fore.BLUE}2. {Style.RESET_ALL}Download a {Fore.BLUE}specific range of episodes sequentially{Style.RESET_ALL} (same quality)")
        print(f"{Fore.RED}3. {Style.RESET_ALL}Return to the {Fore.RED}main menu{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}{'-' * 60}{Style.RESET_ALL}")

        choice = input(f"{Fore.CYAN}Enter your choice (1-3): {Style.RESET_ALL}")

        if choice == "1":
            # تنزيل جميع الحلقات بنفس الجودة
            print(f"{Fore.MAGENTA}Downloading all episodes sequentially with the same quality...{Style.RESET_ALL}")
            # إعادة تعيين متغير الجودة العالمي للتأكد من أنه فارغ
            selected_episode_quality = None
            batch_download_episodes(formatted_episodes)

        elif choice == "2":
            # تنزيل نطاق محدد من الحلقات بنفس الجودة
            start = int(input(f"{Fore.CYAN}Enter the starting episode number: {Style.RESET_ALL}"))
            end = int(input(f"{Fore.CYAN}Enter the ending episode number: {Style.RESET_ALL}"))

            print(f"{Fore.BLUE}Downloading episodes {start} to {end} sequentially with the same quality...{Style.RESET_ALL}")
            # إعادة تعيين متغير الجودة العالمي للتأكد من أنه فارغ
            selected_episode_quality = None
            batch_download_episodes(formatted_episodes, start, end)

        elif choice == "3":
            print(f"{Fore.YELLOW}Returning to the main menu...{Style.RESET_ALL}")
        else:
            print(f"{Fore.RED}Invalid choice. Please try again.{Style.RESET_ALL}")

        return formatted_episodes

    except requests.exceptions.RequestException as e:
        print(f"{Fore.RED}[ERROR] Failed to fetch season details: {e}{Style.RESET_ALL}")
        return []
    except Exception as e:
        print(f"{Fore.RED}[ERROR] An unexpected error occurred: {e}{Style.RESET_ALL}")
        return []

def send_to_player_base_api(content_id, episode_name, batch_mode=False):
    """Send contentId to the PlayerBaseInfo API."""
    headers = headers_HD if selected_quality == "HD" else headers_4K

    # تحميل البوسترات للمسلسل قبل تحميل الحلقة
    try:
        # تحميل البوسترات للمسلسل
        sanitized_series_title = sanitize_filename(global_series_title)
        series_folder = os.path.join("downloads", sanitized_series_title)
        posters_folder = os.path.join(series_folder, "posters")

        # إنشاء المجلدات إذا لم تكن موجودة
        os.makedirs(series_folder, exist_ok=True)
        os.makedirs(posters_folder, exist_ok=True)

        print(f"{Fore.YELLOW}[INFO] Downloading posters for series...{Style.RESET_ALL}")

        # استخدام معرف المسلسل المخزن في قاعدة البيانات
        conn = sqlite3.connect("series.db")
        cursor = conn.cursor()
        cursor.execute("SELECT content_id FROM series WHERE title LIKE ?", (f"%{sanitized_series_title.replace('.', '%')}%",))
        result = cursor.fetchone()
        conn.close()

        poster_files = []
        if result:
            series_id = result[0]

            # الحصول على بيانات المسلسل من API مباشرة
            movie_card_response = fetch_and_print_movie_card(series_id, silent=True)
            if movie_card_response and "data" in movie_card_response and "movieByContentUuid" in movie_card_response["data"]:
                # استخدام البيانات من استجابة API مباشرة
                poster_files = download_posters_from_api_response(movie_card_response["data"]["movieByContentUuid"], posters_folder, verbose=False)
                if not poster_files:
                    # استخدام الطريقة القديمة إذا فشلت الطريقة الجديدة
                    poster_files = download_movie_posters(series_id, posters_folder, verbose=False)
            else:
                # استخدام الطريقة القديمة إذا فشلت الطريقة الجديدة
                poster_files = download_movie_posters(series_id, posters_folder, verbose=False)
        else:
            # محاولة استخدام معرف الحلقة للعثور على معرف المسلسل
            try:
                # استخدام PlayerBaseInfo API للحصول على معرف المسلسل
                episode_response = fetch_and_print_movie_card(content_id, silent=True)
                if episode_response and "data" in episode_response and "movieByContentUuid" in episode_response["data"]:
                    episode_data = episode_response["data"]["movieByContentUuid"]
                    if "tvSeries" in episode_data and "contentId" in episode_data["tvSeries"]:
                        series_id = episode_data["tvSeries"]["contentId"]
                        # استخدام معرف المسلسل لتحميل البوسترات
                        series_response = fetch_and_print_movie_card(series_id, silent=True)
                        if series_response and "data" in series_response and "movieByContentUuid" in series_response["data"]:
                            poster_files = download_posters_from_api_response(series_response["data"]["movieByContentUuid"], posters_folder, verbose=False)
            except Exception:
                pass

        # عرض رسالة النجاح فقط إذا تم تحميل البوسترات
        if poster_files:
            print(f"{Fore.GREEN}[SUCCESS] Downloaded {len(poster_files)} posters successfully{Style.RESET_ALL}")
        else:
            print(f"{Fore.YELLOW}[INFO] No posters were downloaded{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}[ERROR] Failed to download posters: {str(e)}{Style.RESET_ALL}")

    # بعد تحميل البوسترات، متابعة معالجة الحلقة
    print(f"{Fore.YELLOW}Processing {episode_name} (ID: {content_id})...{Style.RESET_ALL}")

    url = "https://kp-graphql-api.movies.yango.com/graphql/?operationName=PlayerBaseInfo"

    payload = {
        "operationName": "PlayerBaseInfo",
        "variables": {
            "prerollsSupported": True,
            "isKidSubProfile": False,
            "contentId": content_id,
 },
  "query": "query PlayerBaseInfo($contentId: String!, $prerollsSupported: Boolean = false, $isKidSubProfile: Boolean = false) { content(contentId: $contentId) { ... on Catchup { ...CatchupPlayerBaseInfo __typename } ... on VideoInterface { ...FilmPlayerBaseInfo __typename } ... on Episode { ...EpisodePlayerBaseInfo __typename } ... on TvChannel { ...ChannelPlayerBaseInfo __typename } ... on Clip { ...ClipPlayerBaseInfo __typename } __typename } userProfile @include(if: $isKidSubProfile) { ...ChildLockPlayerKidSubProfile __typename } } fragment CatchupTvChannel on Catchup { tvChannel { contentId ageRestriction title gallery { logos { main { avatarsUrl __typename } __typename } __typename } __typename } __typename } fragment MovieStreamAudioMeta on AudioMeta { audioChannelsNumber audioGroupId forAdult language languageName quality studioName title visibleByDefault __typename } fragment MovieStreamDrmConfig on DrmConfig { drmServers { certificateUrl name processSPCPath provisioningUrl url __typename } headers requestParams __typename } fragment MovieStreamSubtitleMeta on SubtitleMeta { forAdult languageName studio type language title url visibleByDefault __typename } fragment MovieStreamTile on Tile { spriteDuration tileDuration uriTemplate tilePixelResolution { height width __typename } spriteMatrixSize { columns rows __typename } spritePixelResolution { height width __typename } __typename } fragment MovieStreamTiles on TilesContainer { highResolutionTiles { ...MovieStreamTile __typename } lowResolutionTiles { ...MovieStreamTile __typename } offset __typename } fragment MovieStreamPrerolls on PrerollMeta { durationMs promotedEntityId __typename } fragment MovieStreamSkippableFragment on SkippableFragment { startTime endTime final type __typename } fragment MovieStreamMeta on StreamMeta { audioMetas { ...MovieStreamAudioMeta __typename } drmConfig { ...MovieStreamDrmConfig __typename } subtitleMetas { ...MovieStreamSubtitleMeta __typename } tilesContainer { ...MovieStreamTiles __typename } trackings videoMetas { bitrate height width __typename } prerollsDuration prerolls { ...MovieStreamPrerolls __typename } skippableFragments { ...MovieStreamSkippableFragment __typename } videoToken cuesUrl __typename } fragment MovieStream on Stream { streamMeta { ...MovieStreamMeta __typename } uri __typename } fragment MovieOnlineStreams on OnlineStreams { licenseStatus drmRequirement streams { ...MovieStream __typename } sessionId concurrencyArbiterConfig { concurrencyArbiterRequestParams server __typename } playerRestrictionConfig { subtitlesButtonEnable __typename } monetizationModel __typename } fragment CatchupStreams on Catchup { onlineStreams { ...MovieOnlineStreams __typename } __typename } fragment CatchupMeta on Catchup { catchupTitle: title duration hasSmokingScenes ageRestriction viewOption { watchabilityExpirationTime __typename } __typename } fragment FilmPlayerOnlineStreams on Ott { ... on Ott_AbstractVideo { onlineStreams(prerollsSupported: $prerollsSupported) { ...MovieOnlineStreams __typename } __typename } __typename } fragment SmokingFilmFlag on Ott { ... on Ott_AbstractVideo { hasSmokingScenes __typename } __typename } fragment MovieTiming on OttTiming { current __typename } fragment FilmOttPreview on Ott_AbstractVideo { preview { ... on OttPreview_AbstractVideo { timing { ...MovieTiming __typename } duration __typename } __typename } __typename } fragment MovieWatchParams on OttUserData { watchParams { audioLanguage subtitleLanguage __typename } __typename } fragment FilmAgeRestriction on VideoInterface { restriction { age __typename } __typename } fragment EpisodeDetails on Episode { episodeContentId: contentId number tvSeries { contentId id __typename } season { contentId number __typename } episodeOtt: ott { viewOption { watchabilityStatus __typename } __typename } __typename } fragment EpisodeAgeRestriction on Episode { tvSeries { contentId restriction { age __typename } __typename } __typename } fragment EpisodePlayerOnlineStreams on OttEpisode { onlineStreams(prerollsSupported: $prerollsSupported) { ...MovieOnlineStreams __typename } __typename } fragment EpisodeSmokingFlag on OttEpisode { hasSmokingScenes __typename } fragment EpisodePlayerNextEpisode on Episode { neighbourhoodInSeries(limit: 1, filter: {onlyOnline: true}) { items { ...EpisodeDetails __typename } __typename } __typename } fragment ChannelMeta on TvChannel { ageRestriction title gallery { logos { main { avatarsUrl fallbackUrl __typename } __typename } covers { horizontal { avatarsUrl fallbackUrl __typename } __typename } __typename } isNeedToHidePrograms isMultiplex __typename } fragment ChannelStreams on TvChannel { onlineStreams { ...MovieOnlineStreams __typename } __typename } fragment TvChannelProgram on TvProgram { id title startTime endTime episodeTitle ageRestriction typeName catchupContentId image { avatarsUrl fallbackUrl __typename } __typename } fragment ChannelPrograms on TvChannel { programs: tvPrograms(duration: \"PT4H\") { ...TvChannelProgram __typename } __typename } fragment ClipStreamMeta on StreamMeta { audioMetas { ...MovieStreamAudioMeta __typename } drmConfig { ...MovieStreamDrmConfig __typename } subtitleMetas { ...MovieStreamSubtitleMeta __typename } tilesContainer { ...MovieStreamTiles __typename } trackings videoMetas { bitrate height width __typename } skippableFragments { ...MovieStreamSkippableFragment __typename } videoToken cuesUrl __typename } fragment ClipStream on Stream { streamMeta { ...ClipStreamMeta __typename } uri __typename } fragment ClipOnlineStreams on OnlineStreams { licenseStatus drmRequirement streams { ...ClipStream __typename } sessionId concurrencyArbiterConfig { concurrencyArbiterRequestParams server __typename } playerRestrictionConfig { subtitlesButtonEnable __typename } monetizationModel __typename } fragment ClipStreams on Clip { onlineStreams { ...ClipOnlineStreams __typename } __typename } fragment CatchupPlayerBaseInfo on Catchup { catchupContentId: contentId ...CatchupTvChannel ...CatchupStreams ...CatchupMeta __typename } fragment FilmPlayerBaseInfo on VideoInterface { filmContentId: contentId id filmOtt: ott { ...FilmPlayerOnlineStreams ...SmokingFilmFlag ...FilmOttPreview userData { ...MovieWatchParams __typename } __typename } ...FilmAgeRestriction __typename } fragment EpisodePlayerBaseInfo on Episode { id episodeContentId: contentId ...EpisodeDetails ...EpisodeAgeRestriction episodeOtt: ott { ...EpisodePlayerOnlineStreams ...EpisodeSmokingFlag timing { ...MovieTiming __typename } duration __typename } ...EpisodePlayerNextEpisode tvSeries { contentId ott { userData { ...MovieWatchParams __typename } __typename } __typename } __typename } fragment ChannelPlayerBaseInfo on TvChannel { channelContentId: contentId ...ChannelMeta ...ChannelStreams ...ChannelPrograms __typename } fragment ClipPlayerBaseInfo on Clip { clipContentId: contentId ...ClipStreams __typename } fragment ChildLockPlayerKidSubProfile on UserKidSubProfile { id { ottId puid __typename } restrictions { parentalControlVideo { streamUrl id __typename } parentalControlEnabled __typename } __typename } "
}

    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()

        # Extract MPD URL and requestParams
        data = response.json()
        streams = data.get("data", {}).get("content", {}).get("episodeOtt", {}).get("onlineStreams", {}).get("streams", [])

        if len(streams) > 1:
            mpd_url = streams[1].get("uri", "")
            request_params = streams[1].get("streamMeta", {}).get("drmConfig", {}).get("requestParams", {})

            if not mpd_url:
                print(f"{Fore.RED}MPD URL not found.{Style.RESET_ALL}")
                return

            print(f"{Fore.YELLOW}[+] MPD:{Style.RESET_ALL} {Fore.CYAN}{mpd_url}{Style.RESET_ALL}")

            # Pass MPD URL and requestParams to extract_keys_from_mpd with batch_mode parameter
            extract_series_keys_from_mpd(mpd_url, request_params, batch_mode=batch_mode)
            return True
        else:
            print(f"{Fore.RED}No valid streams found in response.{Style.RESET_ALL}")
            return False

    except requests.exceptions.RequestException as e:
        print(f"{Fore.RED}Error fetching movie details: {e}{Style.RESET_ALL}")
        return False
    except KeyError as e:
        print(f"{Fore.RED}Missing key in response: {e}{Style.RESET_ALL}")
        return False
    except Exception as e:
        print(f"{Fore.RED}An unexpected error occurred: {e}{Style.RESET_ALL}")
        return False

def sanitize_filename(filename):
    """
    تعديل اسم الملف لجعله آمنًا وإضافة النقاط بين الكلمات.
    """
    # التحقق من وجود قيمة
    if not filename:
        return "unknown_title"

    # إزالة الرموز غير المسموح بها في أسماء الملفات في Windows
    sanitized = re.sub(r'[<>:"/\\|?*]', '', filename)

    # استبدال المسافات والشرطات السفلية بنقاط
    sanitized = re.sub(r'[\s_]+', '.', sanitized)

    # إزالة النقاط المتكررة
    sanitized = re.sub(r'\.+', '.', sanitized)

    # إزالة النقاط من بداية ونهاية الاسم
    return sanitized.strip('.')

def parse_series_mpd(mpd_url, title="default_title", grouptag="YANGO", batch_mode=False):
    """
    تحليل ملف MPD لمسلسل وعرض جدول يحتوي على الجودات والصوتيات والترجمات مع التنزيل والدمج.
    إذا كان batch_mode=True، سيتم استخدام الجودة المخزنة مسبقًا بدلاً من طلب اختيار المستخدم.

    المعاملات:
        mpd_url: رابط ملف MPD
        title: عنوان المسلسل (يستخدم في ملف المفاتيح)
        grouptag: علامة المجموعة لاسم الملف
        batch_mode: إذا كان صحيحًا، يتم استخدام الجودة المخزنة مسبقًا
    """
    global selected_episode_quality

    # استخدام العنوان للسجلات فقط
    print(f"{Fore.GREEN}[INFO] Processing {title}...{Style.RESET_ALL}")

    try:
        response = requests.get(mpd_url)
        response.raise_for_status()

        # إعداد الملفات
        cache_dir = "cache"

        # تعديل اسم المسلسل باستخدام sanitize_filename لإضافة النقاط
        formatted_series_title = sanitize_filename(global_series_title)

        # إعداد مسار المجلد الخاص بالمسلسل والموسم
        series_dir = os.path.join("downloads", formatted_series_title)
        season_number = global_season_number if global_season_number.startswith("S") else f"S{int(global_season_number):02}"
        season_dir = os.path.join(series_dir, season_number)

        # إنشاء مجلد المسلسل إذا لم يكن موجودًا
        os.makedirs(series_dir, exist_ok=True)
        os.makedirs(season_dir, exist_ok=True)

        episode_number = global_episode_number.replace("E", "") if global_episode_number.startswith("E") else global_episode_number
        episode_number = f"{int(episode_number):02}"  # تنسيق الرقم ليكون بصيغة رقمين

        # تحليل XML
        mpd_content = ET.fromstring(response.content)
        namespaces = {"": "urn:mpeg:dash:schema:mpd:2011"}

        # البحث عن AdaptationSet
        adaptation_sets = mpd_content.findall(".//AdaptationSet", namespaces)

        video_tracks = []
        audio_languages = set()
        subtitle_languages = set()

        # تعديل الجودات لتحويل القيم فقط للعرض
        def display_resolution(resolution):
            if resolution == "816":
                return "720"
            elif resolution == "1088":
                return "1080"
            elif resolution == "1632":
                return "2160"
            return resolution

        for adaptation in adaptation_sets:
            mime_type = adaptation.get("mimeType", "")
            content_type = adaptation.get("contentType", "")
            lang = adaptation.get("lang", "unknown")

            if "video" in mime_type:
                for rep in adaptation.findall("Representation", namespaces):
                    quality = rep.get('height')
                    bandwidth = rep.get('bandwidth')
                    if quality and bandwidth:
                        video_tracks.append({
                            "Quality": f"{quality}p",
                            "Bandwidth": f"{bandwidth} bps",
                            "Resolution": quality,
                        })

            elif "audio" in mime_type:
                audio_languages.add(lang)

            elif content_type == "text" or "text" in mime_type:
                subtitle_languages.add(lang)

        if not video_tracks:
            print(f"{Fore.RED}[ERROR] No video tracks found in MPD.{Style.RESET_ALL}")
            return

        # ترتيب الفيديو حسب الجودة
        sorted_video_tracks = sorted(video_tracks, key=lambda x: int(x["Resolution"]))

        # إعداد الجدول بدون تغيير القيم الأصلية
        numbered_tracks = [
            {
                "No.": i + 1,
                "Resolution": f'{display_resolution(track["Resolution"])}P',  # للعرض فقط
                "Bandwidth": track["Bandwidth"],
                "Audios": " | ".join(sorted(audio_languages)),
                "Subtitles": " | ".join(sorted(subtitle_languages)),
                "_OriginalResolution": track["Resolution"],  # مخفي ولكنه يظل جزءًا من البيانات
            }
            for i, track in enumerate(sorted_video_tracks)
        ]

        # إذا كنا في وضع الدفعة، استخدم الجودة المخزنة مسبقًا
        if batch_mode and selected_episode_quality:
            # البحث عن الجودة المخزنة في القائمة
            selected_track = None
            for track in numbered_tracks:
                if track["_OriginalResolution"] == selected_episode_quality:
                    selected_track = track
                    break

            # إذا لم يتم العثور على الجودة المخزنة، استخدم الجودة الأعلى المتاحة
            if not selected_track:
                selected_track = numbered_tracks[-1]  # أعلى جودة متاحة
                print(f"{Fore.YELLOW}[INFO] Saved quality not found, using highest available quality.{Style.RESET_ALL}")
            else:
                print(f"{Fore.GREEN}[INFO] Using saved quality: {display_resolution(selected_track['_OriginalResolution'])}P{Style.RESET_ALL}")
        else:
            # إعداد رأس الجدول بدون OriginalResolution
            headers = {
                "No.": f"{Fore.YELLOW}No.{Style.RESET_ALL}",
                "Resolution": f"{Fore.CYAN}Resolution{Style.RESET_ALL}",
                "Bandwidth": f"{Fore.BLUE}Bandwidth{Style.RESET_ALL}",
                "Audios": f"{Fore.GREEN}Audios{Style.RESET_ALL}",
                "Subtitles": f"{Fore.MAGENTA}Subtitles{Style.RESET_ALL}",
            }

            # عرض الجدول بدون OriginalResolution
            try:
                print(tabulate(
                    [
                        {key: row[key] for key in headers.keys()}  # إزالة العمود المخفي عند العرض
                        for row in numbered_tracks
                    ],
                    headers=headers,
                    tablefmt="fancy_grid",
                    stralign="center",
                    colalign=["center"] * len(headers),
                ))
            except Exception as e:
                print(f"{Fore.RED}[ERROR] Failed to display table: {e}{Style.RESET_ALL}")

            choice = int(input(f"\n{Fore.YELLOW}Enter the number of the quality to download (or 0 to cancel): {Style.RESET_ALL}"))
            if choice == 0:
                print(f"{Fore.YELLOW}Download cancelled.{Style.RESET_ALL}")
                return

            selected_track = numbered_tracks[choice - 1]

            # حفظ الجودة المختارة للاستخدام في التنزيلات اللاحقة
            selected_episode_quality = selected_track["_OriginalResolution"]
            print(f"{Fore.GREEN}[INFO] Quality saved for batch downloads: {display_resolution(selected_episode_quality)}P{Style.RESET_ALL}")

        original_resolution = selected_track["_OriginalResolution"]  # استخدام القيمة الأصلية
        displayed_resolution = display_resolution(original_resolution)

        # تعديل اسم الملف النهائي باستخدام اسم المسلسل المنسق
        filename = f"{formatted_series_title}.{season_number}.E{episode_number}.{displayed_resolution}p.{grouptag}.WEB-DL.H264.AAC.mkv"
        file_path = os.path.join(season_dir, filename)

        # التحقق إذا تم التحميل بالفعل
        if os.path.exists(file_path):
            print(f"{Fore.GREEN}[INFO] File already exists: {file_path}{Style.RESET_ALL}")
            return  # إنهاء العملية إذا كان الملف موجودًا

        print(f"{Fore.YELLOW}[INFO] File not found. Starting download process...{Style.RESET_ALL}")

        # إعداد الملفات المؤقتة
        video_file = os.path.join(cache_dir, f"{filename}.mp4")
        audio_files = [
            os.path.join(cache_dir, f"{filename}.eng.m4a"),
            os.path.join(cache_dir, f"{filename}.ara.m4a")
        ]
        subtitle_files = [
            os.path.join(cache_dir, f"{filename}.eng.srt"),
            os.path.join(cache_dir, f"{filename}.ara.srt")
        ]

        print(f"{Fore.YELLOW}[INFO] Preparing to download: {filename}{Style.RESET_ALL}")

        # تنزيل الملفات
        audio_option = f'--select-audio "lang=ar|en|tur:for=best3"'
        subtitle_option = f'--select-subtitle "lang=ar|en:for=best2"'


        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)

        download_command = (
            f'"{n_m3u8dl_path}" "{mpd_url}" -mt '
            f'--select-video "res={original_resolution}" '
            f'{audio_option} '
            f'{subtitle_option} '
            f'--tmp-dir "{cache_dir}" '
            f'--save-dir "{cache_dir}" '
            f'--save-name "{filename}" '
            f'--decryption-binary-path="{mp4decrypt_path}" '
            f'--key-text-file="{KEYS_PATH}" '
            f'--log-level "OFF"'
        )

        print(f"{Fore.YELLOW}[INFO] Running download command...{Style.RESET_ALL}")
        subprocess.call(download_command, shell=True)

        # التحقق من التحميل
        if not os.path.exists(video_file):
            print(f"{Fore.RED}[ERROR] Video file not found after download.{Style.RESET_ALL}")
            return

        handle_merge_with_mkvmerge(
            video_file=video_file,
            audio_files=[f for f in audio_files if os.path.exists(f)],
            subtitle_files=[f for f in subtitle_files if os.path.exists(f)],
            output_dir=season_dir,
            output_filename=filename,
            cache_dir=cache_dir
        )

        return True  # إرجاع قيمة نجاح للتحقق في وظائف أخرى

    except Exception as e:
        print(f"{Fore.RED}[ERROR] An unexpected error occurred: {e}{Style.RESET_ALL}")
        return False

def handle_merge_with_mkvmerge(video_file, audio_files, subtitle_files, output_dir, output_filename, cache_dir):
    """
    دمج الفيديو والصوت والترجمات باستخدام mkvmerge مع تعطيل الترجمة بشكل افتراضي.
    """
    try:
        # تعديل اسم المسلسل والموسم ليكونا آمنين
        sanitized_series_title = sanitize_filename(global_series_title)
        sanitized_season_number = sanitize_filename(global_season_number)

        # إنشاء المجلد النهائي للمسلسل
        series_folder = os.path.join("downloads", sanitized_series_title)
        if not os.path.exists(series_folder):
            os.makedirs(series_folder)
            print(f"{Fore.GREEN}[INFO] Created series folder: {series_folder}{Style.RESET_ALL}")

        # إعداد مسار الموسم
        season_folder = os.path.join(series_folder, sanitized_season_number)
        if not os.path.exists(season_folder):
            os.makedirs(season_folder)
            print(f"{Fore.GREEN}[INFO] Created season folder: {season_folder}{Style.RESET_ALL}")

        # إنشاء مجلد مؤقت إذا لزم الأمر
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # بناء أمر الدمج
        merge_command = [
            mkvmerge_path, "-o", os.path.join(output_dir, output_filename),
            video_file  # إضافة ملف الفيديو
        ]

        # إضافة ملفات الصوت
        for audio_file in audio_files:
            language = "eng" if "eng" in audio_file else "ara"  # تحديد اللغة بناءً على اسم الملف
            merge_command.extend(["--language", f"0:{language}", audio_file])

        # إضافة ملفات الترجمة
        for subtitle_file in subtitle_files:
            if "ara" in subtitle_file:
                merge_command.extend(["--language", "0:ara", "--default-track", "0:no", subtitle_file])
            elif "eng" in subtitle_file:
                merge_command.extend(["--language", "0:eng", "--default-track", "0:no", subtitle_file])
            else:
                merge_command.extend(["--language", "0:und", "--default-track", "0:no", subtitle_file])  # لغة غير محددة

        # تنفيذ الدمج
        print(f"Running merge command: {' '.join(merge_command)}")
        subprocess.run(merge_command, check=True)

        print(f"{Fore.GREEN}[INFO] Merge completed successfully!{Style.RESET_ALL}")

        # البوسترات تم تحميلها بالفعل في بداية العملية

        # **نقل الملف النهائي إلى مجلد الموسم**
        final_file_path = os.path.join(output_dir, output_filename)
        moved_file_path = os.path.join(season_folder, output_filename)

        # نقل الملف النهائي إلى المجلد الخاص بالموسم
        shutil.move(final_file_path, moved_file_path)
        print(f"{Fore.GREEN}[INFO] Moved file to season folder: {moved_file_path}{Style.RESET_ALL}")

        # حذف مجلد الكاش بالكامل بعد الدمج ونقل الملف
        if os.path.exists(cache_dir):
            shutil.rmtree(cache_dir)
            print(f"{Fore.GREEN}[INFO] Cache directory '{cache_dir}' deleted successfully!{Style.RESET_ALL}")

    except Exception as e:
        print(f"{Fore.RED}[ERROR] Failed to merge or move files: {e}{Style.RESET_ALL}")


# تابع لاستخراج المفاتيح من ملف MPD الخاص بالمسلسل
def check_content_availability(_):
    """
    تم تعديل هذه الدالة لإرجاع أن جميع المحتويات متاحة دائمًا.

    المعاملات:
        _: معرف المحتوى للتحقق منه (غير مستخدم)

    الإرجاع:
        قاموس يحتوي على معلومات التوفر مع تعيين is_available دائمًا إلى True
    """

    # تجاهل الاتصال بالـ API وإرجاع أن المحتوى متاح دائمًا
    print(f"{Fore.GREEN}[INFO] Skipping API check and assuming content is available.{Style.RESET_ALL}")

    # إرجاع أن المحتوى متاح دائمًا
    result = {
        "is_available": True,
        "availability_status": "WATCHABLE",
        "availability_date": None,
        "formatted_date": None
    }

    return result


def extract_series_keys_from_mpd(mpd_url, requests_params, title="default_title", batch_mode=False):
    try:
        # استخراج PSSH
        pssh = get_pssh(mpd_url)
        if not pssh:
            print(f"{Fore.RED}[ERROR] Failed to extract PSSH.{Style.RESET_ALL}")
            return None
        #print(f"\n{Fore.YELLOW}[+] PSSH:{Style.RESET_ALL} {Fore.CYAN}{pssh}{Style.RESET_ALL}\n")

        # استخراج المفاتيح
        keys = get_decryption_key(pssh, requests_params)
        if not keys:
            print(f"{Fore.RED}[ERROR] Failed to extract decryption keys.{Style.RESET_ALL}")
            return None

        # عرض المفاتيح المستخرجة
        for key in keys:
            print(f"{Fore.YELLOW}[+] Key:{Style.RESET_ALL} {key}")
            print()

        # حفظ المفتاح في ملف KEYS.txt
        keys_dir = os.path.dirname(KEYS_PATH)
        os.makedirs(keys_dir, exist_ok=True)

        with open(KEYS_PATH, "a") as keys_file:
            keys_file.write(f"\n# Series: {title}\n")
            keys_file.write("\n".join(keys) + "\n")

        # استدعاء دالة عرض الجودات مع تمرير وضع الدفعة
        result = parse_series_mpd(mpd_url, title, grouptag="YANGO", batch_mode=batch_mode)
        return keys if result else None

    except Exception as e:
        print(f"{Fore.RED}[ERROR] Error in extract_series_keys_from_mpd: {e}{Style.RESET_ALL}")
        import traceback
        traceback.print_exc()  # طباعة التفاصيل الكاملة للخطأ
        return None


def download_entire_season(season_id):
    """
    تحميل موسم كامل بنفس الجودة لجميع الحلقات.
    يتخطى عرض جدول الحلقات وينتقل مباشرة إلى تحميل جميع الحلقات بنفس الجودة.
    """
    print(f"{Fore.YELLOW}[INFO] Preparing to download entire season...{Style.RESET_ALL}")

    # البوسترات تم تحميلها بالفعل في بداية العملية عند استدعاء send_to_player_base_api

    # استخدام نفس الدالة المستخدمة لعرض الحلقات ولكن بدون عرض جدول الحلقات
    global selected_episode_quality
    selected_episode_quality = None  # إعادة تعيين متغير الجودة العالمي

    # استدعاء الدالة الموجودة لعرض تفاصيل الموسم والحصول على الحلقات
    episodes = fetch_season_details_and_display(season_id, auto_download=True, skip_episodes_table=True)

    if not episodes:
        print(f"{Fore.RED}[ERROR] Failed to fetch episodes for season.{Style.RESET_ALL}")
        return


def batch_download_episodes(episodes, start_episode=None, end_episode=None):
    """
    تنزيل مجموعة من الحلقات بنفس الجودة المختارة للحلقة الأولى.
    يمكن تحديد نطاق من الحلقات باستخدام start_episode و end_episode.
    يتحقق من توفر الحلقات قبل التنزيل ويعرض تاريخ التوفر للحلقات غير المتاحة.
    """
    global global_episode_number, selected_episode_quality

    # تحميل البوسترات للمسلسل قبل تحميل الحلقات
    try:
        # تحميل البوسترات للمسلسل
        sanitized_series_title = sanitize_filename(global_series_title)
        series_folder = os.path.join("downloads", sanitized_series_title)
        posters_folder = os.path.join(series_folder, "posters")

        # إنشاء المجلدات إذا لم تكن موجودة
        os.makedirs(series_folder, exist_ok=True)
        os.makedirs(posters_folder, exist_ok=True)  # إنشاء مجلد البوسترات دائمًا

        # تحميل البوسترات حتى لو كان المجلد موجودًا
        print(f"{Fore.YELLOW}[INFO] Downloading posters for series...{Style.RESET_ALL}")

        # استخدام معرف المسلسل المخزن في قاعدة البيانات
        conn = sqlite3.connect("series.db")
        cursor = conn.cursor()
        cursor.execute("SELECT content_id FROM series WHERE title LIKE ?", (f"%{sanitized_series_title.replace('.', '%')}%",))
        result = cursor.fetchone()
        conn.close()

        poster_files = []
        if result:
            series_id = result[0]
            # الحصول على بيانات المسلسل من API مباشرة
            movie_card_response = fetch_and_print_movie_card(series_id, silent=True)
            if movie_card_response and "data" in movie_card_response and "movieByContentUuid" in movie_card_response["data"]:
                # استخدام البيانات من استجابة API مباشرة
                poster_files = download_posters_from_api_response(movie_card_response["data"]["movieByContentUuid"], posters_folder, verbose=False)
                if not poster_files:
                    # استخدام الطريقة القديمة إذا فشلت الطريقة الجديدة
                    poster_files = download_movie_posters(series_id, posters_folder, verbose=False)
            else:
                # استخدام الطريقة القديمة إذا فشلت الطريقة الجديدة
                poster_files = download_movie_posters(series_id, posters_folder, verbose=False)

        # عرض رسالة النجاح فقط إذا تم تحميل البوسترات
        if poster_files:
            print(f"{Fore.GREEN}[SUCCESS] Downloaded {len(poster_files)} posters successfully{Style.RESET_ALL}")
        else:
            print(f"{Fore.YELLOW}[INFO] No posters were downloaded{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}[ERROR] Failed to download posters: {str(e)}{Style.RESET_ALL}")

    # إذا تم تحديد نطاق معين، قم بتصفية الحلقات
    if start_episode is not None and end_episode is not None:
        # تصفية الحلقات بناءً على النطاق المحدد
        range_episodes = [
            episode for episode in episodes
            if episode.get("Content ID") and start_episode <= int(episode["Episode"][1:]) <= end_episode
        ]
    else:
        # إذا لم يتم تحديد نطاق، استخدم جميع الحلقات
        range_episodes = [
            episode for episode in episodes
            if episode.get("Content ID")
        ]

    if not range_episodes:
        print(f"{Fore.RED}[ERROR] No episodes found in the specified range.{Style.RESET_ALL}")
        return

    # تقسيم الحلقات إلى متاحة وغير متاحة
    available_episodes = []
    unavailable_episodes = []

    # التحقق من توفر كل حلقة باستخدام المعلومات المخزنة في الجدول فقط
    for episode in range_episodes:
        # الحلقات المتاحة بالفعل في الجدول
        if episode["Status/Release Date"] == "Available":
            available_episodes.append(episode)
        else:
            # إضافة الحلقة إلى قائمة الحلقات غير المتاحة مع معلومات التوفر
            release_date = episode["Status/Release Date"]
            unavailable_episodes.append({
                "episode": episode,
                "availability": {
                    "is_available": False,
                    "availability_status": "FUTURE",
                    "availability_date": release_date,
                    "formatted_date": release_date
                }
            })

    # عرض معلومات عن الحلقات غير المتاحة بشكل مميز
    if unavailable_episodes:
        print(f"\n{Fore.YELLOW}{'=' * 80}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}[FUTURE EPISODES] {Fore.CYAN}The following episodes are not available yet:{Style.RESET_ALL}")

        # إعداد بيانات الجدول
        table_data = []
        for item in unavailable_episodes:
            episode = item["episode"]
            availability = item["availability"]
            release_date = availability["formatted_date"] if availability["formatted_date"] else "Coming Soon"
            table_data.append({
                "Episode": f"{Fore.CYAN}{episode['Episode']}{Style.RESET_ALL}",
                "Release Date": f"{Fore.MAGENTA}{release_date}{Style.RESET_ALL}"
            })

        # عرض الجدول
        headers = {
            "Episode": f"{Fore.YELLOW}Episode{Style.RESET_ALL}",
            "Release Date": f"{Fore.YELLOW}Release Date{Style.RESET_ALL}"
        }
        print(tabulate(table_data, headers=headers, tablefmt="fancy_grid", stralign="center"))
        print(f"{Fore.YELLOW}{'=' * 80}{Style.RESET_ALL}\n")

    if not available_episodes:
        print(f"{Fore.RED}[ERROR] No available episodes found in the specified range.{Style.RESET_ALL}")
        return

    total_episodes = len(available_episodes)
    print(f"{Fore.GREEN}[INFO] Starting batch download of {total_episodes} available episodes...{Style.RESET_ALL}")

    # تنزيل الحلقة الأولى لتحديد الجودة
    first_episode = available_episodes[0]
    global_episode_number = first_episode["Episode"]  # تحديث رقم الحلقة العالمي

    # تنزيل الحلقة الأولى لتحديد الجودة
    print(f"{Fore.CYAN}[INFO] Processing first episode {global_episode_number} to select quality...{Style.RESET_ALL}")

    try:
        # محاولة تنزيل الحلقة الأولى
        success = send_to_player_base_api(first_episode["Content ID"], first_episode["Episode"], batch_mode=False)

        # التحقق من أن الجودة تم تحديدها وأن التنزيل نجح
        if not selected_episode_quality or not success:
            print(f"{Fore.RED}[ERROR] Failed to set quality for batch download.{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}[INFO] Please try downloading a different episode or check if the content is available.{Style.RESET_ALL}")
            return
    except Exception as e:
        print(f"{Fore.RED}[ERROR] An error occurred while processing the first episode: {e}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}[INFO] Please try downloading a different episode or check if the content is available.{Style.RESET_ALL}")
        return

    # تنزيل بقية الحلقات بنفس الجودة
    for i, episode in enumerate(available_episodes[1:], 2):
        print(f"{Fore.CYAN}[INFO] Processing episode {i}/{total_episodes}: {episode['Episode']}{Style.RESET_ALL}")
        global_episode_number = episode["Episode"]  # تحديث رقم الحلقة العالمي
        send_to_player_base_api(episode["Content ID"], episode["Episode"], batch_mode=True)

    print(f"{Fore.GREEN}[INFO] Batch download completed for {total_episodes} episodes.{Style.RESET_ALL}")

    # إذا كانت هناك حلقات غير متاحة، ذكر المستخدم بها مرة أخرى
    if unavailable_episodes:
        print(f"{Fore.YELLOW}[REMINDER] {len(unavailable_episodes)} episodes were not downloaded because they are not available yet.{Style.RESET_ALL}")

######################################### Series Menu Update #########################################
def series_menu():
    global selected_quality
    selected_quality = select_quality()  # سؤال الجودة هنا

    clear_screen()
    print_banner()
    print(f"{Fore.GREEN}[+] SERIES SECTION ({selected_quality}):{Style.RESET_ALL}\n")

    # عرض رسالة للمستخدم أثناء تحميل المسلسلات
    print(f"{Fore.CYAN}[INFO] Loading series with availability information from database...{Style.RESET_ALL}")

    series = fetch_series()
    sorted_series = display_movies_with_numbers(series)

    # عرض معلومات عن كيفية تحديث معلومات التوفر
    print(f"{Fore.YELLOW}[TIP] To update availability information, use the 'Update Database' option from the main menu.{Style.RESET_ALL}")

    if sorted_series:
        try:
            # عرض رسالة أكثر وضوحًا للمستخدم
            print(f"\n{Fore.CYAN}[INFO] Select a series number from the table above (1-{len(sorted_series)}) or 0 to return{Style.RESET_ALL}")
            choice = int(input(f"{Fore.YELLOW}Your choice: {Style.RESET_ALL}"))
            if 0 < choice <= len(sorted_series):
                selected_series = sorted_series[choice - 1]

                series_title = selected_series["Title"]
                content_id = selected_series["ID"].strip()

                # استخدام معلومات التوفر من قاعدة البيانات
                if "availability" in selected_series:
                    # استخدام معلومات التوفر من قاعدة البيانات
                    availability = selected_series["availability"]
                    is_available = availability["is_available"]
                    print(f"{Fore.YELLOW}[INFO] Using availability information from database.{Style.RESET_ALL}")
                else:
                    # إذا لم تكن معلومات التوفر متوفرة في قاعدة البيانات، نفترض أنه متاح
                    print(f"{Fore.YELLOW}[INFO] No availability information in database. Assuming content is available.{Style.RESET_ALL}")
                    availability = {
                        "is_available": True,
                        "availability_status": "WATCHABLE",
                        "availability_date": None,
                        "formatted_date": None
                    }
                    is_available = True

                # إذا كان المسلسل غير متاح وله تاريخ إصدار مستقبلي
                if not is_available and availability["availability_date"]:
                    # عرض رسالة واضحة ومميزة للمستخدم
                    print(f"\n{Fore.YELLOW}{'=' * 80}{Style.RESET_ALL}")
                    print(f"{Fore.YELLOW}[FUTURE RELEASE] {Fore.CYAN}{series_title}{Style.RESET_ALL}")
                    print(f"{Fore.GREEN}[INFO] This series will be available on: {Fore.MAGENTA}{availability['formatted_date']}{Style.RESET_ALL}")
                    print(f"{Fore.YELLOW}{'=' * 80}{Style.RESET_ALL}\n")
                    # الاستمرار لعرض معلومات المسلسل إذا كانت متوفرة

                movie_card_response = fetch_and_print_movie_card(content_id)

                if movie_card_response:
                    series_release_year = movie_card_response.get("data", {}).get("movieByContentUuid", {}).get("releaseYears", [{}])[0].get("start", "N/A")

                    seasons = movie_card_response.get("data", {}).get("movieByContentUuid", {}).get("seasons", {}).get("items", [])
                    if seasons:
                        formatted_seasons = [
                            {
                                "Season Number": season.get("number"),
                                "Content ID": season.get("contentId"),
                                "Total Episodes": season.get("episodes", {}).get("total", 0),
                                "Release Year": series_release_year
                            }
                            for season in seasons
                        ]

                        print(f"\n{Fore.GREEN}[+] Seasons for Series: {series_title}{Style.RESET_ALL}\n")
                        display_seasons_table(formatted_seasons)

                        # عرض خيارات الموسم
                        print(f"\n{Fore.YELLOW}{'-' * 60}")
                        print(f"{'SEASON OPTIONS':^60}")
                        print(f"{'-' * 60}{Style.RESET_ALL}")
                        print(f"{Fore.CYAN}1. {Style.RESET_ALL}View {Fore.CYAN}episodes list{Style.RESET_ALL} for a season")
                        print(f"{Fore.MAGENTA}2. {Style.RESET_ALL}Download {Fore.MAGENTA}entire season{Style.RESET_ALL} (same quality for all episodes)")
                        print(f"{Fore.RED}3. {Style.RESET_ALL}Return to the {Fore.RED}main menu{Style.RESET_ALL}")
                        print(f"{Fore.YELLOW}{'-' * 60}{Style.RESET_ALL}")

                        option_choice = input(f"\n{Fore.YELLOW}Enter your choice (1-3): {Style.RESET_ALL}")

                        if option_choice == "1":
                            # عرض الحلقات
                            season_choice = int(input(f"\n{Fore.YELLOW}Enter the season number to view episodes: {Style.RESET_ALL}"))
                            selected_season = next(
                                (season for season in formatted_seasons if season["Season Number"] == season_choice),
                                None
                            )

                            if selected_season:
                                fetch_season_details_and_display(selected_season["Content ID"])
                            else:
                                print(f"{Fore.RED}[ERROR] Invalid season number. Please try again.{Style.RESET_ALL}")

                        elif option_choice == "2":
                            # تحميل موسم كامل
                            season_choice = int(input(f"\n{Fore.YELLOW}Enter the season number to download: {Style.RESET_ALL}"))
                            selected_season = next(
                                (season for season in formatted_seasons if season["Season Number"] == season_choice),
                                None
                            )

                            if selected_season:
                                # تعيين المتغيرات العالمية للمسلسل والموسم
                                global global_series_title, global_season_number
                                global_series_title = series_title
                                global_season_number = f"S{str(season_choice).zfill(2)}"

                                # طباعة معلومات الموسم للتشخيص
                                season_id = selected_season["Content ID"]
                                print(f"{Fore.YELLOW}[DEBUG] Selected season: {season_choice}, Content ID: {season_id}{Style.RESET_ALL}")

                                # تحميل الموسم بالكامل
                                download_entire_season(season_id)
                            else:
                                print(f"{Fore.RED}[ERROR] Invalid season number. Please try again.{Style.RESET_ALL}")

                        elif option_choice == "3":
                            # العودة للقائمة الرئيسية
                            pass
                        else:
                            print(f"{Fore.RED}[ERROR] Invalid choice. Please try again.{Style.RESET_ALL}")
                    else:
                        # إذا لم يتم العثور على مواسم، تحقق من توفر المسلسل
                        print(f"{Fore.YELLOW}[DEBUG] Checking availability for series with no seasons. Availability data: {availability}{Style.RESET_ALL}")

                        if not is_available and availability["availability_date"]:
                            # عرض رسالة واضحة ومميزة للمستخدم
                            print(f"\n{Fore.YELLOW}{'=' * 80}{Style.RESET_ALL}")
                            print(f"{Fore.YELLOW}[FUTURE RELEASE] {Fore.CYAN}{series_title}{Style.RESET_ALL}")
                            print(f"{Fore.GREEN}[INFO] This series will be available on: {Fore.MAGENTA}{availability['formatted_date']}{Style.RESET_ALL}")
                            print(f"{Fore.YELLOW}{'=' * 80}{Style.RESET_ALL}\n")
                        else:
                            print(f"{Fore.RED}[ERROR] No seasons found for the selected series.{Style.RESET_ALL}")
                else:
                    # إذا فشل جلب تفاصيل المسلسل، تحقق من توفر المسلسل
                    print(f"{Fore.YELLOW}[DEBUG] Failed to fetch movie card. Checking availability data: {availability}{Style.RESET_ALL}")

                    if not is_available and availability["availability_date"]:
                        # عرض رسالة واضحة ومميزة للمستخدم
                        print(f"\n{Fore.YELLOW}{'=' * 80}{Style.RESET_ALL}")
                        print(f"{Fore.YELLOW}[FUTURE RELEASE] {Fore.CYAN}{series_title}{Style.RESET_ALL}")
                        print(f"{Fore.GREEN}[INFO] This series will be available on: {Fore.MAGENTA}{availability['formatted_date']}{Style.RESET_ALL}")
                        print(f"{Fore.YELLOW}{'=' * 80}{Style.RESET_ALL}\n")
                    else:
                        print(f"{Fore.RED}[ERROR] Failed to fetch series details.{Style.RESET_ALL}")

            elif choice == 0:
                return
            else:
                print(f"{Fore.RED}Invalid choice. Please try again.{Style.RESET_ALL}")
        except ValueError:
            print(f"{Fore.RED}Invalid input. Please enter a number.{Style.RESET_ALL}")

    input(f"\n{Fore.YELLOW}Press Enter to return to the main menu...{Style.RESET_ALL}")


########################################### menu ###########################################
# مسح الشاشة
def clear_screen():
    os.system('cls' if os.name == 'nt' else 'clear')

# طباعة العنوان العلوي
def print_banner():
    banner = f"""
    {Fore.BLUE}╔════════════════════════════════════════════════════╗
    ║                                                    ║
    ║     {Fore.YELLOW}WELCOME TO OSN PLUS DOWNLOADER V1.0{Style.RESET_ALL}         ║
    ║     {Fore.GREEN}BY MOUSTAFA KAMEL © 2024{Style.RESET_ALL}                      ║
    ║                                                    ║
    ║                                                    ║
    ║     {Fore.GREEN}CONTACT: {Style.RESET_ALL}@MoustafaKamel95                   ║
    ║                                                    ║
    ╚════════════════════════════════════════════════════╝{Style.RESET_ALL}
    """
    print(banner)

def movie_menu():
    global selected_quality
    selected_quality = select_quality()  # سؤال الجودة هنا

    clear_screen()
    print_banner()
    print(f"{Fore.GREEN}[+] MOVIE SECTION ({selected_quality}):{Style.RESET_ALL}\n")

    movies = fetch_movies()
    sorted_movies = display_movies_with_numbers(movies)

    if sorted_movies:
        try:
            # عرض رسالة أكثر وضوحًا للمستخدم
            print(f"\n{Fore.CYAN}[INFO] Select a movie number from the table above (1-{len(sorted_movies)}) or 0 to return{Style.RESET_ALL}")
            choice = int(input(f"{Fore.YELLOW}Your choice: {Style.RESET_ALL}"))
            if 0 < choice <= len(sorted_movies):
                selected_movie = sorted_movies[choice - 1]
                print()
                print(f"{Fore.YELLOW}[+] Movie Title:{Fore.RESET}{Fore.CYAN} {selected_movie['Title']}{Style.RESET_ALL}{Fore.RESET}")
                print()
                fetch_movie_details(selected_movie["ID"])
            elif choice == 0:
                return
            else:
                print(f"{Fore.RED}Invalid choice. Please try again.{Style.RESET_ALL}")
        except ValueError:
            print(f"{Fore.RED}Invalid input. Please enter a number.{Style.RESET_ALL}")

    input(f"\n{Fore.YELLOW}Press Enter to return to the main menu...{Style.RESET_ALL}")


########################################### Main Menu Update ###########################################
def main_menu():
    global selected_quality
    while True:
        clear_screen()
        print_banner()
        menu = f"""
        {Fore.GREEN}[+] PLEASE CHOOSE AN OPTION:{Style.RESET_ALL}

        {Fore.YELLOW}1.{Style.RESET_ALL} {Fore.CYAN}MOVIES SECTION{Style.RESET_ALL}
        {Fore.YELLOW}2.{Style.RESET_ALL} {Fore.CYAN}SERIES SECTION{Style.RESET_ALL}
        {Fore.YELLOW}3.{Style.RESET_ALL} {Fore.CYAN}UPDATE DATABASE{Style.RESET_ALL}
        {Fore.YELLOW}0.{Style.RESET_ALL} {Fore.CYAN}EXIT{Style.RESET_ALL}
        """
        print(menu)

        choice = input(f"{Fore.WHITE}[+] Please select an option (0-4): {Style.RESET_ALL}")
        if choice == "1":
            movie_menu()
        elif choice == "2":
            series_menu()
        elif choice == "3":
            update_database()
        elif choice == "4":
            selected_quality = select_quality()
        elif choice == "0":
            print(f"{Fore.GREEN}Exiting the program. Goodbye!{Style.RESET_ALL}")
            break
        else:
            print(f"{Fore.RED}Invalid option. Please try again.{Style.RESET_ALL}")
            input(f"\n{Fore.YELLOW}Press Enter to return to the main menu...{Style.RESET_ALL}")

def select_quality():
    clear_screen()
    print_banner()
    print(f"{Fore.GREEN}[+] SELECT STREAM QUALITY:{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}1.{Style.RESET_ALL} {Fore.CYAN}HD{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}2.{Style.RESET_ALL} {Fore.CYAN}4K{Style.RESET_ALL}")

    choice = input(f"\n{Fore.WHITE}[+] Enter your choice (1 or 2): {Style.RESET_ALL}")
    if choice == "1":
        return "HD"
    elif choice == "2":
        return "4K"
    else:
        print(f"{Fore.RED}Invalid choice, defaulting to HD.{Style.RESET_ALL}")
        return "HD"


########################################### Initialization ###########################################
if __name__ == "__main__":
    initialize_movies_database()  # إنشاء قاعدة بيانات الأفلام
    initialize_series_database()  # إنشاء قاعدة بيانات المسلسلات
    main_menu()