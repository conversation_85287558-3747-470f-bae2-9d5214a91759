# 🎬 Multi-Episode Download System for OSN_NEW

## 📋 Overview
تم إنشاء نظام تحميل متعدد الحلقات متطور في OSN_NEW يسمح للمستخدمين بتحديد نطاق من الحلقات وتحميلها بإعدادات موحدة.

## ✨ المميزات الجديدة

### 🎯 1. اختيار نطاق الحلقات
- **اختيار الكل**: تحديد جميع الحلقات بنقرة واحدة
- **إلغاء التحديد**: إلغاء تحديد جميع الحلقات
- **اختيار النطاق**: تحديد من الحلقة X إلى الحلقة Y
- **اختيار يدوي**: تحديد حلقات محددة بالنقر

### 🎬 2. إعدادات موحدة للجميع
```
✅ اختيار الاستريم مرة واحدة → يطبق على جميع الحلقات
✅ اختيار الجودة مرة واحدة → يطبق على جميع الحلقات  
✅ اختيار التراكات الصوتية مرة واحدة → يطبق على جميع الحلقات
✅ اختيار الترجمات مرة واحدة → يطبق على جميع الحلقات
```

### 📺 3. تحميل متتالي ذكي
- تحميل الحلقات واحدة تلو الأخرى
- جلب `contentId` و `streamId` لكل حلقة داخلياً
- تطبيق نفس الإعدادات على كل حلقة
- تتبع التقدم لكل حلقة منفصلة

### 📊 4. تكامل مع جدول التحميلات
- إضافة جميع الحلقات المختارة إلى جدول التحميلات
- عرض التقدم الفردي لكل حلقة
- حالة التحميل لكل حلقة (Queued, Downloading, Completed)

## 🔧 التطبيق التقني

### الملفات المحدثة:

#### `modules/osn_downloader.py`
```python
# وظائف جديدة:
✅ download_multiple_episodes()           # تحميل متعدد الحلقات
✅ _download_multiple_episodes_thread()   # معالجة متوازية
✅ _get_episode_stream_details()          # جلب تفاصيل الاستريم
✅ _get_mpd_url_from_api()               # جلب MPD URL
✅ _download_episode_with_saved_settings() # تحميل بالإعدادات المحفوظة
```

#### `modules/osn_ui.py`
```python
# وظائف محدثة:
✅ download_selected_episodes()          # تحميل الحلقات المختارة
✅ start_multi_episode_download()        # بدء التحميل المتعدد
✅ show_multi_episode_stream_dialog()    # حوار اختيار الإعدادات
✅ populate_multi_episode_options()      # ملء خيارات الجودة/الصوت
✅ execute_multi_episode_download()      # تنفيذ التحميل
✅ add_episode_to_downloads_table()      # إضافة للجدول
```

## 🎮 كيفية الاستخدام

### 1. اختيار الحلقات
```
1. ابحث عن مسلسل واختر موسم
2. ستظهر قائمة الحلقات مع أزرار التحكم:
   - [Select All] - تحديد الكل
   - [Select None] - إلغاء التحديد  
   - From: [1] To: [5] [Select Range] - اختيار نطاق
3. اختر الحلقات المطلوبة
4. اضغط [Download Selected Episodes]
```

### 2. تكوين الإعدادات
```
📺 حوار "Multi-Episode Download Settings" سيظهر:

🎬 Stream Selection:
   - Stream 1: 1080p (HDR)
   - Stream 2: 720p (SDR)
   
🎯 Quality Selection:
   - 1080p (5000000 bps)
   - 720p (3000000 bps)
   - 480p (1500000 bps)
   
🔊 Audio Tracks (Multi-select):
   ☑️ Arabic
   ☑️ English
   ☐ Turkish
   
📝 Subtitle Tracks (Multi-select):
   ☑️ Arabic
   ☐ English
```

### 3. بدء التحميل
```
1. اضغط [Start Download]
2. ستتم إضافة جميع الحلقات إلى جدول التحميلات
3. سيبدأ التحميل المتتالي تلقائياً
4. راقب التقدم في تاب Downloads
```

## 📈 مثال عملي

### السيناريو:
```
🎬 المسلسل: "Game of Thrones"
📺 الموسم: Season 1 (10 حلقات)
🎯 المطلوب: تحميل الحلقات 1-5
```

### الخطوات:
```
1. اختيار النطاق: From: 1, To: 5
2. اختيار الاستريم: Stream 1: 1080p (HDR)
3. اختيار الجودة: 1080p (5000000 bps)
4. اختيار الصوت: Arabic + English
5. اختيار الترجمة: Arabic
6. بدء التحميل
```

### النتيجة:
```
📊 جدول التحميلات:
┌─────────────────────────────────┬────────┬─────────┬──────────┬──────────┬─────────┐
│ Title                           │ Season │ Episode │ Quality  │ Progress │ Status  │
├─────────────────────────────────┼────────┼─────────┼──────────┼──────────┼─────────┤
│ Game of Thrones S01E01          │   1    │    1    │  1080p   │   45%    │ Downloading │
│ Game of Thrones S01E02          │   1    │    2    │  1080p   │    0%    │ Queued  │
│ Game of Thrones S01E03          │   1    │    3    │  1080p   │    0%    │ Queued  │
│ Game of Thrones S01E04          │   1    │    4    │  1080p   │    0%    │ Queued  │
│ Game of Thrones S01E05          │   1    │    5    │  1080p   │    0%    │ Queued  │
└─────────────────────────────────┴────────┴─────────┴──────────┴──────────┴─────────┘
```

## 🔄 تدفق العمل التقني

### 1. اختيار الحلقات
```python
selected_episodes = [episode1, episode2, episode3, ...]
```

### 2. حفظ الإعدادات
```python
saved_settings = {
    'quality': selected_quality,
    'audio_tracks': selected_audio,
    'subtitle_tracks': selected_subtitles,
    'drm_info': drm_info
}
```

### 3. التحميل المتتالي
```python
for episode in selected_episodes:
    # جلب contentId و streamId للحلقة
    mpd_url = get_episode_stream_details(episode)
    
    # تحميل بالإعدادات المحفوظة
    download_episode_with_saved_settings(mpd_url, saved_settings)
```

## 🎯 المميزات التقنية

### ✅ معالجة الـ IDs التلقائية
- جلب `contentId` لكل حلقة من البيانات
- استخراج `streamId` من الاستريم المختار
- بناء MPD URL لكل حلقة منفصلة

### ✅ إدارة الإعدادات
- حفظ الإعدادات مرة واحدة
- تطبيقها على جميع الحلقات
- عدم الحاجة لإعادة الاختيار

### ✅ التحميل المتوازي المحكوم
- تحميل حلقة واحدة في كل مرة
- منع التداخل والتضارب
- استخدام الموارد بكفاءة

### ✅ تتبع التقدم المتقدم
- شريط تقدم لكل حلقة
- حالة مفصلة لكل تحميل
- رسائل واضحة للمستخدم

## 🎉 النتيجة النهائية

تم إنشاء نظام تحميل متعدد الحلقات متكامل في OSN_NEW يوفر:

- 🎯 **سهولة الاستخدام**: اختيار نطاق وإعدادات موحدة
- ⚡ **كفاءة عالية**: تحميل متتالي بدون تداخل  
- 📊 **تتبع دقيق**: مراقبة تقدم كل حلقة منفصلة
- 🔧 **مرونة تقنية**: معالجة IDs والاستريمات تلقائياً

---
*نظام متطور لتحميل الحلقات المتعددة في OSN_NEW* 🚀
