﻿LOG 2025/06/17
Save Path: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\Logs
Task Start: 2025/06/17 00:06:05
Task CommandLine: C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\N_m3u8DL-RE.exe https://osn-video.anghcdn.co/public/156055-57917-PR681512-BX-AS045370-284229-b96c267951fc8027bbbb50e35b1f5975-1749796808/stream.mpd?token=P2Fkcy1lbmFibGVkPTAmY2xpZW50LXBsYXRmb3JtPXdlYi1vc24mY2xpZW50LXZlcnNpb249MS4xLjEmY29udGVudC1pZD01NzkxNyZleHBpcnk9MTc1MDE1MTE2MCZzaWduYXR1cmU9ZWU4NGRiZmI2OTNiMGUyOTRjMjc0YzhlNjk4OWMzYzFhMzRlMWYzNCZzdHJlYW0taWQ9MTI5MDA1JnVzZXItaWQ9MTUyMTE3ODk2 -mt --select-video id=375a1b17-2a60-4c12-9f77-1ef95fcc4890 --select-audio lang=ar --drop-subtitle .* --tmp-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\cache --save-dir C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\cache --save-name "Al Mushardoon S01E31.720p.OSN+.WEB-DL.H264.AAC" --decryption-binary-path=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\mp4decrypt.exe --key-text-file=C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\KEYS\OSN_KEYS.txt --log-level OFF

00:06:05.915 EXTRA: ffmpeg => C:\Users\<USER>\Desktop\OSN_NEW\OSN_NEW\YANGO\binaries\ffmpeg.exe
00:06:06.255 EXTRA: DropSubtitleFilter => For: best
00:06:06.255 EXTRA: VideoFilter => GroupIdReg: 375a1b17-2a60-4c12-9f77-1ef95fcc4890 For: best
00:06:06.255 EXTRA: AudioFilter => LanguageReg: ar For: best
