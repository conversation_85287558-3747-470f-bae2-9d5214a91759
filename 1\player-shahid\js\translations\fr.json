{"advertising": {"admessage": "La publicité prendra fin dans xx", "cuetext": "Publicité", "displayHeading": "Publicité", "loadingAd": "Chargement de la publicité", "podmessage": "Publicité __AD_POD_CURRENT__ sur __AD_POD_LENGTH__.", "skipmessage": "Sauter la publicité dans xx", "skiptext": "Sauter"}, "airplay": "AirPlay", "audioTracks": "Pistes Audio", "auto": "Automatique", "buffer": "Chargement", "cast": "Chromecast", "cc": "Sous-titres", "close": "<PERSON><PERSON><PERSON>", "errors": {"badConnection": "À la suite d'un problème de connexion Internet, la vidéo ne peut être lue.", "cantLoadPlayer": "Désolé, la vidéo n'a pas pu se charger.", "cantPlayInBrowser": "Ce navigateur n'est pas pris en charge.", "cantPlayVideo": "La vidéo ne peut être lue.", "errorCode": "Code d'erreur", "liveStreamDown": "Le streaming en live est en maintenance ou a pris fin.", "protectedContent": "Nous n'avons pas réussi à accéder à ce contenu protégé.", "technicalError": "Une erreur technique empêche la lecture de la vidéo."}, "exitFullscreen": "Sortir du Mode Plein Écran", "fullscreen": "<PERSON><PERSON>", "hd": "Qualité", "liveBroadcast": "Live", "logo": "Logo", "mute": "Désactiver le Son", "next": "Suite", "nextUp": "Prochaine <PERSON>", "notLive": "Hors Live", "off": "Désactiver", "pause": "Pause", "pipIcon": "Image dans l’image", "play": "Play", "playback": "Play", "playbackRates": "<PERSON><PERSON><PERSON>", "player": "Video Player", "poweredBy": "<PERSON>ni par", "prev": "Précédent", "related": {"autoplaymessage": "Prochaine vidéo dans xx", "heading": "Plus de Vidéos"}, "replay": "Replay", "rewind": "10 Secondes en Arrière", "settings": "Paramètres", "sharing": {"copied": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "embed": "Intégrer", "heading": "Partager", "link": "<PERSON><PERSON>"}, "slider": "<PERSON><PERSON>", "stop": "Stop", "unmute": "<PERSON><PERSON>", "videoInfo": "À Propos de Cette Vidéo", "volume": "Volume", "volumeSlider": "Barre de Volume", "shortcuts": {"playPause": "Play/Pause", "volumeToggle": "Dés<PERSON><PERSON> le Son/<PERSON><PERSON> le <PERSON>", "fullscreenToggle": "Plein Écran/Sortir du Mode Plein Écran", "seekPercent": "Avancer %", "keyboardShortcuts": "<PERSON><PERSON><PERSON><PERSON>", "increaseVolume": "Augmenter le Volume", "decreaseVolume": "Diminuer le Volume", "seekForward": "Avancer", "seekBackward": "<PERSON><PERSON><PERSON>", "spacebar": "Espace", "captionsToggle": "Sous-titres Activés/Désactivés"}, "captionsStyles": {"subtitleSettings": "Réglages des sous-titres", "color": "Couleur de police", "fontOpacity": "Opacité de police", "userFontScale": "Taille de police", "fontFamily": "Police", "edgeStyle": "<PERSON><PERSON><PERSON>", "backgroundColor": "<PERSON><PERSON><PERSON> de fond", "backgroundOpacity": "Opacité de la couleur de fond", "windowColor": "<PERSON><PERSON><PERSON> de fenêtre", "windowOpacity": "Opacité de fenêtre", "white": "<PERSON>", "black": "Noir", "red": "Rouge", "green": "<PERSON>ert", "blue": "Bleu", "yellow": "Jaune", "magenta": "Ma<PERSON><PERSON>", "cyan": "<PERSON><PERSON>", "none": "Aucun", "raised": "Relief", "depressed": "Empreinte", "uniform": "<PERSON>tour", "dropShadow": "Ombre"}, "disabled": "Désactivé", "enabled": "Activé", "reset": "Réinitialiser"}