#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
YANGO Settings Module
Handles application settings including proxy, filename format, and device path
"""

import os
import json
from pathlib import Path
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QLineEdit, QCheckBox, QGroupBox,
                               QFileDialog, QTextEdit, QFrame, QGridLayout,
                               QSpacerItem, QSizePolicy)
from PySide6.QtCore import QObject, Signal
from PySide6.QtGui import QFont

class YangoSettings(QObject):
    """YANGO Settings Manager"""
    
    # Signals
    settings_changed = Signal(dict)
    
    def __init__(self):
        super().__init__()
        # Point to YANGO root directory for settings file
        yango_root = Path(__file__).parent.parent
        self.settings_file = yango_root / "config" / "yango_main_settings.json"
        # Create config directory if it doesn't exist
        self.settings_file.parent.mkdir(exist_ok=True)
        self.default_settings = {
            "proxy": {
                "enabled": False,
                "http_proxy": "",
                "https_proxy": "",
                "username": "",
                "password": ""
            },
            "quality": {
                "default_quality": "HD",
                "available_qualities": ["HD", "4K"]
            },
            "filename_format": {
                "movies": "{title}.{quality}.YANGO.WEB-DL.H264.AAC",
                "series": "{title}.{season}.{episode}.{quality}.YANGO.WEB-DL.H264.AAC",
                "custom_tag": "YANGO.WEB-DL.H264.AAC"
            },
            "device_path": {
                "wvd_file": "device.wvd"
            },
            "downloads": {
                "base_directory": "downloads"
            }
        }
        self.current_settings = self.load_settings()
    
    def load_settings(self):
        """Load settings from file"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    # Merge with defaults to ensure all keys exist
                    settings = self.default_settings.copy()
                    self._deep_update(settings, loaded_settings)
                    return settings
            else:
                return self.default_settings.copy()
        except Exception as e:
            print(f"❌ Error loading settings: {e}")
            return self.default_settings.copy()
    
    def save_settings(self):
        """Save settings to file"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_settings, f, indent=4, ensure_ascii=False)
            print("✅ Settings saved successfully")
            self.settings_changed.emit(self.current_settings)
            return True
        except Exception as e:
            print(f"❌ Error saving settings: {e}")
            return False
    
    def _deep_update(self, base_dict, update_dict):
        """Deep update dictionary"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def get_setting(self, category, key, default=None):
        """Get specific setting value"""
        try:
            return self.current_settings.get(category, {}).get(key, default)
        except:
            return default
    
    def set_setting(self, category, key, value):
        """Set specific setting value"""
        if category not in self.current_settings:
            self.current_settings[category] = {}
        self.current_settings[category][key] = value
    
    def get_proxy_config(self):
        """Get proxy configuration for requests"""
        proxy_settings = self.current_settings.get("proxy", {})
        if not proxy_settings.get("enabled", False):
            return None
        
        proxies = {}
        if proxy_settings.get("http_proxy"):
            proxies["http"] = proxy_settings["http_proxy"]
        if proxy_settings.get("https_proxy"):
            proxies["https"] = proxy_settings["https_proxy"]
        
        # Add authentication if provided
        username = proxy_settings.get("username", "")
        password = proxy_settings.get("password", "")
        if username and password:
            for protocol in proxies:
                url = proxies[protocol]
                if "://" in url:
                    protocol_part, rest = url.split("://", 1)
                    proxies[protocol] = f"{protocol_part}://{username}:{password}@{rest}"
        
        return proxies if proxies else None
    
    def get_filename_format(self, content_type="movie"):
        """Get filename format for movies or series"""
        formats = self.current_settings.get("filename_format", {})
        if content_type == "movie":
            return formats.get("movies", self.default_settings["filename_format"]["movies"])
        else:
            return formats.get("series", self.default_settings["filename_format"]["series"])
    
    def get_device_path(self):
        """Get device.wvd file path"""
        from modules.resource_utils import get_resource_path
        default_path = get_resource_path("device.wvd")
        return self.current_settings.get("device_path", {}).get("wvd_file", default_path)

class YangoSettingsWidget(QWidget):
    """Settings UI Widget"""
    
    def __init__(self, settings_manager):
        super().__init__()
        self.settings = settings_manager
        self.init_ui()
        self.load_current_settings()
    
    def init_ui(self):
        """Initialize the settings UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # Title
        title = QLabel("⚙️ YANGO Settings")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #50fa7b; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # Create settings sections
        self.create_proxy_section(layout)
        self.create_quality_section(layout)
        self.create_filename_section(layout)
        self.create_device_section(layout)
        self.create_buttons_section(layout)
        
        # Add stretch to push everything to top
        layout.addStretch()
    
    def create_proxy_section(self, parent_layout):
        """Create proxy settings section"""
        proxy_group = QGroupBox("🌐 Proxy Settings")
        proxy_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #ffb86c;
                border: 2px solid #44475a;
                border-radius: 8px;
                margin: 5px 0;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        proxy_layout = QVBoxLayout(proxy_group)
        proxy_layout.setSpacing(10)
        
        # Enable proxy checkbox
        self.proxy_enabled = QCheckBox("Enable Proxy")
        self.proxy_enabled.setStyleSheet("""
            QCheckBox {
                color: #f8f8f2;
                font-size: 12px;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }
            QCheckBox::indicator:unchecked {
                background-color: #44475a;
                border: 2px solid #6272a4;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                background-color: #50fa7b;
                border: 2px solid #50fa7b;
                border-radius: 3px;
            }
        """)
        self.proxy_enabled.toggled.connect(self.toggle_proxy_fields)
        proxy_layout.addWidget(self.proxy_enabled)
        
        # Proxy fields
        proxy_grid = QGridLayout()
        proxy_grid.setSpacing(10)
        
        # HTTP Proxy
        proxy_grid.addWidget(QLabel("HTTP Proxy:"), 0, 0)
        self.http_proxy = QLineEdit()
        self.http_proxy.setPlaceholderText("http://proxy.example.com:8080")
        proxy_grid.addWidget(self.http_proxy, 0, 1)
        
        # HTTPS Proxy
        proxy_grid.addWidget(QLabel("HTTPS Proxy:"), 1, 0)
        self.https_proxy = QLineEdit()
        self.https_proxy.setPlaceholderText("https://proxy.example.com:8080")
        proxy_grid.addWidget(self.https_proxy, 1, 1)
        
        # Username
        proxy_grid.addWidget(QLabel("Username:"), 2, 0)
        self.proxy_username = QLineEdit()
        self.proxy_username.setPlaceholderText("Optional")
        proxy_grid.addWidget(self.proxy_username, 2, 1)
        
        # Password
        proxy_grid.addWidget(QLabel("Password:"), 3, 0)
        self.proxy_password = QLineEdit()
        self.proxy_password.setEchoMode(QLineEdit.Password)
        self.proxy_password.setPlaceholderText("Optional")
        proxy_grid.addWidget(self.proxy_password, 3, 1)
        
        # Style proxy input fields
        for i in range(proxy_grid.count()):
            widget = proxy_grid.itemAt(i).widget()
            if isinstance(widget, QLineEdit):
                widget.setStyleSheet("""
                    QLineEdit {
                        background-color: #44475a;
                        border: 2px solid #6272a4;
                        border-radius: 5px;
                        padding: 8px;
                        color: #f8f8f2;
                        font-size: 12px;
                    }
                    QLineEdit:focus {
                        border: 2px solid #50fa7b;
                    }
                """)
            elif isinstance(widget, QLabel):
                widget.setStyleSheet("color: #f8f8f2; font-size: 12px;")
        
        proxy_layout.addLayout(proxy_grid)
        parent_layout.addWidget(proxy_group)

    def create_quality_section(self, parent_layout):
        """Create quality settings section"""
        from PySide6.QtWidgets import QComboBox

        quality_group = QGroupBox("🎬 Quality Settings")
        quality_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #50fa7b;
                border: 2px solid #6272a4;
                border-radius: 8px;
                margin: 10px 0;
                padding-top: 15px;
                background-color: #282a36;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        quality_layout = QVBoxLayout(quality_group)
        quality_layout.setSpacing(15)
        quality_layout.setContentsMargins(15, 20, 15, 15)

        # Default Quality
        default_quality_layout = QHBoxLayout()
        default_quality_label = QLabel("Default Quality:")
        default_quality_label.setStyleSheet("color: #f8f8f2; font-size: 12px; font-weight: normal;")

        self.default_quality_combo = QComboBox()
        self.default_quality_combo.addItems(["HD", "4K"])
        self.default_quality_combo.setStyleSheet("""
            QComboBox {
                background-color: #44475a;
                border: 2px solid #6272a4;
                border-radius: 6px;
                padding: 8px 12px;
                color: #f8f8f2;
                font-size: 12px;
                min-width: 100px;
            }
            QComboBox:hover {
                border-color: #8be9fd;
            }
            QComboBox:focus {
                border-color: #50fa7b;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #f8f8f2;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                background-color: #44475a;
                border: 2px solid #6272a4;
                border-radius: 6px;
                color: #f8f8f2;
                selection-background-color: #50fa7b;
                selection-color: #282a36;
            }
        """)

        default_quality_layout.addWidget(default_quality_label)
        default_quality_layout.addWidget(self.default_quality_combo)
        default_quality_layout.addStretch()

        quality_layout.addLayout(default_quality_layout)

        # Quality info
        quality_info = QLabel("• HD: Standard quality with browser headers\n• 4K: High quality with iOS device simulation")
        quality_info.setStyleSheet("color: #6272a4; font-size: 11px; font-style: italic; margin-top: 5px;")
        quality_layout.addWidget(quality_info)

        parent_layout.addWidget(quality_group)

    def create_filename_section(self, parent_layout):
        """Create filename format settings section"""
        filename_group = QGroupBox("📁 Filename Format Settings")
        filename_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #bd93f9;
                border: 2px solid #44475a;
                border-radius: 8px;
                margin: 5px 0;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        filename_layout = QVBoxLayout(filename_group)
        filename_layout.setSpacing(10)
        
        # Movies format
        movies_layout = QHBoxLayout()
        movies_layout.addWidget(QLabel("Movies:"))
        self.movies_format = QLineEdit()
        self.movies_format.setPlaceholderText("{title}.{quality}.YANGO.WEB-DL.H264.AAC")
        movies_layout.addWidget(self.movies_format)
        filename_layout.addLayout(movies_layout)
        
        # Series format
        series_layout = QHBoxLayout()
        series_layout.addWidget(QLabel("Series:"))
        self.series_format = QLineEdit()
        self.series_format.setPlaceholderText("{title}.{season}.{episode}.{quality}.YANGO.WEB-DL.H264.AAC")
        series_layout.addWidget(self.series_format)
        filename_layout.addLayout(series_layout)
        
        # Custom tag
        tag_layout = QHBoxLayout()
        tag_layout.addWidget(QLabel("Custom Tag:"))
        self.custom_tag = QLineEdit()
        self.custom_tag.setPlaceholderText("YANGO.WEB-DL.H264.AAC")
        tag_layout.addWidget(self.custom_tag)
        filename_layout.addLayout(tag_layout)
        
        # Style filename input fields
        for field in [self.movies_format, self.series_format, self.custom_tag]:
            field.setStyleSheet("""
                QLineEdit {
                    background-color: #44475a;
                    border: 2px solid #6272a4;
                    border-radius: 5px;
                    padding: 8px;
                    color: #f8f8f2;
                    font-size: 12px;
                }
                QLineEdit:focus {
                    border: 2px solid #bd93f9;
                }
            """)
        
        # Style labels
        for i in range(filename_layout.count()):
            item = filename_layout.itemAt(i)
            if hasattr(item, 'itemAt'):
                for j in range(item.count()):
                    widget = item.itemAt(j).widget()
                    if isinstance(widget, QLabel):
                        widget.setStyleSheet("color: #f8f8f2; font-size: 12px; min-width: 80px;")
        
        # Help text
        help_text = QLabel("Available variables: {title}, {season}, {episode}, {quality}")
        help_text.setStyleSheet("color: #8be9fd; font-size: 10px; font-style: italic;")
        filename_layout.addWidget(help_text)
        
        parent_layout.addWidget(filename_group)

    def create_device_section(self, parent_layout):
        """Create device path settings section"""
        device_group = QGroupBox("🔑 Device Settings")
        device_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: bold;
                color: #f1fa8c;
                border: 2px solid #44475a;
                border-radius: 8px;
                margin: 5px 0;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)

        device_layout = QVBoxLayout(device_group)
        device_layout.setSpacing(10)

        # Device path
        device_path_layout = QHBoxLayout()
        device_path_layout.addWidget(QLabel("Device.wvd Path:"))

        self.device_path = QLineEdit()
        self.device_path.setPlaceholderText("device.wvd")
        self.device_path.setStyleSheet("""
            QLineEdit {
                background-color: #44475a;
                border: 2px solid #6272a4;
                border-radius: 5px;
                padding: 8px;
                color: #f8f8f2;
                font-size: 12px;
            }
            QLineEdit:focus {
                border: 2px solid #f1fa8c;
            }
        """)
        device_path_layout.addWidget(self.device_path)

        # Browse button
        self.browse_device_btn = QPushButton("📁 Browse")
        self.browse_device_btn.setStyleSheet("""
            QPushButton {
                background-color: #f1fa8c;
                color: #282a36;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #ffff99;
            }
            QPushButton:pressed {
                background-color: #e6e600;
            }
        """)
        self.browse_device_btn.clicked.connect(self.browse_device_file)
        device_path_layout.addWidget(self.browse_device_btn)

        device_layout.addLayout(device_path_layout)

        # Style device label
        for i in range(device_path_layout.count()):
            widget = device_path_layout.itemAt(i).widget()
            if isinstance(widget, QLabel):
                widget.setStyleSheet("color: #f8f8f2; font-size: 12px; min-width: 120px;")

        parent_layout.addWidget(device_group)

    def create_buttons_section(self, parent_layout):
        """Create action buttons section"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # Save button
        self.save_btn = QPushButton("💾 Save Settings")
        self.save_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #50fa7b, stop: 1 #40e060);
                color: #282a36;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #60ff8b, stop: 1 #50fa7b);
            }
            QPushButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #40e060, stop: 1 #30d050);
            }
        """)
        self.save_btn.clicked.connect(self.save_settings)

        # Reset button
        self.reset_btn = QPushButton("🔄 Reset to Default")
        self.reset_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ff7979, stop: 1 #e55656);
                color: #ffffff;
                border: none;
                padding: 12px 25px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 14px;
                min-width: 150px;
            }
            QPushButton:hover {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #ff8989, stop: 1 #ff7979);
            }
            QPushButton:pressed {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                            stop: 0 #e55656, stop: 1 #d63031);
            }
        """)
        self.reset_btn.clicked.connect(self.reset_settings)

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.reset_btn)
        buttons_layout.addStretch()

        parent_layout.addLayout(buttons_layout)

    def toggle_proxy_fields(self, enabled):
        """Toggle proxy input fields based on checkbox"""
        self.http_proxy.setEnabled(enabled)
        self.https_proxy.setEnabled(enabled)
        self.proxy_username.setEnabled(enabled)
        self.proxy_password.setEnabled(enabled)

    def browse_device_file(self):
        """Browse for device.wvd file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Device.wvd File",
            "",
            "WVD Files (*.wvd);;All Files (*)"
        )
        if file_path:
            self.device_path.setText(file_path)

    def load_current_settings(self):
        """Load current settings into UI"""
        # Proxy settings
        proxy_settings = self.settings.current_settings.get("proxy", {})
        self.proxy_enabled.setChecked(proxy_settings.get("enabled", False))
        self.http_proxy.setText(proxy_settings.get("http_proxy", ""))
        self.https_proxy.setText(proxy_settings.get("https_proxy", ""))
        self.proxy_username.setText(proxy_settings.get("username", ""))
        self.proxy_password.setText(proxy_settings.get("password", ""))
        self.toggle_proxy_fields(self.proxy_enabled.isChecked())

        # Quality settings
        quality_settings = self.settings.current_settings.get("quality", {})
        default_quality = quality_settings.get("default_quality", "HD")
        self.default_quality_combo.setCurrentText(default_quality)

        # Filename settings
        filename_settings = self.settings.current_settings.get("filename_format", {})
        self.movies_format.setText(filename_settings.get("movies", ""))
        self.series_format.setText(filename_settings.get("series", ""))
        self.custom_tag.setText(filename_settings.get("custom_tag", ""))

        # Device settings
        device_settings = self.settings.current_settings.get("device_path", {})
        self.device_path.setText(device_settings.get("wvd_file", ""))

    def save_settings(self):
        """Save settings from UI"""
        # Update proxy settings
        self.settings.current_settings["proxy"] = {
            "enabled": self.proxy_enabled.isChecked(),
            "http_proxy": self.http_proxy.text().strip(),
            "https_proxy": self.https_proxy.text().strip(),
            "username": self.proxy_username.text().strip(),
            "password": self.proxy_password.text().strip()
        }

        # Update quality settings
        self.settings.current_settings["quality"] = {
            "default_quality": self.default_quality_combo.currentText(),
            "available_qualities": ["HD", "4K"]
        }

        # Update filename settings
        self.settings.current_settings["filename_format"] = {
            "movies": self.movies_format.text().strip() or self.settings.default_settings["filename_format"]["movies"],
            "series": self.series_format.text().strip() or self.settings.default_settings["filename_format"]["series"],
            "custom_tag": self.custom_tag.text().strip() or self.settings.default_settings["filename_format"]["custom_tag"]
        }

        # Update device settings
        self.settings.current_settings["device_path"] = {
            "wvd_file": self.device_path.text().strip() or "device.wvd"
        }

        # Save to file
        if self.settings.save_settings():
            self.show_message("✅ Settings saved successfully!")
        else:
            self.show_message("❌ Failed to save settings!")

    def reset_settings(self):
        """Reset settings to default"""
        self.settings.current_settings = self.settings.default_settings.copy()
        self.load_current_settings()
        self.show_message("🔄 Settings reset to default!")

    def show_message(self, message):
        """Show a temporary message"""
        print(message)  # For now, just print. Can be enhanced with QMessageBox
