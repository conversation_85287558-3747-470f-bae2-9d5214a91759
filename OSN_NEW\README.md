# OSN+ Downloader - Modern GUI

A modern, user-friendly GUI application for downloading content from OSN+ platform.

## Features

- **Modern Interface**: Clean and intuitive GUI built with PySide6
- **Movie Downloads**: Download movies from OSN+ with multiple quality options
- **Series Downloads**: Download TV series episodes with season/episode selection
- **DRM Support**: Automatic DRM key extraction and decryption
- **Quality Selection**: Choose from available video qualities
- **Audio & Subtitle Options**: Select preferred audio and subtitle tracks
- **Progress Tracking**: Real-time download progress monitoring
- **Auto Token Refresh**: Automatic authentication token management

## Requirements

- Python 3.8+
- PySide6
- requests
- pathlib
- xml.etree.ElementTree

## Installation

1. Clone or download this repository
2. Install required dependencies:
   ```bash
   pip install PySide6 requests
   ```
3. Place your OSN+ cookies in `cookies.txt` file
4. Run the application:
   ```bash
   python main.py
   ```

## Usage

### Authentication
1. Export your OSN+ cookies from your browser
2. Place them in the `cookies.txt` file in Netscape format
3. The application will automatically refresh authentication tokens

### Downloading Movies
1. Click on "Search" in the left menu
2. Enter the OSN+ movie URL
3. Select desired quality and options
4. Click "Download" to start

### Downloading Series
1. Click on "Search" in the left menu
2. Enter the OSN+ series URL
3. Select season and episodes
4. Choose quality and options
5. Click "Download" to start

## File Structure

```
OSN_NEW/
├── main.py                 # Main application entry point
├── modules/                # Core modules
│   ├── osn_api.py         # OSN+ API integration
│   ├── osn_downloader.py  # Download functionality
│   ├── osn_drm.py         # DRM handling
│   ├── osn_ui.py          # UI management
│   ├── settings_manager.py # Settings management
│   └── ui_main.py         # Main UI definition
├── widgets/               # Custom UI widgets
├── images/                # UI images and icons
├── themes/                # Application themes
├── binaries/              # Required binary tools
│   ├── N_m3u8DL-RE.exe   # Download tool
│   ├── ffmpeg.exe        # Media processing
│   └── mp4decrypt.exe    # DRM decryption
├── downloads/             # Downloaded content
├── cache/                 # Temporary files
├── KEYS/                  # DRM keys storage
└── config/                # Configuration files
```

## Configuration

The application stores settings in `config/settings.json`. You can modify:

- Download paths
- Default quality preferences
- Audio/subtitle language preferences
- Concurrent download limits
- Token refresh intervals

## Troubleshooting

### Authentication Issues
- Ensure cookies.txt contains valid OSN+ authentication cookies
- Check if cookies are in correct Netscape format
- Try refreshing cookies from your browser

### Download Issues
- Verify internet connection
- Check if required binaries are present in `binaries/` folder
- Ensure sufficient disk space for downloads

### DRM Issues
- Make sure mp4decrypt.exe is present and executable
- Check if DRM keys are being extracted properly
- Verify content is not geo-blocked

## Legal Notice

This tool is for educational purposes only. Users are responsible for complying with OSN+ terms of service and applicable copyright laws. Only download content you have legal rights to access.

## Credits

- Built with PySide6 and Qt framework
- Uses N_m3u8DL-RE for downloading
- FFmpeg for media processing
- Modern UI design inspired by PyDracula theme

## License

This project is provided as-is for educational purposes. Please respect content creators and platform terms of service.
