#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
YANGO Player Module
This module handles playing MPD content with the custom player in the player directory
"""

import os
import threading
import urllib.parse
import http.server
import socketserver
import subprocess
import sys
import time
import webbrowser
from pathlib import Path

class YangoPlayer:
    def __init__(self, parent=None):
        """
        Initialize the YANGO Player module.

        Args:
            parent (QWidget, optional): Parent widget for the player window
        """
        # Get the script directory (parent of the modules directory)
        self.dir_path = Path(__file__).parent.parent
        self.player_dir = self.dir_path / "player"
        print(f"YANGO Player initialized with base directory: {self.dir_path}")
        print(f"Player directory: {self.player_dir}")

        self.http_server = None
        self.server_thread = None
        self.parent = parent

    def _start_http_server(self):
        """
        Start a local HTTP server to serve the player files.

        Returns:
            tuple: (success, port)
        """
        # Store player_dir for the handler
        player_directory = str(self.player_dir)
        
        # Custom HTTP handler for player files
        class PlayerHTTPHandler(http.server.SimpleHTTPRequestHandler):
            def __init__(self, *args, **kwargs):
                super().__init__(*args, directory=player_directory, **kwargs)

            def log_message(self, format, *args):
                print(f"[HTTP] {format % args}")

            def end_headers(self):
                # Add CORS headers
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
                super().end_headers()

        # Try multiple ports
        for port in range(8012, 8020):
            try:
                print(f"🔄 Trying to start HTTP server on port {port}...")
                
                # Create server
                server = socketserver.TCPServer(("localhost", port), PlayerHTTPHandler)
                server.allow_reuse_address = True
                
                # Start server in background thread
                server_thread = threading.Thread(target=server.serve_forever, daemon=True)
                server_thread.start()
                
                # Store server reference for cleanup
                self.http_server = server
                self.server_thread = server_thread
                
                print(f"✅ HTTP server started on port {port}")
                time.sleep(0.5)  # Give server time to start
                
                return True, port
                
            except OSError as e:
                if "Address already in use" in str(e):
                    print(f"⚠️ Port {port} is busy, trying next port...")
                    continue
                else:
                    print(f"❌ Error starting server on port {port}: {e}")
                    break
            except Exception as e:
                print(f"❌ Error starting server on port {port}: {e}")
                break

        print("❌ Failed to start HTTP server on any port")
        return False, None

    def _stop_http_server(self):
        """Stop the HTTP server."""
        if self.http_server:
            try:
                self.http_server.shutdown()
                self.http_server.server_close()
                print("🔴 Stopped HTTP server")
            except Exception as e:
                print(f"❌ Error stopping HTTP server: {e}")
            finally:
                self.http_server = None
                self.server_thread = None

    def play(self, mpd_url, keys=None, content_data=None, content_type='movie'):
        """
        Play a video using the YANGO player.

        Args:
            mpd_url (str): The MPD URL to play
            keys (list, optional): List of DRM keys in the format ["kid:key", ...]
            content_data (dict, optional): Content metadata
            content_type (str): Type of content ('movie' or 'episode')

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Stop any existing player
            self.stop()

            # Validate inputs
            if not mpd_url:
                print("❌ MPD URL is required")
                return False

            print(f"🎬 Starting YANGO Player...")
            print(f"📺 MPD URL: {mpd_url}")

            # Prepare key data for player - send all keys
            all_keys_string = ""
            single_key_id = ""
            single_key_value = ""
            
            if keys and len(keys) > 0:
                print(f"🔑 Processing {len(keys)} DRM keys for player...")
                
                # Create string with all keys (comma-separated)
                all_keys_string = ",".join(keys)
                print(f"🔑 All keys string: {all_keys_string}")
                
                # Also keep first key for backward compatibility
                first_key = keys[0]
                if ':' in first_key:
                    key_parts = first_key.split(':')
                    if len(key_parts) >= 2:
                        single_key_id = key_parts[0]
                        single_key_value = key_parts[1]
                        print(f"🔑 First key for compatibility: {single_key_id}:{single_key_value}")

            # Start HTTP server
            success, port = self._start_http_server()
            if not success:
                print("❌ Failed to start HTTP server")
                return False

            # Create player URL with query parameters
            base_url = f"http://localhost:{port}/YANGO_player.html"
            
            # Prepare URL parameters - include all keys
            params = {
                'mpd': mpd_url,
                'keyId': single_key_id,
                'key': single_key_value,
                'keys': all_keys_string  # All keys
            }

            player_url = f"{base_url}?" + urllib.parse.urlencode(params)
            print(f"🚀 Player URL: {player_url}")

            # Open player in separate Chrome window
            return self._open_in_chrome(player_url, content_data, content_type)

        except Exception as e:
            print(f"❌ Error starting YANGO player: {str(e)}")
            self._stop_http_server()
            return False

    def _open_in_chrome(self, player_url, content_data, content_type):
        """Open player in Chrome app mode like Shahid"""
        try:
            print(f"🚀 Opening YANGO Player in separate Chrome window...")

            # Try to find Chrome executable
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe"),
                "chrome.exe",  # If in PATH
                "google-chrome",  # Linux
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"  # macOS
            ]

            chrome_exe = None
            for path in chrome_paths:
                if os.path.exists(path):
                    chrome_exe = path
                    break

            if chrome_exe:
                # Launch Chrome in app mode (separate window)
                print(f"🔍 Found Chrome: {chrome_exe}")
                
                # Create user data directory for YANGO player
                user_data_dir = os.path.join(os.path.expanduser('~'), 'yango_player_chrome')
                
                cmd = [
                    chrome_exe,
                    f"--app={player_url}",
                    "--window-size=1280,720",
                    "--window-position=100,100",
                    f"--user-data-dir={user_data_dir}",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor",
                    "--autoplay-policy=no-user-gesture-required",
                    "--disable-infobars",
                    "--disable-notifications",
                    "--disable-default-apps",
                    "--disable-extensions",
                    "--no-first-run",
                    "--no-default-browser-check",
                    "--disable-translate",
                    "--disable-background-timer-throttling",
                    "--disable-backgrounding-occluded-windows",
                    "--disable-renderer-backgrounding",
                    "--disable-field-trial-config",
                    "--disable-ipc-flooding-protection",
                    "--test-type",
                    "--disable-logging",
                    "--silent-debugger-extension-api",
                    "--disable-component-extensions-with-background-pages",
                    "--disable-background-networking",
                    "--disable-sync",
                    "--metrics-recording-only",
                    "--no-report-upload",
                    "--disable-prompt-on-repost",
                    "--disable-hang-monitor",
                    "--disable-client-side-phishing-detection",
                    "--disable-popup-blocking",
                    "--disable-dev-shm-usage",
                    "--no-sandbox"
                ]

                print(f"🚀 Launching Chrome with app mode...")
                subprocess.Popen(cmd, shell=False)
                
                # Log success without showing message dialog
                if content_type == 'episode':
                    episode_title = content_data.get('title', 'Unknown Episode') if content_data else 'Unknown Episode'
                    episode_number = content_data.get('number', 'N/A') if content_data else 'N/A'
                    print(f"✅ YANGO Player opened for Episode {episode_number}: {episode_title}")
                else:
                    movie_title = content_data.get('title', 'Unknown Movie') if content_data else 'Unknown Movie'
                    movie_year = content_data.get('year', 'Unknown Year') if content_data else 'Unknown Year'
                    print(f"✅ YANGO Player opened for {movie_title} ({movie_year})")

                return True

            else:
                # Fallback to default browser if Chrome not found
                print("⚠️ Chrome not found, opening in default browser...")
                
                # On Windows, try to open maximized
                if sys.platform == 'win32':
                    try:
                        # Use start /max to open maximized on Windows
                        os.system(f'start /max "" "{player_url}"')
                    except Exception as e:
                        print(f"Error opening maximized, using alternative method: {e}")
                        webbrowser.open(player_url)
                else:
                    # On other systems, use standard method
                    webbrowser.open(player_url)

                # Log success without showing message dialog
                if content_type == 'episode':
                    episode_title = content_data.get('title', 'Unknown Episode') if content_data else 'Unknown Episode'
                    episode_number = content_data.get('number', 'N/A') if content_data else 'N/A'
                    print(f"✅ YANGO Player opened in browser for Episode {episode_number}: {episode_title}")
                else:
                    movie_title = content_data.get('title', 'Unknown Movie') if content_data else 'Unknown Movie'
                    movie_year = content_data.get('year', 'Unknown Year') if content_data else 'Unknown Year'
                    print(f"✅ YANGO Player opened in browser for {movie_title} ({movie_year})")

                return True

        except Exception as e:
            print(f"❌ Error opening player: {str(e)}")
            return False

    def stop(self):
        """Stop the player."""
        print("🔴 Stopping YANGO Player...")
        # Stop the HTTP server (this will stop the player)
        self._stop_http_server()
        return True

# Function to play content directly without creating a class instance
def play_yango_content(mpd_url, keys=None, content_data=None, content_type='movie', parent=None):
    """
    Play YANGO content with the custom player (standalone function).

    Args:
        mpd_url (str): The URL of the MPD file to play
        keys (list, optional): List of DRM keys for decryption in the format ["kid:key", ...]
        content_data (dict, optional): Content metadata
        content_type (str): Type of content ('movie' or 'episode')
        parent (QWidget, optional): Parent widget for the player window

    Returns:
        bool: True if player started successfully, False otherwise
    """
    player = YangoPlayer(parent)
    return player.play(mpd_url, keys, content_data, content_type)
