<!DOCTYPE html>
<html lang="en">
<head>
    <title>YANGO PLAY Player</title>
    <link rel="icon" type="image/png" href="play-on.png">
    <link rel="stylesheet" href="tod_skin.css">
    <style>
        html,body{height:100% !important;overflow:hidden !important;margin:0;padding:0}
        body{background-color:#000}
        .player-section{height:100% !important;position:relative}
        #player{height:100% !important;width:100% !important}
        .jw-aspect.jw-reset[style*=padding-top]{padding-top:unset !important}
        
        /* Player loading state */
        .player-section.player-loading .play-button-container::after {
            content: "Loading...";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 18px;
        }
        
        /* Hide play button when player is active */
        .player-section.player-active .play-button-container {
            display: none !important;
        }
        
        /* Play button container */
        .play-button-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
        }
        
        #playStreamButton {
            display: none;
        }
    </style>
</head>
<body>
    <div class="player-section">
        <div id="player"></div>
        <div class="play-button-container">
            <button id="playStreamButton" style="display: none;"></button>
        </div>
    </div>
    <script src="js/jwplayer.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/shaka-player/dist/shaka-player.ui.js"></script>
    <script src="js/provider.shaka.js"></script>
    <script src="player.js"></script>
    <script>
        // Get URL parameters
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                mpd: params.get('mpd') || '',
                keyId: params.get('keyId') || '',
                key: params.get('key') || '',
                keys: params.get('keys') || '' // All keys as JSON string
            };
        }

        // Initialize player when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            const params = getUrlParams();
            const mpdUrl = params.mpd;
            const keyId = params.keyId;
            const key = params.key;
            const allKeys = params.keys;

            console.log('Initializing YANGO player with:', { mpdUrl, keyId, key, allKeys });

            if (mpdUrl) {
                // Set up the button with stream data
                const playButton = document.getElementById('playStreamButton');
                if (playButton) {
                    playButton.dataset.mpdUrl = mpdUrl;
                    
                    // Set key data - prefer all keys if available, fallback to single key
                    if (allKeys) {
                        playButton.dataset.keyData = allKeys;
                        console.log('Using all keys:', allKeys);
                    } else if (keyId && key) {
                        playButton.dataset.keyData = `${keyId}:${key}`;
                        console.log('Using single key:', `${keyId}:${key}`);
                    } else {
                        playButton.dataset.keyData = '';
                        console.log('No keys provided');
                    }

                    // Auto-start playback
                    setTimeout(() => {
                        console.log('Auto-clicking play button');
                        playButton.click();

                        // Unmute audio after a delay
                        setTimeout(() => {
                            try {
                                const player = jwplayer('player');
                                if (player) {
                                    player.setMute(false);
                                    player.setVolume(100);
                                    console.log('Audio unmuted');
                                }

                                const video = document.querySelector('video');
                                if (video) {
                                    video.muted = false;
                                    video.volume = 1.0;
                                }
                            } catch (e) {
                                console.log('Error unmuting:', e);
                            }
                        }, 2000);
                    }, 500);
                }
            } else {
                console.error('No MPD URL provided');
                document.getElementById('player').innerHTML =
                    '<div style="color: white; text-align: center; padding: 20px; font-size: 18px;">No stream URL provided</div>';
            }
        });
    </script>
</body>
</html>
