import os
import subprocess
import requests
import threading
import shutil
import glob
import time
import xml.etree.ElementTree as ET
from PySide6.QtCore import QObject, Signal, QThread, QTimer
from pathlib import Path

class OSNDownloader(QObject):
    # Signals for UI updates
    download_progress = Signal(int, str)  # progress percentage, status message
    download_completed = Signal(str, bool)  # file path, success
    download_error = Signal(str)  # error message
    status_update = Signal(str)  # status message for enhanced solution
    video_progress = Signal(int)  # video progress signal
    audio_progress = Signal(int)  # audio progress signal
    subtitle_progress = Signal(int)  # subtitle progress signal

    def __init__(self):
        super().__init__()
        self.setup_paths()
        self.current_downloads = {}
        self.current_poster_url = None  # Store poster URL for downloads

    def setup_paths(self):
        """Setup download paths and binaries"""
        self.base_dir = Path(__file__).parent.parent
        self.downloads_dir = self.base_dir / "downloads"
        self.cache_dir = self.base_dir / "cache"
        self.keys_dir = self.base_dir / "KEYS"
        self.binaries_dir = self.base_dir / "binaries"

        # Create directories if they don't exist
        self.downloads_dir.mkdir(exist_ok=True)
        self.cache_dir.mkdir(exist_ok=True)
        self.keys_dir.mkdir(exist_ok=True)

        # Binary paths
        self.n_m3u8dl_path = self.binaries_dir / "N_m3u8DL-RE.exe"
        self.yt_dlp_path = self.binaries_dir / "yt-dlp.exe"
        self.aria2c_path = self.binaries_dir / "aria2c.exe"  # Add aria2c path
        self.mp4decrypt_path = self.binaries_dir / "mp4decrypt.exe"
        self.mkvmerge_path = self.binaries_dir / "mkvmerge.exe"  # Add mkvmerge path
        self.ffmpeg_path = self.binaries_dir / "ffmpeg.exe"
        self.keys_file = self.keys_dir / "OSNPLUS_KEYS.txt"

        # Download method preference: yt-dlp (more stable) or N_m3u8DL-RE
        self.use_yt_dlp = False  # Set to True to use yt-dlp, False for N_m3u8DL-RE


    def sanitize_filename(self, filename):
        """Sanitize filename for Windows"""
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '')
        return filename.strip()

    def parse_mpd_file(self, mpd_url):
        """Parse MPD file to extract available qualities and tracks"""
        try:
            response = requests.get(mpd_url)
            response.raise_for_status()

            root = ET.fromstring(response.content)
            namespaces = {'mpd': 'urn:mpeg:dash:schema:mpd:2011'}

            qualities = []
            audio_tracks = set()
            subtitle_tracks = set()

            # Extract video qualities
            for adaptation_set in root.findall('.//mpd:Period/mpd:AdaptationSet', namespaces):
                mime_type = adaptation_set.get('mimeType', '')

                if mime_type == 'video/mp4':
                    for representation in adaptation_set.findall('mpd:Representation', namespaces):
                        height = representation.get('height')
                        width = representation.get('width')
                        bandwidth = representation.get('bandwidth')
                        uuid = representation.get('id')

                        if height and width and bandwidth and uuid:
                            resolution = f"{width}x{height}"
                            qualities.append({
                                'resolution': f"{height}p",
                                'bandwidth': int(bandwidth),
                                'uuid': uuid,
                                'display': f"{height}p ({bandwidth} bps)"
                            })

                elif mime_type == 'audio/mp4':
                    lang = adaptation_set.get('lang', 'unknown')
                    audio_tracks.add(lang)

                elif mime_type == 'text/vtt':
                    lang = adaptation_set.get('lang', 'unknown')
                    subtitle_tracks.add(lang)

            # Sort qualities by resolution
            qualities.sort(key=lambda x: x['bandwidth'], reverse=True)

            return {
                'qualities': qualities,
                'audio_tracks': list(audio_tracks),
                'subtitle_tracks': list(subtitle_tracks)
            }

        except Exception as e:
            self.download_error.emit(f"Error parsing MPD file: {str(e)}")
            return None

    def download_movie(self, mpd_url, movie_data, selected_quality, audio_tracks=None, subtitle_tracks=None, drm_info=None):
        """Download movie with specified parameters - following original OSN.py structure"""
        try:
            # Start download in separate thread to prevent UI freezing
            download_thread = threading.Thread(
                target=self._download_movie_thread,
                args=(mpd_url, movie_data, selected_quality, audio_tracks, subtitle_tracks, drm_info),
                daemon=True
            )
            download_thread.start()

        except Exception as e:
            self.download_error.emit(f"Error starting movie download: {str(e)}")

    def _download_movie_thread(self, mpd_url, movie_data, selected_quality, audio_tracks, subtitle_tracks, drm_info):
        """Download movie in separate thread"""
        try:
            # Save DRM keys to file first
            if drm_info and drm_info.get('keys'):
                self.save_keys_to_file(drm_info, movie_data)

            # Prepare movie info like original code
            title = movie_data.get('title', {}).get('en', 'Unknown Movie')
            year = movie_data.get('year', '')

            # Sanitize title like original code
            sanitized_title = f"{title} - {year}".replace(":", " -").replace("?", "").replace("*", "").replace("<", "").replace(">", "").replace("|", "").replace("\\", "").replace("/", "")

            # Get resolution and UUID like original code
            resolution = selected_quality.get('resolution', '720p')
            uuid = selected_quality.get('uuid', selected_quality.get('id', ''))

            # Map resolution like original code
            resolution_map = {
                "256x144": "144p", "426x240": "240p", "426x252": "252p", "512x288": "288p",
                "640x360": "360p", "832x468": "468p", "854x480": "480p", "1024x576": "576p",
                "1280x720": "720p", "1920x1080": "1080p", "2560x1440": "1440p", "3840x2160": "2160p"
            }
            actual_resolution = resolution_map.get(resolution, resolution)

            # Create download directory like original code
            download_path = self.downloads_dir / sanitized_title
            download_path.mkdir(exist_ok=True)

            # Prepare output file like original code
            output_file = download_path / f"{sanitized_title}.{actual_resolution}.OSN+.WEB-DL.H264.AAC.mkv"

            # Check if file already exists like original code
            if output_file.exists():
                print(f"The movie '{sanitized_title}' already exists at: {output_file}. Skipping download.")
                self.download_completed.emit(str(output_file), True)
                return

            # Download and save poster directly to movie folder (like original OSN.py)
            poster_url = movie_data.get('images', {}).get('longImageWithTitleUrl') or movie_data.get('images', {}).get('wideImageWithTitleUrl')
            if poster_url:
                print(f"📸 Found movie poster URL: {poster_url}")
                self.download_and_save_poster(poster_url, sanitized_title, download_path)
            else:
                print("⚠️ No poster URL found for movie")

            # Start download using original method
            self._download_movie_original(
                mpd_url=mpd_url,
                sanitized_title=sanitized_title,
                actual_resolution=actual_resolution,
                uuid=uuid,
                download_path=download_path,
                output_file=output_file,
                audio_tracks=audio_tracks,
                subtitle_tracks=subtitle_tracks
            )

        except Exception as e:
            self.download_error.emit(f"Error downloading movie: {str(e)}")

    def _build_selection_options(self, audio_tracks, subtitle_tracks):
        """Build audio and subtitle selection options based on user choices"""
        try:
            print(f"🔧 Building selection options...")
            print(f"🎵 Input audio_tracks: {audio_tracks}")
            print(f"📝 Input subtitle_tracks: {subtitle_tracks}")

            # Build audio option based on user selection
            if audio_tracks and len(audio_tracks) > 0:
                # Build more specific audio selection based on exact user choice
                audio_selectors = []
                for track in audio_tracks:
                    if isinstance(track, dict):
                        lang = track.get('language', 'en')
                        codecs = track.get('codecs', '')

                        # Create specific selector based on exact user choice
                        # Use the unique ID for precise track selection

                        # Method 1: Try using track ID if available (most precise)
                        track_id = track.get('id', None)
                        if track_id:
                            selector = f"id={track_id}"
                            print(f"🎯 Using track ID selector: {selector}")
                        else:
                            # Method 2: Try using track index if available
                            track_index = track.get('index', None)
                            if track_index is not None:
                                selector = f"index={track_index}"
                                print(f"🎯 Using track index selector: {selector}")
                            else:
                                # Method 3: Fallback to exact language matching
                                exact_lang = lang  # Keep original: en, en-ddp, en-da, fr, etc.
                                selector = f"lang={exact_lang}"
                                print(f"🎯 Using EXACT language selector: {selector} (original: {lang})")

                        audio_selectors.append(selector)
                        print(f"🎵 Created audio selector: {selector} (from {lang}, {codecs})")
                    else:
                        lang = str(track).replace('-ddp', '')
                        audio_selectors.append(f"lang={lang}")

                if audio_selectors:
                    # Use the first selector (user's primary choice)
                    audio_option = f'--select-audio "{audio_selectors[0]}"'
                    print(f"🎵 User audio selection: {audio_option}")
                else:
                    audio_option = '--select-audio "lang=ar|en:for=best"'
                    print(f"🎵 Fallback audio selection: {audio_option}")
            else:
                # No audio selected - use default
                audio_option = '--select-audio "lang=ar|en:for=best"'
                print(f"🎵 Default audio selection: {audio_option}")

            # Build subtitle option based on user selection
            if subtitle_tracks and len(subtitle_tracks) > 0:
                # Extract language codes from user selections
                subtitle_langs = []
                for track in subtitle_tracks:
                    if isinstance(track, dict):
                        lang = track.get('language', 'ar')
                    else:
                        lang = str(track)
                    # Clean up language code
                    lang = lang.lower().strip()
                    if lang and lang not in subtitle_langs:
                        subtitle_langs.append(lang)

                if subtitle_langs:
                    subtitle_option = f'--select-subtitle "lang={"|".join(subtitle_langs)}:for=best"'
                    print(f"📝 User subtitle selection: {subtitle_option}")
                else:
                    subtitle_option = '--drop-subtitle ".*"'
                    print(f"📝 No valid subtitles selected - dropping all subtitles")
            else:
                # No subtitles selected - drop all subtitles
                subtitle_option = '--drop-subtitle ".*"'
                print(f"📝 No subtitles selected - dropping all subtitles")

            print(f"✅ Final audio option: {audio_option}")
            print(f"✅ Final subtitle option: {subtitle_option}")
            return audio_option, subtitle_option

        except Exception as e:
            print(f"❌ Error building selection options: {str(e)}")
            # Return safe defaults - NO SUBTITLES unless explicitly selected
            return '--select-audio "lang=ar|en:for=best"', '--drop-subtitle ".*"'

    def _download_movie_original(self, mpd_url, sanitized_title, actual_resolution, uuid, download_path, output_file, audio_tracks, subtitle_tracks):
        """Download movie using original OSN.py method with friend's progress monitoring"""
        try:
            print(f"🚀 Starting movie download: {sanitized_title}")
            print(f"🎵 Selected audio tracks: {audio_tracks}")
            print(f"📝 Selected subtitle tracks: {subtitle_tracks}")

            # Create cache directory like original code
            cache_dir = self.base_dir / "cache"
            cache_dir.mkdir(exist_ok=True)

            # Build audio and subtitle options based on user selections
            audio_option, subtitle_option = self._build_selection_options(audio_tracks, subtitle_tracks)

            # Prepare download name like original code
            download_name = f"{sanitized_title}.{actual_resolution}.OSN+.VIP.WEB-DL.H264.AAC"

            # Build download command like original code
            download_command = (
                f'"{self.n_m3u8dl_path}" "{mpd_url}" -mt '
                f'--select-video "id={uuid}" '
                f'{audio_option} '
                f'{subtitle_option} '
                f'--tmp-dir "{cache_dir}" '
                f'--save-dir "{cache_dir}" '
                f'--save-name "{download_name}" '
                f'--decryption-binary-path="{self.mp4decrypt_path}" '
                f'--key-text-file="{self.keys_file}" '
                f'--log-level "OFF"'
            )

            print(f"🔧 Movie download command: {download_command}")

            # Start download progress
            self.download_progress.emit(0, f"Starting movie download: {sanitized_title}")

            # Use friend's solution for progress monitoring
            process = subprocess.Popen(
                download_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Redirect stderr to stdout
                text=True,
                universal_newlines=True,
                bufsize=1  # Line buffered
            )

            # Monitor progress using friend's method for movies
            self._monitor_n_m3u8dl_movie_with_friend_solution(process, download_name, cache_dir, output_file)

        except Exception as e:
            print(f"❌ Error in movie download: {str(e)}")
            self.download_error.emit(f"Movie download error: {str(e)}")

    def _monitor_n_m3u8dl_movie_with_friend_solution(self, process, download_name, cache_dir, output_file):
        """Monitor N_m3u8DL-RE progress for movies using enhanced YANGO solution"""
        try:
            print(f"🔍 Starting enhanced movie progress monitoring for: {download_name}")

            import re
            import time
            import threading

            # Enhanced progress tracking system from YANGO
            current_progress = {
                'video': 0,
                'audio': 0,
                'subtitle': 0,
                'overall': 0
            }

            target_progress = 0
            smooth_progress = 0
            download_phase = "initializing"  # initializing, subtitles, video, audio, merging, completed

            # Send initial progress signal
            self.download_progress.emit(1, f"Initializing movie download: {download_name}")
            print(f"📡 Sent initial movie progress signal: 1%")

            # Enhanced smooth progress animation function for movies
            def smooth_progress_animation():
                nonlocal smooth_progress, target_progress, download_phase
                while smooth_progress < 100:
                    if smooth_progress < target_progress:
                        smooth_progress += 1

                        # Enhanced status messages based on phase
                        if download_phase == "subtitles":
                            status_msg = f"Movie subtitles: {smooth_progress}%"
                        elif download_phase == "video":
                            status_msg = f"Movie video: {smooth_progress}%"
                        elif download_phase == "audio":
                            status_msg = f"Movie audio: {smooth_progress}%"
                        elif download_phase == "merging":
                            status_msg = f"Movie merging: {smooth_progress}%"
                        else:
                            status_msg = f"Movie: {smooth_progress}%"

                        self.download_progress.emit(smooth_progress, status_msg)
                        print(f"🎯 Enhanced Movie Progress: {smooth_progress}% - {status_msg}")
                        time.sleep(0.1)  # Update every 100ms for smooth animation
                    else:
                        time.sleep(0.2)  # Wait for new target

            # Start smooth progress thread
            progress_thread = threading.Thread(target=smooth_progress_animation, daemon=True)
            progress_thread.start()

            while True:
                line = process.stdout.readline()
                if not line:
                    break

                line_content = line.strip()

                # Enhanced subtitle progress monitoring
                if line_content.startswith("Sub ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress['subtitle']:
                            download_phase = "subtitles"
                            # Map subtitle progress to 0-15% range
                            mapped_progress = min(int(prog * 0.15), 15)
                            target_progress = mapped_progress
                            current_progress['subtitle'] = prog
                            print(f"📝 Movie Subtitle Progress: {prog}% -> Mapped: {mapped_progress}%")
                    continue

                # Enhanced video progress monitoring
                if line_content.startswith("Vid ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress['video']:
                            download_phase = "video"
                            # Map video progress to 15-70% range
                            mapped_progress = 15 + int(prog * 0.55)
                            target_progress = mapped_progress
                            current_progress['video'] = prog
                            print(f"📹 Movie Video Progress: {prog}% -> Mapped: {mapped_progress}%")
                    continue

                # Enhanced audio progress monitoring
                if line_content.startswith("Aud ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress['audio']:
                            download_phase = "audio"
                            # Map audio progress to 70-85% range
                            mapped_progress = 70 + int(prog * 0.15)
                            target_progress = mapped_progress
                            current_progress['audio'] = prog
                            print(f"🔊 Movie Audio Progress: {prog}% -> Mapped: {mapped_progress}%")
                    continue

                # Enhanced status update for other lines
                if line_content and not any(line_content.startswith(prefix) for prefix in ["Vid ", "Aud ", "Sub "]):
                    self.status_update.emit(f"OSN Movie: {line_content}")

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Movie download completed successfully")
                download_phase = "merging"
                target_progress = 85
                time.sleep(0.3)  # Allow progress to reach 85%

                self.download_progress.emit(85, "Movie download completed, starting merge...")

                # Find downloaded files like original code
                video_file = cache_dir / f"{download_name}.mp4"
                audio_files = [f for f in cache_dir.glob("*.m4a")]

                if video_file.exists() and audio_files:
                    print("Downloaded movie files found, proceeding to merge...")

                    # Update progress for merge phase
                    target_progress = 90
                    time.sleep(0.3)

                    # Convert to string paths for merge function
                    audio_paths = [str(f) for f in audio_files]

                    # Merge using movie-specific merge function
                    self._merge_movie_with_mkvmerge(str(video_file), audio_paths, str(output_file), str(cache_dir))

                    # Clean up cache directory like original code
                    try:
                        shutil.rmtree(cache_dir)
                        print(f"Cache directory {cache_dir} has been deleted.")
                    except Exception as e:
                        print(f"An error occurred while deleting the cache directory: {e}")

                    # Set final target to 100% for smooth completion
                    download_phase = "completed"
                    target_progress = 100
                    time.sleep(0.5)  # Give time for smooth animation to reach 100%
                    self.download_completed.emit(str(output_file), True)
                    self.download_progress.emit(100, "✅ Movie download completed successfully!")
                else:
                    print("Movie download failed or required files not found. Nothing to merge.")
                    self.download_error.emit("Movie download failed or required files not found")
            else:
                print(f"❌ Movie download failed with code: {process.returncode}")
                self.download_error.emit(f"Movie download failed with code: {process.returncode}")

        except Exception as e:
            print(f"❌ Error in enhanced movie progress monitoring: {str(e)}")
            self.download_error.emit(f"Movie progress monitoring error: {str(e)}")

    def _merge_movie_with_mkvmerge(self, video_file, audio_files, output_file, cache_dir):
        """Merge video and audio files into MKV format using mkvmerge for movies - following original OSN.py"""
        try:
            print(f"🔧 Starting movie merge with mkvmerge...")
            self.download_progress.emit(90, "Merging movie files...")

            mkvmerge_command = [str(self.mkvmerge_path), "-o", output_file]

            # Add video file
            mkvmerge_command.append(video_file)

            # Add audio files with labels like original code
            audio_map = {
                ".ar.m4a": ("ara", "Arabic"),
                ".tr.m4a": ("tur", "Turkish"),
                ".en-atmos.m4a": ("eng", "English Atmos"),
                ".en-ddp.m4a": ("eng", "English Dolby 5.1"),
                ".en-2CH.m4a": ("eng", "English Stereo"),
                ".en.m4a": ("eng", "English")  # Default if no specific label is found
            }

            for audio_file in audio_files:
                track_language = "und"
                track_label = "Unknown"
                for key, (language_code, label) in audio_map.items():
                    if key in audio_file:
                        track_language = language_code
                        track_label = label
                        break

                # Add the audio file to the mkvmerge command
                mkvmerge_command.extend([
                    "--language", f"0:{track_language}",
                    "--track-name", f"0:{track_label}",
                    audio_file
                ])
                print(f"🔊 Adding {track_label} audio to the movie MKV file")

            # Add subtitle files if they exist like original code
            subtitle_files = {
                "ar": next((f for f in Path(cache_dir).glob("*ar*.srt")), None),
                "en": next((f for f in Path(cache_dir).glob("*en*.srt")), None),
            }

            subtitle_map = {
                "ar": ("ara", "Arabic"),
                "en": ("eng", "English")
            }

            for lang, subtitle_file in subtitle_files.items():
                if subtitle_file:
                    language_code, label = subtitle_map.get(lang, ("und", "Unknown"))
                    subtitle_path = str(subtitle_file)

                    # Add the subtitle file to the mkvmerge command
                    mkvmerge_command.extend([
                        "--language", f"0:{language_code}",
                        "--track-name", f"0:{label}",
                        subtitle_path
                    ])
                    print(f"📝 Adding {label} subtitle to the movie MKV file")

            # Print and run the command like original code
            print("Running mkvmerge command:", " ".join(mkvmerge_command))
            result = subprocess.run(mkvmerge_command, check=True)

            if result.returncode == 0:
                print(f"✅ Movie merge completed successfully: {output_file}")
                self.download_progress.emit(95, "Movie merge completed!")
            else:
                print(f"❌ Movie merge failed")
                self.download_error.emit("Movie merge failed")

        except Exception as e:
            print(f"❌ Error in movie merge process: {str(e)}")
            self.download_error.emit(f"Movie merge error: {str(e)}")

    def download_episode(self, mpd_url, series_data, episode_data, selected_quality, audio_tracks=None, subtitle_tracks=None, drm_info=None):
        """Download series episode with specified parameters using threading"""
        try:
            # Save DRM keys to file first
            if drm_info and drm_info.get('keys'):
                self.save_keys_to_file(drm_info, episode_data)

            # Start download in separate thread to prevent UI freezing
            download_thread = threading.Thread(
                target=self._download_episode_thread,
                args=(mpd_url, series_data, episode_data, selected_quality, audio_tracks, subtitle_tracks, drm_info),
                daemon=True
            )
            download_thread.start()

        except Exception as e:
            self.download_error.emit(f"Error starting episode download: {str(e)}")

    def _download_episode_thread(self, mpd_url, series_data, episode_data, selected_quality, audio_tracks, subtitle_tracks, drm_info):
        """Download episode in separate thread using original OSN.py method"""
        try:
            # Prepare series info like original code
            series_title = series_data.get('title', {}).get('en', 'Unknown Series')
            season_number = episode_data.get('seasonNumber', 1)
            episode_number = episode_data.get('episodeNumber', 1)
            episode_title = episode_data.get('title', {}).get('en', 'Unknown Episode')

            # DEBUG: Print all episode data to see what we're getting
            print(f"🔍 DEBUG Episode Data:")
            print(f"   Series Title: {series_title}")
            print(f"   Season Number: {season_number} (type: {type(season_number)})")
            print(f"   Episode Number: {episode_number} (type: {type(episode_number)})")
            print(f"   Episode Title: {episode_title}")
            print(f"   Full episode_data keys: {list(episode_data.keys())}")
            print(f"   Full episode_data: {episode_data}")

            # Sanitize title like original code - use proper series title
            clean_series_title = series_title.replace('.', ' ').replace(':', ' -').replace('?', '').replace('*', '').replace('<', '').replace('>', '').replace('|', '').replace('\\', '').replace('/', '')
            sanitized_series_title = f"{clean_series_title}"
            sanitized_title = f"{clean_series_title} S{str(season_number).zfill(2)}E{str(episode_number).zfill(2)}"

            # Get resolution and UUID like original code
            resolution = selected_quality.get('resolution', '720p')
            uuid = selected_quality.get('uuid', selected_quality.get('id', ''))

            # Map resolution like original code
            resolution_map = {
                "256x144": "144p", "426x240": "240p", "426x252": "252p", "512x288": "288p",
                "640x360": "360p", "832x468": "468p", "854x480": "480p", "1024x576": "576p",
                "1280x720": "720p", "1920x1080": "1080p", "2560x1440": "1440p", "3840x2160": "2160p"
            }
            actual_resolution = resolution_map.get(resolution, resolution)

            # Create season folder path like original code
            season_folder_path = self.downloads_dir / sanitized_series_title
            season_folder_path.mkdir(parents=True, exist_ok=True)

            # Prepare output file like original code
            download_name = f"{sanitized_title}.{actual_resolution}.OSN+.WEB-DL.H264.AAC"
            output_file = season_folder_path / f"{download_name}.mkv"

            # Check if file already exists like original code
            if output_file.exists():
                print(f"Episode '{episode_title}' already exists at: {output_file}. Skipping download.")
                self.download_completed.emit(str(output_file), True)
                return

            # Download poster to cache before starting episode download (like original OSN.py)
            # The poster URL should be passed from the UI when starting download
            poster_url = getattr(self, 'current_poster_url', None)
            if not poster_url and series_data:
                poster_url = series_data.get('images', {}).get('longImageWithTitleUrl') or series_data.get('images', {}).get('wideImageWithTitleUrl')

            if poster_url:
                print(f"📸 Found poster URL: {poster_url}")
                self.download_poster_to_cache(poster_url)
            else:
                print("⚠️ No poster URL found for series")

            # Start download using original method
            self._download_series_episode_original(
                mpd_url=mpd_url,
                series_title=series_title,
                season_number=season_number,
                episode_number=episode_number,
                uuid=uuid,
                actual_resolution=actual_resolution,
                download_name=download_name,
                season_folder_path=season_folder_path,
                output_file=output_file,
                audio_tracks=audio_tracks,
                subtitle_tracks=subtitle_tracks
            )

        except Exception as e:
            self.download_error.emit(f"Error in download thread: {str(e)}")

    def save_keys_to_file(self, drm_info, episode_data):
        """Save DRM keys to OSNPLUS_KEYS.txt file like original code"""
        try:
            keys = drm_info.get('keys', [])
            if not keys:
                return

            # Create KEYS directory
            keys_dir = self.base_dir / "KEYS"
            keys_dir.mkdir(exist_ok=True)

            keys_file = keys_dir / "OSNPLUS_KEYS.txt"

            # Read existing keys
            existing_keys = set()
            if keys_file.exists():
                with open(keys_file, 'r') as f:
                    existing_keys = set(line.strip() for line in f if line.strip())

            # Filter new keys
            new_keys = [key for key in keys if key not in existing_keys]

            if new_keys:
                episode_title = episode_data.get('title', {}).get('en', 'Unknown Episode')
                print(f"🔑 OBTAINED KEYS for {episode_title}:")
                for key in keys:
                    print(f"🔑 --key {key}")

                # Append new keys to file
                with open(keys_file, 'a', encoding='utf-8') as f:
                    f.write(f'\n\n{episode_title}\n')
                    for key in new_keys:
                        f.write(f'{key}\n')

                print(f"✅ Success Saving KEYS to {keys_file}")
            else:
                print("🔑 KEYS Already Saved!")

        except Exception as e:
            print(f"❌ Error saving keys: {str(e)}")

    def _download_series_episode_enhanced(self, mpd_url, series_title, season_number, episode_number, selected_quality, audio_tracks, subtitle_tracks, cache_dir, output_file, download_name):
        """Download episode using N_m3u8DL-RE with enhanced unified progress monitoring"""
        try:
            print(f"🚀 Starting enhanced download: {download_name}")

            # Get UUID from selected quality
            uuid = selected_quality.get('uuid', selected_quality.get('id', ''))
            resolution = selected_quality.get('resolution', '720p')

            if uuid:
                print(f"✅ Found UUID: {uuid} for resolution: {resolution}")
                print(f"🎯 Using video selection: id={uuid}")
            else:
                print(f"⚠️ No UUID found for resolution: {resolution}")
                print(f"🎯 Auto-selecting best quality available")
                uuid = "best"

            print(f"🎵 Selected audio tracks: {audio_tracks}")
            print(f"📝 Selected subtitle tracks: {subtitle_tracks}")

            # Build audio and subtitle options based on user selections
            audio_option, subtitle_option = self._build_selection_options(audio_tracks, subtitle_tracks)

            # Build final command with user selections
            download_command = (
                f'"{self.n_m3u8dl_path}" "{mpd_url}" -mt '
                f'--select-video "id={uuid}" '
                f'{audio_option} '
                f'{subtitle_option} '
                f'--tmp-dir "{cache_dir}" '
                f'--save-dir "{cache_dir}" '
                f'--save-name "{download_name}" '
                f'--decryption-binary-path="{self.mp4decrypt_path}" '
                f'--key-text-file="{self.keys_file}" '
                f'--log-level "OFF"'
            )

            # Start download with immediate progress update
            self.download_progress.emit(0, f"Starting download: {download_name}")
            print(f"🎯 UI: Starting download for: {download_name}")

            print(f"🔧 OSN.py format command: {download_command}")

            # Immediate progress update to show download started
            self.download_progress.emit(2, f"Initializing: {download_name}")
            print(f"🎯 UI: Download initialized")

            # Use subprocess.Popen to capture output for friend's solution
            import subprocess

            # CRITICAL: Use the EXACT same method as friend's solution
            process = subprocess.Popen(
                download_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Redirect stderr to stdout for complete output
                text=True,
                universal_newlines=True,
                bufsize=1  # Line buffered for real-time output
            )

            # Simple download without complex progress monitoring
            print(f"🚀 Starting download process...")

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Download completed successfully")
                self.download_progress.emit(95, "Merging files...")

                # Find downloaded files
                video_files = list(cache_dir.glob(f"{download_name}*.mp4"))
                audio_files = list(cache_dir.glob(f"{download_name}*.m4a"))

                if video_files:
                    video_file = video_files[0]
                    print(f"📹 Found video file: {video_file}")

                    # Merge files
                    self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

                    # Clean up
                    try:
                        shutil.rmtree(cache_dir)
                        print(f"🧹 Cache cleaned")
                    except Exception as e:
                        print(f"⚠️ Error cleaning cache: {e}")

                    self.download_completed.emit(str(output_file), True)
                    self.download_progress.emit(100, "✅ Completed!")
                else:
                    print("❌ No video files found")
                    self.download_error.emit("No video files found")
            else:
                print(f"❌ Download failed with code: {process.returncode}")
                self.download_error.emit(f"Download failed: {process.returncode}")

        except Exception as e:
            print(f"❌ Error in enhanced download: {str(e)}")
            self.download_error.emit(f"Enhanced download error: {str(e)}")


    def _parse_n_m3u8dl_progress(self, line):
        """Parse progress percentage from N_m3u8DL-RE output"""
        try:
            import re

            # Convert line to lowercase for easier matching
            line_lower = line.lower()

            # Look for specific N_m3u8DL-RE progress patterns
            patterns = [
                # Common N_m3u8DL-RE patterns
                r'(\d+\.?\d*)%',  # Basic percentage
                r'progress[:\s]*(\d+\.?\d*)%',  # Progress: XX%
                r'\[(\d+\.?\d*)%\]',  # [XX%]
                r'(\d+\.?\d*)\s*%\s*complete',  # XX% complete
                r'downloaded[:\s]*(\d+\.?\d*)%',  # Downloaded: XX%
                r'(\d+)/(\d+)\s*segments',  # X/Y segments
                r'segment\s*(\d+)\s*of\s*(\d+)',  # segment X of Y
            ]

            for pattern in patterns:
                match = re.search(pattern, line_lower)
                if match:
                    if len(match.groups()) == 2:  # X/Y format
                        current = float(match.group(1))
                        total = float(match.group(2))
                        if total > 0:
                            percentage = (current / total) * 100
                        else:
                            percentage = 0
                    else:
                        percentage = float(match.group(1))

                    # Map to 0-85% range (leave room for merge)
                    mapped_percentage = min(int(percentage * 0.85), 85)
                    if mapped_percentage > 0:
                        return mapped_percentage

            # Look for specific download phase indicators
            if any(keyword in line_lower for keyword in [
                'downloading', 'download started', 'fetching', 'getting',
                'processing', 'decrypting', 'segment'
            ]):
                return 10  # Show initial progress

            # Look for completion indicators
            if any(keyword in line_lower for keyword in [
                'completed', 'finished', 'done', 'success'
            ]):
                return 80  # Near completion

            return 0

        except Exception as e:
            print(f"⚠️ Error parsing progress from line '{line}': {e}")
            return 0

    def _simple_download_monitor(self, process, download_name, cache_dir, output_file):
        """Simple download monitor without complex progress tracking"""
        try:
            print(f"� Starting simple download monitor for: {download_name}")

            # Just wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Download completed successfully")
                self.download_progress.emit(95, "Merging files...")

                # Find downloaded files
                video_files = list(cache_dir.glob(f"{download_name}*.mp4"))
                audio_files = list(cache_dir.glob(f"{download_name}*.m4a"))

                if video_files:
                    video_file = video_files[0]
                    print(f"📹 Found video file: {video_file}")

                    # Merge files
                    self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

                    # Clean up
                    try:
                        shutil.rmtree(cache_dir)
                        print(f"🧹 Cache cleaned")
                    except Exception as e:
                        print(f"⚠️ Error cleaning cache: {e}")

                    self.download_completed.emit(str(output_file), True)
                    self.download_progress.emit(100, "✅ Completed!")
                else:
                    print("❌ No video files found")
                    self.download_error.emit("No video files found")
            else:
                print(f"❌ Download failed with code: {process.returncode}")
                self.download_error.emit(f"Download failed: {process.returncode}")

        except Exception as e:
            print(f"❌ Error in download monitor: {str(e)}")
            self.download_error.emit(f"Monitor error: {str(e)}")





    def _monitor_real_progress(self, process, download_name, cache_dir, output_file):
        """Monitor download progress using real file size tracking"""
        try:
            print(f"🔍 Starting real progress monitoring for: {download_name}")

            # Start file monitoring in separate thread
            monitor_thread = threading.Thread(
                target=self._file_size_monitor,
                args=(cache_dir, download_name, process),
                daemon=True
            )
            monitor_thread.start()

            # Also monitor N_m3u8DL-RE output for completion
            last_percentage = 0

            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    line = output.strip()
                    print(f"📥 N_m3u8DL-RE: {line}")

                    # Detect completion
                    if "任务结束" in line or "Task End" in line or "Done" in line or "All tasks completed" in line:
                        print(f"🎯 Download phase completed")
                        self.download_progress.emit(85, "Download completed, finalizing...")
                        last_percentage = 85

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Real progress download completed successfully")
                self.download_progress.emit(95, "Download completed, starting final merge...")

                # Find downloaded files in cache
                video_files = list(cache_dir.glob(f"{download_name}*.mp4"))
                audio_files = list(cache_dir.glob(f"{download_name}*.m4a"))

                if video_files:
                    video_file = video_files[0]
                    print(f"📹 Found video file: {video_file}")

                    # Merge files using mkvmerge
                    self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

                    # Clean up cache directory
                    try:
                        shutil.rmtree(cache_dir)
                        print(f"🧹 Cache directory {cache_dir} has been deleted.")
                    except Exception as e:
                        print(f"⚠️ Error deleting cache directory: {e}")

                    self.download_completed.emit(str(output_file), True)
                else:
                    print("❌ No video files found after download")
                    self.download_error.emit("No video files found after download")
            else:
                error_msg = process.stderr.read() if process.stderr else "Download failed"
                print(f"❌ Real progress download failed: {error_msg}")
                self.download_error.emit(f"Download failed: {error_msg}")

        except Exception as e:
            print(f"❌ Error monitoring real progress: {str(e)}")
            self.download_error.emit(f"Monitor error: {str(e)}")

    def _file_size_monitor(self, cache_dir, download_name, process):
        """Monitor file sizes in real-time to calculate actual download progress"""
        try:
            print(f"📊 Starting file size monitoring in: {cache_dir}")

            expected_files = []
            total_expected_size = 0
            last_progress = 0

            # Wait a bit for files to start appearing
            time.sleep(2)

            while process.poll() is None:
                try:
                    # Find all files being downloaded
                    current_files = list(cache_dir.glob(f"{download_name}*"))
                    temp_files = list(cache_dir.glob("*.tmp")) + list(cache_dir.glob("*.part"))
                    all_files = current_files + temp_files

                    if all_files:
                        total_current_size = 0

                        for file_path in all_files:
                            if file_path.exists():
                                try:
                                    file_size = file_path.stat().st_size
                                    total_current_size += file_size

                                    # Estimate total expected size based on current files
                                    if file_path not in expected_files:
                                        expected_files.append(file_path)
                                        # Rough estimation: assume each file will be at least 10MB
                                        total_expected_size += max(file_size * 2, 10 * 1024 * 1024)

                                except (OSError, PermissionError):
                                    continue

                        # Calculate progress based on file sizes
                        if total_expected_size > 0:
                            progress = min(int((total_current_size / total_expected_size) * 70), 70)
                        else:
                            # Fallback: use number of files as progress indicator
                            progress = min(len(all_files) * 10, 50)

                        # Only update if progress increased significantly
                        if progress > last_progress and progress - last_progress >= 2:
                            self.download_progress.emit(progress, f"Downloading: {download_name}")
                            print(f"📊 Real Progress: {progress}% (Size: {total_current_size/1024/1024:.1f}MB/{total_expected_size/1024/1024:.1f}MB, Files: {len(all_files)})")
                            last_progress = progress

                    time.sleep(1)  # Check every second

                except Exception as e:
                    print(f"⚠️ Error in file monitoring loop: {e}")
                    time.sleep(2)

            print(f"📊 File size monitoring completed")

        except Exception as e:
            print(f"❌ Error in file size monitor: {str(e)}")

    def _monitor_simple_progress(self, process, download_name, cache_dir, output_file):
        """Simple but effective progress monitoring with file size tracking"""
        try:
            print(f"🔍 Starting simple progress monitoring for: {download_name}")

            last_percentage = 0
            download_started = False

            # Start file size monitoring in parallel
            file_monitor_thread = threading.Thread(
                target=self._smart_file_monitor,
                args=(cache_dir, download_name, process),
                daemon=True
            )
            file_monitor_thread.start()

            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    line = output.strip()
                    print(f"📥 N_m3u8DL-RE: {line}")

                    # Detect when download actually starts
                    if not download_started and ("Selected" in line or "Vid " in line or "Aud " in line):
                        download_started = True
                        self.download_progress.emit(5, f"Starting download: {download_name}")
                        print(f"🚀 Download started!")
                        last_percentage = 5

                    # Look for percentage in the output - but be more conservative
                    if "%" in line and download_started:
                        try:
                            import re
                            # Find all percentages in the line
                            percentages = re.findall(r'(\d+)%', line)
                            if percentages:
                                # Get the highest percentage from the line
                                current_progress = max(int(p) for p in percentages)

                                # Be more conservative - don't let it jump too fast
                                # Only allow gradual increases
                                max_allowed_progress = last_percentage + 5  # Max 5% jump at once
                                display_progress = min(current_progress, max_allowed_progress, 80)

                                # Update only if it's a meaningful increase
                                if display_progress > last_percentage and display_progress - last_percentage >= 1:
                                    self.download_progress.emit(display_progress, f"Downloading: {download_name}")
                                    print(f"📊 N_m3u8DL Progress: {display_progress}% (Raw: {current_progress}%, Limited from {last_percentage}%)")
                                    last_percentage = display_progress

                        except Exception as e:
                            print(f"⚠️ Error parsing progress: {e}")

                    # Detect completion
                    if "任务结束" in line or "Task End" in line or "Done" in line or "All tasks completed" in line:
                        print(f"🎯 Download phase completed")
                        # Ensure we reach at least 75% before completion
                        if last_percentage < 75:
                            self.download_progress.emit(75, "Finalizing download...")
                            time.sleep(0.5)
                        self.download_progress.emit(85, "Download completed, finalizing...")
                        last_percentage = 85

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Simple progress download completed successfully")
                self.download_progress.emit(95, "Download completed, starting final merge...")

                # Find downloaded files in cache
                video_files = list(cache_dir.glob(f"{download_name}*.mp4"))
                audio_files = list(cache_dir.glob(f"{download_name}*.m4a"))

                if video_files:
                    video_file = video_files[0]
                    print(f"📹 Found video file: {video_file}")

                    # Merge files using mkvmerge
                    self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

                    # Clean up cache directory
                    try:
                        shutil.rmtree(cache_dir)
                        print(f"🧹 Cache directory {cache_dir} has been deleted.")
                    except Exception as e:
                        print(f"⚠️ Error deleting cache directory: {e}")

                    self.download_completed.emit(str(output_file), True)
                else:
                    print("❌ No video files found after download")
                    self.download_error.emit("No video files found after download")
            else:
                error_msg = process.stderr.read() if process.stderr else "Download failed"
                print(f"❌ Simple progress download failed: {error_msg}")
                self.download_error.emit(f"Download failed: {error_msg}")

        except Exception as e:
            print(f"❌ Error monitoring simple progress: {str(e)}")
            self.download_error.emit(f"Monitor error: {str(e)}")

    def _smart_file_monitor(self, cache_dir, download_name, process):
        """Smart file monitoring with realistic progress calculation"""
        try:
            print(f"📊 Starting smart file monitoring in: {cache_dir}")

            last_progress = 0
            last_total_size = 0
            estimated_total_size = 0
            stable_size_count = 0

            # Wait for download to start
            time.sleep(3)

            while process.poll() is None:
                try:
                    # Find all files being downloaded
                    all_files = []

                    # Look for various file patterns
                    patterns = [
                        f"{download_name}*",
                        "*.mp4", "*.m4a", "*.vtt", "*.srt",
                        "*.tmp", "*.part", "*.downloading"
                    ]

                    for pattern in patterns:
                        all_files.extend(cache_dir.glob(pattern))

                    if all_files:
                        current_total_size = 0
                        file_count = len(all_files)

                        for file_path in all_files:
                            if file_path.exists():
                                try:
                                    file_size = file_path.stat().st_size
                                    current_total_size += file_size
                                except (OSError, PermissionError):
                                    continue

                        # Convert size to MB
                        size_mb = current_total_size / (1024 * 1024)

                        # Estimate total expected size based on growth pattern
                        if current_total_size > last_total_size:
                            # Files are growing - download is active
                            growth_rate = current_total_size - last_total_size

                            # Estimate total size based on current growth
                            if estimated_total_size == 0 and size_mb > 5:  # Wait for some data
                                # Rough estimate: assume current size is about 30-50% of total
                                estimated_total_size = current_total_size * 2.5
                                print(f"📊 Estimated total size: {estimated_total_size/1024/1024:.1f}MB")

                            stable_size_count = 0
                        else:
                            # Size is stable - might be near completion
                            stable_size_count += 1

                        # Calculate realistic progress
                        if estimated_total_size > 0:
                            # Use realistic calculation based on estimated total
                            real_progress = min(int((current_total_size / estimated_total_size) * 80), 80)
                        else:
                            # Fallback: gradual increment
                            real_progress = min(int(size_mb * 2), 60)  # 2% per MB, max 60%

                        # If size is stable for too long, gradually increase to completion
                        if stable_size_count > 5:  # 10+ seconds of stable size
                            real_progress = min(real_progress + stable_size_count, 80)

                        # Only update if progress increased meaningfully
                        if real_progress > last_progress and real_progress - last_progress >= 1:
                            self.download_progress.emit(real_progress, f"Downloading: {download_name} ({size_mb:.1f}MB, {file_count} files)")
                            print(f"📊 Real Progress: {real_progress}% (Size: {size_mb:.1f}MB, Growth: {(current_total_size-last_total_size)/1024:.1f}KB)")
                            last_progress = real_progress

                        last_total_size = current_total_size

                    time.sleep(1)  # Check every second for more responsiveness

                except Exception as e:
                    print(f"⚠️ Error in smart file monitoring: {e}")
                    time.sleep(2)

            print(f"📊 Smart file monitoring completed")

        except Exception as e:
            print(f"❌ Error in smart file monitor: {str(e)}")

    def _monitor_stderr_progress(self, process, download_name, cache_dir, output_file):
        """Simple and effective progress monitoring"""
        try:
            print(f"🔍 Starting simple progress monitoring for: {download_name}")

            # Start with initial progress
            self.download_progress.emit(5, "🚀 Starting download...")

            # Start file size monitoring in background
            file_monitor_thread = threading.Thread(
                target=self._simple_file_size_monitor,
                args=(cache_dir, download_name, process),
                daemon=True
            )
            file_monitor_thread.start()

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Download completed successfully")
                self.download_progress.emit(90, "Download completed, starting final merge...")

                # Find downloaded files in cache
                video_files = list(cache_dir.glob(f"{download_name}*.mp4"))
                audio_files = list(cache_dir.glob(f"{download_name}*.m4a"))

                if video_files:
                    video_file = video_files[0]
                    print(f"📹 Found video file: {video_file}")

                    # Merge files using mkvmerge
                    self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

                    # Clean up cache directory
                    try:
                        shutil.rmtree(cache_dir)
                        print(f"🧹 Cache directory {cache_dir} has been deleted.")
                    except Exception as e:
                        print(f"⚠️ Error deleting cache directory: {e}")

                    self.download_completed.emit(str(output_file), True)
                else:
                    print("❌ No video files found after download")
                    self.download_error.emit("No video files found after download")
            else:
                print(f"❌ Download failed with return code: {process.returncode}")
                self.download_error.emit(f"Download failed with return code: {process.returncode}")

        except Exception as e:
            print(f"❌ Error monitoring progress: {str(e)}")
            self.download_error.emit(f"Monitor error: {str(e)}")

    def _simple_file_size_monitor(self, cache_dir, download_name, process):
        """Simple and effective file size monitoring with automatic progress"""
        try:
            print(f"🔄 Starting automatic progress monitor for: {download_name}")
            progress_counter = 10
            time_elapsed = 0

            import time
            time.sleep(3)  # Give download time to start

            while process.poll() is None:
                try:
                    # Get total size of all files in cache
                    total_size = 0
                    file_count = 0

                    for file_path in cache_dir.glob("*"):
                        if file_path.is_file():
                            try:
                                total_size += file_path.stat().st_size
                                file_count += 1
                            except (OSError, PermissionError):
                                continue

                    # Convert to MB
                    size_mb = total_size / (1024 * 1024)
                    time_elapsed += 3

                    # Automatic progress based on time and file presence
                    if file_count > 0:  # Files exist - download is working
                        # Gradual automatic progress
                        if time_elapsed < 30:  # First 30 seconds
                            progress_counter = min(10 + (time_elapsed * 1), 40)
                        elif time_elapsed < 60:  # Next 30 seconds
                            progress_counter = min(40 + ((time_elapsed - 30) * 0.8), 65)
                        elif time_elapsed < 120:  # Next minute
                            progress_counter = min(65 + ((time_elapsed - 60) * 0.3), 80)
                        else:  # After 2 minutes
                            progress_counter = min(80 + ((time_elapsed - 120) * 0.1), 85)

                        self.download_progress.emit(int(progress_counter), f"📁 Downloading... {size_mb:.1f}MB ({file_count} files)")
                        print(f"📊 Auto Progress: {int(progress_counter)}% ({size_mb:.1f}MB, {file_count} files, {time_elapsed}s)")
                    else:
                        # No files yet - still initializing
                        if time_elapsed < 15:
                            progress_counter = min(5 + (time_elapsed * 0.3), 10)
                            self.download_progress.emit(int(progress_counter), "🚀 Initializing download...")
                            print(f"📊 Init Progress: {int(progress_counter)}% (Initializing, {time_elapsed}s)")

                    time.sleep(3)  # Check every 3 seconds

                except Exception as e:
                    print(f"⚠️ Error in auto monitor: {e}")
                    time.sleep(3)

            print(f"🔄 Automatic progress monitor completed")

        except Exception as e:
            print(f"❌ Error in auto progress monitor: {str(e)}")

    def _process_output_line(self, line, source, current_progress):
        """Process a single line of output from N_m3u8DL-RE"""
        try:
            # Look for ANY percentage in the line
            if "%" in line:
                import re
                percentages = re.findall(r'(\d+)%', line)
                if percentages:
                    raw_progress = max(int(p) for p in percentages)

                    # Map to our progress range (5-80%)
                    mapped_progress = min(5 + int((raw_progress / 100) * 75), 80)

                    if mapped_progress > current_progress:
                        # Determine phase based on content
                        if "Vid " in line or "Video" in line or "video" in line.lower():
                            phase_msg = f"🎬 Video: {raw_progress}%"
                        elif "Aud " in line or "Audio" in line or "audio" in line.lower():
                            phase_msg = f"🔊 Audio: {raw_progress}%"
                        elif "Sub " in line or "Subtitle" in line or "subtitle" in line.lower():
                            phase_msg = f"📝 Subtitles: {raw_progress}%"
                        else:
                            phase_msg = f"📥 Downloading: {raw_progress}%"

                        self.download_progress.emit(mapped_progress, phase_msg)
                        print(f"📊 Progress Update: {mapped_progress}% (Raw: {raw_progress}%)")
                        return mapped_progress

            # Detect download start
            if any(keyword in line.lower() for keyword in ["starting", "begin", "download"]):
                if current_progress < 10:
                    self.download_progress.emit(10, "🚀 Download initialized...")
                    print(f"🚀 Download started")
                    return 10

            # Detect completion
            if any(keyword in line for keyword in ["任务结束", "Task End", "Done", "All tasks completed", "Success", "Finished"]):
                print(f"🎯 Download phase completed")
                self.download_progress.emit(85, "Download completed, finalizing...")
                return 85

            return current_progress

        except Exception as e:
            print(f"⚠️ Error processing output line: {e}")
            return current_progress

    def _download_components_separately(self, mpd_url, uuid, download_name, cache_dir, output_file, audio_tracks, subtitle_tracks):
        """Download audio, subtitles, and video separately for realistic progress"""
        try:
            print(f"🎯 Starting separate component downloads for: {download_name}")

            # Phase 1: Download Audio (0-25%)
            self.download_progress.emit(5, "🔊 Downloading audio tracks...")
            audio_files = self._download_audio_only(mpd_url, download_name, cache_dir, audio_tracks)
            self.download_progress.emit(25, "✅ Audio download completed")

            # Phase 2: Download Subtitles (25-35%)
            self.download_progress.emit(30, "📝 Downloading subtitles...")
            subtitle_files = self._download_subtitles_only(mpd_url, download_name, cache_dir, subtitle_tracks)
            self.download_progress.emit(35, "✅ Subtitles download completed")

            # Phase 3: Download Video (35-80%)
            self.download_progress.emit(40, "🎬 Downloading video...")
            video_files = self._download_video_only(mpd_url, uuid, download_name, cache_dir)
            self.download_progress.emit(80, "✅ Video download completed")

            # Phase 4: Merge Everything (80-100%)
            self.download_progress.emit(85, "🔧 Merging all components...")
            self._merge_all_components(video_files, audio_files, subtitle_files, output_file, cache_dir)
            self.download_progress.emit(95, "✅ Merge completed")

            # Cleanup
            try:
                shutil.rmtree(cache_dir)
                print(f"🧹 Cache directory {cache_dir} has been deleted.")
            except Exception as e:
                print(f"⚠️ Error deleting cache directory: {e}")

            self.download_completed.emit(str(output_file), True)
            self.download_progress.emit(100, "✅ Download completed successfully!")

        except Exception as e:
            print(f"❌ Error in separate downloads: {str(e)}")
            self.download_error.emit(f"Separate download error: {str(e)}")

    def _download_audio_only(self, mpd_url, download_name, cache_dir, selected_audio_tracks):
        """Download only selected audio tracks"""
        try:
            print(f"🔊 Starting audio-only download")
            print(f"🎵 Selected audio tracks: {selected_audio_tracks}")

            audio_command = [
                str(self.n_m3u8dl_path),
                mpd_url,
                "-mt",
                "--tmp-dir", str(cache_dir),
                "--save-dir", str(cache_dir),
                "--save-name", f"{download_name}_audio",
                "--decryption-binary-path", str(self.mp4decrypt_path),
                "--key-text-file", str(self.keys_file),
                "--binary-merge",
                "--del-after-done",
                "--skip-download"  # Skip actual download, just get info first
            ]

            # Add specific audio selection based on user choice - ONLY ONE TRACK
            if selected_audio_tracks and len(selected_audio_tracks) > 0:
                # Use the first selected audio track
                selected_audio = selected_audio_tracks[0]
                if 'language' in selected_audio:
                    lang = selected_audio['language']
                    # Use for=1 to get exactly ONE track
                    audio_command.extend(["--select-audio", f"lang={lang}:for=1"])
                    print(f"🎵 Using specific audio language: {lang} (SINGLE TRACK)")
                else:
                    audio_command.extend(["--select-audio", "for=1"])
                    print(f"🎵 Using best audio track (SINGLE TRACK)")
            else:
                # Fallback to English - SINGLE TRACK
                audio_command.extend(["--select-audio", "lang=en:for=1"])
                print(f"🎵 Fallback to English audio (SINGLE TRACK)")

            # Drop all subtitles for audio-only download
            audio_command.extend(["--drop-subtitle", ".*"])

            print(f"🔧 Audio command: {' '.join(audio_command)}")

            process = subprocess.Popen(
                audio_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )

            # Monitor audio download progress (5-25%)
            self._monitor_component_progress(process, "Audio", 5, 25)

            # Find downloaded audio files
            audio_files = list(cache_dir.glob(f"{download_name}_audio*.m4a"))
            if not audio_files:
                audio_files = list(cache_dir.glob("*.m4a"))

            print(f"🔊 Found {len(audio_files)} audio files")
            return audio_files

        except Exception as e:
            print(f"❌ Error downloading audio: {str(e)}")
            return []

    def _download_subtitles_only(self, mpd_url, download_name, cache_dir, selected_subtitle_tracks):
        """Download only selected subtitle tracks"""
        try:
            print(f"📝 Starting subtitles-only download")
            print(f"📝 Selected subtitle tracks: {selected_subtitle_tracks}")

            subtitle_command = [
                str(self.n_m3u8dl_path),
                mpd_url,
                "-mt",
                "--tmp-dir", str(cache_dir),
                "--save-dir", str(cache_dir),
                "--save-name", f"{download_name}_subs",
                "--drop-video", ".*",  # Drop all video
                "--drop-audio", ".*",  # Drop all audio
                "--sub-format", "SRT"
            ]

            # Add specific subtitle selection based on user choice - ONLY ONE TRACK
            if selected_subtitle_tracks and len(selected_subtitle_tracks) > 0:
                # Use the first selected subtitle track
                selected_subtitle = selected_subtitle_tracks[0]
                if 'language' in selected_subtitle:
                    lang = selected_subtitle['language']
                    # Use for=1 to get exactly ONE track
                    subtitle_command.extend(["--select-subtitle", f"lang={lang}:for=1"])
                    print(f"📝 Using specific subtitle language: {lang} (SINGLE TRACK)")
                else:
                    subtitle_command.extend(["--select-subtitle", "for=1"])
                    print(f"📝 Using best subtitle track (SINGLE TRACK)")
            else:
                # Fallback to Arabic or English - SINGLE TRACK
                subtitle_command.extend(["--select-subtitle", "lang=ar|en:for=1"])
                print(f"📝 Fallback to Arabic/English subtitles (SINGLE TRACK)")

            print(f"🔧 Subtitle command: {' '.join(subtitle_command)}")

            process = subprocess.Popen(
                subtitle_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )

            # Monitor subtitle download progress (25-35%)
            self._monitor_component_progress(process, "Subtitles", 25, 35)

            # Find downloaded subtitle files
            subtitle_files = list(cache_dir.glob(f"{download_name}_subs*.srt"))
            if not subtitle_files:
                subtitle_files = list(cache_dir.glob("*.srt"))

            print(f"📝 Found {len(subtitle_files)} subtitle files")
            return subtitle_files

        except Exception as e:
            print(f"❌ Error downloading subtitles: {str(e)}")
            return []

    def _download_video_only(self, mpd_url, uuid, download_name, cache_dir):
        """Download only video track"""
        try:
            print(f"🎬 Starting video-only download")

            video_command = [
                str(self.n_m3u8dl_path),
                mpd_url,
                "-mt",
                "--tmp-dir", str(cache_dir),
                "--save-dir", str(cache_dir),
                "--save-name", f"{download_name}_video",
                "--decryption-binary-path", str(self.mp4decrypt_path),
                "--key-text-file", str(self.keys_file),
                "--binary-merge",
                "--del-after-done",
                "--select-video", f"id={uuid}",
                "--drop-audio", ".*",  # Drop all audio
                "--drop-subtitle", ".*"  # Drop all subtitles
            ]

            print(f"🔧 Video command: {' '.join(video_command)}")

            process = subprocess.Popen(
                video_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )

            # Monitor video download progress (35-80%) - longest phase
            self._monitor_component_progress(process, "Video", 35, 80)

            # Find downloaded video files
            video_files = list(cache_dir.glob(f"{download_name}_video*.mp4"))
            if not video_files:
                video_files = list(cache_dir.glob("*.mp4"))

            print(f"🎬 Found {len(video_files)} video files")
            return video_files

        except Exception as e:
            print(f"❌ Error downloading video: {str(e)}")
            return []

    def _monitor_component_progress(self, process, component_name, start_percent, end_percent):
        """Monitor progress for a specific component"""
        try:
            print(f"📊 Monitoring {component_name} progress ({start_percent}%-{end_percent}%)")

            current_progress = start_percent
            progress_range = end_percent - start_percent

            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    line = output.strip()
                    print(f"📥 {component_name}: {line}")

                    # Look for percentage in the output
                    if "%" in line:
                        try:
                            import re
                            percentages = re.findall(r'(\d+)%', line)
                            if percentages:
                                raw_progress = max(int(p) for p in percentages)
                                # Map raw progress to our range
                                mapped_progress = start_percent + int((raw_progress / 100) * progress_range)

                                if mapped_progress > current_progress:
                                    self.download_progress.emit(mapped_progress, f"📥 {component_name}: {raw_progress}%")
                                    print(f"📊 {component_name} Progress: {mapped_progress}% (Raw: {raw_progress}%)")
                                    current_progress = mapped_progress

                        except Exception as e:
                            print(f"⚠️ Error parsing {component_name} progress: {e}")

                    # Detect completion
                    if "任务结束" in line or "Task End" in line or "Done" in line or "All tasks completed" in line:
                        print(f"🎯 {component_name} download completed")
                        self.download_progress.emit(end_percent, f"✅ {component_name} completed")
                        break

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print(f"✅ {component_name} download successful")
                self.download_progress.emit(end_percent, f"✅ {component_name} completed")
            else:
                error_msg = process.stderr.read() if process.stderr else f"{component_name} download failed"
                print(f"❌ {component_name} download failed: {error_msg}")

        except Exception as e:
            print(f"❌ Error monitoring {component_name}: {str(e)}")

    def _merge_all_components(self, video_files, audio_files, subtitle_files, output_file, cache_dir):
        """Merge all downloaded components into final file"""
        try:
            print(f"🔧 Starting merge of all components")

            if not video_files:
                raise Exception("No video files found for merging")

            video_file = video_files[0]
            print(f"📹 Using video file: {video_file}")

            # Use existing merge function
            self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

        except Exception as e:
            print(f"❌ Error merging components: {str(e)}")
            raise

    def _monitor_enhanced_progress(self, process, download_name, cache_dir, output_file):
        """Monitor N_m3u8DL-RE with enhanced unified progress tracking"""
        try:
            last_percentage = 0
            total_files = 0
            completed_files = 0
            current_file_progress = 0

            # Track different phases
            phase = "starting"  # starting, downloading, merging, completed

            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    line = output.strip()
                    print(f"📥 N_m3u8DL-RE: {line}")

                    # Detect total files to download
                    if "Selected" in line and ("video" in line.lower() or "audio" in line.lower() or "subtitle" in line.lower()):
                        total_files += 1
                        print(f"📊 Total files to download: {total_files}")

                    # Enhanced progress tracking - look for any percentage in download lines
                    if "%" in line:
                        try:
                            import re
                            # Look for percentage patterns in any line
                            percentage_matches = re.findall(r'(\d+)%', line)
                            if percentage_matches:
                                current_progress = int(percentage_matches[-1])
                                print(f"🔍 Found progress: {current_progress}% in line: {line}")

                                # Create a smooth unified progress
                                if total_files > 0:
                                    # Each completed file contributes to progress
                                    base_progress = (completed_files / total_files) * 70
                                    current_file_contribution = (current_progress / 100) * (70 / total_files)
                                    unified_progress = int(base_progress + current_file_contribution)
                                else:
                                    # If we don't know total files, use current progress but cap it
                                    unified_progress = min(current_progress, 70)

                                # Update progress more frequently for better feedback
                                if unified_progress > last_percentage:
                                    self.download_progress.emit(unified_progress, f"Downloading: {download_name}")
                                    print(f"📊 Progress: {unified_progress}% (Current: {current_progress}%, Files: {completed_files}/{total_files})")
                                    last_percentage = unified_progress

                        except Exception as e:
                            print(f"⚠️ Error parsing progress: {e}")

                    # Detect file completion
                    if "写入完成" in line or "Write completed" in line or "Downloaded" in line:
                        completed_files += 1
                        print(f"✅ File completed: {completed_files}/{total_files}")

                        # Update progress when file completes
                        if total_files > 0:
                            file_completion_progress = int((completed_files / total_files) * 70)
                            if file_completion_progress > last_percentage:
                                self.download_progress.emit(file_completion_progress, f"Completed {completed_files}/{total_files} files")
                                last_percentage = file_completion_progress

                    # Detect merge phase
                    if "Muxing" in line or "Merging" in line or "合并" in line or "binary-merge" in line:
                        if phase != "merging":
                            phase = "merging"
                            self.download_progress.emit(75, "Merging files...")
                            print(f"🔗 Starting merge phase")
                            last_percentage = 75

                    # Detect completion
                    if "任务结束" in line or "Task End" in line or "Done" in line or "All tasks completed" in line:
                        phase = "completed"
                        self.download_progress.emit(85, "Download completed, finalizing...")
                        print(f"🎯 Download phase completed")
                        last_percentage = 85

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Enhanced download completed successfully")
                self.download_progress.emit(95, "Download completed, starting final merge...")

                # Find downloaded files in cache
                video_files = list(cache_dir.glob(f"{download_name}*.mp4"))
                audio_files = list(cache_dir.glob(f"{download_name}*.m4a"))

                if video_files:
                    video_file = video_files[0]
                    print(f"📹 Found video file: {video_file}")

                    # Merge files using mkvmerge
                    self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

                    # Clean up cache directory
                    try:
                        import shutil
                        shutil.rmtree(cache_dir)
                        print(f"🧹 Cache directory {cache_dir} has been deleted.")
                    except Exception as e:
                        print(f"⚠️ Error deleting cache directory: {e}")

                    self.download_completed.emit(str(output_file), True)
                else:
                    print("❌ No video files found after download")
                    self.download_error.emit("No video files found after download")
            else:
                error_msg = process.stderr.read() if process.stderr else "Download failed"
                print(f"❌ Enhanced download failed: {error_msg}")
                self.download_error.emit(f"Download failed: {error_msg}")

        except Exception as e:
            print(f"❌ Error monitoring enhanced progress: {str(e)}")
            self.download_error.emit(f"Monitor error: {str(e)}")

    def _download_series_episode_original(self, mpd_url, series_title, season_number, episode_number, uuid, actual_resolution, download_name, season_folder_path, output_file, audio_tracks, subtitle_tracks):
        """Download episode using original OSN.py method with friend's progress monitoring"""
        try:
            print(f"🚀 Starting episode download: {download_name}")
            print(f"🎵 Selected audio tracks: {audio_tracks}")
            print(f"📝 Selected subtitle tracks: {subtitle_tracks}")

            # Create cache directory like original code
            cache_dir = self.base_dir / "cache"
            cache_dir.mkdir(exist_ok=True)

            # Build audio and subtitle options based on user selections
            audio_option, subtitle_option = self._build_selection_options(audio_tracks, subtitle_tracks)

            # Build download command like original code
            download_command = (
                f'"{self.n_m3u8dl_path}" "{mpd_url}" -mt '
                f'--select-video "id={uuid}" '
                f'{audio_option} '
                f'{subtitle_option} '
                f'--tmp-dir "{cache_dir}" '
                f'--save-dir "{cache_dir}" '
                f'--save-name "{download_name}" '
                f'--decryption-binary-path="{self.mp4decrypt_path}" '
                f'--key-text-file="{self.keys_file}" '
                f'--log-level "OFF"'
            )

            print(f"🔧 Episode download command: {download_command}")

            # Start download progress
            self.download_progress.emit(0, f"Starting episode download: {download_name}")

            # Use friend's solution for progress monitoring
            process = subprocess.Popen(
                download_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Redirect stderr to stdout
                text=True,
                universal_newlines=True,
                bufsize=1  # Line buffered
            )

            # Monitor progress using friend's method
            self._monitor_n_m3u8dl_with_friend_solution(process, download_name, cache_dir, output_file, season_folder_path)

        except Exception as e:
            print(f"❌ Error in episode download: {str(e)}")
            self.download_error.emit(f"Episode download error: {str(e)}")

    def _monitor_n_m3u8dl_with_friend_solution(self, process, download_name, cache_dir, output_file, season_folder_path=None):
        """Monitor N_m3u8DL-RE progress using enhanced YANGO solution"""
        try:
            print(f"🔍 Starting enhanced progress monitoring for: {download_name}")

            import re
            import time
            import threading

            # Enhanced progress tracking system from YANGO
            current_progress = {
                'video': 0,
                'audio': 0,
                'subtitle': 0,
                'overall': 0
            }

            target_progress = 0
            smooth_progress = 0
            download_phase = "initializing"  # initializing, subtitles, video, audio, merging, completed

            # Send initial progress signal
            self.download_progress.emit(1, f"Initializing download: {download_name}")
            print(f"📡 Sent initial progress signal: 1%")

            # Enhanced smooth progress animation function
            def smooth_progress_animation():
                nonlocal smooth_progress, target_progress, download_phase
                while smooth_progress < 100:
                    if smooth_progress < target_progress:
                        smooth_progress += 1

                        # Enhanced status messages based on phase
                        if download_phase == "subtitles":
                            status_msg = f"Downloading subtitles: {smooth_progress}%"
                        elif download_phase == "video":
                            status_msg = f"Downloading video: {smooth_progress}%"
                        elif download_phase == "audio":
                            status_msg = f"Downloading audio: {smooth_progress}%"
                        elif download_phase == "merging":
                            status_msg = f"Merging files: {smooth_progress}%"
                        else:
                            status_msg = f"Progress: {smooth_progress}%"

                        self.download_progress.emit(smooth_progress, status_msg)
                        print(f"🎯 Enhanced Progress: {smooth_progress}% - {status_msg}")
                        time.sleep(0.1)  # Update every 100ms for smooth animation
                    else:
                        time.sleep(0.2)  # Wait for new target

            # Start smooth progress thread
            progress_thread = threading.Thread(target=smooth_progress_animation, daemon=True)
            progress_thread.start()

            while True:
                line = process.stdout.readline()
                if not line:
                    break

                line_content = line.strip()

                # Enhanced subtitle progress monitoring
                if line_content.startswith("Sub ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress['subtitle']:
                            download_phase = "subtitles"
                            # Map subtitle progress to 0-15% range
                            mapped_progress = min(int(prog * 0.15), 15)
                            target_progress = mapped_progress
                            current_progress['subtitle'] = prog
                            print(f"📝 Subtitle Progress: {prog}% -> Mapped: {mapped_progress}%")
                    continue

                # Enhanced video progress monitoring
                if line_content.startswith("Vid ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress['video']:
                            download_phase = "video"
                            # Map video progress to 15-70% range
                            mapped_progress = 15 + int(prog * 0.55)
                            target_progress = mapped_progress
                            current_progress['video'] = prog
                            print(f"📹 Video Progress: {prog}% -> Mapped: {mapped_progress}%")
                    continue

                # Enhanced audio progress monitoring
                if line_content.startswith("Aud ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress['audio']:
                            download_phase = "audio"
                            # Map audio progress to 70-85% range
                            mapped_progress = 70 + int(prog * 0.15)
                            target_progress = mapped_progress
                            current_progress['audio'] = prog
                            print(f"🔊 Audio Progress: {prog}% -> Mapped: {mapped_progress}%")
                    continue

                # Enhanced status update for other lines
                if line_content and not any(line_content.startswith(prefix) for prefix in ["Vid ", "Aud ", "Sub "]):
                    self.status_update.emit(f"OSN: {line_content}")

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Episode download completed successfully")
                download_phase = "merging"
                target_progress = 85
                time.sleep(0.3)  # Allow progress to reach 85%

                self.download_progress.emit(85, "Download completed, starting merge...")

                # Find downloaded files like original code
                video_file = cache_dir / f"{download_name}.mp4"
                audio_files = [f for f in cache_dir.glob("*.m4a")]

                if video_file.exists() and audio_files:
                    print("Downloaded files found, proceeding to merge...")

                    # Update progress for merge phase
                    target_progress = 90
                    time.sleep(0.3)

                    # Convert to string paths for merge function
                    audio_paths = [str(f) for f in audio_files]

                    # Merge using episode-specific merge function
                    self._merge_episode_with_mkvmerge(str(video_file), audio_paths, str(output_file), str(cache_dir))

                    # Move poster to final folder like original code
                    self.move_poster_to_final_folder(season_folder_path)

                    # Clean up cache directory like original code
                    try:
                        shutil.rmtree(cache_dir)
                        print(f"Cache directory {cache_dir} has been deleted.")
                    except Exception as e:
                        print(f"An error occurred while deleting the cache directory: {e}")

                    # Set final target to 100% for smooth completion
                    download_phase = "completed"
                    target_progress = 100
                    time.sleep(0.5)  # Give time for smooth animation to reach 100%
                    self.download_completed.emit(str(output_file), True)
                    self.download_progress.emit(100, "✅ Episode download completed successfully!")
                else:
                    print("Download failed or required files not found. Nothing to merge.")
                    self.download_error.emit("Episode download failed or required files not found")
            else:
                print(f"❌ Episode download failed with code: {process.returncode}")
                self.download_error.emit(f"Episode download failed with code: {process.returncode}")

        except Exception as e:
            print(f"❌ Error in enhanced progress monitoring: {str(e)}")
            self.download_error.emit(f"Progress monitoring error: {str(e)}")

    def _merge_episode_with_mkvmerge(self, video_file, audio_files, output_file, cache_dir):
        """Merge video and audio files using mkvmerge for episodes - following original OSN.py"""
        try:
            print(f"🔧 Starting episode merge with mkvmerge...")
            self.download_progress.emit(90, "Merging episode files...")

            mkvmerge_command = [str(self.mkvmerge_path), "-o", output_file]

            # Add video file
            mkvmerge_command.append(video_file)

            # Audio mapping like original code
            audio_map = {
                ".ar.m4a": ("ara", "Arabic"),
                ".tr.m4a": ("tur", "Turkish"),
                ".en.m4a": ("eng", "English"),
                ".en-ddp.m4a": ("eng", "English Dolby 5.1")
            }

            # Add audio files with proper language labels like original code
            for audio_file in audio_files:
                audio_filename = Path(audio_file).name
                language_code, label = ("eng", "English")  # Default

                for suffix, (code, name) in audio_map.items():
                    if suffix in audio_filename:
                        language_code, label = code, name
                        break

                mkvmerge_command.extend([
                    "--language", f"0:{language_code}",
                    "--track-name", f"0:{label}",
                    audio_file
                ])
                print(f"🔊 Adding {label} audio to the episode MKV file")

            # Add subtitle files if they exist like original code
            subtitle_files = {
                "ar": next((f for f in Path(cache_dir).glob("*ar*.srt")), None),
                "en": next((f for f in Path(cache_dir).glob("*en*.srt")), None),
            }

            subtitle_map = {
                "ar": ("ara", "Arabic"),
                "en": ("eng", "English")
            }

            for lang, subtitle_file in subtitle_files.items():
                if subtitle_file:
                    language_code, label = subtitle_map.get(lang, ("und", "Unknown"))
                    subtitle_path = str(subtitle_file)

                    # Add the subtitle file to the mkvmerge command
                    mkvmerge_command.extend([
                        "--language", f"0:{language_code}",
                        "--track-name", f"0:{label}",
                        subtitle_path
                    ])
                    print(f"📝 Adding {label} subtitle to the episode MKV file")

            # Print and run the command like original code
            print("Running mkvmerge command:", " ".join(mkvmerge_command))
            result = subprocess.run(mkvmerge_command, check=True)

            if result.returncode == 0:
                print(f"✅ Episode merge completed successfully: {output_file}")
                self.download_progress.emit(95, "Episode merge completed!")
            else:
                print(f"❌ Episode merge failed")
                self.download_error.emit("Episode merge failed")

        except Exception as e:
            print(f"❌ Error in episode merge process: {str(e)}")
            self.download_error.emit(f"Episode merge error: {str(e)}")

    def _monitor_n_m3u8dl_progress(self, process, download_name, cache_dir, output_file):
        """Monitor N_m3u8DL-RE download progress in real-time"""
        try:
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break

                if output:
                    line = output.strip()
                    print(f"📥 N_m3u8DL-RE: {line}")  # Debug output

                    # Parse progress from N_m3u8DL-RE output
                    if "%" in line:
                        try:
                            # Look for percentage patterns
                            import re
                            percentage_match = re.search(r'(\d+)%', line)
                            if percentage_match:
                                percentage = int(percentage_match.group(1))
                                self.download_progress.emit(percentage, f"Downloading: {download_name}")
                                print(f"📊 Progress: {percentage}%")
                        except Exception as e:
                            print(f"⚠️ Error parsing progress: {e}")

                    # Check for completion indicators
                    if "任务结束" in line or "Task End" in line or "Done" in line:
                        self.download_progress.emit(90, "Download completed, starting merge...")

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ Download completed successfully")
                self.download_progress.emit(95, "Download completed, starting merge...")

                # Find downloaded files in cache
                video_files = list(cache_dir.glob(f"{download_name}*.mp4"))
                audio_files = list(cache_dir.glob(f"{download_name}*.m4a"))

                if video_files:
                    video_file = video_files[0]
                    print(f"📹 Found video file: {video_file}")

                    # Merge files using mkvmerge like original code
                    self._merge_episode_with_mkvmerge(video_file, audio_files, output_file, cache_dir)

                    # Clean up cache directory
                    try:
                        import shutil
                        shutil.rmtree(cache_dir)
                        print(f"🧹 Cache directory {cache_dir} has been deleted.")
                    except Exception as e:
                        print(f"⚠️ Error deleting cache directory: {e}")

                    self.download_completed.emit(str(output_file), True)
                else:
                    print("❌ No video files found after download")
                    self.download_error.emit("No video files found after download")
            else:
                error_msg = process.stderr.read() if process.stderr else "Download failed"
                print(f"❌ Download failed: {error_msg}")
                self.download_error.emit(f"Download failed: {error_msg}")

        except Exception as e:
            print(f"❌ Error monitoring N_m3u8DL-RE progress: {str(e)}")
            self.download_error.emit(f"Monitor error: {str(e)}")

    def save_keys_to_file(self, drm_info, content_data):
        """Save DRM keys to file like original OSN.py"""
        try:
            if not drm_info or not drm_info.get('keys'):
                return

            # Create KEYS directory if it doesn't exist
            keys_dir = self.base_dir / "KEYS"
            keys_dir.mkdir(exist_ok=True)

            # Prepare content info
            title = content_data.get('title', {}).get('en', 'Unknown')
            content_type = 'Movie' if 'year' in content_data else 'Series'

            # Format key entry like original code
            key_entry = f"\n# {content_type}: {title}\n"
            for key in drm_info['keys']:
                key_entry += f"{key}\n"

            # Append to OSNPLUS_KEYS.txt file like original code
            keys_file_path = keys_dir / "OSNPLUS_KEYS.txt"
            with open(keys_file_path, 'a', encoding='utf-8') as f:
                f.write(key_entry)

            print(f"✅ DRM keys saved to: {keys_file_path}")

        except Exception as e:
            print(f"❌ Error saving DRM keys: {str(e)}")

    def _start_n_m3u8dl_download(self, mpd_url, output_dir, filename, selected_quality, audio_tracks, subtitle_tracks, drm_info, content_type):
        """Start N_m3u8DL-RE download process"""
        try:
            # Prepare command
            cmd = [str(self.n_m3u8dl_path), mpd_url]
            cmd.extend(["--save-dir", str(output_dir)])
            cmd.extend(["--save-name", filename])

            # Add quality selection
            if selected_quality and selected_quality.get('resolution'):
                # Use resolution for video selection
                resolution = selected_quality.get('resolution', '720p')
                cmd.extend(["--select-video", f"res={resolution}"])

            # Add audio selection
            if audio_tracks:
                for track in audio_tracks:
                    cmd.extend(["--select-audio", f"lang:{track}"])

            # Add subtitle selection
            if subtitle_tracks:
                for track in subtitle_tracks:
                    cmd.extend(["--select-subtitle", f"lang:{track}"])

            # Add DRM keys if available
            if drm_info and drm_info.get('formatted_key'):
                print(f"🔐 Adding DRM key: {drm_info['formatted_key']}")
                cmd.extend(["--key", drm_info['formatted_key']])
                cmd.extend(["--key-text-file", str(self.keys_file)])
                cmd.extend(["--decryption-binary-path", str(self.mp4decrypt_path)])
            else:
                print(f"⚠️ No DRM keys available. DRM info: {drm_info}")

            # Add other options
            cmd.extend(["--binary-merge"])
            cmd.extend(["--del-after-done"])
            cmd.extend(["-mt"])  # Concurrent download
            cmd.extend(["--download-retry-count", "3"])
            cmd.extend(["--ffmpeg-binary-path", str(self.ffmpeg_path)])

            # Start download process
            self.download_progress.emit(0, f"Starting download: {filename}")

            # Print the command for debugging
            print(f"🚀 N_m3u8DL-RE Command:")
            print(f"   {' '.join(cmd)}")
            print(f"📁 Working Directory: {self.base_dir}")
            print(f"📁 Output Directory: {output_dir}")

            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Redirect stderr to stdout for friend's solution
                text=True,
                universal_newlines=True,
                bufsize=1,  # Line buffered
                cwd=str(self.base_dir)
            )

            # Monitor download progress using friend's solution
            self._monitor_download_progress(process, filename, str(output_dir / f"{filename}.mkv"))

        except Exception as e:
            self.download_error.emit(f"Error starting download: {str(e)}")

    def _monitor_download_progress(self, process, filename, output_file):
        """Monitor download progress using friend's working solution"""
        try:
            print(f"🔍 Starting friend's progress monitoring for: {filename}")

            import re
            current_progress_video = 0

            while True:
                line = process.stdout.readline()
                if not line:
                    break

                line_content = line.strip()

                # Video progress monitoring - adapted for OSN N_m3u8DL-RE format
                if line_content.startswith("Vid ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress_video:
                            self.video_progress.emit(prog)
                            self.download_progress.emit(prog, f"Video: {prog}%")
                            current_progress_video = prog
                            print(f"📊 Video Progress: {prog}%")
                    # Don't send status update for video progress lines
                    continue

                # Audio progress monitoring - also extract progress
                if line_content.startswith("Aud ") and "%" in line_content:
                    m = re.search(r"(\d+)%", line_content)
                    if m:
                        prog = int(m.group(1))
                        # For audio, we can also update progress but with lower priority
                        if prog > current_progress_video:
                            self.download_progress.emit(prog, f"Audio: {prog}%")
                            print(f"🔊 Audio Progress: {prog}%")
                    # Don't send status update for audio progress lines
                    continue

                # Status update for other lines
                self.status_update.emit(f"N_m3u8DL: {line_content}")

            # Check if download completed successfully
            if process.returncode == 0 and os.path.exists(output_file):
                self.download_completed.emit(output_file, True)
                self.download_progress.emit(100, f"Completed: {filename}")
            else:
                self.download_error.emit(f"Download failed: {filename}")

        except Exception as e:
            self.download_error.emit(f"Error monitoring download: {str(e)}")

    def download_poster_to_cache(self, poster_url):
        """Download poster to cache directory like original OSN.py"""
        try:
            import requests

            # Create cache directory
            cache_dir = self.base_dir / "cache"
            cache_dir.mkdir(exist_ok=True)

            print(f"📸 Downloading poster from: {poster_url}")

            response = requests.get(poster_url, stream=True, timeout=10)
            response.raise_for_status()

            cache_poster_path = cache_dir / "poster.jpg"
            with open(cache_poster_path, "wb") as file:
                file.write(response.content)

            print(f"✅ Poster downloaded and saved as {cache_poster_path}")
            return str(cache_poster_path)

        except Exception as e:
            print(f"❌ Failed to download poster: {e}")
            return None

    def move_poster_to_final_folder(self, series_folder_path):
        """Move poster from cache to final series folder like original OSN.py"""
        try:
            cache_dir = self.base_dir / "cache"
            cache_poster_path = cache_dir / "poster.jpg"
            final_poster_path = series_folder_path / "poster.jpg"

            if cache_poster_path.exists():
                import shutil
                shutil.move(str(cache_poster_path), str(final_poster_path))
                print(f"📸 Poster moved to {final_poster_path}")
            else:
                print("⚠️ Poster not found in cache to move")

        except Exception as e:
            print(f"❌ Error while moving poster: {e}")

    def download_and_save_poster(self, poster_url, sanitized_title, download_path):
        """Download and save poster directly to movie folder like original OSN.py"""
        try:
            import requests

            print(f"📸 Downloading movie poster from: {poster_url}")

            response = requests.get(poster_url, stream=True, timeout=10)
            response.raise_for_status()

            filename = f"{sanitized_title}_poster.jpg"
            file_path = download_path / filename

            with open(file_path, "wb") as file:
                file.write(response.content)

            print(f"✅ Movie poster downloaded and saved as {file_path}")

        except Exception as e:
            print(f"❌ Failed to download movie poster: {e}")

    def set_poster_url(self, poster_url):
        """Set the poster URL for current download session"""
        self.current_poster_url = poster_url
        print(f"📸 Poster URL set for download session: {poster_url}")

    def _monitor_aria2c_progress(self, process, download_name, output_file):
        """Monitor aria2c download progress using enhanced YANGO solution"""
        try:
            print(f"🔍 Starting enhanced aria2c progress monitoring for: {download_name}")

            import re
            import time
            import threading

            # Enhanced progress tracking system from YANGO
            current_progress = 0
            target_progress = 0
            smooth_progress = 0
            download_phase = "downloading"

            # Send initial progress signal
            self.download_progress.emit(1, f"Starting aria2c download: {download_name}")
            print(f"📡 Sent initial aria2c progress signal: 1%")

            # Enhanced smooth progress animation function
            def smooth_progress_animation():
                nonlocal smooth_progress, target_progress, download_phase
                while smooth_progress < 100:
                    if smooth_progress < target_progress:
                        smooth_progress += 1
                        status_msg = f"Downloading: {smooth_progress}%"
                        self.download_progress.emit(smooth_progress, status_msg)
                        print(f"🎯 Enhanced aria2c Progress: {smooth_progress}% - {status_msg}")
                        time.sleep(0.1)  # Update every 100ms for smooth animation
                    else:
                        time.sleep(0.2)  # Wait for new target

            # Start smooth progress thread
            progress_thread = threading.Thread(target=smooth_progress_animation, daemon=True)
            progress_thread.start()

            while True:
                line = process.stdout.readline()
                if not line:
                    break

                line_content = line.strip()

                # Enhanced aria2c progress monitoring
                # Look for patterns like: [#1 SIZE/TOTAL(PERCENTAGE%)]
                if "[#" in line_content and "%" in line_content:
                    # Pattern: [#1 1.2MiB/10.5MiB(11%)]
                    m = re.search(r'\[#\d+.*?(\d+)%\]', line_content)
                    if m:
                        prog = int(m.group(1))
                        if prog != current_progress:
                            # Map aria2c progress to 0-95% range (leave room for completion)
                            mapped_progress = min(int(prog * 0.95), 95)
                            target_progress = mapped_progress
                            current_progress = prog
                            print(f"📊 aria2c Progress: {prog}% -> Mapped: {mapped_progress}%")
                    continue

                # Look for download speed and ETA information
                if "DL:" in line_content:
                    # Extract download speed for status
                    speed_match = re.search(r'DL:([^\s]+)', line_content)
                    if speed_match:
                        speed = speed_match.group(1)
                        self.status_update.emit(f"OSN aria2c: Speed {speed}")
                    continue

                # Enhanced status update for other lines
                if line_content and not line_content.startswith("[#"):
                    self.status_update.emit(f"OSN aria2c: {line_content}")

            # Wait for process to complete
            process.wait()

            if process.returncode == 0:
                print("✅ aria2c download completed successfully")
                download_phase = "completed"
                target_progress = 100
                time.sleep(0.5)  # Give time for smooth animation to reach 100%
                self.download_completed.emit(str(output_file), True)
                self.download_progress.emit(100, "✅ aria2c download completed successfully!")
            else:
                print(f"❌ aria2c download failed with code: {process.returncode}")
                self.download_error.emit(f"aria2c download failed with code: {process.returncode}")

        except Exception as e:
            print(f"❌ Error in enhanced aria2c progress monitoring: {str(e)}")
            self.download_error.emit(f"aria2c progress monitoring error: {str(e)}")

    def download_with_aria2c(self, url, output_path, filename):
        """Download file using aria2c with enhanced progress monitoring"""
        try:
            print(f"🚀 Starting aria2c download: {filename}")

            # Build aria2c command
            aria2c_command = [
                str(self.aria2c_path),
                "--continue=true",
                "--max-tries=5",
                "--retry-wait=3",
                "--timeout=60",
                "--max-connection-per-server=16",
                "--split=16",
                "--min-split-size=1M",
                "--dir", str(output_path),
                "--out", filename,
                url
            ]

            print(f"🔧 aria2c command: {' '.join(aria2c_command)}")

            # Start aria2c process
            process = subprocess.Popen(
                aria2c_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                universal_newlines=True,
                bufsize=1
            )

            # Monitor progress using enhanced solution
            output_file = output_path / filename
            self._monitor_aria2c_progress(process, filename, output_file)

        except Exception as e:
            print(f"❌ Error starting aria2c download: {str(e)}")
            self.download_error.emit(f"aria2c download error: {str(e)}")
