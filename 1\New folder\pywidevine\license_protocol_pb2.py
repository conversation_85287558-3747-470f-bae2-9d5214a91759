# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: license_protocol.proto
# Protobuf Python Version: 4.25.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16license_protocol.proto\x12\x1bpywidevine_license_protocol\"\xbd\x01\n\x15LicenseIdentification\x12\x12\n\nrequest_id\x18\x01 \x01(\x0c\x12\x12\n\nsession_id\x18\x02 \x01(\x0c\x12\x13\n\x0bpurchase_id\x18\x03 \x01(\x0c\x12\x36\n\x04type\x18\x04 \x01(\x0e\x32(.pywidevine_license_protocol.LicenseType\x12\x0f\n\x07version\x18\x05 \x01(\x05\x12\x1e\n\x16provider_session_token\x18\x06 \x01(\x0c\"\xf1\x18\n\x07License\x12>\n\x02id\x18\x01 \x01(\x0b\x32\x32.pywidevine_license_protocol.LicenseIdentification\x12;\n\x06policy\x18\x02 \x01(\x0b\x32+.pywidevine_license_protocol.License.Policy\x12>\n\x03key\x18\x03 \x03(\x0b\x32\x31.pywidevine_license_protocol.License.KeyContainer\x12\x1a\n\x12license_start_time\x18\x04 \x01(\x03\x12*\n\x1bremote_attestation_verified\x18\x05 \x01(\x08:\x05\x66\x61lse\x12\x1d\n\x15provider_client_token\x18\x06 \x01(\x0c\x12\x19\n\x11protection_scheme\x18\x07 \x01(\r\x12\x17\n\x0fsrm_requirement\x18\x08 \x01(\x0c\x12\x12\n\nsrm_update\x18\t \x01(\x0c\x12w\n\x1cplatform_verification_status\x18\n \x01(\x0e\x32\x37.pywidevine_license_protocol.PlatformVerificationStatus:\x18PLATFORM_NO_VERIFICATION\x12\x11\n\tgroup_ids\x18\x0b \x03(\x0c\x1a\xae\x04\n\x06Policy\x12\x17\n\x08\x63\x61n_play\x18\x01 \x01(\x08:\x05\x66\x61lse\x12\x1a\n\x0b\x63\x61n_persist\x18\x02 \x01(\x08:\x05\x66\x61lse\x12\x18\n\tcan_renew\x18\x03 \x01(\x08:\x05\x66\x61lse\x12\"\n\x17rental_duration_seconds\x18\x04 \x01(\x03:\x01\x30\x12$\n\x19playback_duration_seconds\x18\x05 \x01(\x03:\x01\x30\x12#\n\x18license_duration_seconds\x18\x06 \x01(\x03:\x01\x30\x12,\n!renewal_recovery_duration_seconds\x18\x07 \x01(\x03:\x01\x30\x12\x1a\n\x12renewal_server_url\x18\x08 \x01(\t\x12 \n\x15renewal_delay_seconds\x18\t \x01(\x03:\x01\x30\x12)\n\x1erenewal_retry_interval_seconds\x18\n \x01(\x03:\x01\x30\x12\x1f\n\x10renew_with_usage\x18\x0b \x01(\x08:\x05\x66\x61lse\x12\'\n\x18\x61lways_include_client_id\x18\x0c \x01(\x08:\x05\x66\x61lse\x12*\n\x1fplay_start_grace_period_seconds\x18\r \x01(\x03:\x01\x30\x12-\n\x1esoft_enforce_playback_duration\x18\x0e \x01(\x08:\x05\x66\x61lse\x12*\n\x1csoft_enforce_rental_duration\x18\x0f \x01(\x08:\x04true\x1a\xbc\x10\n\x0cKeyContainer\x12\n\n\x02id\x18\x01 \x01(\x0c\x12\n\n\x02iv\x18\x02 \x01(\x0c\x12\x0b\n\x03key\x18\x03 \x01(\x0c\x12G\n\x04type\x18\x04 \x01(\x0e\x32\x39.pywidevine_license_protocol.License.KeyContainer.KeyType\x12`\n\x05level\x18\x05 \x01(\x0e\x32?.pywidevine_license_protocol.License.KeyContainer.SecurityLevel:\x10SW_SECURE_CRYPTO\x12_\n\x13required_protection\x18\x06 \x01(\x0b\x32\x42.pywidevine_license_protocol.License.KeyContainer.OutputProtection\x12`\n\x14requested_protection\x18\x07 \x01(\x0b\x32\x42.pywidevine_license_protocol.License.KeyContainer.OutputProtection\x12Q\n\x0bkey_control\x18\x08 \x01(\x0b\x32<.pywidevine_license_protocol.License.KeyContainer.KeyControl\x12y\n operator_session_key_permissions\x18\t \x01(\x0b\x32O.pywidevine_license_protocol.License.KeyContainer.OperatorSessionKeyPermissions\x12q\n\x1cvideo_resolution_constraints\x18\n \x03(\x0b\x32K.pywidevine_license_protocol.License.KeyContainer.VideoResolutionConstraint\x12(\n\x19\x61nti_rollback_usage_table\x18\x0b \x01(\x08:\x05\x66\x61lse\x12\x13\n\x0btrack_label\x18\x0c \x01(\t\x1a\x33\n\nKeyControl\x12\x19\n\x11key_control_block\x18\x01 \x01(\x0c\x12\n\n\x02iv\x18\x02 \x01(\x0c\x1a\x9c\x05\n\x10OutputProtection\x12`\n\x04hdcp\x18\x01 \x01(\x0e\x32G.pywidevine_license_protocol.License.KeyContainer.OutputProtection.HDCP:\tHDCP_NONE\x12\x66\n\ncgms_flags\x18\x02 \x01(\x0e\x32G.pywidevine_license_protocol.License.KeyContainer.OutputProtection.CGMS:\tCGMS_NONE\x12y\n\rhdcp_srm_rule\x18\x03 \x01(\x0e\x32N.pywidevine_license_protocol.License.KeyContainer.OutputProtection.HdcpSrmRule:\x12HDCP_SRM_RULE_NONE\x12$\n\x15\x64isable_analog_output\x18\x04 \x01(\x08:\x05\x66\x61lse\x12%\n\x16\x64isable_digital_output\x18\x05 \x01(\x08:\x05\x66\x61lse\"y\n\x04HDCP\x12\r\n\tHDCP_NONE\x10\x00\x12\x0b\n\x07HDCP_V1\x10\x01\x12\x0b\n\x07HDCP_V2\x10\x02\x12\r\n\tHDCP_V2_1\x10\x03\x12\r\n\tHDCP_V2_2\x10\x04\x12\r\n\tHDCP_V2_3\x10\x05\x12\x1b\n\x16HDCP_NO_DIGITAL_OUTPUT\x10\xff\x01\"C\n\x04\x43GMS\x12\r\n\tCGMS_NONE\x10*\x12\r\n\tCOPY_FREE\x10\x00\x12\r\n\tCOPY_ONCE\x10\x02\x12\x0e\n\nCOPY_NEVER\x10\x03\"6\n\x0bHdcpSrmRule\x12\x16\n\x12HDCP_SRM_RULE_NONE\x10\x00\x12\x0f\n\x0b\x43URRENT_SRM\x10\x01\x1a\xba\x01\n\x19VideoResolutionConstraint\x12\x1d\n\x15min_resolution_pixels\x18\x01 \x01(\r\x12\x1d\n\x15max_resolution_pixels\x18\x02 \x01(\r\x12_\n\x13required_protection\x18\x03 \x01(\x0b\x32\x42.pywidevine_license_protocol.License.KeyContainer.OutputProtection\x1a\x9d\x01\n\x1dOperatorSessionKeyPermissions\x12\x1c\n\rallow_encrypt\x18\x01 \x01(\x08:\x05\x66\x61lse\x12\x1c\n\rallow_decrypt\x18\x02 \x01(\x08:\x05\x66\x61lse\x12\x19\n\nallow_sign\x18\x03 \x01(\x08:\x05\x66\x61lse\x12%\n\x16\x61llow_signature_verify\x18\x04 \x01(\x08:\x05\x66\x61lse\"l\n\x07KeyType\x12\x0b\n\x07SIGNING\x10\x01\x12\x0b\n\x07\x43ONTENT\x10\x02\x12\x0f\n\x0bKEY_CONTROL\x10\x03\x12\x14\n\x10OPERATOR_SESSION\x10\x04\x12\x0f\n\x0b\x45NTITLEMENT\x10\x05\x12\x0f\n\x0bOEM_CONTENT\x10\x06\"z\n\rSecurityLevel\x12\x14\n\x10SW_SECURE_CRYPTO\x10\x01\x12\x14\n\x10SW_SECURE_DECODE\x10\x02\x12\x14\n\x10HW_SECURE_CRYPTO\x10\x03\x12\x14\n\x10HW_SECURE_DECODE\x10\x04\x12\x11\n\rHW_SECURE_ALL\x10\x05\"\xbd\r\n\x0eLicenseRequest\x12\x44\n\tclient_id\x18\x01 \x01(\x0b\x32\x31.pywidevine_license_protocol.ClientIdentification\x12U\n\ncontent_id\x18\x02 \x01(\x0b\x32\x41.pywidevine_license_protocol.LicenseRequest.ContentIdentification\x12\x45\n\x04type\x18\x03 \x01(\x0e\x32\x37.pywidevine_license_protocol.LicenseRequest.RequestType\x12\x14\n\x0crequest_time\x18\x04 \x01(\x03\x12$\n\x1ckey_control_nonce_deprecated\x18\x05 \x01(\x0c\x12S\n\x10protocol_version\x18\x06 \x01(\x0e\x32,.pywidevine_license_protocol.ProtocolVersion:\x0bVERSION_2_0\x12\x19\n\x11key_control_nonce\x18\x07 \x01(\r\x12W\n\x13\x65ncrypted_client_id\x18\x08 \x01(\x0b\x32:.pywidevine_license_protocol.EncryptedClientIdentification\x1a\x8f\t\n\x15\x43ontentIdentification\x12p\n\x12widevine_pssh_data\x18\x01 \x01(\x0b\x32R.pywidevine_license_protocol.LicenseRequest.ContentIdentification.WidevinePsshDataH\x00\x12\x62\n\x0bwebm_key_id\x18\x02 \x01(\x0b\x32K.pywidevine_license_protocol.LicenseRequest.ContentIdentification.WebmKeyIdH\x00\x12m\n\x10\x65xisting_license\x18\x03 \x01(\x0b\x32Q.pywidevine_license_protocol.LicenseRequest.ContentIdentification.ExistingLicenseH\x00\x12_\n\tinit_data\x18\x04 \x01(\x0b\x32J.pywidevine_license_protocol.LicenseRequest.ContentIdentification.InitDataH\x00\x1ay\n\x10WidevinePsshData\x12\x11\n\tpssh_data\x18\x01 \x03(\x0c\x12>\n\x0clicense_type\x18\x02 \x01(\x0e\x32(.pywidevine_license_protocol.LicenseType\x12\x12\n\nrequest_id\x18\x03 \x01(\x0c\x1ao\n\tWebmKeyId\x12\x0e\n\x06header\x18\x01 \x01(\x0c\x12>\n\x0clicense_type\x18\x02 \x01(\x0e\x32(.pywidevine_license_protocol.LicenseType\x12\x12\n\nrequest_id\x18\x03 \x01(\x0c\x1a\xbe\x01\n\x0f\x45xistingLicense\x12\x46\n\nlicense_id\x18\x01 \x01(\x0b\x32\x32.pywidevine_license_protocol.LicenseIdentification\x12\x1d\n\x15seconds_since_started\x18\x02 \x01(\x03\x12!\n\x19seconds_since_last_played\x18\x03 \x01(\x03\x12!\n\x19session_usage_table_entry\x18\x04 \x01(\x0c\x1a\x8c\x02\n\x08InitData\x12u\n\x0einit_data_type\x18\x01 \x01(\x0e\x32W.pywidevine_license_protocol.LicenseRequest.ContentIdentification.InitData.InitDataType:\x04\x43\x45NC\x12\x11\n\tinit_data\x18\x02 \x01(\x0c\x12>\n\x0clicense_type\x18\x03 \x01(\x0e\x32(.pywidevine_license_protocol.LicenseType\x12\x12\n\nrequest_id\x18\x04 \x01(\x0c\"\"\n\x0cInitDataType\x12\x08\n\x04\x43\x45NC\x10\x01\x12\x08\n\x04WEBM\x10\x02\x42\x14\n\x12\x63ontent_id_variant\"0\n\x0bRequestType\x12\x07\n\x03NEW\x10\x01\x12\x0b\n\x07RENEWAL\x10\x02\x12\x0b\n\x07RELEASE\x10\x03\"\xf3\x01\n\nMetricData\x12\x12\n\nstage_name\x18\x01 \x01(\t\x12\x46\n\x0bmetric_data\x18\x02 \x03(\x0b\x32\x31.pywidevine_license_protocol.MetricData.TypeValue\x1a_\n\tTypeValue\x12@\n\x04type\x18\x01 \x01(\x0e\x32\x32.pywidevine_license_protocol.MetricData.MetricType\x12\x10\n\x05value\x18\x02 \x01(\x03:\x01\x30\"(\n\nMetricType\x12\x0b\n\x07LATENCY\x10\x01\x12\r\n\tTIMESTAMP\x10\x02\"K\n\x0bVersionInfo\x12\x1b\n\x13license_sdk_version\x18\x01 \x01(\t\x12\x1f\n\x17license_service_version\x18\x02 \x01(\t\"\xf6\x05\n\rSignedMessage\x12\x44\n\x04type\x18\x01 \x01(\x0e\x32\x36.pywidevine_license_protocol.SignedMessage.MessageType\x12\x0b\n\x03msg\x18\x02 \x01(\x0c\x12\x11\n\tsignature\x18\x03 \x01(\x0c\x12\x13\n\x0bsession_key\x18\x04 \x01(\x0c\x12\x1a\n\x12remote_attestation\x18\x05 \x01(\x0c\x12<\n\x0bmetric_data\x18\x06 \x03(\x0b\x32\'.pywidevine_license_protocol.MetricData\x12\x46\n\x14service_version_info\x18\x07 \x01(\x0b\x32(.pywidevine_license_protocol.VersionInfo\x12\x64\n\x10session_key_type\x18\x08 \x01(\x0e\x32\x39.pywidevine_license_protocol.SignedMessage.SessionKeyType:\x0fWRAPPED_AES_KEY\x12\x1e\n\x16oemcrypto_core_message\x18\t \x01(\x0c\"\xec\x01\n\x0bMessageType\x12\x13\n\x0fLICENSE_REQUEST\x10\x01\x12\x0b\n\x07LICENSE\x10\x02\x12\x12\n\x0e\x45RROR_RESPONSE\x10\x03\x12\x1f\n\x1bSERVICE_CERTIFICATE_REQUEST\x10\x04\x12\x17\n\x13SERVICE_CERTIFICATE\x10\x05\x12\x0f\n\x0bSUB_LICENSE\x10\x06\x12\x17\n\x13\x43\x41S_LICENSE_REQUEST\x10\x07\x12\x0f\n\x0b\x43\x41S_LICENSE\x10\x08\x12\x1c\n\x18\x45XTERNAL_LICENSE_REQUEST\x10\t\x12\x14\n\x10\x45XTERNAL_LICENSE\x10\n\"S\n\x0eSessionKeyType\x12\r\n\tUNDEFINED\x10\x00\x12\x13\n\x0fWRAPPED_AES_KEY\x10\x01\x12\x1d\n\x19\x45PHERMERAL_ECC_PUBLIC_KEY\x10\x02\"\xc7\x0e\n\x14\x43lientIdentification\x12Q\n\x04type\x18\x01 \x01(\x0e\x32;.pywidevine_license_protocol.ClientIdentification.TokenType:\x06KEYBOX\x12\r\n\x05token\x18\x02 \x01(\x0c\x12P\n\x0b\x63lient_info\x18\x03 \x03(\x0b\x32;.pywidevine_license_protocol.ClientIdentification.NameValue\x12\x1d\n\x15provider_client_token\x18\x04 \x01(\x0c\x12\x17\n\x0flicense_counter\x18\x05 \x01(\r\x12\x61\n\x13\x63lient_capabilities\x18\x06 \x01(\x0b\x32\x44.pywidevine_license_protocol.ClientIdentification.ClientCapabilities\x12\x10\n\x08vmp_data\x18\x07 \x01(\x0c\x12_\n\x12\x64\x65vice_credentials\x18\x08 \x03(\x0b\x32\x43.pywidevine_license_protocol.ClientIdentification.ClientCredentials\x1a(\n\tNameValue\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\x1a\xd6\x08\n\x12\x43lientCapabilities\x12\x1b\n\x0c\x63lient_token\x18\x01 \x01(\x08:\x05\x66\x61lse\x12\x1c\n\rsession_token\x18\x02 \x01(\x08:\x05\x66\x61lse\x12+\n\x1cvideo_resolution_constraints\x18\x03 \x01(\x08:\x05\x66\x61lse\x12u\n\x10max_hdcp_version\x18\x04 \x01(\x0e\x32P.pywidevine_license_protocol.ClientIdentification.ClientCapabilities.HdcpVersion:\tHDCP_NONE\x12\x1e\n\x16oem_crypto_api_version\x18\x05 \x01(\r\x12(\n\x19\x61nti_rollback_usage_table\x18\x06 \x01(\x08:\x05\x66\x61lse\x12\x13\n\x0bsrm_version\x18\x07 \x01(\r\x12\x1d\n\x0e\x63\x61n_update_srm\x18\x08 \x01(\x08:\x05\x66\x61lse\x12\x7f\n\x1esupported_certificate_key_type\x18\t \x03(\x0e\x32W.pywidevine_license_protocol.ClientIdentification.ClientCapabilities.CertificateKeyType\x12\x98\x01\n\x1a\x61nalog_output_capabilities\x18\n \x01(\x0e\x32].pywidevine_license_protocol.ClientIdentification.ClientCapabilities.AnalogOutputCapabilities:\x15\x41NALOG_OUTPUT_UNKNOWN\x12(\n\x19\x63\x61n_disable_analog_output\x18\x0b \x01(\x08:\x05\x66\x61lse\x12\x1f\n\x14resource_rating_tier\x18\x0c \x01(\r:\x01\x30\"\x80\x01\n\x0bHdcpVersion\x12\r\n\tHDCP_NONE\x10\x00\x12\x0b\n\x07HDCP_V1\x10\x01\x12\x0b\n\x07HDCP_V2\x10\x02\x12\r\n\tHDCP_V2_1\x10\x03\x12\r\n\tHDCP_V2_2\x10\x04\x12\r\n\tHDCP_V2_3\x10\x05\x12\x1b\n\x16HDCP_NO_DIGITAL_OUTPUT\x10\xff\x01\"i\n\x12\x43\x65rtificateKeyType\x12\x0c\n\x08RSA_2048\x10\x00\x12\x0c\n\x08RSA_3072\x10\x01\x12\x11\n\rECC_SECP256R1\x10\x02\x12\x11\n\rECC_SECP384R1\x10\x03\x12\x11\n\rECC_SECP521R1\x10\x04\"\x8d\x01\n\x18\x41nalogOutputCapabilities\x12\x19\n\x15\x41NALOG_OUTPUT_UNKNOWN\x10\x00\x12\x16\n\x12\x41NALOG_OUTPUT_NONE\x10\x01\x12\x1b\n\x17\x41NALOG_OUTPUT_SUPPORTED\x10\x02\x12!\n\x1d\x41NALOG_OUTPUT_SUPPORTS_CGMS_A\x10\x03\x1au\n\x11\x43lientCredentials\x12Q\n\x04type\x18\x01 \x01(\x0e\x32;.pywidevine_license_protocol.ClientIdentification.TokenType:\x06KEYBOX\x12\r\n\x05token\x18\x02 \x01(\x0c\"s\n\tTokenType\x12\n\n\x06KEYBOX\x10\x00\x12\x1a\n\x16\x44RM_DEVICE_CERTIFICATE\x10\x01\x12\"\n\x1eREMOTE_ATTESTATION_CERTIFICATE\x10\x02\x12\x1a\n\x16OEM_DEVICE_CERTIFICATE\x10\x03\"\xbb\x01\n\x1d\x45ncryptedClientIdentification\x12\x13\n\x0bprovider_id\x18\x01 \x01(\t\x12)\n!service_certificate_serial_number\x18\x02 \x01(\x0c\x12\x1b\n\x13\x65ncrypted_client_id\x18\x03 \x01(\x0c\x12\x1e\n\x16\x65ncrypted_client_id_iv\x18\x04 \x01(\x0c\x12\x1d\n\x15\x65ncrypted_privacy_key\x18\x05 \x01(\x0c\"\xba\x07\n\x0e\x44rmCertificate\x12>\n\x04type\x18\x01 \x01(\x0e\x32\x30.pywidevine_license_protocol.DrmCertificate.Type\x12\x15\n\rserial_number\x18\x02 \x01(\x0c\x12\x1d\n\x15\x63reation_time_seconds\x18\x03 \x01(\r\x12\x1f\n\x17\x65xpiration_time_seconds\x18\x0c \x01(\r\x12\x12\n\npublic_key\x18\x04 \x01(\x0c\x12\x11\n\tsystem_id\x18\x05 \x01(\r\x12\"\n\x16test_device_deprecated\x18\x06 \x01(\x08\x42\x02\x18\x01\x12\x13\n\x0bprovider_id\x18\x07 \x01(\t\x12N\n\rservice_types\x18\x08 \x03(\x0e\x32\x37.pywidevine_license_protocol.DrmCertificate.ServiceType\x12M\n\talgorithm\x18\t \x01(\x0e\x32\x35.pywidevine_license_protocol.DrmCertificate.Algorithm:\x03RSA\x12\x0e\n\x06rot_id\x18\n \x01(\x0c\x12Q\n\x0e\x65ncryption_key\x18\x0b \x01(\x0b\x32\x39.pywidevine_license_protocol.DrmCertificate.EncryptionKey\x1ar\n\rEncryptionKey\x12\x12\n\npublic_key\x18\x01 \x01(\x0c\x12M\n\talgorithm\x18\x02 \x01(\x0e\x32\x35.pywidevine_license_protocol.DrmCertificate.Algorithm:\x03RSA\"L\n\x04Type\x12\x08\n\x04ROOT\x10\x00\x12\x10\n\x0c\x44\x45VICE_MODEL\x10\x01\x12\n\n\x06\x44\x45VICE\x10\x02\x12\x0b\n\x07SERVICE\x10\x03\x12\x0f\n\x0bPROVISIONER\x10\x04\"\x86\x01\n\x0bServiceType\x12\x18\n\x14UNKNOWN_SERVICE_TYPE\x10\x00\x12\x16\n\x12LICENSE_SERVER_SDK\x10\x01\x12\x1c\n\x18LICENSE_SERVER_PROXY_SDK\x10\x02\x12\x14\n\x10PROVISIONING_SDK\x10\x03\x12\x11\n\rCAS_PROXY_SDK\x10\x04\"d\n\tAlgorithm\x12\x15\n\x11UNKNOWN_ALGORITHM\x10\x00\x12\x07\n\x03RSA\x10\x01\x12\x11\n\rECC_SECP256R1\x10\x02\x12\x11\n\rECC_SECP384R1\x10\x03\x12\x11\n\rECC_SECP521R1\x10\x04\"\xce\x01\n\x14SignedDrmCertificate\x12\x17\n\x0f\x64rm_certificate\x18\x01 \x01(\x0c\x12\x11\n\tsignature\x18\x02 \x01(\x0c\x12\x41\n\x06signer\x18\x03 \x01(\x0b\x32\x31.pywidevine_license_protocol.SignedDrmCertificate\x12G\n\x0ehash_algorithm\x18\x04 \x01(\x0e\x32/.pywidevine_license_protocol.HashAlgorithmProto\"\xf6\x05\n\x10WidevinePsshData\x12\x0f\n\x07key_ids\x18\x02 \x03(\x0c\x12\x12\n\ncontent_id\x18\x04 \x01(\x0c\x12\x1b\n\x13\x63rypto_period_index\x18\x07 \x01(\r\x12\x19\n\x11protection_scheme\x18\t \x01(\r\x12\x1d\n\x15\x63rypto_period_seconds\x18\n \x01(\r\x12H\n\x04type\x18\x0b \x01(\x0e\x32\x32.pywidevine_license_protocol.WidevinePsshData.Type:\x06SINGLE\x12\x14\n\x0ckey_sequence\x18\x0c \x01(\r\x12\x11\n\tgroup_ids\x18\r \x03(\x0c\x12P\n\rentitled_keys\x18\x0e \x03(\x0b\x32\x39.pywidevine_license_protocol.WidevinePsshData.EntitledKey\x12\x15\n\rvideo_feature\x18\x0f \x01(\t\x12N\n\talgorithm\x18\x01 \x01(\x0e\x32\x37.pywidevine_license_protocol.WidevinePsshData.AlgorithmB\x02\x18\x01\x12\x14\n\x08provider\x18\x03 \x01(\tB\x02\x18\x01\x12\x16\n\ntrack_type\x18\x05 \x01(\tB\x02\x18\x01\x12\x12\n\x06policy\x18\x06 \x01(\tB\x02\x18\x01\x12\x1b\n\x0fgrouped_license\x18\x08 \x01(\x0c\x42\x02\x18\x01\x1az\n\x0b\x45ntitledKey\x12\x1a\n\x12\x65ntitlement_key_id\x18\x01 \x01(\x0c\x12\x0e\n\x06key_id\x18\x02 \x01(\x0c\x12\x0b\n\x03key\x18\x03 \x01(\x0c\x12\n\n\x02iv\x18\x04 \x01(\x0c\x12&\n\x1a\x65ntitlement_key_size_bytes\x18\x05 \x01(\r:\x02\x33\x32\"5\n\x04Type\x12\n\n\x06SINGLE\x10\x00\x12\x0f\n\x0b\x45NTITLEMENT\x10\x01\x12\x10\n\x0c\x45NTITLED_KEY\x10\x02\"(\n\tAlgorithm\x12\x0f\n\x0bUNENCRYPTED\x10\x00\x12\n\n\x06\x41\x45SCTR\x10\x01\"\xd1\x01\n\nFileHashes\x12\x0e\n\x06signer\x18\x01 \x01(\x0c\x12\x45\n\nsignatures\x18\x02 \x03(\x0b\x32\x31.pywidevine_license_protocol.FileHashes.Signature\x1al\n\tSignature\x12\x10\n\x08\x66ilename\x18\x01 \x01(\t\x12\x14\n\x0ctest_signing\x18\x02 \x01(\x08\x12\x12\n\nSHA512Hash\x18\x03 \x01(\x0c\x12\x10\n\x08main_exe\x18\x04 \x01(\x08\x12\x11\n\tsignature\x18\x05 \x01(\x0c*8\n\x0bLicenseType\x12\r\n\tSTREAMING\x10\x01\x12\x0b\n\x07OFFLINE\x10\x02\x12\r\n\tAUTOMATIC\x10\x03*\xd9\x01\n\x1aPlatformVerificationStatus\x12\x17\n\x13PLATFORM_UNVERIFIED\x10\x00\x12\x15\n\x11PLATFORM_TAMPERED\x10\x01\x12\x1e\n\x1aPLATFORM_SOFTWARE_VERIFIED\x10\x02\x12\x1e\n\x1aPLATFORM_HARDWARE_VERIFIED\x10\x03\x12\x1c\n\x18PLATFORM_NO_VERIFICATION\x10\x04\x12-\n)PLATFORM_SECURE_STORAGE_SOFTWARE_VERIFIED\x10\x05*D\n\x0fProtocolVersion\x12\x0f\n\x0bVERSION_2_0\x10\x14\x12\x0f\n\x0bVERSION_2_1\x10\x15\x12\x0f\n\x0bVERSION_2_2\x10\x16*\x86\x01\n\x12HashAlgorithmProto\x12\x1e\n\x1aHASH_ALGORITHM_UNSPECIFIED\x10\x00\x12\x18\n\x14HASH_ALGORITHM_SHA_1\x10\x01\x12\x1a\n\x16HASH_ALGORITHM_SHA_256\x10\x02\x12\x1a\n\x16HASH_ALGORITHM_SHA_384\x10\x03\x42$\n com.rlaphoenix.pywidevine.protosH\x03')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'license_protocol_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  _globals['DESCRIPTOR']._options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.rlaphoenix.pywidevine.protosH\003'
  _globals['_DRMCERTIFICATE'].fields_by_name['test_device_deprecated']._options = None
  _globals['_DRMCERTIFICATE'].fields_by_name['test_device_deprecated']._serialized_options = b'\030\001'
  _globals['_WIDEVINEPSSHDATA'].fields_by_name['algorithm']._options = None
  _globals['_WIDEVINEPSSHDATA'].fields_by_name['algorithm']._serialized_options = b'\030\001'
  _globals['_WIDEVINEPSSHDATA'].fields_by_name['provider']._options = None
  _globals['_WIDEVINEPSSHDATA'].fields_by_name['provider']._serialized_options = b'\030\001'
  _globals['_WIDEVINEPSSHDATA'].fields_by_name['track_type']._options = None
  _globals['_WIDEVINEPSSHDATA'].fields_by_name['track_type']._serialized_options = b'\030\001'
  _globals['_WIDEVINEPSSHDATA'].fields_by_name['policy']._options = None
  _globals['_WIDEVINEPSSHDATA'].fields_by_name['policy']._serialized_options = b'\030\001'
  _globals['_WIDEVINEPSSHDATA'].fields_by_name['grouped_license']._options = None
  _globals['_WIDEVINEPSSHDATA'].fields_by_name['grouped_license']._serialized_options = b'\030\001'
  _globals['_LICENSETYPE']._serialized_start=10442
  _globals['_LICENSETYPE']._serialized_end=10498
  _globals['_PLATFORMVERIFICATIONSTATUS']._serialized_start=10501
  _globals['_PLATFORMVERIFICATIONSTATUS']._serialized_end=10718
  _globals['_PROTOCOLVERSION']._serialized_start=10720
  _globals['_PROTOCOLVERSION']._serialized_end=10788
  _globals['_HASHALGORITHMPROTO']._serialized_start=10791
  _globals['_HASHALGORITHMPROTO']._serialized_end=10925
  _globals['_LICENSEIDENTIFICATION']._serialized_start=56
  _globals['_LICENSEIDENTIFICATION']._serialized_end=245
  _globals['_LICENSE']._serialized_start=248
  _globals['_LICENSE']._serialized_end=3433
  _globals['_LICENSE_POLICY']._serialized_start=764
  _globals['_LICENSE_POLICY']._serialized_end=1322
  _globals['_LICENSE_KEYCONTAINER']._serialized_start=1325
  _globals['_LICENSE_KEYCONTAINER']._serialized_end=3433
  _globals['_LICENSE_KEYCONTAINER_KEYCONTROL']._serialized_start=2128
  _globals['_LICENSE_KEYCONTAINER_KEYCONTROL']._serialized_end=2179
  _globals['_LICENSE_KEYCONTAINER_OUTPUTPROTECTION']._serialized_start=2182
  _globals['_LICENSE_KEYCONTAINER_OUTPUTPROTECTION']._serialized_end=2850
  _globals['_LICENSE_KEYCONTAINER_OUTPUTPROTECTION_HDCP']._serialized_start=2604
  _globals['_LICENSE_KEYCONTAINER_OUTPUTPROTECTION_HDCP']._serialized_end=2725
  _globals['_LICENSE_KEYCONTAINER_OUTPUTPROTECTION_CGMS']._serialized_start=2727
  _globals['_LICENSE_KEYCONTAINER_OUTPUTPROTECTION_CGMS']._serialized_end=2794
  _globals['_LICENSE_KEYCONTAINER_OUTPUTPROTECTION_HDCPSRMRULE']._serialized_start=2796
  _globals['_LICENSE_KEYCONTAINER_OUTPUTPROTECTION_HDCPSRMRULE']._serialized_end=2850
  _globals['_LICENSE_KEYCONTAINER_VIDEORESOLUTIONCONSTRAINT']._serialized_start=2853
  _globals['_LICENSE_KEYCONTAINER_VIDEORESOLUTIONCONSTRAINT']._serialized_end=3039
  _globals['_LICENSE_KEYCONTAINER_OPERATORSESSIONKEYPERMISSIONS']._serialized_start=3042
  _globals['_LICENSE_KEYCONTAINER_OPERATORSESSIONKEYPERMISSIONS']._serialized_end=3199
  _globals['_LICENSE_KEYCONTAINER_KEYTYPE']._serialized_start=3201
  _globals['_LICENSE_KEYCONTAINER_KEYTYPE']._serialized_end=3309
  _globals['_LICENSE_KEYCONTAINER_SECURITYLEVEL']._serialized_start=3311
  _globals['_LICENSE_KEYCONTAINER_SECURITYLEVEL']._serialized_end=3433
  _globals['_LICENSEREQUEST']._serialized_start=3436
  _globals['_LICENSEREQUEST']._serialized_end=5161
  _globals['_LICENSEREQUEST_CONTENTIDENTIFICATION']._serialized_start=3944
  _globals['_LICENSEREQUEST_CONTENTIDENTIFICATION']._serialized_end=5111
  _globals['_LICENSEREQUEST_CONTENTIDENTIFICATION_WIDEVINEPSSHDATA']._serialized_start=4391
  _globals['_LICENSEREQUEST_CONTENTIDENTIFICATION_WIDEVINEPSSHDATA']._serialized_end=4512
  _globals['_LICENSEREQUEST_CONTENTIDENTIFICATION_WEBMKEYID']._serialized_start=4514
  _globals['_LICENSEREQUEST_CONTENTIDENTIFICATION_WEBMKEYID']._serialized_end=4625
  _globals['_LICENSEREQUEST_CONTENTIDENTIFICATION_EXISTINGLICENSE']._serialized_start=4628
  _globals['_LICENSEREQUEST_CONTENTIDENTIFICATION_EXISTINGLICENSE']._serialized_end=4818
  _globals['_LICENSEREQUEST_CONTENTIDENTIFICATION_INITDATA']._serialized_start=4821
  _globals['_LICENSEREQUEST_CONTENTIDENTIFICATION_INITDATA']._serialized_end=5089
  _globals['_LICENSEREQUEST_CONTENTIDENTIFICATION_INITDATA_INITDATATYPE']._serialized_start=5055
  _globals['_LICENSEREQUEST_CONTENTIDENTIFICATION_INITDATA_INITDATATYPE']._serialized_end=5089
  _globals['_LICENSEREQUEST_REQUESTTYPE']._serialized_start=5113
  _globals['_LICENSEREQUEST_REQUESTTYPE']._serialized_end=5161
  _globals['_METRICDATA']._serialized_start=5164
  _globals['_METRICDATA']._serialized_end=5407
  _globals['_METRICDATA_TYPEVALUE']._serialized_start=5270
  _globals['_METRICDATA_TYPEVALUE']._serialized_end=5365
  _globals['_METRICDATA_METRICTYPE']._serialized_start=5367
  _globals['_METRICDATA_METRICTYPE']._serialized_end=5407
  _globals['_VERSIONINFO']._serialized_start=5409
  _globals['_VERSIONINFO']._serialized_end=5484
  _globals['_SIGNEDMESSAGE']._serialized_start=5487
  _globals['_SIGNEDMESSAGE']._serialized_end=6245
  _globals['_SIGNEDMESSAGE_MESSAGETYPE']._serialized_start=5924
  _globals['_SIGNEDMESSAGE_MESSAGETYPE']._serialized_end=6160
  _globals['_SIGNEDMESSAGE_SESSIONKEYTYPE']._serialized_start=6162
  _globals['_SIGNEDMESSAGE_SESSIONKEYTYPE']._serialized_end=6245
  _globals['_CLIENTIDENTIFICATION']._serialized_start=6248
  _globals['_CLIENTIDENTIFICATION']._serialized_end=8111
  _globals['_CLIENTIDENTIFICATION_NAMEVALUE']._serialized_start=6722
  _globals['_CLIENTIDENTIFICATION_NAMEVALUE']._serialized_end=6762
  _globals['_CLIENTIDENTIFICATION_CLIENTCAPABILITIES']._serialized_start=6765
  _globals['_CLIENTIDENTIFICATION_CLIENTCAPABILITIES']._serialized_end=7875
  _globals['_CLIENTIDENTIFICATION_CLIENTCAPABILITIES_HDCPVERSION']._serialized_start=7496
  _globals['_CLIENTIDENTIFICATION_CLIENTCAPABILITIES_HDCPVERSION']._serialized_end=7624
  _globals['_CLIENTIDENTIFICATION_CLIENTCAPABILITIES_CERTIFICATEKEYTYPE']._serialized_start=7626
  _globals['_CLIENTIDENTIFICATION_CLIENTCAPABILITIES_CERTIFICATEKEYTYPE']._serialized_end=7731
  _globals['_CLIENTIDENTIFICATION_CLIENTCAPABILITIES_ANALOGOUTPUTCAPABILITIES']._serialized_start=7734
  _globals['_CLIENTIDENTIFICATION_CLIENTCAPABILITIES_ANALOGOUTPUTCAPABILITIES']._serialized_end=7875
  _globals['_CLIENTIDENTIFICATION_CLIENTCREDENTIALS']._serialized_start=7877
  _globals['_CLIENTIDENTIFICATION_CLIENTCREDENTIALS']._serialized_end=7994
  _globals['_CLIENTIDENTIFICATION_TOKENTYPE']._serialized_start=7996
  _globals['_CLIENTIDENTIFICATION_TOKENTYPE']._serialized_end=8111
  _globals['_ENCRYPTEDCLIENTIDENTIFICATION']._serialized_start=8114
  _globals['_ENCRYPTEDCLIENTIDENTIFICATION']._serialized_end=8301
  _globals['_DRMCERTIFICATE']._serialized_start=8304
  _globals['_DRMCERTIFICATE']._serialized_end=9258
  _globals['_DRMCERTIFICATE_ENCRYPTIONKEY']._serialized_start=8827
  _globals['_DRMCERTIFICATE_ENCRYPTIONKEY']._serialized_end=8941
  _globals['_DRMCERTIFICATE_TYPE']._serialized_start=8943
  _globals['_DRMCERTIFICATE_TYPE']._serialized_end=9019
  _globals['_DRMCERTIFICATE_SERVICETYPE']._serialized_start=9022
  _globals['_DRMCERTIFICATE_SERVICETYPE']._serialized_end=9156
  _globals['_DRMCERTIFICATE_ALGORITHM']._serialized_start=9158
  _globals['_DRMCERTIFICATE_ALGORITHM']._serialized_end=9258
  _globals['_SIGNEDDRMCERTIFICATE']._serialized_start=9261
  _globals['_SIGNEDDRMCERTIFICATE']._serialized_end=9467
  _globals['_WIDEVINEPSSHDATA']._serialized_start=9470
  _globals['_WIDEVINEPSSHDATA']._serialized_end=10228
  _globals['_WIDEVINEPSSHDATA_ENTITLEDKEY']._serialized_start=10009
  _globals['_WIDEVINEPSSHDATA_ENTITLEDKEY']._serialized_end=10131
  _globals['_WIDEVINEPSSHDATA_TYPE']._serialized_start=10133
  _globals['_WIDEVINEPSSHDATA_TYPE']._serialized_end=10186
  _globals['_WIDEVINEPSSHDATA_ALGORITHM']._serialized_start=10188
  _globals['_WIDEVINEPSSHDATA_ALGORITHM']._serialized_end=10228
  _globals['_FILEHASHES']._serialized_start=10231
  _globals['_FILEHASHES']._serialized_end=10440
  _globals['_FILEHASHES_SIGNATURE']._serialized_start=10332
  _globals['_FILEHASHES_SIGNATURE']._serialized_end=10440
# @@protoc_insertion_point(module_scope)
