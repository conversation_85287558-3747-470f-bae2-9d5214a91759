/*!
   JW Player version 8.27.1
   Copyright (c) 2023, JW Player, All Rights Reserved
   This source code and its use and distribution is subject to the terms
   and conditions of the applicable license agreement.
   https://www.jwplayer.com/tos/
   This product includes portions of other software. For the full text of licenses, see
   https:/js/notice.txt
*/
"use strict";(self.webpackChunkjwplayer=self.webpackChunkjwplayer||[]).push([[250],{8377:(e,t,r)=>{r.d(t,{M:()=>i,_:()=>n});const i=function(e,t){let r;const i=e.kind||"cc";return r=e.default||e.defaulttrack?"default":e._id||e.file||i+t,r},n=function(e,t){let r=e.label||e.name||e.language;return r||(r="CC",(t+=1)>1&&(r+=` [${t}]`)),{label:r,unknownCount:t}}},6103:(e,t,r)=>{r.d(t,{VS:()=>T,xl:()=>m});var i=r(7477),n=r(2894),a=r(6886),s=r(7941),o=r(7387),c=r(2957),l=r(4446);const d=e=>{throw new l.rG(null,e)};const u=function(e){return e.map((e=>new i.Z(e.begin,e.end,e.text)))},h=function(e,t,i,a){let h,m,T=e.responseXML?e.responseXML.firstChild:null;if(T)for("xml"===(0,s.r1)(T)&&(T=T.nextSibling);T&&T.nodeType===T.COMMENT_NODE;)T=T.nextSibling;try{if(T&&"tt"===(0,s.r1)(T)){if(!e.responseXML)throw new Error("Empty XML response");h=function(e){e||d(306007);const t=[];let r=e.getElementsByTagName("p"),i=30;const n=e.getElementsByTagName("tt");if(null!=n&&n[0]){const e=parseFloat(n[0].getAttribute("ttp:frameRate")||"");isNaN(e)||(i=e)}r||d(306005),r.length||(r=e.getElementsByTagName("tt:p"),r.length||(r=e.getElementsByTagName("tts:p")));for(let n=0;n<r.length;n++){const a=r[n],s=a.getElementsByTagName("br");for(let t=0;t<s.length;t++){const r=s[t];null!=r&&r.parentNode&&r.parentNode.replaceChild(e.createTextNode("\r\n"),r)}const o=a.innerHTML||a.textContent||a.text||"",l=(0,c.fy)(o).replace(/>\s+</g,"><").replace(/(<\/?)tts?:/g,"$1").replace(/<br.*?\/>/g,"\r\n");if(l){const e=a.getAttribute("begin")||"",r=a.getAttribute("dur")||"",n=a.getAttribute("end")||"",s={begin:(0,c.m9)(e,i),text:l};n?s.end=(0,c.m9)(n,i):r&&(s.end=(s.begin||0)+(0,c.m9)(r,i)),t.push(s)}}return t.length||d(306005),t}(e.responseXML),m=u(h),delete t.xhr,i(m)}else{const s=e.responseText;s.indexOf("WEBVTT")>=0?r.e(347).then(function(e){return r(2776).default}.bind(null,r)).catch((0,n.Jt)(301131)).then((e=>{const r=new e(window);m=[],r.oncue=function(e){m.push(e)},r.onflush=function(){delete t.xhr,i(m)},r.parse(s)})).catch((e=>{delete t.xhr,a((0,l.Mm)(null,l.Y7,e))})):(h=(0,o.Z)(s),m=u(h),delete t.xhr,i(m))}}catch(e){delete t.xhr,a((0,l.Mm)(null,l.Y7,e))}},m=function(e,t,r){e.xhr=(0,a.h)(e.file,(function(i){h(i,e,t,r)}),((e,t,i,n)=>{r((0,l.l9)(n,l.Y7))}))},T=function(e){e&&e.forEach((e=>{const t=e.xhr;t&&(t.onload=null,t.onreadystatechange=null,t.onerror=null,"abort"in t&&t.abort()),delete e.xhr}))}},7387:(e,t,r)=>{r.d(t,{Z:()=>a});var i=r(2957);const n=e=>{const t={};let r=e.split("\r\n");1===r.length&&(r=e.split("\n"));let n=1;if(r[0].indexOf(" --\x3e ")>0&&(n=0),r.length>n+1&&r[n+1]){const e=r[n],a=e.indexOf(" --\x3e ");a>0&&(t.begin=(0,i.m9)(e.substr(0,a)),t.end=(0,i.m9)(e.substr(a+5)),t.text=r.slice(n+1).join("\r\n"))}return t};function a(e){const t=[];let r=(e=(0,i.fy)(e)).split("\r\n\r\n");1===r.length&&(r=e.split("\n\n"));for(let e=0;e<r.length;e++){if("WEBVTT"===r[e])continue;const i=n(r[e]);i.text&&t.push(i)}return t}},7477:(e,t,r)=>{r.d(t,{Z:()=>a});let i=window.VTTCue;const n=e=>{if("string"!=typeof e)return!1;return!!{start:!0,middle:!0,end:!0,left:!0,right:!0}[e.toLowerCase()]&&e.toLowerCase()};if(!i){const e="auto";i=function(t,r,i){const a=this;a.hasBeenReset=!1;let s="",o=!1,c=t,l=r,d=i,u=null,h="",m=!0,T=e,f="start",g=e,p=100,k="middle";Object.defineProperty(a,"id",{enumerable:!0,get:()=>s,set(e){s=`${e}`}}),Object.defineProperty(a,"pauseOnExit",{enumerable:!0,get:()=>o,set(e){o=Boolean(e)}}),Object.defineProperty(a,"startTime",{enumerable:!0,get:()=>c,set(e){if("number"!=typeof e)throw new TypeError("Start time must be set to a number.");c=e,this.hasBeenReset=!0}}),Object.defineProperty(a,"endTime",{enumerable:!0,get:()=>l,set(e){if("number"!=typeof e)throw new TypeError("End time must be set to a number.");l=e,this.hasBeenReset=!0}}),Object.defineProperty(a,"text",{enumerable:!0,get:()=>d,set(e){d=`${e}`,this.hasBeenReset=!0}}),Object.defineProperty(a,"region",{enumerable:!0,get:()=>u,set(e){u=e,this.hasBeenReset=!0}}),Object.defineProperty(a,"vertical",{enumerable:!0,get:()=>h,set(e){const t=(e=>"string"==typeof e&&(!!{"":!0,lr:!0,rl:!0}[e.toLowerCase()]&&e.toLowerCase()))(e);if(!1===t)throw new SyntaxError("An invalid or illegal string was specified.");h=t,this.hasBeenReset=!0}}),Object.defineProperty(a,"snapToLines",{enumerable:!0,get:()=>m,set(e){m=Boolean(e),this.hasBeenReset=!0}}),Object.defineProperty(a,"line",{enumerable:!0,get:()=>T,set(t){if("number"!=typeof t&&t!==e)throw new SyntaxError("An invalid number or illegal string was specified.");T=t,this.hasBeenReset=!0}}),Object.defineProperty(a,"lineAlign",{enumerable:!0,get:()=>f,set(e){const t=n(e);if(!t)throw new SyntaxError("An invalid or illegal string was specified.");f=t,this.hasBeenReset=!0}}),Object.defineProperty(a,"position",{enumerable:!0,get:()=>g,set(e){if(e<0||e>100)throw new Error("Position must be between 0 and 100.");g=e,this.hasBeenReset=!0}}),Object.defineProperty(a,"size",{enumerable:!0,get:()=>p,set(e){if(e<0||e>100)throw new Error("Size must be between 0 and 100.");p=e,this.hasBeenReset=!0}}),Object.defineProperty(a,"align",{enumerable:!0,get:()=>k,set(e){const t=n(e);if(!t)throw new SyntaxError("An invalid or illegal string was specified.");k=t,this.hasBeenReset=!0}}),a.displayState=void 0},i.prototype.getCueAsHTML=function(){return window.WebVTT.convertCueToDOMTree(window,this.text)}}const a=i},4506:(e,t,r)=>{r.d(t,{Z:()=>i});const i=e=>({bitrate:e.bitrate,label:e.label,width:e.width,height:e.height})},3328:(e,t,r)=>{r.d(t,{Z:()=>x});var i=r(6103),n=r(8377);const a={TIT2:"title",TT2:"title",WXXX:"url",TPE1:"artist",TP1:"artist",TALB:"album",TAL:"album"},s=(e,t)=>{const r=e.length;let i,n,a,s="",o=t||0;for(;o<r;)if(i=e[o++],0!==i&&3!==i)switch(i>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:s+=String.fromCharCode(i);break;case 12:case 13:n=e[o++],s+=String.fromCharCode((31&i)<<6|63&n);break;case 14:n=e[o++],a=e[o++],s+=String.fromCharCode((15&i)<<12|(63&n)<<6|(63&a)<<0)}return s},o=e=>{const t=(e=>{let t="0x";for(let r=0;r<e.length;r++)e[r]<16&&(t+="0"),t+=e[r].toString(16);return parseInt(t,16)})(e);return 127&t|(32512&t)>>1|(8323072&t)>>2|(2130706432&t)>>3},c=e=>{const t={};if(!("value"in e)&&"data"in e&&e.data instanceof ArrayBuffer){const t=new Uint8Array(e.data);let r=t.length;e={value:{key:"",data:""}};let i=10;for(;i<14&&i<t.length&&0!==t[i];)e.value.key+=String.fromCharCode(t[i]),i++;let n=19,a=t[n];3!==a&&0!==a||(a=t[++n],r--);let c=0;if(1!==a&&2!==a)for(let e=n+1;e<r;e++)if(0===t[e]){c=e-n;break}if(c>0){const r=s(t.subarray(n,n+=c),0);if("PRIV"===e.value.key){if("com.apple.streaming.transportStreamTimestamp"===r){const r=1&o(t.subarray(n,n+=4)),i=o(t.subarray(n,n+=4))+(r?4294967296:0);e.value.data=i}else e.value.data=s(t,n+1);e.value.info=r}else e.value.info=r,e.value.data=s(t,n+1)}else{const r=t[n];e.value.data=1===r||2===r?((e,t)=>{const r=e.length-1;let i="",n=t||0;for(;n<r;)254===e[n]&&255===e[n+1]||(i+=String.fromCharCode((e[n]<<8)+e[n+1])),n+=2;return i})(t,n+1):s(t,n+1)}}if(function(e,t){if(null==e)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(Object(e),t)}(a,e.value.key)&&(t[a[e.value.key]]=e.value.data),e.value.info){let r=t[e.value.key];r!==Object(r)&&(r={},t[e.value.key]=r),r[e.value.info]=e.value.data}else t[e.value.key]=e.value.data;return t};var l=r(8348),d=r(1643),u=r(6042);const h=(e,t,r)=>{null!=t&&t.length&&(0,u.S6)(t,(function(t){const i=t._id||"";if(r&&(t._id=void 0),!l.Browser.ie&&!l.Browser.safari||!e||!/^(native|subtitle|cc)/.test(i)){if(l.Browser.ie&&"disabled"===t.mode||(t.mode="disabled",t.mode="hidden"),t.cues)for(let e=t.cues.length;e--;)t.removeCue(t.cues[e]);t.embedded||(t.mode="disabled"),t.inuse=!1}}))},m=e=>/^native(?:captions|subtitles)/.test(e),T=e=>"captions"===e||"subtitles"===e,f=function(e){const t=e.target,{activeCues:r,cues:i}=t,n=t._id,a=this._cues,s=this._activeCues;if(null!=i&&i.length){const e=a[n];a[n]=Array.prototype.slice.call(i),this.parseNativeID3Cues(i,e)}else delete a[n];if(null!=r&&r.length){const e=s[n],t=s[n]=Array.prototype.slice.call(r);this.triggerActiveCues(t,e)}else delete s[n]},g=(e,t,r)=>{if(l.Browser.ie){let i=r;(e||"metadata"===t.kind)&&(i=new window.TextTrackCue(r.startTime,r.endTime,r.text),r.value&&(i.value=r.value)),((e,t)=>{const r=[],i=e.mode;e.mode="hidden";const n=e.cues;if(n)for(let i=n.length-1;i>=0&&n[i].startTime>t.startTime;i--)r.unshift(n[i]),e.removeCue(n[i]);try{e.addCue(t),r.forEach((t=>e.addCue(t)))}catch(e){console.error(e)}e.mode=i})(t,i)}else try{t.addCue(r)}catch(e){console.error(e)}},p=function(e){const t=this._textTracks,r=this._tracksById;if(e.length>t.length)return!0;for(let t=0;t<e.length;t++){const i=e[t];if(!i._id||!r[i._id])return!0}return!1},k=function(){const e=this.video.textTracks,t=(0,u.hX)(e,(function(e){return(e.inuse||!e._id)&&T(e.kind)}));if(!this._textTracks||p.call(this,t))return void this.setTextTracks(e);let r=-1;for(let e=0;e<this._textTracks.length;e++)if("showing"===this._textTracks[e].mode){r=e;break}r!==this._currentTextTrackIndex&&this.setSubtitlesTrack(r+1)},y=function(e){const t=e.track;null!=t&&t._id||this.setTextTracks(this.video.textTracks)},v=(e,t)=>e.startTime===t.startTime&&e.endTime===t.endTime&&e.text===t.text&&e.data===t.data&&JSON.stringify(e.value)===JSON.stringify(t.value),b=e=>{const t=c(e);return{metadataType:"id3",metadataTime:e.startTime,metadata:t}},x={_itemTracks:null,_textTracks:null,_currentTextTrackIndex:-1,_tracksById:null,_cuesByTrackId:null,_cachedVTTCues:null,_metaCuesByTextTime:null,_unknownCount:0,_activeCues:null,_cues:null,textTrackChangeHandler:null,addTrackHandler:null,cueChangeHandler:null,renderNatively:!1,_initTextTracks(){this._textTracks=[],this._tracksById={},this._metaCuesByTextTime={},this._cuesByTrackId={},this._cachedVTTCues={},this._cues={},this._activeCues={},this._unknownCount=0},addTracksListener(e,t,r){e&&(this.removeTracksListener(e,t,r),this.instreamMode||(e.addEventListener?e.addEventListener(t,r):e[`on${t}`]=r))},removeTracksListener(e,t,r){e&&(e.removeEventListener&&r?e.removeEventListener(t,r):e[`on${t}`]=null)},clearTracks(){(0,i.VS)(this._itemTracks);const{_tracksById:e}=this;if(e&&Object.keys(e).forEach((t=>{if(0===t.indexOf("nativemetadata")){const r=e[t];this.cueChangeHandler&&r.removeEventListener("cuechange",this.cueChangeHandler),h(this.renderNatively,[r],!0)}})),this._itemTracks=null,this._textTracks=null,this._tracksById=null,this._cuesByTrackId=null,this._metaCuesByTextTime=null,this._unknownCount=0,this._currentTextTrackIndex=-1,this._activeCues={},this._cues={},this.renderNatively){const e=this.video.textTracks;this.textTrackChangeHandler&&this.removeTracksListener(e,"change",this.textTrackChangeHandler),h(this.renderNatively,e,!0)}},clearMetaCues(){const{_tracksById:e,_cachedVTTCues:t}=this;e&&t&&Object.keys(e).forEach((r=>{if(0===r.indexOf("nativemetadata")){const i=e[r];h(this.renderNatively,[i],!1),i.mode="hidden",i.inuse=!0,i._id&&(t[i._id]={})}}))},clearCueData(e){const t=this._cachedVTTCues;null!=t&&t[e]&&(t[e]={},this._tracksById&&(this._tracksById[e].data=[]))},disableTextTrack(){const e=this.getCurrentTextTrack();if(e){e.mode="disabled";const t=e._id;(t&&m(t)||this.renderNatively&&l.OS.iOS)&&(e.mode="hidden")}},enableTextTrack(){const e=this.getCurrentTextTrack();e&&(e.mode="showing")},getCurrentTextTrack(){if(this._textTracks)return this._textTracks[this._currentTextTrackIndex]},getSubtitlesTrack(){return this._currentTextTrackIndex},addTextTracks(e){var t;const r=[];return e?(this._textTracks||this._initTextTracks(),e.forEach((e=>{if(e.includedInManifest||e.kind&&!T(e.kind))return;const t=this._createTrack(e);this._addTrackToList(t),r.push(t),e.file&&(e.data=[],(0,i.xl)(e,(e=>{t.sideloaded=!0,this.addVTTCuesToTrack(t,e)}),(e=>{this.trigger(d.cM,e)})))})),null!=this&&null!=(t=this._textTracks)&&t.length&&this.trigger(d.jt,{tracks:this._textTracks}),r):r},setTextTracks(e){var t;if(this._currentTextTrackIndex=-1,e){if(this._textTracks){const e=this._tracksById;this._activeCues={},this._cues={},this._unknownCount=0,this._textTracks=this._textTracks.filter((t=>{const r=t._id;return this.renderNatively&&r&&m(r)?(delete e[r],!1):(t.name&&0===t.name.indexOf("CC")&&this._unknownCount++,0===r.indexOf("nativemetadata")&&"com.apple.streaming"===t.inBandMetadataTrackDispatchType&&delete e[r],!0)}),this)}else this._initTextTracks();if(e.length){let t=0;const r=e.length,i=this._tracksById,a=this._cuesByTrackId;for(;t<r;t++){const r=e[t];let s=r._id||"";if(!s){if(!1===r.inuse&&T(r.kind)&&this.renderNatively){r._id=`native${r.kind}${t}`;continue}if(T(r.kind)||"metadata"===r.kind){if(s=r._id=`native${r.kind}${t}`,!r.label&&"captions"===r.kind){const e=(0,n._)(r,this._unknownCount);r.name=e.label,this._unknownCount=e.unknownCount}}else s=r._id=(0,n.M)(r,this._textTracks?this._textTracks.length:0);if(i[s])continue;r.inuse=!0}if(r.inuse&&!i[s])if("metadata"===r.kind){r.mode="hidden";const e=this.cueChangeHandler=this.cueChangeHandler||f.bind(this);r.removeEventListener("cuechange",e),r.addEventListener("cuechange",e),i[s]=r}else if(T(r.kind)){const e=r.mode;let t;if(r.mode="hidden",(!r.cues||!r.cues.length)&&r.embedded)continue;if(("disabled"!==e||m(s))&&(r.mode=e),a[s]&&!a[s].loaded){const i=a[s].cues;for(;t=i.shift();)g(this.renderNatively,r,t);r.mode=e,a[s].loaded=!0}this._addTrackToList(r)}}}this.renderNatively&&this.addTrackListeners(e),null!=this&&null!=(t=this._textTracks)&&t.length&&this.trigger(d.jt,{tracks:this._textTracks})}},addTrackListeners(e){let t=this.textTrackChangeHandler=this.textTrackChangeHandler||k.bind(this);this.removeTracksListener(e,"change",t),this.addTracksListener(e,"change",t),(l.Browser.edge&&l.Browser.ie||l.Browser.firefox)&&(t=this.addTrackHandler=this.addTrackHandler||y.bind(this),this.removeTracksListener(e,"addtrack",t),this.addTracksListener(e,"addtrack",t))},setupSideloadedTracks(e){if(!this.renderNatively)return;const t=(e=e||null)===this._itemTracks;t||(0,i.VS)(this._itemTracks),this._itemTracks=e,e&&(t||(this.disableTextTrack(),this._clearSideloadedTextTracks(),this.addTextTracks(e)))},setSubtitlesTrack(e){if(!this.renderNatively)return void(this.setCurrentSubtitleTrack&&this.setCurrentSubtitleTrack(e-1));if(!this._textTracks)return;if(0===e&&this._textTracks.forEach((e=>{e.mode=e.embedded?"hidden":"disabled"})),this._currentTextTrackIndex===e-1)return;this.disableTextTrack(),this._currentTextTrackIndex=e-1;const t=this.getCurrentTextTrack();t&&(t.mode="showing"),this.trigger(d.UF,{currentTrack:this._currentTextTrackIndex+1,tracks:this._textTracks})},createCue:(e,t,r)=>new(window.VTTCue||window.TextTrackCue)(e,Math.max(t||0,e+.25),r),addVTTCue(e,t){this._tracksById||this._initTextTracks();const r=e.track?e.track:`native${e.type}`;let i=this._tracksById[r];const n="captions"===e.type?"CC":"ID3 Metadata",a=e.cue;if(!i){const t={kind:e.type,_id:r,label:n,default:!1};this.renderNatively||"metadata"===t.kind?(i=this._createTrack(t),i.embedded=!0,this.setTextTracks(this.video.textTracks)):i=this.addTextTracks([t])[0]}if(this._cacheVTTCue(i,a,t)){const e=this.renderNatively||"metadata"===i.kind;return e?g(e,i,a):i.data.push(a),a}return null},addVTTCuesToTrack(e,t){var r,i;if(!this.renderNatively)return;const n=e._id,a=this._tracksById;let s=this._cuesByTrackId;const o=a[n];if(!o)return s||(s=this._cuesByTrackId={}),void(s[n]={cues:t,loaded:!1});if(null!=(r=s)&&null!=(i=r[n])&&i.loaded)return;let c;for(s[n]={cues:t,loaded:!0};c=t.shift();)g(this.renderNatively,o,c)},parseNativeID3Cues(e,t){const r=e[e.length-1];if(t&&t.length===e.length&&(r._parsed||v(t[t.length-1],r)))return;const i=[],n=[];let a=-1,s=-1,o=-1;for(let t=0;t<e.length;t++){const r=e[t];if(!r._extended&&Boolean(r.data||r.value)){if(r.startTime!==s||null===r.endTime){o=s,s=r.startTime;const e=i[a];i[++a]=[],n[a]=[];if(e&&s-o>0)for(let t=0;t<e.length;t++){const r=e[t];r.endTime=s,r._extended=!0}}i[a].push(r),r._parsed||(n[a].push(r),r.endTime-s<.25&&(r.endTime=s+.25),r._parsed=!0)}}for(let e=0;e<n.length;e++)n[e].length&&n[e].forEach((e=>{const t=b(e);this.trigger(d.O1,t)}))},triggerActiveCues(e,t){const r=e.filter((e=>{if(null!=t&&t.some((t=>v(e,t))))return!1;if(e.data)return!0;const r=e.text?(e=>{let t;try{t=JSON.parse(e.text)}catch(e){return null}const r={metadataType:t.metadataType,metadataTime:e.startTime,metadata:t};return t.programDateTime&&(r.programDateTime=t.programDateTime),r})(e):null;if(r)"emsg"===r.metadataType&&(r.metadata=r.metadata||{},r.metadata.messageData=e.value),this.trigger(d.rx,r);else if(e.value)return!0;return!1}));r.length&&r.forEach((e=>{const t=b(e);this.trigger(d.rx,t)}))},ensureMetaTracksActive(){const e=this.video.textTracks,t=e.length;for(let r=0;r<t;r++){const t=e[r];"metadata"===t.kind&&"disabled"===t.mode&&(t.mode="hidden")}},_cacheVTTCue(e,t,r){const i=e.kind,n=e._id,a=this._cachedVTTCues;a[n]||(a[n]={});const s=a[n];let o;switch(i){case"captions":case"subtitles":{o=r||Math.floor(20*t.startTime);const e=`_${t.line||"auto"}`,i=Math.floor(20*t.endTime),n=s[o+e]||s[o+1+e]||s[o-1+e];return!(n&&Math.abs(n-i)<=1)&&(s[o+e]=i,!0)}case"metadata":{const e=t.data?new Uint8Array(t.data).join(""):t.text;return o=r||t.startTime+e,s[o]?!1:(s[o]=t.endTime,!0)}default:return!1}},_addTrackToList(e){this._textTracks.push(e),this._tracksById[e._id]=e},_createTrack(e){let t;const r=(0,n._)(e,this._unknownCount),i=r.label;if(this._unknownCount=r.unknownCount,this.renderNatively||"metadata"===e.kind){const r=this.video.textTracks;t=(0,u._e)(r,{label:i}),t||(t=this.video.addTextTrack(e.kind,i,e.language||"")),t.default=e.default,t.mode="disabled",t.inuse=!0}else t=e,t.data=t.data||[];return t._id||(t._id=(0,n.M)(e,this._textTracks?this._textTracks.length:0)),t},_clearSideloadedTextTracks(){if(!this._textTracks)return;const e=this._textTracks.filter((e=>e.embedded||"subs"===e.groupid));this._initTextTracks();const t=this._tracksById;e.forEach((e=>{t[e._id]=e})),this._textTracks=e}}},9601:(e,t,r)=>{r.d(t,{E:()=>n,Z:()=>a});var i=r(4446);const n=e=>e>=400&&e<600?e:6;function a(e,t,r){let a=e+1e3,s=i.ul;return t>0?(403===t&&(s=i.H4),a+=n(t)):"http:"===`${r}`.substring(0,5)&&"https:"===document.location.protocol?a+=12:0===t&&(a+=11),{code:a,key:s}}},5099:(e,t,r)=>{r.d(t,{Z:()=>n});const i=(e,t,r)=>{const i=new Error(r);return i.name=e,i.code=t,i};function n(e){return new Promise((function(t,r){if(e.paused)return r(i("NotAllowedError",0,"play() failed."));let n;const a=function(e){if(n(),"playing"!==e.type){const t=`The play() request was interrupted by a "${e.type}" event.`;return"error"===e.type?r(i("NotSupportedError",9,t)):r(i("AbortError",20,t))}t()},s=function(){e.addEventListener("playing",a),e.addEventListener("abort",a),e.addEventListener("error",a),e.addEventListener("pause",a)};n=function(){e.removeEventListener("play",s),e.removeEventListener("playing",a),e.removeEventListener("pause",a),e.removeEventListener("abort",a),e.removeEventListener("error",a)},e.addEventListener("play",s)}))}},686:(e,t,r)=>{r.d(t,{s:()=>n,v:()=>a});const i=e=>void 0===e?120:Math.max(e,0),n=(e,t)=>e!==1/0&&Math.abs(e)>=Math.max(i(t),0),a=(e,t)=>{let r="VOD";return e===1/0?r="LIVE":e<0&&(r=n(e,i(t))?"DVR":"LIVE"),r}},3949:(e,t,r)=>{r.d(t,{Z:()=>s});var i=r(8348),n=r(974),a=r(9974);const s={container:null,volume(e){this.video.volume=Math.min(Math.max(0,e/100),1)},mute(e){this.video.muted=Boolean(e),this.video.muted||this.video.removeAttribute("muted")},resize(e,t,r){const{video:a}=this,{videoWidth:s,videoHeight:o}=a;if(!(e&&t&&s&&o))return;const c={objectFit:"",width:"",height:""},l=e/t,d=s/o;if("uniform"===r){let i;i=l>d?e-e/(l/d):t-t/(d/l),i<6&&(c.objectFit="fill",r="exactfit")}if(i.Browser.ie||i.OS.iOS&&(i.OS.version.major||0)<9||i.Browser.androidNative)if("uniform"!==r){c.objectFit="contain";let i=1,u=1;"none"===r?i=u=l>d?Math.ceil(100*o/t)/100:Math.ceil(100*s/e)/100:"fill"===r?i=u=l>d?l/d:d/l:"exactfit"===r&&(l>d?(i=l/d,u=1):(i=1,u=d/l)),(0,n.vs)(a,`matrix(${i.toFixed(2)}, 0, 0, ${u.toFixed(2)}, 0, 0)`)}else c.top=c.left=c.margin="",(0,n.vs)(a,"");(0,n.oB)(a,c)},getContainer(){return this.container},setContainer(e){this.container=e,this.video.parentNode!==e&&e.appendChild(this.video)},removeFromContainer(){const{container:e,video:t}=this;this.container=null,e&&e===t.parentNode&&e.removeChild(t)},remove(){this.stop(),this.destroy(),this.removeFromContainer()},atEdgeOfLiveStream(){if(!this.isLive())return!1;return(0,a.Z)(this.video.buffered)-this.video.currentTime<=2}}},186:(e,t,r)=>{r.d(t,{Z:()=>i});const i={_eventsOn(){},_eventsOff(){},attachMedia(){this._eventsOn()},detachMedia(){return this._eventsOff()}}},8702:(e,t,r)=>{r.d(t,{Z:()=>s});var i=r(1643),n=r(1261),a=r(5678);const s={canplay(){this.renderNatively&&this.setTextTracks(this.video.textTracks),this.trigger(i.Jl)},play(){this.stallTime=-1,this.video.paused||this.state===i._5||this.state===i.r0||this.setState(i.ik)},loadedmetadata(){const e={metadataType:"media",duration:this.getDuration(),height:this.video.videoHeight,width:this.video.videoWidth,seekRange:this.getSeekRange()},t=this.drmUsed;t&&(e.drm=t),this.trigger(i.rx,e)},timeupdate(){const e=this.video.currentTime,t=this.getCurrentTime(),r=this.getDuration();if(isNaN(r))return;this.seeking||this.video.paused||this.state!==i.nQ&&this.state!==i.ik||this.stallTime===e||(this.stallTime=-1,this.setState(i.r0),this.trigger(i.Gj));const n={position:t,duration:r,currentTime:e,seekRange:this.getSeekRange(),metadata:{currentTime:e},absolutePosition:(0,a.e)(this)},s=this.getLiveLatency();if(null!==s&&(n.latency=s,this.getTargetLatency)){const e=this.getTargetLatency();null!==e&&(n.targetLatency=e)}(this.state===i.r0||this.seeking&&this.state!==i.bc)&&this.trigger(i.R2,n)},click(e){this.trigger(i.ot,e)},volumechange(){const e=this.video;this.trigger(i.yH,{volume:Math.round(100*e.volume)}),this.trigger(i.gy,{mute:e.muted})},seeking(){if(this.state===i.ik){const e=this.video.buffered.length?this.video.buffered.start(0):-1;if(this.video.currentTime===e)return}else if(this.state===i.bc)return;this.seeking=!0},seeked(){this.seeking&&(this.seeking=!1,this.trigger(i.aQ))},playing(){-1===this.stallTime&&this.setState(i.r0),this.trigger(i.Gj)},pause(){this.state!==i.xQ&&(this.video.ended||this.video.error||this.video.currentTime!==this.video.duration&&this.setState(i._5))},progress(){const e=this.getDuration();if(e<=0||e===1/0)return;const t=this.video.buffered;if(!t||0===t.length)return;const r=(0,n.v)(t.end(t.length-1)/e,0,1);this.trigger(i.uT,{bufferPercent:100*r,position:this.getCurrentTime(),duration:e,currentTime:this.video.currentTime,seekRange:this.getSeekRange(),absolutePosition:(0,a.e)(this)})},ratechange(){this.trigger(i.TJ,{playbackRate:this.video.playbackRate})},ended(){this.state!==i.bc&&this.state!==i.xQ&&this.trigger(i.Ms)}}},5678:(e,t,r)=>{r.d(t,{e:()=>i});const i=e=>{var t;const r=1e3*(null==e||null==(t=e.video)?void 0:t.currentTime);return null!=e&&e.startDateTime&&r?new Date(e.startDateTime+r):null}},9974:(e,t,r)=>{function i(e){return e&&e.length?e.end(e.length-1):0}r.d(t,{Z:()=>i})},3343:(e,t,r)=>{r.d(t,{q:()=>s});var i=r(6042),n=r(1643);const a=(e,t)=>{const r=e[t];return(0,i.xV)(r)&&r>=0?r:null},s=function(e,t,r){const s=((e,t,r)=>{let n,s;if(n=(0,i.xV)(r.startPTS)?a(r,"startPTS"):a(r,"start"),null===n)return null;switch(e){case"PROGRAM-DATE-TIME":return s="program-date-time",{metadataType:s,programDateTime:t,start:n,end:n+a(r,"duration")};case"EXT-X-DATERANGE":{const a={},o=t.split(",").map((e=>{const t=e.split("="),r=t[0],i=(t[1]||"").replace(/^"|"$/g,"");return a[r]=i,{name:r,value:i}})),c=a["START-DATE"];if(!c)return null;const l=a["END-DATE"];let d=n;if((0,i.xV)(r.programDateTime)&&(d+=(new Date(c).getTime()-new Date(r.programDateTime).getTime())/1e3),isNaN(d))return null;let u=parseFloat(a["PLANNED-DURATION"]||a.DURATION)||0;return!u&&l&&(u=(new Date(l).getTime()-new Date(c).getTime())/1e3),s="date-range",{metadataType:"date-range",tag:e,content:t,attributes:o,start:d,end:d+u,startDate:c,endDate:l,duration:u}}case"EXT-X-CUE-IN":case"EXT-X-CUE-OUT":return s="scte-35",{metadataType:s,tag:e,content:t,start:n,end:n+(parseFloat(t)||0)};case"DISCONTINUITY":{const i=n+a(r,"duration");let o;return"cc"in r&&(o=r.cc),s="discontinuity",{metadataType:s,tag:e,discontinuitySequence:o,PTS:t,start:n,end:i}}default:return null}})(e,t,r);if(s){if(!(0,i.xV)(s.start))return;const a=this.createCue(s.start,s.end,JSON.stringify(s)),o=`${r.sn}_${e}_${t}`;if(this.addVTTCue({type:"metadata",cue:a},o)){const e=s.metadataType;delete s.metadataType,this.trigger(n.O1,{metadataType:e,metadata:s})}}}},9181:(e,t,r)=>{r.r(t),r.d(t,{default:()=>G});var i=r(5140),n=r(1643),a=r(4506),s=r(8348),o=r(1628),c=r(8702),l=r(3949),d=r(186),u=r(686),h=r(974),m=r(2799),T=r(6528),f=r(328),g=r(3328),p=r(9974),k=r(5099),y=r(6042),v=r(5004),b=r(4446),x=r(1384);const w=324e3,_=window.clearTimeout,E=function(){},C=(e,t)=>{Object.keys(e).forEach((r=>{t.removeEventListener(r,e[r])}))},L=function(e,t,r){const i=this;i.state=n.bc,i.seeking=!1,i.currentTime=-1,i.retries=0,i.maxRetries=3,i.muteToggle=s.OS.iOS||s.Browser.safari;const T=t.loadAndParseHlsMetadata;i.loadAndParseHlsMetadata=void 0===T||Boolean(T);let L=t.minDvrWindow;const S=r,D={level:{}};let N,B=!1,O=0,R=null,M=null,I=-1,A=E,P=null,j=-1,V=-1,H=!1,U=null,X=!1,Z=null,F=null,$=0;this.video=S,this.supportsPlaybackRate=!0,this.startDateTime=0;const q=()=>{if(!(S.readyState<2&&0===S.buffered.length))return 0===S.videoHeight},W=function(){if(i.muteToggle&&S.muted){const e=q();if(void 0===e)return;const r=!S.paused;S.muted=i.muteToggle=!1,e?S.muted=t.mute:(S.muted=!0,r&&S.paused&&S.play())}},G=null!==t.liveTimeout?1e3*t.liveTimeout:3e4,Q=()=>{_(V),V=-1},K=function(){if(0===G)return;const e=(0,p.Z)(S.buffered);i.isLive()&&e&&U===e?-1===V&&(V=window.setTimeout((function(){H=!0,H&&i.atEdgeOfLiveStream()&&i.trigger(n.Ew,new b.rG(b.Sp,220001))}),G)):(Q(),H=!1),U=e},Y=e=>{M=e},J=function(){const e=q();if(void 0!==e){const t=e?"audio":"video";i.trigger(n.oZ,{mediaType:t})}},z=function(){const e=D.level;if(e.width!==S.videoWidth||e.height!==S.videoHeight){if(!S.videoWidth&&!q()||-1===I)return;i.ensureMetaTracksActive(),e.width=S.videoWidth,e.height=S.videoHeight,J(),D.reason=D.reason||"auto";const t="hls"===N[I].type?"auto":"manual";e.index=I,e.label=N[I].label,i.trigger(n.ug,{reason:D.reason,mode:t,bitrate:0,level:{width:e.width,height:e.height,index:e.index,label:e.label}}),D.reason=""}},ee=function(e){null!=S&&S.audioTracks&&P&&e>-1&&e<S.audioTracks.length&&e!==j&&(S.audioTracks[j].enabled=!1,j=e,S.audioTracks[j].enabled=!0,i.trigger(n._B,{currentTrack:j,tracks:P}))},te=()=>{let e=-1;const t=S.audioTracks;for(let r=0;r<t.length;r++)if(t[r].enabled){e=r;break}ee(e)},re=function(e){const t=i.getSeekRange();return i.isLive()&&(0,u.s)(t.end-t.start,L)?Math.min(0,e-t.end):e},ie=function(){S&&(i.disableTextTrack(),S.removeAttribute("preload"),S.removeAttribute("src"),(0,m.EU)(S),(0,h.oB)(S,{objectFit:""}),I=-1,!s.Browser.msie&&"load"in S&&S.load())},ne={progress(){c.Z.progress.call(i),q()&&W(),K()},timeupdate(){i.currentTime>=0&&(i.retries=0),i.currentTime=S.currentTime,(0,x.If)()&&M!==S.currentTime&&Y(S.currentTime),c.Z.timeupdate.call(i),K(),s.Browser.ie&&z()},resize:z,ended(){I=-1,Q(),c.Z.ended.call(i)},loadedmetadata(){let e=i.getDuration();X&&e===1/0&&(e=0);const t={metadataType:"media",duration:e,height:S.videoHeight,width:S.videoWidth,seekRange:i.getSeekRange()};i.fairplay&&(t.drm="fairplay"),i.trigger(n.rx,t),z()},durationchange(){X||c.Z.progress.call(i)},loadeddata(){var e;!function(){const e=S;if(e.getStartDate){const t=e.getStartDate(),r=t.getTime?t.getTime():NaN;r===i.startDateTime||isNaN(r)||(i.setStartDateTime(r),i.trigger(n.AQ,{ready:!0,startDateTime:r}))}}(),function(e){if(P=null,e){if(e.length){for(let t=0;t<e.length;t++)if(e[t].enabled){j=t;break}-1===j&&(j=0,e[j].enabled=!0),P=(0,y.UI)(e,(function(e){return{name:e.label||e.language,language:e.language}}))}i.addTracksListener(e,"change",te),P&&i.trigger(n.j0,{currentTrack:j,tracks:P})}}(S.audioTracks),e=i.getDuration(),O&&-1!==O&&e&&e!==1/0&&i.seek(O)},canplay(){B||(B=!0,X||J(),z(),c.Z.canplay.call(i))},seeking(){const e=M,t=null!==R?re(R):i.getCurrentTime(),r=re(e);M=R,R=null,O=0,i.seeking=!0,i.trigger(n.NZ,{position:r,offset:t,duration:i.getDuration(),currentTime:e,seekRange:i.getSeekRange(),metadata:{currentTime:e}})},seeked(){c.Z.seeked.call(i),i.ensureMetaTracksActive()},waiting(){i.seeking||i.video.seeking?i.setState(n.ik):i.state===n.r0&&(i.atEdgeOfLiveStream()&&i.setPlaybackRate(1),i.stallTime=i.video.currentTime,i.setState(n.nQ))},error(){const{video:e}=i,t=e.error,r=(null==t?void 0:t.code)||-1;if((3===r||4===r)&&i.retries<i.maxRetries)return i.trigger(n.cM,new b.rG(null,w+r-1,t)),i.retries++,S.load(),void(-1!==i.currentTime&&(B=!1,i.seek(i.currentTime),i.currentTime=-1));let a=224e3,s=b.ul;1===r?a+=r:2===r?(s=b.MD,a=221e3):3===r||4===r?(a+=r-1,4===r&&e.src===location.href&&(a=224005)):s=b.ud,ie(),i.trigger(n.Ew,new b.rG(s,a,t))}};Object.keys(c.Z).forEach((e=>{if(!ne[e]){const t=c.Z[e];ne[e]=e=>{t.call(i,e)}}}));const ae=function(){if(!s.Browser.safari)return!0;const e=i.getCurrentTextTrack();return null==e?void 0:e.sideloaded},se=e=>{let r=Math.max(0,I);const i=t.qualityLabel;if(e)for(let t=0;t<e.length;t++)if(e[t].default&&(r=t),i&&e[t].label===i)return t;return D.reason="initial choice",D.level.width&&D.level.height||(D.level={}),r},oe=e=>{P=null,j=-1,D.reason||(D.reason="initial choice",D.level={}),B=!1;const t=document.createElement("source");t.src=e.file;S.src!==t.src&&(S.src=e.file)};var ce;Object.assign(this,f.ZP,l.Z,d.Z,g.Z,{renderNatively:(ce=t.renderCaptionsNatively,!(!s.OS.iOS&&!s.Browser.safari)||ce&&s.Browser.chrome),_eventsOn(){var e,t;e=ne,t=S,Object.keys(e).forEach((r=>{t.removeEventListener(r,e[r]),t.addEventListener(r,e[r])})),(0,x.Nm)(i,S)},_eventsOff(){C(ne,S),(0,x.IP)(S)},detachMedia(){d.Z.detachMedia.call(i),Q(),this.removeTracksListener(S.textTracks,"change",this.textTrackChangeHandler),this.removeTracksListener(S.textTracks,"addtrack",this.addTrackHandler),this.videoLoad&&(S.load=this.videoLoad),ae()&&this.disableTextTrack()},attachMedia(){if(d.Z.attachMedia.call(i),B=!1,this.seeking=!1,S.loop=Boolean(t.loop),s.OS.iOS&&!this.videoLoad){const e=this.videoLoad=S.load;S.load=function(){return S.src===location.href?(-1===I&&(I=se(N)),oe(N[I]),i.state===n.r0&&S.play(),void i.trigger(n.cM,new b.rG(null,324005,new Error("video.load() was called after setting video.src to empty while playing video")))):e.call(S)}}ae()&&this.enableTextTrack(),this.renderNatively&&this.setTextTracks(this.video.textTracks),this.addTracksListener(S.textTracks,"change",this.textTrackChangeHandler)},isLive:()=>S.duration===1/0});const le=function(e){const t=i.getSeekRange();if(i.isLive()){if((!F||Math.abs(Z-t.end)>1)&&((e=>{Z=e.end,F=Math.min(0,S.currentTime-Z),$=(0,v.z)()})(t),i.ensureMetaTracksActive()),(0,u.s)(t.end-t.start,L))return F}return e};i.setStartDateTime=function(e){i.startDateTime=e;const t=new Date(e).toISOString();let{start:r,end:n}=i.getSeekRange();r=Math.max(0,r),n=Math.max(r,n+10);const a={metadataType:"program-date-time",programDateTime:t,start:r,end:n},s=i.createCue(r,n,JSON.stringify(a));i.addVTTCue({type:"metadata",cue:s})},i.getCurrentTime=function(){return le(S.currentTime)};const de=()=>{let e=0;return["buffered","seekable"].forEach((t=>{const r=S[t];let i=r?r.length:0;for(;i--;){const t=Math.max(e,r.end(i));(0,y.xV)(t)&&(e=t)}})),e},ue=()=>{let e=1/0;return["buffered","seekable"].forEach((t=>{const r=S[t];let i=r?r.length:0;for(;i--;){const t=Math.min(e,r.start(i));(0,y.xV)(t)&&(e=t)}})),e};i.getDuration=function(){let e=S.duration;if(X&&e===1/0&&0===S.currentTime||isNaN(e))return 0;const t=de();if(i.isLive()&&t){const r=t-ue();(0,u.s)(r,L)&&(e=-r)}return e},i.getSeekRange=function(){const e={start:0,end:0};return S.seekable.length?(e.end=de(),e.start=ue()):(0,y.xV)(S.duration)&&(e.end=S.duration),e},i.getLiveLatency=function(){let e=null;const t=de();return i.isLive()&&t&&(e=(t+((0,v.z)()-$))/1e3-S.currentTime),e};const he=e=>{let t;return Array.isArray(e)&&e.length>0&&(t=e.map((function(e,t){return{label:e.label||t}}))),t},me=function(e){i.currentTime=-1,L=e.minDvrWindow,N=e.sources,I=se(N)},Te=function(){return S.paused&&S.played&&S.played.length&&i.isLive()&&!(0,u.s)(de()-ue(),L)&&(i.attachMedia(),S.load()),S.play()||(0,k.Z)(S)},fe=function(e){i.currentTime=-1,O=0,Q();const t=S.src,r=document.createElement("source");r.src=N[I].file;r.src!==t?(oe(N[I]),t&&S.load()):0===e&&S.currentTime>0&&(O=-1,i.seek(e)),e>0&&S.currentTime!==e&&(s.Browser.safari&&"hls"===N[0].type?S.currentTime=e:i.seek(e));const a=he(N);a&&i.trigger(n.UZ,{levels:a,currentQuality:I}),N.length&&"hls"!==N[0].type&&J()};this.stop=function(){Q(),ie(),this.clearTracks(),s.Browser.ie&&S.pause(),this.setState(n.bc)},this.destroy=function(){const{addTrackHandler:e,cueChangeHandler:t,textTrackChangeHandler:r}=i,n=S.textTracks;if(i.off(),i.videoLoad&&(S.load=i.videoLoad),A=E,C(ne,S),i.removeTracksListener(S.audioTracks,"change",te),i.removeTracksListener(n,"change",r),i.removeTracksListener(n,"addtrack",e),t)for(let e=0,r=n.length;e<r;e++)n[e].removeEventListener("cuechange",t)},this.init=function(e){i.retries=0,i.maxRetries=e.adType?0:3,me(e);const t=N[I];X=(0,o.V)(t),X&&(i.supportsPlaybackRate=!1,ne.waiting=E),i._eventsOn(),N.length&&"hls"!==N[0].type&&this.sendMediaType(N),D.reason=""},this.preload=function(e){me(e);const t=N[I],r=t.preload||"metadata";"none"!==r&&(S.setAttribute("preload",r),oe(t))},this.load=function(e){me(e),S.loop=Boolean(t.loop),fe(e.starttime),this.setupSideloadedTracks(e.tracks)},this.play=function(){return A(),Te()},this.pause=function(){Q(),A=function(){if(S.paused&&S.currentTime&&i.isLive()){const e=de(),t=e-ue(),r=!(0,u.s)(t,L),i=e-S.currentTime;if(r&&e&&(i>15||i<0)){if(R=Math.max(e-10,e-t),!(0,y.xV)(R))return;Y(S.currentTime),S.currentTime=R}}},S.pause()},this.seek=function(e){const t=i.getSeekRange();let r=e;if(e<0&&(r+=t.end),B||(B=Boolean(de())),B){W(),O=0;try{if(i.seeking=!0,i.isLive()&&(0,u.s)(t.end-t.start,L)&&(F=Math.min(0,r-Z),e<0)){r+=Math.min(12,((0,v.z)()-$)/1e3)}R=r,Y(S.currentTime),S.currentTime=r}catch(e){i.seeking=!1,O=r}}else O=r,s.Browser.firefox&&S.paused&&Te()},this.setVisibility=function(e){(e=Boolean(e))||s.OS.android?(0,h.oB)(i.container,{visibility:"visible",opacity:1}):(0,h.oB)(i.container,{visibility:"",opacity:0})},i.getFullscreen=function(){return(0,x.If)()||Boolean(S.webkitDisplayingFullscreen)},this.setCurrentQuality=function(e){I!==e&&e>=0&&N&&N.length>e&&(I=e,D.reason="api",D.level={},this.trigger(n.aM,{currentQuality:e,levels:he(N)}),t.qualityLabel=N[e].label,fe(S.currentTime||0),Te())},this.setPlaybackRate=function(e){S.playbackRate=S.defaultPlaybackRate=e},this.getPlaybackRate=function(){return S.playbackRate},this.getCurrentQuality=function(){return I},this.getQualityLevels=function(){return Array.isArray(N)?N.map((e=>(0,a.Z)(e))):[]},this.getName=function(){return{name:"html5"}},this.setCurrentAudioTrack=ee;this.getAudioTracks=()=>P||[];this.getCurrentAudioTrack=()=>j};Object.assign(L.prototype,T.Z),L.getName=function(){return{name:"html5"}};const S=L;var D=r(6886),N=r(9601),B=r(2957);const O=new RegExp([/#EXTINF:\s*(\d*(?:\.\d+)?)(?:,(.*)\s+)?/.source,/|(?!#)([\S+ ?]+)/.source,/|#EXT-X-PROGRAM-DATE-TIME:(.+)/.source,/|#.*/.source].join(""),"g"),R=/(?:(?:#(EXTM3U))|(?:#EXT-X-(PLAYLIST-TYPE):(.+))|(?:#EXT-X-(MEDIA-SEQUENCE): *(\d+))|(?:#EXT-X-(TARGETDURATION): *(\d+))|(?:#EXT-X-(KEY):(.+))|(?:#EXT-X-(START):(.+))|(?:#EXT-X-(ENDLIST))|(?:#EXT-X-(DISCONTINUITY-SEQ)UENCE:(\d+))|(?:#EXT-X-(DIS)CONTINUITY))|(?:#EXT-X-(VERSION):(\d+))|(?:#EXT-X-(MAP):(.+))|(?:(#)([^:]*):(.*))|(?:(#)(.*))(?:.*)\r?\n?/;class M{constructor(){this.relurl=null,this.tagList=[],this.cc=this.sn=this.start=0,this.title=null,this.programDateTime=this.rawProgramDateTime=null}}class I{constructor(e){this.fragments=[],this.url=e,this.live=!0,this.startSN=this.endSN=this.startCC=this.endCC=this.targetduration=this.totalduration=0}get startProgramDateTime(){return this.fragments[0]?this.fragments[0].programDateTime:null}}const A=(e,t)=>{e.rawProgramDateTime?e.programDateTime=Date.parse(e.rawProgramDateTime):null!=t&&t.programDateTime&&(e.programDateTime=t.endProgramDateTime),(0,y.xV)(e.programDateTime)||(e.programDateTime=null,e.rawProgramDateTime=null)},P=(e,t)=>{const r=new I(t);let i,n,a=0,s=0,o=0,c=null,l=new M,d=null;for(O.lastIndex=0;null!==(i=O.exec(e));){const e=i[1];if(e){l.duration=parseFloat(e);const t=` ${i[2]}`.slice(1);l.title=t||null,l.tagList.push(t?["INF",e,t]:["INF",e])}else if(i[3]){if((0,y.xV)(l.duration)){const e=a++;l.start=s,l.sn=e,l.cc=o,l.relurl=` ${i[3]}`.slice(1),A(l,c),r.fragments.push(l),c=l,s+=l.duration,l=new M}}else if(i[4])l.rawProgramDateTime=` ${i[4]}`.slice(1),l.tagList.push(["PROGRAM-DATE-TIME",l.rawProgramDateTime]),null===d&&(d=r.fragments.length);else{for(i=i[0].match(R),n=1;n<i.length&&void 0===i[n];n++);const e=` ${i[n+1]}`.slice(1),t=` ${i[n+2]}`.slice(1);switch(i[n]){case"#":l.tagList.push(t?[e,t]:[e]);break;case"MEDIA-SEQUENCE":a=r.startSN=parseInt(e,10);break;case"TARGETDURATION":r.targetduration=parseFloat(e);break;case"ENDLIST":r.live=!1;break;case"DIS":o++,l.tagList.push(["DIS"]);break;case"DISCONTINUITY-SEQ":o=parseInt(e,10);break;case"MAP":{const{rawProgramDateTime:e}=l;l=new M,l.rawProgramDateTime=e;break}}}}if(!s)throw new Error("Invalid playlist");return c&&!c.relurl&&(r.fragments.pop(),s-=c.duration),r.totalduration=s,r.endSN=a-1,r.startCC=r.fragments[0]?r.fragments[0].cc:0,r.endCC=o,d&&((e,t)=>{let r=e[t];for(let i=t-1;i>=0;i--){const t=e[i];t.programDateTime=r.programDateTime-1e3*t.duration,r=t}})(r.fragments,d),r},j=window.performance,V=window.URL,H=(e,t=0)=>{e.errors++,t&&e.errors>=t&&(e.ignore=!0)};class U{constructor(e,t){this.video=e,this.endTime=0,this.fetchTime=0,this.parsedTime=0,this.matches={},this.parent={src:"",url:null,topDomain:"",origin:"",pathname:""},this.xhr=null,this.onLevelLoaded=t,this.onResourceBufferFull=e=>{this.run(this.endTime),j.clearResourceTimings()},j.addEventListener("resourcetimingbufferfull",this.onResourceBufferFull)}run(e){const{fetchTime:t,parent:r,video:i}=this,{src:n}=i;if(!n||!n.startsWith("http")||!document.body.contains(i))return;if(r.src!==n){this.matches={},r.src=n;const e=r.url=new V(n);r.topDomain=e.hostname.replace(/.*?([^.]+\.[^.]+)$/,"$1"),r.origin=e.origin,r.pathname=e.pathname}const a=j.getEntriesByType("resource");let s=a.length;for(;s--;){const i=a[s];if(i.responseEnd<=t)break;if("video"===i.initiatorType){const t=i.name;if(t===n){this.fetchTime=Math.max(i.fetchStart,this.fetchTime);break}const a=(0,B.AO)(t);if("ts"===a||"aac"===a||"vtt"===a||"key"===a||"mp4"===a||"m4s"===a||"m4v"===a||"m4a"===a)continue;let s=this.matches[t];if(!s){const e="m3u8"===a,i=t.includes(r.topDomain),n=i&&t.startsWith(r.origin),o=n&&t.startsWith(r.origin+r.pathname);s=this.matches[t]={count:0,ignore:!1,errors:0,onlyVideo:!1,matches:{m3u8:e,topLevelDomain:i,origin:n,path:o}}}s.count++,s.onlyVideo=document.body.querySelectorAll("video audio").length<2,(s.onlyVideo||s.matches.path||s.matches.m3u8&&s.matches.topLevelDomain)&&(this.xhr&&(0,D.E)(this.xhr),this.xhr=(0,D.h)({url:t,responseType:"text",oncomplete:r=>{var i,n;const a=r.responseText;if(!a)return void H(s,3);let o;try{o=P(a,t)}catch(e){H(s,1)}if(null!=(i=o)&&null!=(n=i.fragments)&&n.length){(()=>{const t=this.parsedTime;this.parsedTime=e,this.onLevelLoaded(o,t,e)})()}},onerror(){H(s,3)}})),this.fetchTime=i.responseEnd}}a.length>50&&(0===this.fetchTime||j.now()-this.fetchTime>5e3)&&j.clearResourceTimings(),this.endTime=e}destroy(){this.video=null,this.matches=null,this.onLevelLoaded=null,j.removeEventListener("resourcetimingbufferfull",this.onResourceBufferFull),this.onResourceBufferFull=null,this.xhr&&((0,D.E)(this.xhr),this.xhr=null)}}var X=r(3343);const Z=225e3,F=e=>{const t=new Uint16Array(e.buffer);return String.fromCharCode.apply(null,t)},$=(e,t,r)=>{e&&e.removeEventListener(t,r,!1)},q=(e,t,r)=>{$(e,t,r),e.addEventListener(t,r,!1)},W=function(e,t,r){S.call(this,e,t,r);const a=this,s=a.init,o=a.load,c=a.destroy,l=a.setStartDateTime,d=a.getSeekRange;this.processPlaylistMetadata=X.q;const u=e=>{const t=a.fairplay.session;let r=e;"string"==typeof r&&(r=(e=>{const t=(0,i.t)(e),r=t.length,n=new Uint8Array(new ArrayBuffer(r));for(let e=0;e<r;e++)n[e]=t.charCodeAt(e);return n})(r)),t.update(r)},h=e=>{const t=a.fairplay.extractKey(e);"function"==typeof t.then?t.then(u):u(t)},m=e=>{const t=a.fairplay,r=e.target,i={};(r.getAllResponseHeaders()||"").trim().split(/[\r\n]+/).forEach((e=>{const t=e.split(": "),r=t.shift();i[r]=t.join(": ")}));const n={data:r.response,headers:i},s=t.licenseResponseFilter.call(e.target,n,t);s&&"function"==typeof s.then?s.then((()=>{h(n.data)})):h(n.data)},T=e=>{a.trigger(n.Ew,new b.rG(b.H4,226e3+(0,N.E)(e.currentTarget.status),e))},f=e=>{Object.keys(e.headers).forEach((t=>{e.setRequestHeader(t,e.headers[t])})),e.send(e.body)},g=e=>{const t=a.fairplay,r=e.target,i=e.message,n=new XMLHttpRequest;n.responseType=t.licenseResponseType,n.addEventListener("load",m,!1),n.addEventListener("error",T,!1);let s="";s="function"==typeof t.processSpcUrl?t.processSpcUrl(F(t.initData)):t.processSpcUrl,n.open("POST",s,!0),n.body=t.licenseRequestMessage(i,r),n.headers={},[].concat(t.licenseRequestHeaders||[]).forEach((e=>{n.setRequestHeader(e.name,e.value)}));const o=t.licenseRequestFilter.call(e.target,n,t);o&&"function"==typeof o.then?o.then((()=>{f(n)})):f(n)},p=e=>{a.trigger(n.Ew,new b.rG(b.H4,225650,e))},k=(e,t,r,i)=>{i.code+=Z,i.key=b.H4,a.trigger(n.Ew,i)},y=e=>{const t=e.target,r=e.initData;if(t.webkitKeys||t.webkitSetMediaKeys(new window.WebKitMediaKeys("com.apple.fps.1_0")),!t.webkitKeys)throw new Error("Could not create MediaKeys");const i=a.fairplay;i.initData=r,(0,D.h)(i.certificateUrl,(function(e){const n=new Uint8Array(e.response);let a=i.extractContentId(F(r));"string"==typeof a&&(a=(e=>{const t=new ArrayBuffer(2*e.length),r=new Uint16Array(t);for(let t=0,i=e.length;t<i;t++)r[t]=e.charCodeAt(t);return r})(a));const s=((e,t,r)=>{let i=0;const n=new ArrayBuffer(e.byteLength+4+t.byteLength+4+r.byteLength),a=new DataView(n);new Uint8Array(n,i,e.byteLength).set(e),i+=e.byteLength,a.setUint32(i,t.byteLength,!0),i+=4;const s=new Uint16Array(n,i,t.length);return s.set(t),i+=s.byteLength,a.setUint32(i,r.byteLength,!0),i+=4,new Uint8Array(n,i,r.byteLength).set(r),new Uint8Array(n,0,n.byteLength)})(r,a,n),o=t.webkitKeys.createSession("video/mp4",s);if(!o)throw new Error("Could not create key session");q(o,"webkitkeymessage",g),q(o,"webkitkeyerror",p),i.session=o}),k,{responseType:"arraybuffer"})},v=e=>{const t=e.sources[0];if(a.fairplay&&Object.is(a.fairplay.source,t))return;const r=t.drm;null!=r&&r.fairplay?(a.fairplay=Object.assign({},{certificateUrl:"",processSpcUrl:"",licenseResponseType:"arraybuffer",licenseRequestHeaders:[],licenseRequestMessage:e=>e,licenseRequestFilter(){},licenseResponseFilter(){},extractContentId:e=>e.split("skd://")[1],extractKey:e=>new Uint8Array(e)},r.fairplay),a.fairplay.source=t,a.fairplay.destroy=function(){$(a.video,"webkitneedkey",y);const e=this.session;e&&($(e,"webkitkeymessage",g),$(e,"webkitkeyerror",p))},q(a.video,"webkitneedkey",y)):a.fairplay&&(a.fairplay.destroy(),a.fairplay=null)};this.init=function(e){v(e),s.call(this,e)},this.load=function(e){v(e),o.call(this,e)},this.destroy=function(e){this.fairplay&&(this.fairplay.destroy(),this.fairplay=null),this.metaLoader&&(this.metaLoader.destroy(),this.metaLoader=null),c.call(this,e)},this.setStartDateTime=function(e){const{video:t,loadAndParseHlsMetadata:r}=this;if(r&&(e=>{if(!Boolean(V&&e&&e.getStartDate&&j&&j.getEntriesByType&&j.clearResourceTimings&&j.addEventListener))return!1;const t=e.getStartDate(),r=t.getTime?t.getTime():NaN;return!isNaN(r)})(t)){this.startDateTime=e;(this.metaLoader=new U(t,((r,i,n)=>{const{fragments:a,startProgramDateTime:s}=r,o=(s-e)/1e3;a.forEach((e=>{const r=e.start=e.startPTS=e.start+o;if(r>=i&&r<n&&e.tagList&&(e.tagList.forEach((([t,r])=>this.processPlaylistMetadata(t,r,e))),t.duration===1/0&&t.buffered&&t.buffered.start(0))){const e=t.buffered.start(0),r=this._tracksById.nativemetadata;if(null!=r&&r.cues){const t=r.cues;for(let i=0;i<t.length&&t[i].endTime<e;i++)r.removeCue(t[i])}}}))}))).run(0)}l.call(this,e)},this.getSeekRange=function(){const{metaLoader:e}=this,t=d.call(this);return e&&e.endTime!==t.end&&e.run(t.end),t}};Object.assign(W.prototype,S.prototype),W.getName=S.getName;const G=W}}]);
