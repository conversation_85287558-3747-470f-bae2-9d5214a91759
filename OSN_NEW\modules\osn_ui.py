from PySide6.QtCore import QO<PERSON>, Signal, Qt
from PySide6.QtWidgets import (QMessageBox, QProgressBar, QLabel, QVBoxLayout, QHBoxLayout,
                               QPushButton, QComboBox, QListWidget, QTextEdit, QWidget,
                               QListWidgetItem, QTabWidget, QCheckBox, QGroupBox,
                               QTableWidget, QTableWidgetItem, QFrame)
from PySide6.QtGui import QPixmap
import requests
import json
import os

class OSNUi(QObject):
    # Signals for communication with main window
    status_updated = Signal(str)
    progress_updated = Signal(int, str)
    content_displayed = Signal(dict)

    def __init__(self, main_window):
        super().__init__()
        self.main_window = main_window
        self.widgets = main_window.ui
        self.current_content = None
        self.current_qualities = []
        self.current_seasons = []
        self.current_series_info = {}
        self.setup_ui_connections()
        self.setup_content_tabs()

        # Connect API signals
        if hasattr(main_window, 'osn_api'):
            main_window.osn_api.error_occurred.connect(self.handle_api_error)
            main_window.osn_api.content_found.connect(self.handle_content_found)
            main_window.osn_api.login_status_changed.connect(self.handle_login_status)

    def setup_ui_connections(self):
        """Setup UI connections and initialize components"""
        # Connect search functionality from the existing UI
        if hasattr(self.widgets, 'search_button'):
            self.widgets.search_button.clicked.connect(self.handle_search)
            print("✅ Connected search_button")

        if hasattr(self.widgets, 'url_input'):
            print("✅ Found url_input")

        if hasattr(self.widgets, 'recent_combo'):
            # Use activated signal only - this is triggered only when user clicks on an item
            self.widgets.recent_combo.activated.connect(self.handle_recent_selection_by_index)
            print("✅ Connected recent_combo")

        # Connect clear functionality
        if hasattr(self.widgets, 'clear_button'):
            self.widgets.clear_button.clicked.connect(self.handle_clear)
            print("✅ Connected clear_button")

        # Setup status updates
        self.status_updated.connect(self.update_status_bar)
        self.progress_updated.connect(self.update_progress_bar)

        print("✅ UI connections setup completed")

        # Load recent URLs from file
        self.load_recent_urls_from_file()

    def setup_content_tabs(self):
        """Setup content tabs like Shahid design"""
        # Use existing content_tabs from UI if available
        if hasattr(self.widgets, 'content_tabs'):
            self.content_tabs = self.widgets.content_tabs
            print("✅ Using existing content_tabs from UI")
        else:
            # Create new content tabs widget
            self.content_tabs = QTabWidget()
            print("✅ Created new content_tabs widget")

        # Apply Shahid-like styling
        self.content_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #44475a;
                background-color: #282a36;
            }
            QTabBar::tab {
                background-color: #282a36;
                color: #f8f8f2;
                padding: 10px 20px;
                margin-right: 2px;
                border: 1px solid #44475a;
                border-bottom: none;
            }
            QTabBar::tab:selected {
                background-color: #44475a;
                border-bottom: 3px solid #00bcd4;
            }
            QTabBar::tab:!selected {
                margin-top: 2px;
            }
        """)

        # Clear existing tabs and add new ones
        self.content_tabs.clear()
        print("🔄 Cleared existing tabs")

        self.setup_content_info_tab()
        self.setup_seasons_tab()
        # Episodes will be shown in seasons tab - no separate episodes tab needed
        self.setup_available_streams_tab()
        self.setup_download_options_tab()
        self.setup_downloads_tab()

        # Keep tabs hidden initially - will show after search
        self.content_tabs.setVisible(False)
        self.content_tabs.setCurrentIndex(0)  # Show first tab when visible

        print("✅ All tabs setup completed (hidden until search)")

    def setup_content_info_tab(self):
        """Setup Content Info tab like Shahid"""
        self.content_info_tab = QWidget()
        self.content_info_layout = QVBoxLayout(self.content_info_tab)
        self.content_info_layout.setContentsMargins(10, 10, 10, 10)

        # Content info frame with horizontal layout (poster + details)
        self.content_info_frame = QFrame()
        self.content_info_frame.setFrameShape(QFrame.StyledPanel)
        self.content_info_frame.setFrameShadow(QFrame.Raised)
        self.content_info_frame.setStyleSheet("""
            QFrame {
                background-color: #282a36;
                border-radius: 8px;
                border: 1px solid #44475a;
            }
        """)
        self.content_info_frame_layout = QHBoxLayout(self.content_info_frame)
        self.content_info_frame_layout.setSpacing(20)
        self.content_info_frame_layout.setContentsMargins(20, 20, 20, 20)

        # Left side - Poster
        self.poster_label = QLabel()
        self.poster_label.setMinimumSize(240, 360)
        self.poster_label.setMaximumSize(240, 360)
        self.poster_label.setStyleSheet("""
            QLabel {
                background-color: #44475a;
                border-radius: 4px;
                border: 1px solid #6272a4;
            }
        """)
        self.poster_label.setAlignment(Qt.AlignCenter)
        self.poster_label.setScaledContents(True)
        self.content_info_frame_layout.addWidget(self.poster_label)

        # Right side - Content details
        self.details_widget = QWidget()
        self.details_layout = QVBoxLayout(self.details_widget)
        self.details_layout.setAlignment(Qt.AlignTop)
        self.details_layout.setSpacing(8)

        # Title
        self.title_label = QLabel()
        self.title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #ff79c6; margin-bottom: 10px;")
        self.details_layout.addWidget(self.title_label)

        # Type
        self.type_label = QLabel()
        self.type_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.details_layout.addWidget(self.type_label)

        # Year
        self.year_label = QLabel()
        self.year_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.details_layout.addWidget(self.year_label)

        # Episodes count
        self.episodes_count_label = QLabel()
        self.episodes_count_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.details_layout.addWidget(self.episodes_count_label)

        # Genres
        self.genres_label = QLabel()
        self.genres_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.genres_label.setWordWrap(True)
        self.details_layout.addWidget(self.genres_label)

        # Cast
        self.cast_label = QLabel()
        self.cast_label.setStyleSheet("font-size: 14px; color: #f8f8f2;")
        self.cast_label.setWordWrap(True)
        self.details_layout.addWidget(self.cast_label)

        # Description
        self.description_text = QTextEdit()
        self.description_text.setReadOnly(True)
        self.description_text.setStyleSheet("""
            QTextEdit {
                background-color: transparent;
                border: none;
                color: #f8f8f2;
                font-size: 14px;
            }
        """)
        self.description_text.setMinimumHeight(100)
        self.details_layout.addWidget(self.description_text)

        self.content_info_frame_layout.addWidget(self.details_widget)
        self.content_info_layout.addWidget(self.content_info_frame)

        # Play and Continue buttons
        self.play_button = QPushButton("Play")
        self.play_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                padding: 10px;
                font-size: 14px;
                min-width: 120px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #a5d6a7;
                color: #e8f5e9;
            }
        """)
        self.play_button.setEnabled(False)
        self.play_button.clicked.connect(self.handle_play)

        self.continue_button = QPushButton("Continue")
        self.continue_button.setStyleSheet("""
            QPushButton {
                background-color: #00bcd4;
                color: white;
                padding: 10px;
                font-size: 14px;
                min-width: 120px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #00acc1;
            }
            QPushButton:pressed {
                background-color: #0097a7;
            }
            QPushButton:disabled {
                background-color: #80deea;
                color: #e0f7fa;
            }
        """)
        self.continue_button.setEnabled(False)
        self.continue_button.clicked.connect(self.handle_continue)

        self.content_info_button_layout = QHBoxLayout()
        self.content_info_button_layout.addStretch()
        self.content_info_button_layout.addWidget(self.play_button)
        self.content_info_button_layout.addWidget(self.continue_button)
        self.content_info_layout.addLayout(self.content_info_button_layout)

        self.content_tabs.addTab(self.content_info_tab, "Content Info")

    def setup_seasons_tab(self):
        """Setup Seasons tab"""
        self.seasons_tab = QWidget()
        self.seasons_layout = QVBoxLayout(self.seasons_tab)
        self.seasons_layout.setContentsMargins(10, 10, 10, 10)

        # Seasons label
        self.seasons_label = QLabel("Seasons:")
        self.seasons_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        self.seasons_layout.addWidget(self.seasons_label)

        # Seasons list
        self.seasons_list = QListWidget()
        self.seasons_list.setStyleSheet("""
            QListWidget {
                background-color: #282a36;
                border: 1px solid #44475a;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #44475a;
            }
            QListWidget::item:selected {
                background-color: #00bcd4;
                color: white;
            }
        """)
        self.seasons_layout.addWidget(self.seasons_list)

        self.content_tabs.addTab(self.seasons_tab, "Seasons")

    # Episodes will be shown in seasons tab - no separate episodes tab needed

    def setup_available_streams_tab(self):
        """Setup Available Streams tab"""
        self.available_streams_tab = QWidget()
        self.available_streams_layout = QVBoxLayout(self.available_streams_tab)
        self.available_streams_layout.setContentsMargins(10, 10, 10, 10)

        # Streams container with scroll area
        from PySide6.QtWidgets import QScrollArea, QFrame
        streams_scroll = QScrollArea()
        streams_scroll.setWidgetResizable(True)
        streams_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        streams_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # Streams container widget
        self.streams_container = QWidget()
        self.streams_container_layout = QVBoxLayout(self.streams_container)
        self.streams_container_layout.setContentsMargins(10, 10, 10, 10)
        self.streams_container_layout.setSpacing(10)

        # Style the scroll area
        streams_scroll.setStyleSheet("""
            QScrollArea {
                background-color: #282a36;
                border: 1px solid #44475a;
                border-radius: 5px;
            }
            QScrollBar:vertical {
                background-color: #44475a;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #6272a4;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #00bcd4;
            }
        """)

        streams_scroll.setWidget(self.streams_container)
        self.available_streams_layout.addWidget(streams_scroll)

        # Initialize selected stream variable
        self.selected_stream_data = None

        self.content_tabs.addTab(self.available_streams_tab, "Available Streams")

    def setup_download_options_tab(self):
        """Setup Download Options tab"""
        self.download_options_tab = QWidget()
        self.download_options_layout = QVBoxLayout(self.download_options_tab)
        self.download_options_layout.setContentsMargins(10, 10, 10, 10)

        # Placeholder for download options
        self.download_options_label = QLabel("Download Options")
        self.download_options_label.setStyleSheet("font-size: 16px; font-weight: bold;")
        self.download_options_layout.addWidget(self.download_options_label)

        self.content_tabs.addTab(self.download_options_tab, "Download Options")

    def setup_downloads_tab(self):
        """Setup Downloads tab"""
        self.downloads_tab = QWidget()
        self.downloads_layout = QVBoxLayout(self.downloads_tab)
        self.downloads_layout.setContentsMargins(10, 10, 10, 10)

        # Downloads table
        self.downloads_table = QTableWidget()
        self.downloads_table.setColumnCount(6)  # Removed Codec and Actions columns
        self.downloads_table.setHorizontalHeaderLabels([
            "Title", "Season", "Episode", "Resolution", "Progress", "Status"
        ])

        # Set row height for better visibility
        self.downloads_table.verticalHeader().setDefaultSectionSize(60)  # Increased row height

        # Style the downloads table
        self.downloads_table.setStyleSheet("""
            QTableWidget {
                background-color: #44475a;
                color: #f8f8f2;
                border: 1px solid #6272a4;
                border-radius: 8px;
                gridline-color: #6272a4;
                font-size: 14px;
                font-weight: 500;
            }
            QTableWidget::item {
                padding: 18px 12px;
                border-bottom: 1px solid #6272a4;
                text-align: center;
            }
            QTableWidget::item:selected {
                background-color: #6272a4;
            }
            QHeaderView::section {
                background-color: #282a36;
                color: #f8f8f2;
                padding: 18px 12px;
                border: 1px solid #6272a4;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
            }
        """)

        # Set column widths
        header = self.downloads_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.downloads_table.setColumnWidth(0, 350)  # Title - increased
        self.downloads_table.setColumnWidth(1, 100)  # Season - increased
        self.downloads_table.setColumnWidth(2, 100)  # Episode - increased
        self.downloads_table.setColumnWidth(3, 120)  # Resolution - increased
        self.downloads_table.setColumnWidth(4, 200)  # Progress - increased
        self.downloads_table.setColumnWidth(5, 120)  # Status - increased

        self.downloads_layout.addWidget(self.downloads_table)

        # Progress and control section
        progress_widget = QWidget()
        progress_layout = QHBoxLayout(progress_widget)

        # Overall progress
        self.overall_progress_label = QLabel("Overall Progress: 0%")
        self.overall_progress_label.setStyleSheet("""
            QLabel {
                color: #f8f8f2;
                font-size: 14px;
                font-weight: bold;
                padding: 5px;
            }
        """)

        # Control buttons
        self.start_download_btn = QPushButton("Start Download")
        self.start_download_btn.setStyleSheet("""
            QPushButton {
                background-color: #50fa7b;
                color: #282a36;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #5af78e;
            }
            QPushButton:disabled {
                background-color: #6272a4;
                color: #44475a;
            }
        """)

        self.clear_completed_btn = QPushButton("Clear Completed")
        self.clear_completed_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffb86c;
                color: #282a36;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #ffc78a;
            }
        """)

        self.clear_all_btn = QPushButton("Clear All")
        self.clear_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff5555;
                color: #f8f8f2;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #ff6b6b;
            }
        """)

        progress_layout.addWidget(self.overall_progress_label)
        progress_layout.addStretch()
        progress_layout.addWidget(self.start_download_btn)
        progress_layout.addWidget(self.clear_completed_btn)
        progress_layout.addWidget(self.clear_all_btn)

        self.downloads_layout.addWidget(progress_widget)

        # Connect button signals
        self.start_download_btn.clicked.connect(self.start_all_downloads)
        self.clear_completed_btn.clicked.connect(self.clear_completed_downloads)
        self.clear_all_btn.clicked.connect(self.clear_all_downloads)

        # Downloads list to track download items
        self.download_items = []

        self.content_tabs.addTab(self.downloads_tab, "Downloads")

    def clear_content_data(self):
        """Clear previous content data and reset UI state"""
        try:
            # Hide content tabs initially
            self.content_tabs.setVisible(False)

            # Reset to first tab (Content Info)
            self.content_tabs.setCurrentIndex(0)

            # Clear content data
            self.current_content = None
            self.current_seasons = []
            self.current_series_info = {}

            # Clear UI elements
            self.title_label.setText("Title")
            self.type_label.setText("Type")
            self.year_label.setText("Year")
            self.episodes_count_label.setText("")
            self.genres_label.setText("")
            self.cast_label.setText("")
            self.description_text.setPlainText("")
            self.poster_label.setText("Poster")

            # Clear lists
            if hasattr(self, 'seasons_list'):
                self.seasons_list.clear()

            # Remove episodes section if it exists
            if hasattr(self, 'episodes_section'):
                self.episodes_section.setParent(None)
                self.episodes_section.deleteLater()
                delattr(self, 'episodes_section')

            # Clear streams table
            if hasattr(self, 'streams_table'):
                self.streams_table.setRowCount(0)
                self.selected_episode_label.setText("Select an episode to view available streams")
                self.select_stream_button.setEnabled(False)

            print("🧹 Content data cleared and UI reset")

        except Exception as e:
            print(f"❌ Error clearing content data: {str(e)}")

    def clear_streams_container(self):
        """Clear all stream cards from the streams container"""
        try:
            # Clear existing stream cards
            for i in reversed(range(self.streams_container_layout.count())):
                child = self.streams_container_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)
                    child.deleteLater()
            print("🧹 Streams container cleared")
        except Exception as e:
            print(f"❌ Error clearing streams container: {str(e)}")

    def handle_search(self):
        """Handle search button click - supports both URL and ID input"""
        try:
            # Get search input from input field
            search_input = self.widgets.url_input.text().strip()
            if not search_input:
                self.show_message("Error", "Please enter a valid OSN+ URL or content ID")
                return

            # Clear previous content and reset UI state
            self.clear_content_data()

            # Show loading state
            self.widgets.search_button.setText("Searching...")
            self.widgets.search_button.setEnabled(False)

            # Determine if input is URL or ID and process accordingly
            content_id, content_type = self.parse_search_input(search_input)

            if not content_id or not content_type:
                self.show_message("Error", "Invalid input. Please enter a valid OSN+ URL or content ID")
                return

            print(f"🔍 Searching for {content_type} with ID: {content_id}")

            # Store the original input for later use
            self.current_search_input = search_input
            self.current_content_id = content_id

            # Reset fallback flags for new search
            if hasattr(self, '_tried_series_fallback'):
                delattr(self, '_tried_series_fallback')
            if hasattr(self, '_tried_movie_fallback'):
                delattr(self, '_tried_movie_fallback')

            # Search based on content type
            if content_type == "movie":
                print(f"🎬 Searching for movie with ID: {content_id}")
                self.search_movie(content_id)
            elif content_type == "series":
                print(f"📺 Searching for series with ID: {content_id}")
                self.search_series(content_id)
            elif content_type == "unknown":
                # ID-only input - check content type first to avoid errors
                print(f"🔄 ID-only input, checking content type for ID: {content_id}")
                self.check_and_search_content(content_id)
            else:
                # If we can't determine the type, try movie first (more common)
                print(f"🔄 Unknown content type, trying movie first for ID: {content_id}")
                self.search_movie(content_id)  # Try movie first, will fallback to series if needed

        except Exception as e:
            self.show_message("Error", f"Search failed: {str(e)}")
        finally:
            self.widgets.search_button.setText("Search")
            self.widgets.search_button.setEnabled(True)

    def parse_search_input(self, search_input):
        """Parse search input to determine content ID and type"""
        try:
            # Check if input is a URL
            if "osnplus.com" in search_input:
                print(f"🔍 Parsing URL: {search_input}")

                # Extract content type and ID from URL
                if "/movie/" in search_input or "/movies/" in search_input:
                    # Handle different URL formats:
                    # https://osnplus.com/en-ae/movies/1035538-al-abqari
                    # https://osnplus.com/en-ae/movie/1035538-al-abqari
                    # https://osnplus.com/en-eg/movie/the-fall-guy-43820

                    # Split by '/' and find the last part that contains the ID
                    parts = search_input.split("/")
                    last_part = parts[-1] if parts else ""

                    # Extract ID from the last part
                    content_id = None
                    if "-" in last_part:
                        # Format: name-id or id-name
                        segments = last_part.split("-")
                        for segment in segments:
                            if segment.isdigit():
                                content_id = segment
                                break
                    elif last_part.isdigit():
                        # Format: just ID
                        content_id = last_part

                    if content_id:
                        print(f"✅ Extracted movie ID: {content_id}")
                        return content_id, "movie"
                    else:
                        print(f"❌ Could not extract movie ID from: {last_part}")
                        return None, None

                elif "/series/" in search_input or "/shows/" in search_input:
                    # Handle series URLs similarly
                    parts = search_input.split("/")
                    last_part = parts[-1] if parts else ""

                    # Extract ID from the last part
                    content_id = None
                    if "-" in last_part:
                        # Format: name-id or id-name
                        segments = last_part.split("-")
                        for segment in segments:
                            if segment.isdigit():
                                content_id = segment
                                break
                    elif last_part.isdigit():
                        # Format: just ID
                        content_id = last_part

                    if content_id:
                        print(f"✅ Extracted series ID: {content_id}")
                        return content_id, "series"
                    else:
                        print(f"❌ Could not extract series ID from: {last_part}")
                        return None, None
                else:
                    print(f"❌ Unknown URL format: {search_input}")
                    return None, None
            else:
                # Input is likely just an ID - we need to determine if it's movie or series
                # For now, we'll try both and see which one works
                if search_input.isdigit():
                    print(f"🔍 Input is ID: {search_input}")
                    # Return "unknown" type so the system will try movie first, then series automatically
                    return search_input, "unknown"  # Will trigger fallback logic
                else:
                    print(f"❌ Invalid input format: {search_input}")
                    return None, None

        except Exception as e:
            print(f"❌ Error parsing search input: {str(e)}")
            return None, None

    def check_and_search_content(self, content_id):
        """Check content type first, then search accordingly"""
        try:
            print(f"🔍 Checking content type for ID: {content_id}")
            self.status_updated.emit(f"Checking content type for: {content_id}")

            # Use the API's check_content_type method
            content_type = self.main_window.osn_api.check_content_type(content_id)

            if content_type == "movie":
                print(f"✅ Confirmed as movie, searching...")
                self.search_movie(content_id)
            elif content_type == "series":
                print(f"✅ Confirmed as series, searching...")
                self.search_series(content_id)
            else:
                print(f"❌ Content type could not be determined")
                self.show_message("Error", f"Content with ID {content_id} not found. Please check the ID and try again.")

        except Exception as e:
            print(f"❌ Error checking content type: {str(e)}")
            # Fallback to old method if check fails
            print(f"🔄 Falling back to movie search first")
            self.search_movie(content_id)

    def search_movie(self, movie_id):
        """Search for movie details using movie ID"""
        try:
            print(f"🎬 Searching for movie with ID: {movie_id}")
            self.status_updated.emit(f"Searching for movie: {movie_id}")

            # Store current search info for fallback
            self.current_content_id = movie_id
            self.current_search_input = movie_id

            # Clear previous data only if this is not a fallback search
            if not hasattr(self, '_tried_movie_fallback'):
                self.clear_content_data()

            # Start movie search - use silent_fallback=True for ID-only searches to avoid showing error messages
            # The fallback logic will handle trying series if movie fails
            is_id_only_search = (hasattr(self, 'current_search_input') and
                               self.current_search_input and
                               self.current_search_input.isdigit())

            # Always use silent fallback for ID-only searches or when this is a fallback attempt
            silent_fallback = is_id_only_search or hasattr(self, '_tried_movie_fallback')

            print(f"🔍 Movie search - ID only: {is_id_only_search}, Silent fallback: {silent_fallback}")
            self.main_window.osn_api.get_movie_details(movie_id, silent_fallback=silent_fallback)

        except Exception as e:
            print(f"❌ Error in search_movie: {str(e)}")
            self.show_message("Error", f"Failed to search movie: {str(e)}")

    def search_series(self, series_id):
        """Search for series details using series ID"""
        try:
            print(f"📺 Searching for series with ID: {series_id}")
            self.status_updated.emit(f"Searching for series: {series_id}")

            # Store current search info for fallback
            self.current_content_id = series_id
            self.current_search_input = series_id

            # Clear previous data only if this is not a fallback search
            if not hasattr(self, '_tried_series_fallback'):
                self.clear_content_data()

            # Start series search - use silent_fallback if this is a fallback attempt
            is_fallback = hasattr(self, '_tried_series_fallback')
            self.main_window.osn_api.get_series_details(series_id, silent_fallback=is_fallback)

        except Exception as e:
            print(f"❌ Error in search_series: {str(e)}")
            self.show_message("Error", f"Failed to search series: {str(e)}")

    def display_movie_info(self, movie_data, movie_id):
        """Display movie information in UI like series - show poster and data first"""
        try:
            # Extract movie details
            title = movie_data.get('title', {}).get('en', 'Unknown Title')
            year = movie_data.get('year', 'Unknown Year')
            imdb_rating = movie_data.get('imdbRating', {}).get('rating', 'N/A')

            # Fix genres extraction - they are dict objects with name.en
            genres_raw = movie_data.get('genres', [])
            genres = []
            for genre in genres_raw:
                if isinstance(genre, dict):
                    genre_name = genre.get('name', {}).get('en', 'Unknown Genre')
                    genres.append(genre_name)
                else:
                    genres.append(str(genre))

            # Fix cast extraction - get from credits.actors
            cast_raw = movie_data.get('credits', {}).get('actors', [])
            cast = []
            for actor in cast_raw:
                if isinstance(actor, dict):
                    actor_name = actor.get('fullName', {}).get('en', 'Unknown Actor')
                    cast.append(actor_name)
                else:
                    cast.append(str(actor))

            description = movie_data.get('description', {}).get('en', '')

            # Update title
            self.title_label.setText(title)

            # Update type
            self.type_label.setText("Type: MOVIE")

            # Update year
            self.year_label.setText(f"Year: {year}")

            # Update IMDb rating if available
            if imdb_rating != 'N/A':
                self.episodes_count_label.setText(f"IMDb Rating: {imdb_rating}")
            else:
                self.episodes_count_label.setText("")

            # Update genres
            if genres:
                self.genres_label.setText(f"Genres: {', '.join(genres)}")
            else:
                self.genres_label.setText("")

            # Update cast
            if cast:
                self.cast_label.setText(f"Cast: {', '.join(cast[:5])}")  # Show first 5 cast members
            else:
                self.cast_label.setText("")

            # Update description
            if description:
                self.description_text.setPlainText(description)
            else:
                self.description_text.setPlainText("")

            # Load poster if available
            poster_url = movie_data.get('images', {}).get('longImageWithTitleUrl')
            if poster_url:
                self.load_poster(poster_url)

            # Store current content with streams for later use
            self.current_content = {
                'type': 'movie',
                'data': movie_data,
                'id': movie_id,
                'streams': movie_data.get('streams', [])  # Store streams for later
            }

            # Show the content tabs now that we have data
            self.content_tabs.setVisible(True)
            print("✅ Content tabs are now visible")

            # For movies: Show in seasons tab as a single item like series
            self.setup_movie_as_season(title, movie_data)

            # Enable continue button for movies (like series)
            self.continue_button.setEnabled(True)

            # Add to recent URLs with content info (always save, regardless of input type)
            if hasattr(self, 'current_search_input'):
                content_info = f"{movie_id} - {title} ({year})"
                self.add_to_recent_urls(content_info)
                print(f"✅ Added to recent: {content_info}")

            self.status_updated.emit("Movie information loaded successfully")

        except Exception as e:
            self.show_message("Error", f"Failed to display movie info: {str(e)}")

    def setup_movie_as_season(self, movie_title, movie_data):
        """Setup movie in seasons tab like a series with one season"""
        try:
            # Clear seasons list
            self.seasons_list.clear()

            # Add movie as a single "season" item
            item = QListWidgetItem(f"🎬 {movie_title}")
            item.setData(Qt.UserRole, {
                'type': 'movie',
                'title': movie_title,
                'data': movie_data,
                'streams': movie_data.get('streams', [])
            })

            # Style the item to look like a movie
            item.setToolTip(f"Click to view available streams for {movie_title}")

            self.seasons_list.addItem(item)

            # Connect selection to show streams directly
            self.seasons_list.itemClicked.connect(self.handle_movie_selection)

            print(f"✅ Added movie '{movie_title}' to seasons list")

        except Exception as e:
            print(f"❌ Error setting up movie as season: {str(e)}")

    def handle_movie_selection(self, item):
        """Handle when movie item is clicked in seasons list"""
        try:
            item_data = item.data(Qt.UserRole)
            if item_data and item_data.get('type') == 'movie':
                movie_title = item_data.get('title')
                streams = item_data.get('streams', [])

                print(f"🎬 Movie '{movie_title}' selected, showing {len(streams)} streams")

                if streams:
                    # Display movie streams directly
                    self.display_movie_streams(streams, movie_title)
                    # Switch to Available Streams tab
                    self.content_tabs.setCurrentIndex(2)  # Available Streams tab
                    print("✅ Switched to Available Streams tab for movie")
                else:
                    self.show_message("Error", "No streams available for this movie")

        except Exception as e:
            print(f"❌ Error handling movie selection: {str(e)}")
            self.show_message("Error", f"Failed to load movie streams: {str(e)}")

    def display_movie_streams(self, streams, movie_title):
        """Display movie streams in Available Streams tab"""
        try:
            print(f"🎬 Displaying {len(streams)} streams for movie: {movie_title}")

            # Clear existing streams
            self.clear_streams_container()

            # Filter duplicate streams (keep only DASH streams, remove HLS duplicates)
            unique_streams = self.filter_duplicate_streams(streams)
            print(f"🔧 Filtered from {len(streams)} to {len(unique_streams)} unique streams")

            # Create stream cards for each unique stream
            for i, stream in enumerate(unique_streams):
                stream_card = self.create_stream_card(stream, i + 1)
                self.streams_container_layout.addWidget(stream_card)

            # Add stretch to push cards to top
            self.streams_container_layout.addStretch()

            print(f"✅ Displayed {len(unique_streams)} stream cards for movie")

        except Exception as e:
            print(f"❌ Error displaying movie streams: {str(e)}")
            self.show_message("Error", f"Failed to display movie streams: {str(e)}")

    def filter_duplicate_streams(self, streams):
        """Filter duplicate streams - keep only DASH streams, remove HLS duplicates"""
        try:
            unique_streams = []
            seen_qualities = set()

            # Sort streams to prioritize DASH over HLS
            sorted_streams = sorted(streams, key=lambda x: (
                x.get('highestImageResolution', ''),
                x.get('isHdr', False),
                x.get('isDolbyVision', False),
                0 if x.get('manifestType') == 'MANIFEST_TYPE_DASH' else 1  # DASH first
            ))

            for stream in sorted_streams:
                # Create a unique key based on quality characteristics
                resolution = stream.get('highestImageResolution', 'UNKNOWN')
                is_hdr = stream.get('isHdr', False)
                is_dolby_vision = stream.get('isDolbyVision', False)

                # Create quality key
                quality_key = (resolution, is_hdr, is_dolby_vision)

                # Only add if we haven't seen this quality combination before
                if quality_key not in seen_qualities:
                    seen_qualities.add(quality_key)
                    unique_streams.append(stream)

                    # Debug info
                    manifest_type = stream.get('manifestType', 'UNKNOWN')
                    print(f"🎯 Keeping stream: {resolution} HDR:{is_hdr} DV:{is_dolby_vision} Type:{manifest_type}")
                else:
                    # Debug info for skipped streams
                    manifest_type = stream.get('manifestType', 'UNKNOWN')
                    print(f"⏭️ Skipping duplicate: {resolution} HDR:{is_hdr} DV:{is_dolby_vision} Type:{manifest_type}")

            return unique_streams

        except Exception as e:
            print(f"❌ Error filtering duplicate streams: {str(e)}")
            return streams  # Return original streams if filtering fails

    def display_series_info(self, series_data, series_id):
        """Display series information in UI using new Shahid-like design"""
        try:
            # DEBUG: Print received data structure
            print("\n" + "="*100)
            print("🔍 RECEIVED DATA IN display_series_info:")
            print("="*100)
            import json
            print(json.dumps(series_data, indent=2, ensure_ascii=False))
            print("="*100)

            # Extract series details from the correct structure
            series_info = series_data.get('series', {})
            print(f"📺 Series info extracted: {bool(series_info)}")

            title = series_info.get('title', {}).get('en', 'Unknown Series')
            year = series_info.get('year', 'Unknown Year')

            # Fix genres extraction - they are dict objects, not strings
            genres_raw = series_info.get('genres', [])
            genres = []
            for genre in genres_raw:
                if isinstance(genre, dict):
                    genre_name = genre.get('name', {}).get('en', 'Unknown Genre')
                    genres.append(genre_name)
                else:
                    genres.append(str(genre))

            # Fix cast extraction - get from credits.actors
            cast_raw = series_info.get('credits', {}).get('actors', [])
            cast = []
            for actor in cast_raw[:5]:  # First 5 actors
                if isinstance(actor, dict):
                    actor_name = actor.get('fullName', {}).get('en', 'Unknown Actor')
                    cast.append(actor_name)
                else:
                    cast.append(str(actor))

            description = series_info.get('description', {}).get('en', '')
            seasons = series_info.get('seasons', [])

            print(f"📺 Extracted data:")
            print(f"  - Title: {title}")
            print(f"  - Year: {year}")
            print(f"  - Seasons count: {len(seasons)}")
            print(f"  - Genres: {genres}")
            print("="*50)

            # Calculate total episodes
            total_episodes = 0
            for season in seasons:
                total_episodes += season.get('episodesCount', 0)

            # Update title
            self.title_label.setText(title)

            # Update type
            self.type_label.setText("Type: SERIES")

            # Update year
            self.year_label.setText(f"Year: {year}")

            # Update episodes count
            self.episodes_count_label.setText(f"Seasons: {len(seasons)} | Episodes: {total_episodes}")

            # Update genres
            if genres:
                self.genres_label.setText(f"Genres: {', '.join(genres)}")
            else:
                self.genres_label.setText("")

            # Update cast
            if cast:
                self.cast_label.setText(f"Cast: {', '.join(cast[:5])}")  # Show first 5 cast members
            else:
                self.cast_label.setText("")

            # Update description
            if description:
                self.description_text.setPlainText(description)
            else:
                self.description_text.setPlainText("")

            # Load poster if available
            poster_url = series_info.get('images', {}).get('longImageWithTitleUrl')
            if poster_url:
                self.load_poster(poster_url)

            # Show the content tabs now that we have data
            self.content_tabs.setVisible(True)
            print("✅ Content tabs are now visible")

            # Store seasons data but DON'T display them yet
            self.current_seasons = seasons
            self.current_series_info = series_info

            # Show Continue button to load seasons
            self.continue_button.setVisible(True)
            self.continue_button.setText("🎬 Load Seasons & Episodes")
            self.continue_button.setEnabled(True)

            print(f"\n✅ Series info loaded. Found {len(seasons)} seasons.")
            print("📌 Click 'Load Seasons & Episodes' button to continue.")

            # Store current content
            self.current_content = {
                'type': 'series',
                'data': series_data,
                'id': series_id
            }

            # Add to recent URLs with content info (always save, regardless of input type)
            if hasattr(self, 'current_search_input'):
                content_info = f"{series_id} - {title} ({year})"
                self.add_to_recent_urls(content_info)
                print(f"✅ Added to recent: {content_info}")

            self.status_updated.emit("Series information loaded successfully")

        except Exception as e:
            self.show_message("Error", f"Failed to display series info: {str(e)}")

    def populate_quality_options(self, streams):
        """Populate quality dropdown with available streams"""
        try:
            if not hasattr(self.widgets, 'quality_combo'):
                return

            self.widgets.quality_combo.clear()
            self.current_qualities = []

            for stream in streams:
                if stream.get('manifestType') == 'MANIFEST_TYPE_DASH':
                    resolution = stream.get('highestImageResolution', 'Unknown')
                    stream_id = stream.get('streamId', '')

                    display_text = f"{resolution} - {stream_id}"
                    self.widgets.quality_combo.addItem(display_text)
                    self.current_qualities.append(stream)

        except Exception as e:
            self.show_message("Error", f"Failed to populate quality options: {str(e)}")

    def populate_season_options(self, seasons):
        """Populate season dropdown"""
        try:
            if not hasattr(self.widgets, 'season_combo'):
                return

            self.widgets.season_combo.clear()

            for season in seasons:
                season_number = season.get('seasonNumber', 1)
                episode_count = len(season.get('episodes', []))

                display_text = f"Season {season_number} ({episode_count} episodes)"
                self.widgets.season_combo.addItem(display_text)

        except Exception as e:
            self.show_message("Error", f"Failed to populate season options: {str(e)}")

    def load_poster(self, poster_url):
        """Load and display poster image"""
        try:
            if not poster_url:
                print("❌ No poster URL provided")
                self.poster_label.setText("Poster\nNot Available")
                return

            print(f"🔄 Loading poster from: {poster_url}")

            # Download poster with timeout and headers
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            response = requests.get(poster_url, headers=headers, timeout=10)
            response.raise_for_status()

            # Create pixmap and set to label
            pixmap = QPixmap()
            pixmap.loadFromData(response.content)

            if not pixmap.isNull():
                # Scale to fit label - Fix PySide6 syntax
                scaled_pixmap = pixmap.scaled(
                    240, 360,  # Use the label's fixed size
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation
                )

                self.poster_label.setPixmap(scaled_pixmap)
                print(f"✅ Poster loaded successfully")
            else:
                print("❌ Failed to create pixmap from image data")
                self.poster_label.setText("Poster\nNot Available")

        except Exception as e:
            print(f"❌ Failed to load poster: {str(e)}")
            self.poster_label.setText("Poster\nNot Available")

    def handle_download(self):
        """Handle download button click"""
        try:
            if not self.current_content:
                self.show_message("Error", "No content selected for download")
                return

            # Get selected quality
            if hasattr(self.widgets, 'quality_combo'):
                selected_index = self.widgets.quality_combo.currentIndex()
                if selected_index >= 0 and selected_index < len(self.current_qualities):
                    selected_stream = self.current_qualities[selected_index]
                else:
                    self.show_message("Error", "Please select a quality")
                    return
            else:
                self.show_message("Error", "Quality selection not available")
                return

            # Start download based on content type
            if self.current_content['type'] == 'movie':
                self.start_movie_download(selected_stream)
            elif self.current_content['type'] == 'series':
                self.start_series_download(selected_stream)

        except Exception as e:
            self.show_message("Error", f"Download failed: {str(e)}")

    def handle_continue(self):
        """Handle continue button click - Load seasons/episodes for series or streams for movies"""
        try:
            # Check if this is a movie
            if hasattr(self, 'current_content') and self.current_content:
                content_type = self.current_content.get('type')

                if content_type == 'movie':
                    print(f"\n🎬 Loading movie streams...")

                    # Get movie streams
                    movie_data = self.current_content.get('data', {})
                    streams = movie_data.get('streams', [])
                    movie_title = movie_data.get('title', {}).get('en', 'Unknown Movie')

                    if streams:
                        # Display movie streams directly
                        self.display_movie_streams(streams, movie_title)
                        # Switch to Available Streams tab
                        self.content_tabs.setCurrentIndex(2)  # Available Streams tab
                        print("✅ Switched to Available Streams tab for movie")
                    else:
                        self.show_message("Error", "No streams available for this movie")

                    # Hide continue button
                    self.continue_button.setVisible(False)

                elif content_type == 'series':
                    # Handle series - load seasons and episodes
                    if hasattr(self, 'current_seasons') and self.current_seasons:
                        print(f"\n🎬 Loading seasons and episodes data...")

                        # Populate seasons list in the Seasons tab
                        self.populate_seasons_list(self.current_seasons)

                        # Switch to Seasons tab
                        self.content_tabs.setCurrentWidget(self.seasons_tab)

                        # Hide continue button
                        self.continue_button.setVisible(False)

                        print(f"✅ Seasons loaded successfully!")
                        self.status_updated.emit("Seasons loaded. Select a season to view episodes.")

                    else:
                        self.show_message("Warning", "No seasons data available")
                else:
                    self.show_message("Warning", "Unknown content type")
            else:
                self.show_message("Warning", "No content data available")

        except Exception as e:
            print(f"❌ Error in handle_continue: {str(e)}")
            self.show_message("Error", f"Failed to continue: {str(e)}")

    def populate_seasons_list(self, seasons):
        """Populate seasons list in the Seasons tab"""
        try:
            self.seasons_list.clear()

            for season in seasons:
                season_number = season.get('seasonNumber', 1)
                episode_count = season.get('episodesCount', 0)

                display_text = f"Season {season_number} ({episode_count} episodes)"
                self.seasons_list.addItem(display_text)

            # Connect season selection
            self.seasons_list.itemClicked.connect(self.handle_season_selection)

        except Exception as e:
            self.show_message("Error", f"Failed to populate seasons list: {str(e)}")

    def handle_season_selection(self, item):
        """Handle season selection - Load episodes in the same tab"""
        try:
            season_index = self.seasons_list.row(item)
            if season_index < len(self.current_seasons):
                selected_season = self.current_seasons[season_index]
                season_id = selected_season.get('contentId')
                season_number = selected_season.get('seasonNumber', 1)

                print(f"🎬 Loading episodes for Season {season_number}...")

                # Call API to get episodes for this season
                if hasattr(self.main_window, 'osn_api'):
                    self.main_window.osn_api.get_episodes_by_season(
                        season_id,
                        self.current_series_info.get('title', {}).get('en', 'Unknown'),
                        season_number
                    )

        except Exception as e:
            self.show_message("Error", f"Failed to handle season selection: {str(e)}")

    def get_season_episodes(self, season):
        """Get episodes for selected season"""
        try:
            season_id = season.get('id')
            season_number = season.get('seasonNumber', 1)

            print(f"🎬 Getting episodes for Season {season_number}...")

            # This would call the API to get episodes
            # For now, we'll use the episodes data if available
            episodes = season.get('episodes', [])

            if episodes:
                # Add episodes to the same seasons tab
                self.add_episodes_to_seasons_tab(episodes, season_number)
            else:
                # Call API to get episodes
                if hasattr(self.main_window, 'osn_api'):
                    self.main_window.osn_api.get_episodes_by_season(
                        season_id,
                        self.current_series_info.get('title', {}).get('en', 'Unknown'),
                        season_number
                    )

        except Exception as e:
            self.show_message("Error", f"Failed to get season episodes: {str(e)}")

    def populate_episodes_list(self, episodes, season_number):
        """Populate episodes list in the Episodes tab"""
        try:
            self.episodes_list.clear()

            for i, episode in enumerate(episodes, 1):
                episode_title = episode.get('title', {}).get('en', f'Episode {i}')
                episode_number = episode.get('episodeNumber', i)

                display_text = f"Episode {episode_number}: {episode_title}"
                self.episodes_list.addItem(display_text)

            # Update range spinboxes
            self.range_from.setMaximum(len(episodes))
            self.range_to.setMaximum(len(episodes))
            self.range_to.setValue(len(episodes))

            # Connect episode controls
            self.select_all_button.clicked.connect(self.select_all_episodes)
            self.select_none_button.clicked.connect(self.select_no_episodes)
            self.select_range_button.clicked.connect(self.select_episode_range)

            print(f"✅ Episodes loaded for Season {season_number}")

        except Exception as e:
            self.show_message("Error", f"Failed to populate episodes list: {str(e)}")

    def select_all_episodes(self):
        """Select all episodes"""
        for i in range(self.episodes_list.count()):
            self.episodes_list.item(i).setSelected(True)

    def select_no_episodes(self):
        """Deselect all episodes"""
        self.episodes_list.clearSelection()

    def select_episode_range(self):
        """Select episode range"""
        try:
            start = self.range_from.value() - 1  # Convert to 0-based index
            end = self.range_to.value()  # End is exclusive

            self.episodes_list.clearSelection()
            for i in range(start, min(end, self.episodes_list.count())):
                self.episodes_list.item(i).setSelected(True)

        except Exception as e:
            self.show_message("Error", f"Failed to select episode range: {str(e)}")

    def display_episodes_list(self, episodes_data):
        """Display episodes list in the same Seasons tab"""
        try:
            series_title = episodes_data.get('series_title')
            season_number = episodes_data.get('season_number')
            episodes = episodes_data.get('episodes', [])

            print(f"🎬 Displaying episodes for {series_title} Season {season_number}")

            # Add episodes section to the seasons tab
            self.add_episodes_to_seasons_tab(episodes, season_number)

        except Exception as e:
            self.show_message("Error", f"Failed to display episodes list: {str(e)}")

    def add_episodes_to_seasons_tab(self, episodes, season_number):
        """Add episodes list to the seasons tab with selection controls"""
        try:
            # Remove existing episodes section if it exists
            if hasattr(self, 'episodes_section'):
                self.episodes_section.setParent(None)
                self.episodes_section.deleteLater()

            # Create episodes section
            self.episodes_section = QWidget()
            episodes_layout = QVBoxLayout(self.episodes_section)
            episodes_layout.setContentsMargins(0, 10, 0, 0)

            # Episodes label
            episodes_label = QLabel(f"Episodes (Season {season_number}):")
            episodes_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #ff79c6;")
            episodes_layout.addWidget(episodes_label)

            # Episode controls
            controls_layout = QHBoxLayout()

            # Select All button
            select_all_button = QPushButton("Select All")
            select_all_button.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    padding: 6px 12px;
                    border: none;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)

            # Select None button
            select_none_button = QPushButton("Select None")
            select_none_button.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    padding: 6px 12px;
                    border: none;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)

            # Range selection
            range_label = QLabel("Select Range:")
            range_label.setStyleSheet("color: #f8f8f2; font-size: 12px;")

            from PySide6.QtWidgets import QSpinBox
            range_from = QSpinBox()
            range_from.setMinimum(1)
            range_from.setMaximum(len(episodes))
            range_from.setValue(1)
            range_from.setStyleSheet("""
                QSpinBox {
                    padding: 4px;
                    border: 1px solid #44475a;
                    border-radius: 3px;
                    background-color: #282a36;
                    color: #f8f8f2;
                    font-size: 12px;
                    width: 60px;
                }
            """)

            to_label = QLabel("to")
            to_label.setStyleSheet("color: #f8f8f2; font-size: 12px;")

            range_to = QSpinBox()
            range_to.setMinimum(1)
            range_to.setMaximum(len(episodes))
            range_to.setValue(len(episodes))
            range_to.setStyleSheet("""
                QSpinBox {
                    padding: 4px;
                    border: 1px solid #44475a;
                    border-radius: 3px;
                    background-color: #282a36;
                    color: #f8f8f2;
                    font-size: 12px;
                    width: 60px;
                }
            """)

            select_range_button = QPushButton("Select")
            select_range_button.setStyleSheet("""
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    padding: 6px 12px;
                    border: none;
                    border-radius: 3px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)

            # Add controls to layout
            controls_layout.addWidget(select_all_button)
            controls_layout.addWidget(select_none_button)
            controls_layout.addWidget(range_label)
            controls_layout.addWidget(range_from)
            controls_layout.addWidget(to_label)
            controls_layout.addWidget(range_to)
            controls_layout.addWidget(select_range_button)
            controls_layout.addStretch()  # Push everything to the left

            episodes_layout.addLayout(controls_layout)

            # Episodes list with multi-selection
            episodes_list = QListWidget()
            episodes_list.setSelectionMode(QListWidget.MultiSelection)
            episodes_list.setStyleSheet("""
                QListWidget {
                    background-color: #282a36;
                    border: 1px solid #44475a;
                    border-radius: 5px;
                }
                QListWidget::item {
                    padding: 10px;
                    border-bottom: 1px solid #44475a;
                    color: #f8f8f2;
                }
                QListWidget::item:selected {
                    background-color: #00bcd4;
                    color: white;
                }
                QListWidget::item:hover {
                    background-color: #6272a4;
                    color: #f8f8f2;
                }
            """)

            # Populate episodes
            for i, episode in enumerate(episodes, 1):
                episode_title = episode.get('title', {}).get('en', f'Episode {i}')
                episode_text = f"Episode {i}: {episode_title}"

                item = QListWidgetItem(episode_text)
                item.setData(Qt.UserRole, episode)
                episodes_list.addItem(item)

            episodes_layout.addWidget(episodes_list)

            # Download buttons
            download_layout = QHBoxLayout()
            download_layout.addStretch()  # Push buttons to the right

            # View Streams button - الزر الوحيد المطلوب
            view_streams_button = QPushButton("📺 View Streams")
            view_streams_button.setStyleSheet("""
                QPushButton {
                    background-color: #9c27b0;
                    color: white;
                    border: none;
                    padding: 12px 30px;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 16px;
                }
                QPushButton:hover {
                    background-color: #8e24aa;
                }
            """)

            download_layout.addWidget(view_streams_button)
            episodes_layout.addLayout(download_layout)

            # Connect button events
            select_all_button.clicked.connect(lambda: self.select_all_episodes(episodes_list))
            select_none_button.clicked.connect(lambda: self.select_none_episodes(episodes_list))
            select_range_button.clicked.connect(lambda: self.select_range_episodes(episodes_list, range_from.value(), range_to.value()))
            view_streams_button.clicked.connect(self.handle_view_streams)

            # Connect episode selection to show streams
            episodes_list.itemClicked.connect(self.handle_episode_selection)

            # Store references for later use
            self.current_episodes_list = episodes_list
            self.current_episodes_data = episodes

            # Add episodes section to seasons layout
            self.seasons_layout.addWidget(self.episodes_section)

            print(f"✅ Added {len(episodes)} episodes to seasons tab with selection controls")

        except Exception as e:
            self.show_message("Error", f"Failed to add episodes to seasons tab: {str(e)}")

    def select_all_episodes(self, episodes_list):
        """Select all episodes in the list"""
        try:
            for i in range(episodes_list.count()):
                item = episodes_list.item(i)
                item.setSelected(True)
            print(f"✅ Selected all {episodes_list.count()} episodes")
        except Exception as e:
            print(f"❌ Error selecting all episodes: {str(e)}")

    def select_none_episodes(self, episodes_list):
        """Deselect all episodes in the list"""
        try:
            episodes_list.clearSelection()
            print(f"✅ Deselected all episodes")
        except Exception as e:
            print(f"❌ Error deselecting episodes: {str(e)}")

    def select_range_episodes(self, episodes_list, start, end):
        """Select a range of episodes"""
        try:
            # Clear current selection
            episodes_list.clearSelection()

            # Ensure valid range
            start = max(1, start)
            end = min(episodes_list.count(), end)

            if start > end:
                start, end = end, start

            # Select range (convert to 0-based indexing)
            for i in range(start - 1, end):
                item = episodes_list.item(i)
                if item:
                    item.setSelected(True)

            print(f"✅ Selected episodes {start} to {end}")
        except Exception as e:
            print(f"❌ Error selecting episode range: {str(e)}")

    def download_selected_episodes(self, episodes_list):
        """Download selected episodes"""
        try:
            selected_items = episodes_list.selectedItems()
            if not selected_items:
                self.show_message("Warning", "Please select at least one episode to download")
                return

            selected_episodes = []
            for item in selected_items:
                episode_data = item.data(Qt.UserRole)
                if episode_data:
                    selected_episodes.append(episode_data)

            if selected_episodes:
                episode_count = len(selected_episodes)
                self.show_message("Info", f"Selected {episode_count} episodes for download.\nDownload functionality will be implemented soon.")

                print(f"\n🎬 Selected {episode_count} episodes for download:")
                for ep in selected_episodes:
                    episode_number = ep.get('episodeNumber', 'N/A')
                    episode_title = ep.get('title', {}).get('en', 'No Title')
                    print(f"  - Episode {episode_number}: {episode_title}")

        except Exception as e:
            self.show_message("Error", f"Failed to process selected episodes: {str(e)}")

    def handle_episode_selection(self, item):
        """Handle episode selection to show available streams"""
        try:
            episode_data = item.data(Qt.UserRole)
            if not episode_data:
                return

            episode_number = episode_data.get('episodeNumber', 'N/A')
            episode_title = episode_data.get('title', {}).get('en', 'No Title')
            streams = episode_data.get('streams', [])

            print(f"\n🎬 Episode selected: {episode_number} - {episode_title}")
            print(f"📺 Found {len(streams)} streams")

            # Episode info is now shown in the cards directly

            # Store streams for later use when user clicks "View Streams" button
            self.current_episode_streams = streams
            self.current_selected_episode = episode_data

            # Don't automatically switch tabs - let user choose when to view streams

        except Exception as e:
            print(f"❌ Error handling episode selection: {str(e)}")
            self.show_message("Error", f"Failed to load episode streams: {str(e)}")

    def handle_view_streams(self):
        """Handle View Streams button click - add all selected episodes to download queue"""
        try:
            # تحقق من وجود قائمة الحلقات الحالية
            if not hasattr(self, 'current_episodes_list') or not hasattr(self, 'current_episodes_data'):
                self.show_message("Warning", "No episodes list available.")
                return

            selected_items = self.current_episodes_list.selectedItems()
            if not selected_items:
                self.show_message("Info", "Please select at least one episode.")
                return

            added_count = 0
            for item in selected_items:
                episode_data = item.data(Qt.UserRole)
                if not episode_data:
                    continue
                streams = episode_data.get('streams', [])
                if not streams:
                    continue
                # استخدم أول stream متاح (أو يمكنك عرض اختيار الجودة لاحقاً)
                stream = None
                for s in streams:
                    if s.get('manifestType') == 'MANIFEST_TYPE_DASH':
                        stream = s
                        break
                if not stream:
                    continue
                # جلب تفاصيل DRM و MPD
                content_id = episode_data.get('contentId')
                stream_id = stream.get('streamId')
                access_token = self.main_window.osn_api.get_access_token() if hasattr(self.main_window, 'osn_api') else None
                if not access_token:
                    continue
                drm_info = self.main_window.osn_drm.get_stream_details(content_id, stream_id, access_token)
                if not drm_info:
                    continue
                # بناء detailed_info كما في إضافة حلقة واحدة
                detailed_info = {
                    'content_data': episode_data,
                    'mpd_url': drm_info.get('mpd_url'),
                    'license_url': drm_info.get('license_url'),
                    'drm_token': drm_info.get('drm_token'),
                    'pssh': drm_info.get('pssh'),
                    'kid': drm_info.get('kid'),
                    'keys': drm_info.get('keys', []),
                }
                # إضافة إلى جدول التحميل (جودة افتراضية وأول صوت/ترجمة)
                qualities = drm_info.get('qualities', [])
                selected_quality = qualities[0] if qualities else {'resolution': '360p', 'uuid': stream_id}
                audio_tracks = drm_info.get('audio_tracks', [])
                selected_audio = [audio_tracks[0]] if audio_tracks else []
                subtitle_tracks = drm_info.get('subtitle_tracks', [])
                selected_subtitles = [subtitle_tracks[0]] if subtitle_tracks else []
                self.add_download_to_table(detailed_info, selected_quality, selected_audio, selected_subtitles)
                added_count += 1
            if added_count:
                self.show_message("Success", f"Added {added_count} episodes to download queue!")
                self.content_tabs.setCurrentIndex(4)  # Downloads tab
            else:
                self.show_message("Info", "No valid episodes were added to the download queue.")
        except Exception as e:
            print(f"❌ Error handling view streams: {str(e)}")
            self.show_message("Error", f"Failed to add episodes: {str(e)}")

    def clear_completed_downloads(self):
        """Clear completed downloads"""
        try:
            # Remove completed items from back to front to avoid index issues
            for i in range(len(self.download_items) - 1, -1, -1):
                if self.download_items[i]['status'] == 'Completed':
                    self.downloads_table.removeRow(i)
                    self.download_items.pop(i)

            self.show_message("Info", "Cleared completed downloads")

        except Exception as e:
            print(f"❌ Error clearing completed downloads: {str(e)}")

    def clear_all_downloads(self):
        """Clear all downloads"""
        try:
            # Clear table
            self.downloads_table.setRowCount(0)

            # Clear download items
            self.download_items.clear()

            self.show_message("Info", "Cleared all downloads")

        except Exception as e:
            print(f"❌ Error clearing all downloads: {str(e)}")

    def start_movie_download(self, selected_stream):
        """Start movie download"""
        try:
            content_id = self.current_content['id']
            stream_id = selected_stream.get('streamId')

            # Get access token
            access_token = self.main_window.osn_api.get_access_token()
            if not access_token:
                self.show_message("Error", "No access token available. Please login first.")
                return

            # Process DRM and start download
            drm_info = self.main_window.osn_drm.process_content_drm(content_id, stream_id, access_token)

            # Start download
            self.main_window.osn_downloader.download_movie(
                mpd_url=drm_info.get('mpd_url') if drm_info else None,
                movie_data=self.current_content['data'],
                selected_quality={'resolution': '720p', 'uuid': 'placeholder'},  # This should be parsed from stream
                drm_info=drm_info
            )

            self.status_updated.emit("Movie download started...")

        except Exception as e:
            self.show_message("Error", f"Failed to start movie download: {str(e)}")

    def start_series_download(self, selected_stream):
        """Start series download (placeholder)"""
        self.show_message("Info", "Series download functionality will be implemented")

    def open_player(self, stream_data):
        """Open video player with stream data"""
        try:
            print(f"🎬 Opening player for stream: {stream_data.get('streamId', 'Unknown')}")

            # Get content info - could be episode or movie
            if hasattr(self, 'current_selected_episode') and self.current_selected_episode:
                # For episodes
                content_data = self.current_selected_episode
                content_id = content_data.get('contentId')
                content_type = 'episode'
            elif hasattr(self, 'current_content') and self.current_content:
                # For movies
                content_data = self.current_content.get('data', {})
                content_id = self.current_content.get('id')
                content_type = 'movie'
            else:
                self.show_message("Error", "No content selected")
                return

            stream_id = stream_data.get('streamId')

            print(f"📡 Getting stream details for player - Content ID: {content_id}, Stream ID: {stream_id}")

            # Get access token
            access_token = self.main_window.osn_api.get_access_token()
            if not access_token:
                self.show_message("Error", "No access token available. Please login first.")
                return

            # Get stream details including MPD URL and DRM info
            drm_info = self.main_window.osn_drm.get_stream_details(content_id, stream_id, access_token)

            if not drm_info:
                self.show_message("Error", "Failed to get stream details")
                return

            mpd_url = drm_info.get('mpd_url')
            license_url = drm_info.get('license_url')
            drm_token = drm_info.get('drm_token')

            if not mpd_url:
                self.show_message("Error", "MPD URL not available")
                return

            print(f"✅ Stream details obtained:")
            print(f"📺 MPD URL: {mpd_url}")
            print(f"🔐 License URL: {license_url}")
            print(f"🎫 DRM Token: {drm_token}")

            # Extract PSSH and KID for DRM
            pssh = None
            kid = None
            keys = []

            if license_url and drm_token:
                try:
                    pssh = self.main_window.osn_drm.get_pssh_from_mpd(mpd_url)
                    kid = self.main_window.osn_drm.get_kid_from_mpd(mpd_url)

                    if pssh:
                        # Extract DRM keys
                        keys = self.extract_drm_keys(license_url, pssh, drm_token)
                        print(f"🔓 Extracted {len(keys)} DRM keys for player")

                except Exception as e:
                    print(f"⚠️ Error extracting DRM info: {str(e)}")

            # Launch OSN+ Player
            self.launch_osn_player(mpd_url, keys, content_data, content_type)

        except Exception as e:
            self.show_message("Error", f"Failed to open player: {str(e)}")

    def launch_osn_player(self, mpd_url, keys, content_data, content_type):
        """Launch OSN+ Player using the separate player module"""
        try:
            from .osn_player import play_osn_content

            # Launch player without showing success message
            success = play_osn_content(
                mpd_url=mpd_url,
                keys=keys,
                content_data=content_data,
                content_type=content_type,
                parent=self.main_window
            )

            if not success:
                self.show_message("Error", "Failed to start OSN+ Player")

        except Exception as e:
            print(f"❌ Error launching OSN+ player: {str(e)}")
            self.show_message("Error", f"Failed to launch OSN+ player: {str(e)}")

    def update_status_bar(self, message):
        """Update status bar with message"""
        if hasattr(self.widgets, 'status_label'):
            self.widgets.status_label.setText(message)

    def update_progress_bar(self, percentage, message):
        """Update progress bar - both general and downloads table"""
        # Update general progress bar if exists
        if hasattr(self.widgets, 'progress_bar'):
            self.widgets.progress_bar.setValue(percentage)

        # Update overall progress label in downloads tab
        if hasattr(self, 'overall_progress_label'):
            self.overall_progress_label.setText(f"Overall Progress: {percentage}%")

        # Update the active download row in downloads table
        if hasattr(self, 'downloads_table') and self.downloads_table.rowCount() > 0:
            # Find the most recent download (last row) and update its progress
            last_row = self.downloads_table.rowCount() - 1
            progress_bar = self.downloads_table.cellWidget(last_row, 4)
            if progress_bar:
                progress_bar.setValue(percentage)
                print(f"🎯 Updated downloads table progress: {percentage}%")

            # Update status
            status_item = self.downloads_table.item(last_row, 5)
            if status_item:
                if percentage == 100:
                    status_item.setText("Completed")
                elif percentage > 0:
                    status_item.setText(f"Downloading {percentage}%")
                else:
                    status_item.setText("Starting")

        self.update_status_bar(message)
        print(f"🎯 UI Progress Updated: {percentage}% - {message}")

    def show_message(self, title, message):
        """Show message box"""
        msg_box = QMessageBox()
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.exec_()

    def handle_clear(self):
        """Handle clear button click"""
        try:
            # Clear URL input
            self.widgets.url_input.clear()

            # Clear recent URLs
            if hasattr(self.widgets, 'recent_combo'):
                self.widgets.recent_combo.clear()

            # Clear content info
            self.title_label.setText("")
            self.type_label.setText("")
            self.year_label.setText("")
            self.episodes_count_label.setText("")
            self.genres_label.setText("")
            self.cast_label.setText("")
            self.description_text.setPlainText("")
            self.poster_label.clear()
            self.poster_label.setText("Poster\nNot Available")

            # Clear seasons and episodes
            self.seasons_list.clear()
            self.episodes_list.clear()

            # Reset buttons
            self.play_button.setEnabled(False)
            self.continue_button.setVisible(False)

            # Hide content tabs again
            self.content_tabs.setVisible(False)
            print("✅ Content tabs hidden")

            # Switch back to Content Info tab
            self.content_tabs.setCurrentWidget(self.content_info_tab)

            # Reset current content
            self.current_content = None
            self.current_seasons = []
            self.current_series_info = {}

        except Exception as e:
            self.show_message("Error", f"Clear failed: {str(e)}")

    def handle_play(self):
        """Handle play button click"""
        try:
            if not self.current_content:
                self.show_message("Warning", "No content selected")
                return

            # Switch to Download Options tab
            self.content_tabs.setCurrentWidget(self.download_options_tab)

        except Exception as e:
            self.show_message("Error", f"Play failed: {str(e)}")

    def handle_recent_selection_by_index(self, index):
        """Handle recent selection by index (when user clicks on dropdown item)"""
        try:
            if hasattr(self.widgets, 'recent_combo') and index >= 0:
                selected_item = self.widgets.recent_combo.itemText(index)
                print(f"🔍 Selected by index {index}: {selected_item}")

                # Only proceed if this is a user-initiated selection, not automatic loading
                if hasattr(self, '_loading_recent_data') and self._loading_recent_data:
                    print("⏭️ Skipping auto-search during data loading")
                    return

                if selected_item and selected_item.strip():
                    # Check if the selected item contains ID and name (format: "ID - Name (Year)")
                    if " - " in selected_item:
                        # Extract just the ID part
                        content_id = selected_item.split(" - ")[0].strip()
                        self.widgets.url_input.setText(content_id)
                        print(f"✅ Selected from recent: {content_id}")

                        # Automatically trigger search
                        self.handle_search()
                    else:
                        # It's a regular URL or ID
                        self.widgets.url_input.setText(selected_item.strip())

                        # Automatically trigger search
                        self.handle_search()

        except Exception as e:
            print(f"❌ Error in recent selection by index: {str(e)}")
            self.show_message("Error", f"Recent selection failed: {str(e)}")

    def add_to_recent_urls(self, content_info):
        """Add content info to recent URLs list"""
        try:
            if hasattr(self.widgets, 'recent_combo'):
                # Check if content info already exists
                for i in range(self.widgets.recent_combo.count()):
                    if self.widgets.recent_combo.itemText(i) == content_info:
                        return

                # Add to top of list
                self.widgets.recent_combo.insertItem(0, content_info)

                # Limit to 10 recent items
                if self.widgets.recent_combo.count() > 10:
                    self.widgets.recent_combo.removeItem(10)

                print(f"📝 Added to recent: {content_info}")

                # Save to file immediately
                self.save_recent_urls_to_file()

        except Exception as e:
            print(f"❌ Error adding to recent URLs: {str(e)}")

    def save_recent_urls_to_file(self):
        """Save recent URLs to file for persistence"""
        try:
            # Create config directory if it doesn't exist
            config_dir = os.path.join(os.path.dirname(__file__), "..", "config")
            os.makedirs(config_dir, exist_ok=True)

            # Get all items from recent combo
            recent_items = []
            if hasattr(self.widgets, 'recent_combo'):
                for i in range(self.widgets.recent_combo.count()):
                    item_text = self.widgets.recent_combo.itemText(i)
                    if item_text.strip():
                        recent_items.append(item_text.strip())

            # Save to JSON file
            config_file = os.path.join(config_dir, "recent_urls.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(recent_items, f, ensure_ascii=False, indent=2)

            print(f"💾 Saved {len(recent_items)} recent URLs to {config_file}")

        except Exception as e:
            print(f"❌ Error saving recent URLs to file: {str(e)}")

    def load_recent_urls_from_file(self):
        """Load recent URLs from file"""
        try:
            # Set loading flag to prevent auto-search
            self._loading_recent_data = True

            config_file = os.path.join(os.path.dirname(__file__), "..", "config", "recent_urls.json")

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    recent_items = json.load(f)

                # Add items to recent combo
                if hasattr(self.widgets, 'recent_combo') and recent_items:
                    self.widgets.recent_combo.clear()
                    for item in recent_items:
                        if item.strip():
                            self.widgets.recent_combo.addItem(item.strip())

                    print(f"📂 Loaded {len(recent_items)} recent URLs from {config_file}")
                else:
                    print("📂 No recent URLs file found or empty")
            else:
                print("📂 No recent URLs file found")

        except Exception as e:
            print(f"❌ Error loading recent URLs from file: {str(e)}")
        finally:
            # Clear loading flag after a short delay to allow UI to settle
            if hasattr(self, '_loading_recent_data'):
                delattr(self, '_loading_recent_data')

    def populate_quality_options(self, streams):
        """Populate quality dropdown with available streams"""
        try:
            # This would populate quality options if we had a quality combo
            # For now, just store the streams
            self.current_qualities = streams
            print(f"✅ Found {len(streams)} quality options")

        except Exception as e:
            self.show_message("Error", f"Failed to populate quality options: {str(e)}")

    def add_episodes_tab(self):
        """Add episodes/seasons tab to content tabs"""
        try:
            if not hasattr(self.widgets, 'content_tabs'):
                return

            # Check if tab already exists
            for i in range(self.widgets.content_tabs.count()):
                if self.widgets.content_tabs.tabText(i) == "Episodes":
                    self.widgets.content_tabs.setCurrentIndex(i)
                    return

            # Create episodes tab (placeholder for now)
            from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel

            episodes_tab = QWidget()
            episodes_layout = QVBoxLayout(episodes_tab)

            episodes_label = QLabel("Episodes functionality will be implemented here")
            episodes_label.setStyleSheet("color: #ffffff; padding: 20px;")
            episodes_layout.addWidget(episodes_label)

            self.widgets.content_tabs.addTab(episodes_tab, "Episodes")
            self.widgets.content_tabs.setCurrentWidget(episodes_tab)

        except Exception as e:
            self.show_message("Error", f"Failed to add episodes tab: {str(e)}")

    def add_download_options_tab(self):
        """Add download options tab to content tabs"""
        try:
            if not hasattr(self.widgets, 'content_tabs'):
                return

            # Check if tab already exists
            for i in range(self.widgets.content_tabs.count()):
                if self.widgets.content_tabs.tabText(i) == "Download Options":
                    self.widgets.content_tabs.setCurrentIndex(i)
                    return

            # Create download options tab (placeholder for now)
            from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel

            download_tab = QWidget()
            download_layout = QVBoxLayout(download_tab)

            download_label = QLabel("Download options functionality will be implemented here")
            download_label.setStyleSheet("color: #ffffff; padding: 20px;")
            download_layout.addWidget(download_label)

            self.widgets.content_tabs.addTab(download_tab, "Download Options")
            self.widgets.content_tabs.setCurrentWidget(download_tab)

        except Exception as e:
            self.show_message("Error", f"Failed to add download options tab: {str(e)}")

    def handle_api_error(self, error_message):
        """Handle API errors and try alternative search if needed"""
        # Check if this is a silent fallback message
        is_silent = error_message.startswith("SILENT_FALLBACK:")
        original_error_message = error_message

        try:
            if is_silent:
                # Remove the silent prefix for processing
                error_message = error_message.replace("SILENT_FALLBACK:", "")
                # For silent fallbacks, don't show any error dialog - just handle the fallback logic
                print(f"🔇 Silent fallback triggered: {error_message}")

            # Check if this was a movie search that failed and we should try series
            if (hasattr(self, 'current_content_id') and
                hasattr(self, 'current_search_input') and
                ("This might be a series ID" in error_message or
                 "Movie not found" in error_message or
                 "Invalid series link" in error_message) and
                not hasattr(self, '_tried_series_fallback')):

                print(f"🔄 Movie search failed, trying as series for ID: {self.current_content_id}")
                self._tried_series_fallback = True  # Prevent infinite loop
                # Don't show error message yet, try series search first
                self.search_series(self.current_content_id)
                return

            # If this is a silent fallback and we've already tried both, just return without showing error
            if is_silent:
                print(f"🔇 Silent fallback completed, no error dialog shown")
                return

            # Check if this was a series search that failed and we should try movie
            elif (hasattr(self, 'current_content_id') and
                  hasattr(self, 'current_search_input') and
                  ("Series not found" in error_message) and
                  not hasattr(self, '_tried_movie_fallback')):

                print(f"🔄 Series search failed, trying as movie for ID: {self.current_content_id}")
                self._tried_movie_fallback = True  # Prevent infinite loop
                # Don't show error message yet, try movie search first
                self.search_movie(self.current_content_id)
                return

            # Reset search button state only if we're not trying fallback
            if hasattr(self.widgets, 'search_button'):
                self.widgets.search_button.setText("Search")
                self.widgets.search_button.setEnabled(True)

            # Reset fallback flags for next search
            if hasattr(self, '_tried_series_fallback'):
                delattr(self, '_tried_series_fallback')
            if hasattr(self, '_tried_movie_fallback'):
                delattr(self, '_tried_movie_fallback')

            # Show error message only if both searches failed and it's not a silent fallback
            if not is_silent:
                print(f"❌ Final API Error: {error_message}")

                # Create a more user-friendly error message
                if "Movie not found" in error_message and "series ID" in error_message:
                    final_message = "Content not found. Please check the URL or ID and try again."
                elif "Series not found" in error_message:
                    final_message = "Content not found. Please check the URL or ID and try again."
                else:
                    final_message = error_message

                self.show_message("Search Error", final_message)

                # Update status
                self.status_updated.emit(f"Error: {final_message}")
            else:
                print(f"🔇 Silent fallback error (not shown to user): {error_message}")

        except Exception as e:
            print(f"❌ Error in handle_api_error: {str(e)}")
            # Only show error dialog if it's not a silent fallback
            if not is_silent:
                self.show_message("API Error", original_error_message)

    def handle_content_found(self, content_data):
        """Handle content found signals"""
        try:
            # Reset search button state
            if hasattr(self.widgets, 'search_button'):
                self.widgets.search_button.setText("Search")
                self.widgets.search_button.setEnabled(True)

            # Reset fallback flags since we found content successfully
            if hasattr(self, '_tried_series_fallback'):
                delattr(self, '_tried_series_fallback')
            if hasattr(self, '_tried_movie_fallback'):
                delattr(self, '_tried_movie_fallback')

            # Close any existing error dialogs
            self.close_error_dialogs()

            # Display content based on type
            if content_data['type'] == 'movie':
                print(f"✅ Movie found successfully!")
                self.display_movie_info(content_data['data'], content_data.get('movie_id'))
            elif content_data['type'] == 'series':
                print(f"✅ Series found successfully!")
                self.display_series_info(content_data['data'], content_data.get('series_id'))

            # Update status
            self.status_updated.emit("Content loaded successfully")

        except Exception as e:
            self.show_message("Error", f"Failed to display content: {str(e)}")

    def close_error_dialogs(self):
        """Close any existing error dialogs"""
        try:
            # Find and close any QMessageBox dialogs
            from PySide6.QtWidgets import QApplication, QMessageBox

            for widget in QApplication.topLevelWidgets():
                if isinstance(widget, QMessageBox) and widget.isVisible():
                    print("🔄 Closing existing error dialog...")
                    widget.close()

        except Exception as e:
            print(f"⚠️ Error closing dialogs: {str(e)}")

    def handle_login_status(self, is_logged_in, message):
        """Handle login status changes"""
        try:
            if is_logged_in:
                self.status_updated.emit(f"✅ {message}")
            else:
                self.status_updated.emit(f"❌ {message}")

        except Exception as e:
            print(f"Error handling login status: {str(e)}")

    def display_seasons_table(self, seasons, series_info):
        """Display seasons table like the original code"""
        try:
            from tabulate import tabulate

            # Prepare table data like the original code
            table_data = []
            season_ids = []

            series_title = series_info.get('title', {}).get('en', 'Unknown Series')

            for season in seasons:
                season_number = season.get("seasonNumber", "N/A")
                episodes_count = season.get("episodesCount", "N/A")
                season_id = season.get("contentId", "N/A")
                season_year = season.get("year", series_info.get("year", "N/A"))

                # Extract IMDb rating if available
                imdb_rating = series_info.get("imdbRating", {}).get("rating", "N/A")

                table_data.append([
                    f"{series_title} ({season_year})",
                    season_number,
                    episodes_count,
                    season_id,
                    imdb_rating
                ])
                season_ids.append(season_id)

            # Display table in console like original code
            print("\n" + "="*80)
            print("📺 SERIES DETAILS AND AVAILABLE SEASONS:")
            print("="*80)

            headers = ["Title & Year", "Season", "Episodes", "Season ID", "IMDb Rating"]
            table_output = tabulate(table_data, headers=headers, tablefmt="fancy_grid", colalign=("left", "center", "center", "center", "center"))
            print(table_output)

            print("\n" + "="*80)
            print("🎬 Select a season to view episodes:")
            print("="*80)

            # Store season data for later use
            self.current_seasons = seasons
            self.current_season_ids = season_ids
            self.current_series_info = series_info

            # Add Episodes tab to show seasons (don't auto-load episodes)
            self.add_episodes_tab(seasons, series_info)

        except Exception as e:
            print(f"Error displaying seasons table: {str(e)}")
            self.show_message("Error", f"Failed to display seasons: {str(e)}")

    def add_episodes_tab(self, seasons, series_info):
        """Add Episodes tab with seasons list"""
        try:
            # Create Episodes tab content
            episodes_widget = self.create_episodes_widget(seasons, series_info)

            # Add tab to content tabs
            if hasattr(self.widgets, 'content_tabs'):
                # Remove existing Episodes tab if exists
                for i in range(self.widgets.content_tabs.count()):
                    if self.widgets.content_tabs.tabText(i) == "Episodes":
                        self.widgets.content_tabs.removeTab(i)
                        break

                self.widgets.content_tabs.addTab(episodes_widget, "Episodes")
                self.widgets.content_tabs.setCurrentWidget(episodes_widget)

        except Exception as e:
            self.show_message("Error", f"Failed to add episodes tab: {str(e)}")

    def create_episodes_widget(self, seasons, series_info):
        """Create episodes widget with seasons list"""
        try:
            from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QListWidget, QListWidgetItem, QPushButton
            from PySide6.QtCore import Qt

            widget = QWidget()
            layout = QVBoxLayout(widget)

            # Title
            title_label = QLabel("📺 Available Seasons")
            title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #ff79c6; margin: 10px;")
            layout.addWidget(title_label)

            # Seasons list
            seasons_list = QListWidget()
            seasons_list.setStyleSheet("""
                QListWidget {
                    background-color: #44475a;
                    border: 1px solid #6272a4;
                    border-radius: 5px;
                    color: #f8f8f2;
                    font-size: 14px;
                    padding: 5px;
                }
                QListWidget::item {
                    padding: 10px;
                    border-bottom: 1px solid #6272a4;
                }
                QListWidget::item:hover {
                    background-color: #6272a4;
                }
                QListWidget::item:selected {
                    background-color: #ff79c6;
                    color: #282a36;
                }
            """)

            # Add seasons to list
            for season in seasons:
                season_number = season.get("seasonNumber", "N/A")
                episodes_count = season.get("episodesCount", "N/A")
                season_id = season.get("contentId", "N/A")
                season_year = season.get("year", series_info.get("year", "N/A"))

                item_text = f"Season {season_number} ({season_year}) - {episodes_count} Episodes"
                item = QListWidgetItem(item_text)
                item.setData(Qt.UserRole, season)  # Store season data
                seasons_list.addItem(item)

            layout.addWidget(seasons_list)

            # Connect selection
            seasons_list.itemDoubleClicked.connect(lambda item: self.on_season_selected(item))

            # Select season button
            select_button = QPushButton("🎬 View Episodes")
            select_button.setStyleSheet("""
                QPushButton {
                    background-color: #ff79c6;
                    color: #282a36;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    font-weight: bold;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #ff92d0;
                }
                QPushButton:pressed {
                    background-color: #ff5cc6;
                }
            """)
            select_button.clicked.connect(lambda: self.on_season_selected(seasons_list.currentItem()))
            layout.addWidget(select_button)

            return widget

        except Exception as e:
            self.show_message("Error", f"Failed to create episodes widget: {str(e)}")
            return QWidget()

    def on_season_selected(self, item):
        """Handle season selection"""
        try:
            if not item:
                self.show_message("Warning", "Please select a season first")
                return

            season_data = item.data(Qt.UserRole)
            if not season_data:
                return

            season_id = season_data.get("contentId")
            season_number = season_data.get("seasonNumber", 1)

            if season_id:
                # Get episodes for selected season
                print(f"\n🎬 Loading episodes for Season {season_number}...")
                self.status_updated.emit(f"Loading episodes for Season {season_number}...")

                # Call API to get episodes
                if hasattr(self.main_window, 'osn_api'):
                    self.main_window.osn_api.get_episodes_by_season(
                        season_id,
                        self.current_series_info.get('title', {}).get('en', 'Unknown'),
                        season_number
                    )

        except Exception as e:
            self.show_message("Error", f"Failed to load season episodes: {str(e)}")

    def start_all_downloads(self):
        """Start the first queued download only (sequential mode)"""
        try:
            started = False
            for i, item in enumerate(self.download_items):
                if item['status'] == 'Queued':
                    self.start_individual_download(i)
                    started = True
                    break  # ابدأ واحدة فقط
            if started:
                self.show_message("Info", "Started sequential download queue")
            else:
                self.show_message("Info", "No queued downloads to start")
        except Exception as e:
            print(f"❌ Error starting all downloads: {str(e)}")

    def add_download_to_table(self, detailed_info, selected_quality, selected_audio, selected_subtitles):
        """Add a download item to the downloads table and queue"""
        try:
            # استخراج عنوان الحلقة أو الفيلم
            content_data = detailed_info.get('content_data', {})
            content_title = content_data.get('title', {}).get('en', 'Unknown')
            season_number = content_data.get('seasonNumber', '')
            episode_number = content_data.get('episodeNumber', '')
            resolution = selected_quality.get('resolution', 'N/A')

            # إنشاء عنصر التحميل
            download_item = {
                'detailed_info': detailed_info,
                'selected_quality': selected_quality,
                'selected_audio': selected_audio,
                'selected_subtitles': selected_subtitles,
                'status': 'Queued',
                'progress': 0
            }
            self.download_items.append(download_item)

            # إضافة صف للجدول
            row_position = self.downloads_table.rowCount()
            self.downloads_table.insertRow(row_position)

            # تعبئة بيانات الجدول
            from PySide6.QtWidgets import QTableWidgetItem, QProgressBar
            title_item = QTableWidgetItem(content_title)
            title_item.setTextAlignment(Qt.AlignCenter)
            self.downloads_table.setItem(row_position, 0, title_item)

            season_item = QTableWidgetItem(str(season_number))
            season_item.setTextAlignment(Qt.AlignCenter)
            self.downloads_table.setItem(row_position, 1, season_item)

            episode_item = QTableWidgetItem(str(episode_number))
            episode_item.setTextAlignment(Qt.AlignCenter)
            self.downloads_table.setItem(row_position, 2, episode_item)

            resolution_item = QTableWidgetItem(str(resolution))
            resolution_item.setTextAlignment(Qt.AlignCenter)
            self.downloads_table.setItem(row_position, 3, resolution_item)

            progress_bar = QProgressBar()
            progress_bar.setValue(0)
            progress_bar.setAlignment(Qt.AlignCenter)
            self.downloads_table.setCellWidget(row_position, 4, progress_bar)

            status_item = QTableWidgetItem("Queued")
            status_item.setTextAlignment(Qt.AlignCenter)
            self.downloads_table.setItem(row_position, 5, status_item)

            # حفظ مرجع الصف في العنصر
            download_item['row'] = row_position
            download_item['progress_bar'] = progress_bar

            print(f"✅ Added download to table: {content_title}")
        except Exception as e:
            print(f"❌ Error adding download to table: {str(e)}")
            self.show_message("Error", f"Failed to add download: {str(e)}")
